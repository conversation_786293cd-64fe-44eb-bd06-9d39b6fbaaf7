import { defineConfig, loadEnv } from "vite";
import uni from "@dcloudio/vite-plugin-uni";
import UnoCSS from "unocss/vite";

// https://vitejs.dev/config/
// https://github.com/unocss/unocss
export default defineConfig(({ mode }) => {
	const env = loadEnv(mode, process.env.VITE_ROOT_DIR);
	return {
		plugins: [uni(), UnoCSS()],
		server: {
			proxy: {
				"^/proxy": {
					target: env.VITE_HTTP_BASE_URL,
					changeOrigin: true,
					rewrite: (path) => path.replace(/^\/proxy/, ""),
				},
			},
		},
		esbuild: {
			drop: ["console", "debugger"],
		},
	};
});
