export function parseRichText(html) {
    // 视频标签正则
    const videoRegex = /<video[^>]*>([\s\S]*?)<\/video>/gi
    const sourceRegex = /<source\s+src="([^"]+)"[^>]*>/i
    const posterRegex = /poster="([^"]+)"/i
    const controlsRegex = /controls/i

    // 存储解析结果
    const nodes = []
    let lastIndex = 0

    // 提取所有视频标签
    let match
    while ((match = videoRegex.exec(html)) !== null) {
        const videoTag = match[0]
        const videoStart = match.index
        const videoEnd = videoStart + videoTag.length

        // 添加视频前的文本内容
        if (videoStart > lastIndex) {
            const textContent = html.substring(lastIndex, videoStart)
            nodes.push({
                type: 'text',
                content: textContent,
            })
        }

        // 解析视频属性
        const srcMatch = videoTag.match(sourceRegex)
        const posterMatch = videoTag.match(posterRegex)
        const controls = controlsRegex.test(videoTag)

        // 添加视频节点
        nodes.push({
            type: 'video',
            src: srcMatch ? srcMatch[1] : '',
            poster: posterMatch ? posterMatch[1] : '',
            controls,
        })

        lastIndex = videoEnd
    }

    // 添加剩余文本
    if (lastIndex < html.length) {
        nodes.push({
            type: 'text',
            content: html.substring(lastIndex),
        })
    }

    return nodes
}
