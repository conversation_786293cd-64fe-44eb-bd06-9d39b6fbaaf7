export function debounce(fn, wait = 200) {
	let timer = null;
	return function (...args) {
		if (timer) clearTimeout(timer);
		timer = setTimeout(() => {
			fn.apply(this, args);
		}, wait);
	};
}

/**
 * 节流函数 - 限制函数执行频率
 * @param {Function} fn 要节流的函数
 * @param {number} wait 节流间隔时间（毫秒），默认16ms（60fps）
 * @returns {Function} 节流后的函数
 */
export function throttle(fn, wait = 16) {
	let lastTime = 0;
	return function (...args) {
		const now = Date.now();
		if (now - lastTime >= wait) {
			lastTime = now;
			fn.apply(this, args);
		}
	};
}
