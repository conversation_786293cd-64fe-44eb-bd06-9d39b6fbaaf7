/**
 * Canvas工具类 - 参考lime-echarts核心实现
 * 高度可扩展的Canvas绘制系统
 */
import { compareVersion } from '@/common/utils'

// ==================== 主题系统 ====================

// 预设颜色主题
export const THEMES = {
    // 健康主题
    health: {
        name: '健康主题',
        colors: {
            excellent: '#52c41a', // 优秀 - 绿色
            good: '#00b496', // 良好 - 青绿色
            normal: '#faad14', // 一般 - 橙色
            poor: '#ff7875', // 较差 - 浅红色
            bad: '#ff4d4f', // 差 - 红色
        },
        background: '#f0f0f0',
    },

    // 商务主题
    business: {
        name: '商务主题',
        colors: {
            excellent: '#1890ff', // 蓝色
            good: '#13c2c2', // 青色
            normal: '#faad14', // 橙色
            poor: '#f759ab', // 粉色
            bad: '#ff4d4f', // 红色
        },
        background: '#e8e8e8',
    },

    // 彩虹主题
    rainbow: {
        name: '彩虹主题',
        colors: {
            excellent: '#9254de', // 紫色
            good: '#1890ff', // 蓝色
            normal: '#52c41a', // 绿色
            poor: '#faad14', // 橙色
            bad: '#ff4d4f', // 红色
        },
        background: '#f5f5f5',
    },

    // 暗色主题
    dark: {
        name: '暗色主题',
        colors: {
            excellent: '#73d13d', // 亮绿色
            good: '#36cfc9', // 亮青色
            normal: '#ffc53d', // 亮橙色
            poor: '#ff9c6e', // 亮红橙色
            bad: '#ff7875', // 亮红色
        },
        background: '#434343',
    },
}

export const HEALTHY_SCORE = 95
export const NORMAL_SCORE = 80
// 默认健康等级配置（提取为通用配置）
export const DEFAULT_HEALTH_LEVELS = [
    { score: HEALTHY_SCORE, level: '良好', color: '#05B89B' },
    { score: NORMAL_SCORE, level: '一般', color: '#05B89B' },
    // 默认（小于80分）：警惕，橙色
    { score: 0, level: '警惕', color: '#FF9800' },
]

// 根据分数和健康等级配置获取健康信息
export function getHealthInfo(score, healthLevels = DEFAULT_HEALTH_LEVELS) {
    const matchedLevel = healthLevels.find((level) => score >= level.score)
    return matchedLevel
        ? {
              level: matchedLevel.level,
              color: matchedLevel.color,
          }
        : {
              level: '警惕',
              color: '#FF9800',
          }
}

// 根据分数获取健康等级
export function getHealthLevel(score, healthLevels = DEFAULT_HEALTH_LEVELS) {
    return getHealthInfo(score, healthLevels).level
}

// 根据分数获取健康颜色
export function getHealthColor(score, healthLevels = DEFAULT_HEALTH_LEVELS) {
    return getHealthInfo(score, healthLevels).color
}

// ==================== 动画系统 ====================

// 缓动函数
export const EASING_FUNCTIONS = {
    linear: (t) => t,
    easeInQuad: (t) => t * t,
    easeOutQuad: (t) => t * (2 - t),
    easeInOutQuad: (t) => (t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t),
    easeInCubic: (t) => t * t * t,
    easeOutCubic: (t) => --t * t * t + 1,
    easeInOutCubic: (t) => (t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1),
    easeInQuart: (t) => t * t * t * t,
    easeOutQuart: (t) => 1 - --t * t * t * t,
    easeInOutQuart: (t) => (t < 0.5 ? 8 * t * t * t * t : 1 - 8 * --t * t * t * t),
}

// 动画管理器
export class AnimationManager {
    constructor() {
        this.animations = new Map()
    }

    // 创建动画
    animate(id, from, to, duration, easingName = 'easeInOutCubic', onUpdate, onComplete) {
        // 停止现有动画
        this.stop(id)

        const startTime = Date.now()
        const easingFn = EASING_FUNCTIONS[easingName] || EASING_FUNCTIONS.easeInOutCubic

        const animation = {
            id,
            from,
            to,
            duration,
            startTime,
            easingFn,
            onUpdate,
            onComplete,
            active: true,
        }

        this.animations.set(id, animation)
        this._runAnimation(animation)

        return animation
    }

    // 运行动画
    _runAnimation(animation) {
        if (!animation.active) return

        const now = Date.now()
        const elapsed = now - animation.startTime
        const progress = Math.min(elapsed / animation.duration, 1)

        // 应用缓动函数
        const easedProgress = animation.easingFn(progress)
        const currentValue = animation.from + (animation.to - animation.from) * easedProgress

        // 更新回调
        if (animation.onUpdate) {
            animation.onUpdate(currentValue, progress)
        }

        // 检查是否完成
        if (progress >= 1) {
            this.animations.delete(animation.id)
            if (animation.onComplete) {
                animation.onComplete(animation.to)
            }
        } else {
            // 继续动画
            setTimeout(() => this._runAnimation(animation), 16) // ~60fps
        }
    }

    // 停止动画
    stop(id) {
        const animation = this.animations.get(id)
        if (animation) {
            animation.active = false
            this.animations.delete(id)
        }
    }

    // 停止所有动画
    stopAll() {
        for (const animation of this.animations.values()) {
            animation.active = false
        }
        this.animations.clear()
    }

    // 检查是否有活动动画
    hasActiveAnimation(id) {
        return this.animations.has(id)
    }
}

// 全局动画管理器实例
const globalAnimationManager = new AnimationManager()

// 通用动画渲染函数
export async function renderWithAnimation(animationId, currentValue, targetValue, renderFunction, options = {}) {
    const { duration = 1000, easing = 'easeInOutCubic', onUpdate = null, onComplete = null } = options

    console.log(`开始动画 [${animationId}]: ${currentValue} → ${targetValue}`)

    return new Promise((resolve) => {
        globalAnimationManager.animate(
            animationId,
            currentValue,
            targetValue,
            duration,
            easing,
            // onUpdate
            async (currentVal) => {
                try {
                    await renderFunction(currentVal)
                    if (onUpdate) onUpdate(currentVal)
                } catch (error) {
                    console.error(`动画渲染失败 [${animationId}]:`, error)
                }
            },
            // onComplete
            async (finalVal) => {
                try {
                    const result = await renderFunction(finalVal)
                    console.log(`动画完成 [${animationId}]: ${finalVal}`)
                    if (onComplete) onComplete(finalVal)
                    resolve(result || { success: true, value: finalVal })
                } catch (error) {
                    console.error(`动画完成渲染失败 [${animationId}]:`, error)
                    resolve({ success: false, error: error.message })
                }
            },
        )
    })
}

// 停止指定动画
export function stopAnimation(animationId) {
    globalAnimationManager.stop(animationId)
}

// 停止所有动画
export function stopAllAnimations() {
    globalAnimationManager.stopAll()
}

// 检查动画状态
export function hasActiveAnimation(animationId) {
    return globalAnimationManager.hasActiveAnimation(animationId)
}

// ==================== 绘制器系统 ====================

// 基础绘制器接口
export class BaseRenderer {
    constructor(canvasManager) {
        this.canvasManager = canvasManager
    }

    // 抽象方法，子类必须实现
    async render(data, options = {}) {
        throw new Error('render method must be implemented')
    }

    // 获取Canvas上下文
    async getContext() {
        return await this.canvasManager.getContext()
    }

    // 清空画布
    clear() {
        this.canvasManager.clear()
    }

    // 完成绘制
    draw() {
        this.canvasManager.draw()
    }

    // 设置样式（兼容新旧Canvas API）
    setStyle(ctx, property, value) {
        if (this.canvasManager.use2dCanvas) {
            ctx[property] = value
        } else {
            const methodName = `set${property.charAt(0).toUpperCase() + property.slice(1)}`
            if (ctx[methodName]) {
                ctx[methodName](value)
            }
        }
    }
}

// 圆环绘制器
export class CircleRenderer extends BaseRenderer {
    constructor(canvasManager) {
        super(canvasManager)
        this.currentScore = 0 // 当前显示的分数
        this.cachedContext = null // 缓存的Canvas上下文
        this.healthInfoCache = new Map() // 健康信息缓存
    }

    // 获取缓存的Canvas上下文
    async getCachedContext() {
        if (!this.cachedContext) {
            this.cachedContext = await this.getContext()
        }
        return this.cachedContext
    }

    // 使用通用的健康等级配置
    static HEALTH_LEVELS = DEFAULT_HEALTH_LEVELS

    // 获取健康等级信息（等级和颜色）- 带缓存优化
    getHealthInfo(score, healthLevels = CircleRenderer.HEALTH_LEVELS) {
        // 对于整数分数，使用缓存（只有使用默认配置时才缓存）
        const useCache = healthLevels === CircleRenderer.HEALTH_LEVELS
        const intScore = Math.round(score)

        if (useCache && this.healthInfoCache.has(intScore)) {
            return this.healthInfoCache.get(intScore)
        }

        // 使用find方法查找匹配的等级（更简洁现代）
        const { level = '警惕', color = '#FF9800' } = healthLevels.find((item) => score >= item.score) || {}

        const result = { level, color }

        // 只有使用默认配置时才缓存
        if (useCache) {
            this.healthInfoCache.set(intScore, result)
        }

        return result
    }

    // 获取状态文字
    getStatusText(score, healthLevels) {
        return this.getHealthInfo(score, healthLevels).level
    }

    // 获取健康等级对应的颜色
    getHealthColor(score, healthLevels) {
        return this.getHealthInfo(score, healthLevels).color
    }

    // 清理缓存（防止内存泄漏）
    clearCache() {
        this.healthInfoCache.clear()
        this.cachedContext = null
    }

    // 默认配置 - 详细参数说明
    static defaultOptions = {
        // === 基础几何参数 ===
        radius: 35, // 中线半径（像素），两个环的共同中线
        lineWidth: 8, // 进度环线宽（像素）
        bgLineWidth: null, // 背景环线宽（像素），null时使用lineWidth

        // === 角度参数 ===
        startAngle: -Math.PI / 2, // 起始角度（弧度），-π/2表示12点方向
        endAngle: null, // 结束角度（弧度），null时根据分数计算
        maxAngle: Math.PI * 2, // 最大角度范围（弧度），2π表示完整圆环
        clockwise: true, // 是否顺时针绘制

        // === 颜色和主题 ===
        theme: THEMES.health, // 主题配置
        healthLevels: DEFAULT_HEALTH_LEVELS, // 健康等级配置
        fgColor: null, // 进度环颜色（覆盖主题）
        bgColor: null, // 背景环颜色（覆盖主题）

        // === 样式参数 ===
        lineCap: 'round', // 线条端点样式：round(圆形) | butt(平直) | square(方形)
        showBackground: true, // 是否显示背景环
        shadowBlur: 0, // 阴影模糊度
        shadowColor: 'rgba(0,0,0,0.1)', // 阴影颜色

        // === 文字参数 ===
        showText: false, // 是否显示文字
        scoreText: null, // 分数文字，null时显示分数值
        statusText: null, // 状态文字，null时显示健康等级
        textColor: '#1a1a1a', // 文字颜色
        scoreSize: 20, // 分数字体大小
        statusSize: 12, // 状态字体大小
        textOffsetY: 0, // 文字垂直偏移

        // === 功能参数 ===
        animation: false, // 是否启用动画
        animationDuration: 1000, // 动画时长（毫秒）
        animationEasing: 'easeInOutCubic', // 缓动函数类型
        minValue: 0, // 最小值
        maxValue: 100, // 最大值
    }

    async render(score, options = {}) {
        try {
            const opts = { ...CircleRenderer.defaultOptions, ...options }
            const { animation, animationDuration, animationEasing } = opts

            // 如果启用动画且分数发生变化
            if (animation && score !== this.currentScore) {
                const animationId = `circle-${this.canvasManager.canvasId}`

                // 使用通用动画函数
                return await renderWithAnimation(
                    animationId,
                    this.currentScore,
                    score,
                    // 渲染函数
                    async (currentScore) => {
                        this.currentScore = currentScore
                        return await this._renderFrame(currentScore, opts)
                    },
                    // 动画选项
                    {
                        duration: animationDuration,
                        easing: animationEasing,
                    },
                )
            }

            // 直接渲染（无动画）
            this.currentScore = score
            return await this._renderFrame(score, opts)
        } catch (error) {
            console.error('圆环绘制失败:', error)
            return { success: false, error: error.message }
        }
    }

    // 检查是否需要重绘背景
    _needsBackgroundRedraw(opts) {
        if (!this.backgroundDrawn || !this.lastOptions) return true

        // 检查影响背景的参数是否变化
        const bgParams = ['radius', 'bgLineWidth', 'theme', 'bgColor', 'showBackground', 'shadowBlur', 'shadowColor']
        return bgParams.some((param) => this.lastOptions[param] !== opts[param])
    }

    // 渲染单帧
    async _renderFrame(score, opts) {
        try {
            const ctx = await this.getCachedContext()

            // 清空画布
            this.clear()

            // 计算基础参数
            const centerX = this.canvasManager.width / 2
            const centerY = this.canvasManager.height / 2

            // 解构参数
            const {
                radius,
                lineWidth,
                bgLineWidth,
                startAngle,
                endAngle,
                maxAngle,
                clockwise,
                theme,
                healthLevels,
                fgColor,
                bgColor,
                lineCap,
                showBackground,
                shadowBlur,
                shadowColor,
                minValue,
                maxValue,
                showText,
                scoreText,
                statusText,
                textColor,
                scoreSize,
                statusSize,
                textOffsetY,
            } = opts

            // 计算实际使用的参数
            const actualBgLineWidth = bgLineWidth !== null ? bgLineWidth : lineWidth
            const actualBgColor = bgColor || theme.background
            const actualFgColor = fgColor || this.getHealthColor(score, healthLevels)

            // 计算角度
            const normalizedScore = Math.max(minValue, Math.min(maxValue, score))
            const progress = (normalizedScore - minValue) / (maxValue - minValue)
            const actualEndAngle = endAngle !== null ? endAngle : startAngle + (clockwise ? progress * maxAngle : -progress * maxAngle)

            // 设置阴影
            if (shadowBlur > 0) {
                ctx.shadowBlur = shadowBlur
                ctx.shadowColor = shadowColor
            }

            // 绘制背景环
            if (showBackground) {
                ctx.beginPath()
                ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI)
                this.setStyle(ctx, 'strokeStyle', actualBgColor)
                ctx.lineWidth = actualBgLineWidth
                ctx.stroke()
            }

            // 清除阴影
            if (shadowBlur > 0) {
                ctx.shadowBlur = 0
            }

            // 绘制进度环
            if (normalizedScore > minValue) {
                ctx.beginPath()
                if (clockwise) {
                    ctx.arc(centerX, centerY, radius, startAngle, actualEndAngle)
                } else {
                    ctx.arc(centerX, centerY, radius, startAngle, actualEndAngle, true)
                }
                this.setStyle(ctx, 'strokeStyle', actualFgColor)
                this.setStyle(ctx, 'lineCap', lineCap)
                ctx.lineWidth = lineWidth
                ctx.stroke()
            }

            // 绘制文字
            if (showText) {
                const actualScoreText = scoreText !== null ? scoreText : Math.round(normalizedScore).toString()
                const actualStatusText = statusText !== null ? statusText : this.getStatusText(normalizedScore, healthLevels)

                // 设置文字样式
                this.setStyle(ctx, 'fillStyle', textColor)
                this.setStyle(ctx, 'textAlign', 'center')
                this.setStyle(ctx, 'textBaseline', 'middle')

                // 绘制分数（上方，大字体，粗体）
                this.setStyle(ctx, 'font', `bold ${scoreSize}px Arial`)
                ctx.fillText(actualScoreText, centerX, centerY - 8 + textOffsetY)

                // 绘制状态（下方，小字体，正常）
                this.setStyle(ctx, 'font', `${statusSize}px Arial`)
                ctx.fillText(actualStatusText, centerX, centerY + 12 + textOffsetY)
            }

            // 完成绘制
            this.draw()

            return this._createResult(score, opts)
        } catch (error) {
            console.error('圆环帧绘制失败:', error)
            return { success: false, error: error.message }
        }
    }

    // 创建结果对象
    _createResult(score, opts) {
        const { radius, bgLineWidth, lineWidth, minValue, maxValue, healthLevels } = opts
        const normalizedScore = Math.max(minValue, Math.min(maxValue, score))
        const progress = (normalizedScore - minValue) / (maxValue - minValue)
        const actualBgLineWidth = bgLineWidth !== null ? bgLineWidth : lineWidth

        // 一次性获取健康等级信息（等级和颜色）
        const healthInfo = this.getHealthInfo(score, healthLevels)

        return {
            success: true,
            score: normalizedScore,
            progress: progress,
            color: healthInfo.color,
            level: healthInfo.level,
            radius: radius,
            bgLineWidth: actualBgLineWidth,
            fgLineWidth: lineWidth,
        }
    }
}

// 获取设备信息
function getDeviceInfo() {
    try {
        if (uni.getDeviceInfo || uni.canIUse('getDeviceInfo')) {
            return uni.getDeviceInfo()
        } else {
            return uni.getSystemInfoSync()
        }
    } catch (error) {
        return { platform: 'unknown' }
    }
}

// 获取应用基础信息
function getAppBaseInfo() {
    try {
        if (uni.getAppBaseInfo || uni.canIUse('getAppBaseInfo')) {
            return uni.getAppBaseInfo()
        } else {
            return uni.getSystemInfoSync()
        }
    } catch (error) {
        return { SDKVersion: '0.0.0' }
    }
}

// 版本检测
function gte(version) {
    const { platform } = getDeviceInfo()
    let { SDKVersion } = getAppBaseInfo()
    // #ifdef MP-WEIXIN
    return platform !== 'mac' && compareVersion(SDKVersion, version) >= 0
    // #endif
    return compareVersion(SDKVersion, version) >= 0
}

// Canvas2D支持检测
export function canIUseCanvas2d() {
    // #ifdef MP-WEIXIN
    return gte('2.9.0')
    // #endif
    // #ifdef MP-ALIPAY
    return gte('2.7.0')
    // #endif
    // #ifdef MP-TOUTIAO
    return gte('1.78.0')
    // #endif
    return false
}

// 获取Canvas元素信息 - 参考lime-echarts的getRect实现
export function getRect(selector, context, needNode = false) {
    return new Promise((resolve, reject) => {
        const query = uni.createSelectorQuery().in(context).select(selector)

        const result = (rect) => {
            if (rect) {
                resolve(rect)
            } else {
                reject(new Error(`Element not found: ${selector}`))
            }
        }

        if (needNode) {
            query
                .fields(
                    {
                        node: true,
                        size: true,
                        rect: true,
                    },
                    result,
                )
                .exec()
        } else {
            query.boundingClientRect(result).exec()
        }
    })
}

// 生成唯一Canvas ID
export function generateCanvasId(prefix = 'canvas', instance = null) {
    if (instance) {
        // 使用组件实例uid，类似lime-echarts
        const uid = (instance._ && instance._.uid) || instance._uid || instance.uid
        if (uid) {
            return `${prefix}${uid}`
        }
    }
    // 降级方案
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

// Canvas管理器类
export class CanvasManager {
    constructor(options = {}) {
        this.canvasId = options.canvasId || generateCanvasId('test-canvas', options.instance)
        this.instance = options.instance
        this.width = options.width || 100
        this.height = options.height || 100

        // 状态
        this.use2dCanvas = canIUseCanvas2d()
        this.canvasNode = null
        this.canvasContext = null
        this.isReady = false
        this.error = null

        // 绘制器注册表
        this.renderers = new Map()

        // 注册默认绘制器
        this.registerRenderer('circle', CircleRenderer)

        console.log('CanvasManager初始化:', {
            canvasId: this.canvasId,
            use2dCanvas: this.use2dCanvas,
            width: this.width,
            height: this.height,
            renderers: Array.from(this.renderers.keys()),
        })
    }

    // 注册绘制器
    registerRenderer(name, RendererClass) {
        this.renderers.set(name, RendererClass)
        console.log(`绘制器已注册: ${name}`)
    }

    // 获取绘制器实例
    getRenderer(name) {
        const RendererClass = this.renderers.get(name)
        if (!RendererClass) {
            throw new Error(`未找到绘制器: ${name}`)
        }
        return new RendererClass(this)
    }

    // 通用绘制方法
    async render(type, data, options = {}) {
        try {
            const renderer = this.getRenderer(type)
            return await renderer.render(data, options)
        } catch (error) {
            console.error(`绘制失败 [${type}]:`, error)
            this.error = error.message
            return { success: false, error: error.message }
        }
    }

    // 获取Canvas上下文 - 参考lime-echarts实现
    async getContext() {
        try {
            console.log('开始获取Canvas上下文...', {
                canvasId: this.canvasId,
                use2dCanvas: this.use2dCanvas,
            })

            const res = await getRect(`#${this.canvasId}`, this.instance, this.use2dCanvas)

            if (res) {
                let dpr = uni.getSystemInfoSync().pixelRatio || 1
                let { width: w, height: h, node } = res

                this.width = w = w || this.width
                this.height = h = h || this.height

                if (node) {
                    // Canvas 2D
                    const ctx = node.getContext('2d')
                    node.width = w * dpr
                    node.height = h * dpr
                    ctx.scale(dpr, dpr)

                    this.canvasNode = node
                    this.canvasContext = ctx
                    this.isReady = true
                    this.error = null

                    console.log('Canvas 2D上下文获取成功')
                    return ctx
                } else {
                    // 旧版Canvas
                    const ctx = uni.createCanvasContext(this.canvasId, this.instance)

                    this.canvasContext = ctx
                    this.isReady = true
                    this.error = null

                    console.log('旧版Canvas上下文获取成功')
                    return ctx
                }
            } else {
                throw new Error('Canvas element not found')
            }
        } catch (error) {
            console.error('Canvas上下文获取失败:', error)
            this.error = error.message
            this.isReady = false
            throw error
        }
    }

    // 清空画布
    clear() {
        if (!this.canvasContext) return

        try {
            this.canvasContext.clearRect(0, 0, this.width, this.height)
        } catch (error) {
            console.warn('清空画布失败:', error)
        }
    }

    // 完成绘制
    draw() {
        if (!this.canvasContext) return

        try {
            if (!this.use2dCanvas) {
                this.canvasContext.draw()
            }
            console.log('Canvas绘制完成')
        } catch (error) {
            console.error('Canvas绘制失败:', error)
            throw error
        }
    }

    // 绘制圆环（兼容旧API）
    async drawCircle(score, options = {}) {
        console.log("使用兼容API绘制圆环，建议使用 render('circle', score, options)")
        const result = await this.render('circle', score, options)
        return result.success
    }

    // 获取状态
    getStatus() {
        return {
            canvasId: this.canvasId,
            use2dCanvas: this.use2dCanvas,
            isReady: this.isReady,
            error: this.error,
            width: this.width,
            height: this.height,
        }
    }

    // 重置
    reset() {
        this.canvasNode = null
        this.canvasContext = null
        this.isReady = false
        this.error = null
        console.log('Canvas已重置')
    }
}

// 创建Canvas管理器的工厂函数
export function createCanvasManager(options) {
    return new CanvasManager(options)
}
