/**
 * 显示Toast提示并支持延迟回调
 * @param {Object} options Toast配置选项
 * @param {String} options.title 提示内容
 * @param {String} options.icon 图标类型 success/error/fail/exception/loading/none，默认success
 * @param {String} options.image 自定义图标的本地路径
 * @param {Boolean} options.mask 是否显示透明蒙层，默认false
 * @param {Number} options.duration 显示时长(ms)，默认1500
 * @param {String} options.position 显示位置 top/center/bottom（仅App）
 * @param {Function} options.success 接口调用成功的回调函数
 * @param {Function} options.fail 接口调用失败的回调函数
 * @param {Function} options.complete 接口调用结束的回调函数
 * @param {Function} options.callback 延迟回调函数（自定义扩展）
 * @param {Number} options.delay 回调延迟时间(ms)，默认为duration时间（自定义扩展）
 */
export function showToast(options = {}) {
	// 如果第一个参数是字符串，转换为对象格式
	if (typeof options === "string") {
		options = { title: options };
	}

	// 默认配置（遵循官方默认值）
	const defaultOptions = {
		title: "操作成功",
		icon: "success",
		duration: 1500,
		mask: false,
	};

	// 提取自定义扩展参数
	const { callback, delay, ...toastOptions } = options;

	// 合并配置
	const finalOptions = { ...defaultOptions, ...toastOptions };

	// 确定延迟时间
	const callbackDelay = delay !== undefined ? delay : finalOptions.duration;

	// 显示Toast
	uni.showToast(finalOptions);

	// 如果有延迟回调函数，设置延迟执行
	if (typeof callback === "function") {
		setTimeout(() => {
			callback();
		}, callbackDelay);
	}

	// 返回Promise以支持链式调用
	return new Promise((resolve) => {
		setTimeout(() => {
			resolve();
		}, callbackDelay);
	});
}
