import dayjs from "dayjs";

// 获取今年第几周
export function getWeekNumber(date) {
	const startOfYear = new Date(date.getFullYear(), 0, 1);
	// 计算本年第几天
	const dayOfYear = Math.floor((date - startOfYear) / 86400000) + 1;
	// 返回本年第几周（以周一为每周第一天）
	return Math.ceil((dayOfYear + (startOfYear.getDay() === 0 ? 6 : startOfYear.getDay() - 1)) / 7);
}

/**
 * 格式化日期
 * @param {Date|string|number} date - 日期对象、时间戳或日期字符串
 * @param {string} format - 格式化模板，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = "YYYY-MM-DD HH:mm:ss") {
	if (!date) return "";
	return dayjs(date).format(format);
}

/**
 * 解析日期字符串
 * @param {string} dateString - 日期字符串
 * @param {string} format - 解析格式
 * @returns {Date|null} Date对象或null
 */
export function parseDate(dateString, format) {
	if (!dateString) return null;
	const parsed = format ? dayjs(dateString, format) : dayjs(dateString);
	return parsed.isValid() ? parsed.toDate() : null;
}

/**
 * 日期加法运算
 * @param {Date|string|number} date - 日期
 * @param {number} amount - 数量
 * @param {string} unit - 单位：year/month/day/hour/minute/second
 * @returns {Date|null} 计算后的日期
 */
export function addTime(date, amount, unit = "day") {
	if (!date) return null;
	const result = dayjs(date).add(amount, unit);
	return result.isValid() ? result.toDate() : null;
}

/**
 * 日期减法运算
 * @param {Date|string|number} date - 日期
 * @param {number} amount - 数量
 * @param {string} unit - 单位：year/month/day/hour/minute/second
 * @returns {Date|null} 计算后的日期
 */
export function subtractTime(date, amount, unit = "day") {
	if (!date) return null;
	const result = dayjs(date).subtract(amount, unit);
	return result.isValid() ? result.toDate() : null;
}

/**
 * 获取相对时间描述
 * @param {Date|string|number} date - 日期
 * @param {Date|string|number} baseDate - 基准日期，默认为当前时间
 * @returns {string} 相对时间描述
 */
export function getRelativeTime(date, baseDate = new Date()) {
	if (!date) return "";
	const target = dayjs(date);
	const base = dayjs(baseDate);

	if (!target.isValid() || !base.isValid()) return "";

	const diff = target.diff(base, "second");
	const absDiff = Math.abs(diff);
	const isFuture = diff > 0;

	if (absDiff < 60) {
		return isFuture ? "即将" : "刚刚";
	} else if (absDiff < 3600) {
		const minutes = Math.floor(absDiff / 60);
		return isFuture ? `${minutes}分钟后` : `${minutes}分钟前`;
	} else if (absDiff < 86400) {
		const hours = Math.floor(absDiff / 3600);
		return isFuture ? `${hours}小时后` : `${hours}小时前`;
	} else if (absDiff < 2592000) {
		const days = Math.floor(absDiff / 86400);
		return isFuture ? `${days}天后` : `${days}天前`;
	} else {
		return target.format("YYYY-MM-DD");
	}
}

/**
 * 日期比较：是否在之前
 * @param {Date|string|number} date1 - 第一个日期
 * @param {Date|string|number} date2 - 第二个日期
 * @param {string} unit - 比较精度：year/month/day/hour/minute/second
 * @returns {boolean} 是否在之前
 */
export function isBefore(date1, date2, unit = "day") {
	if (!date1 || !date2) return false;
	return dayjs(date1).isBefore(dayjs(date2), unit);
}

/**
 * 日期比较：是否在之后
 * @param {Date|string|number} date1 - 第一个日期
 * @param {Date|string|number} date2 - 第二个日期
 * @param {string} unit - 比较精度：year/month/day/hour/minute/second
 * @returns {boolean} 是否在之后
 */
export function isAfter(date1, date2, unit = "day") {
	if (!date1 || !date2) return false;
	return dayjs(date1).isAfter(dayjs(date2), unit);
}

/**
 * 日期比较：是否相同
 * @param {Date|string|number} date1 - 第一个日期
 * @param {Date|string|number} date2 - 第二个日期
 * @param {string} unit - 比较精度：year/month/day/hour/minute/second
 * @returns {boolean} 是否相同
 */
export function isSame(date1, date2, unit = "day") {
	if (!date1 || !date2) return false;
	return dayjs(date1).isSame(dayjs(date2), unit);
}

/**
 * 获取日期的指定部分
 * @param {Date|string|number} date - 日期
 * @param {string} part - 部分：year/month/date/hour/minute/second/day(星期)
 * @returns {number|null} 指定部分的值
 */
export function getDatePart(date, part) {
	if (!date) return null;
	const d = dayjs(date);
	if (!d.isValid()) return null;

	switch (part) {
		case "year":
			return d.year();
		case "month":
			return d.month() + 1; // dayjs月份从0开始，这里返回1-12
		case "date":
			return d.date();
		case "hour":
			return d.hour();
		case "minute":
			return d.minute();
		case "second":
			return d.second();
		case "day":
			return d.day(); // 0-6，0为周日
		default:
			return null;
	}
}

/**
 * 获取时间段的开始时间
 * @param {Date|string|number} date - 日期
 * @param {string} unit - 时间单位：year/month/day/hour/minute/second
 * @returns {Date|null} 时间段开始的Date对象
 */
export function getStartOf(date, unit = "day") {
	if (!date) return null;
	const result = dayjs(date).startOf(unit);
	return result.isValid() ? result.toDate() : null;
}

/**
 * 获取时间段的结束时间
 * @param {Date|string|number} date - 日期
 * @param {string} unit - 时间单位：year/month/day/hour/minute/second
 * @returns {Date|null} 时间段结束的Date对象
 */
export function getEndOf(date, unit = "day") {
	if (!date) return null;
	const result = dayjs(date).endOf(unit);
	return result.isValid() ? result.toDate() : null;
}

/**
 * 计算两个日期之间的时间差
 * @param {Date|string|number} date1 - 第一个日期
 * @param {Date|string|number} date2 - 第二个日期
 * @param {string} unit - 时间单位：year/month/day/hour/minute/second
 * @returns {number|null} 时间差
 */
export function diffTime(date1, date2, unit = "day") {
	if (!date1 || !date2) return null;
	const d1 = dayjs(date1);
	const d2 = dayjs(date2);
	if (!d1.isValid() || !d2.isValid()) return null;
	return d1.diff(d2, unit);
}

/**
 * 获取周期列表，label格式如：本周(5.12~5.18) 或 第20周(5.12~5.18)
 * @param {number} maxWeeks - 显示几周（从本周往前）
 * @returns {Array}
 */
export function getWeekList(maxWeeks = 4) {
	const result = [];
	const today = new Date();
	const dayOfWeek = today.getDay() || 7; // 周日为7
	const startOfWeek = new Date(today);
	startOfWeek.setDate(today.getDate() - (dayOfWeek - 1));
	startOfWeek.setHours(0, 0, 0, 0);

	for (let i = 0; i < maxWeeks; i++) {
		const weekStart = new Date(startOfWeek);
		weekStart.setDate(startOfWeek.getDate() - i * 7);
		const weekEnd = new Date(weekStart);
		weekEnd.setDate(weekStart.getDate() + 6);

		// 获取今年第几周
		const weekNum = getWeekNumber(weekStart);

		// 格式化日期 M.D
		const format = (date) => `${date.getMonth() + 1}.${date.getDate()}`;
		const label = i === 0 ? `本周(${format(weekStart)}~${format(weekEnd)})` : `第${weekNum}周(${format(weekStart)}~${format(weekEnd)})`;

		result.push({
			label,
			start: weekStart,
			end: weekEnd,
			weekNum,
			isCurrent: i === 0,
		});
	}

	return result;
}
