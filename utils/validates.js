// 必填校验
export function required(msg = "此项必填") {
	return [
		{
			required: true,
			message: msg,
			trigger: ["blur", "change"],
		},
	];
}

// 手机号校验
export function mobile(msg = "请输入正确的手机号") {
	return [
		{
			required: true,
			message: "请输入手机号",
			trigger: ["blur", "change"],
		},
		{
			pattern: /^1[3-9]\d{9}$/,
			message: msg,
			trigger: ["blur", "change"],
		},
	];
}

// 验证码校验
export function code(len = 4, msg = "请输入正确的验证码") {
	return [
		{
			required: true,
			message: "验证码不能为空",
			trigger: ["blur", "change"],
		},
		{
			len,
			message: msg,
			trigger: ["blur", "change"],
		},
	];
}

// 姓名校验（仅允许中文）
export function name(msg = "请输入正确格式姓名", minLen = 2, maxLen = 20) {
	return [
		{
			required: true,
			message: "请输入姓名",
			trigger: ["blur", "change"],
		},
		{
			pattern: new RegExp(`^[\\u4e00-\\u9fa5]{${minLen},${maxLen}}$`),
			message: msg,
			trigger: ["blur", "change"],
		},
	];
}

// 昵称校验（允许中文、字母、数字）
export function nickName(msg = "请输入正确格式昵称，最大长度4位", minLen = 1, maxLen = 4) {
	return [
		{
			required: true,
			message: "请输入昵称",
			trigger: ["blur", "change"],
		},
		{
			pattern: new RegExp(`^[\\u4e00-\\u9fa5a-zA-Z0-9]{${minLen},${maxLen}}$`),
			message: msg,
			trigger: ["blur", "change"],
		},
	];
}

// 自定义校验（如长度、正则等）
export function custom(pattern, msg = "格式不正确") {
	return [
		{
			pattern,
			message: msg,
			trigger: ["blur", "change"],
		},
	];
}
