import { useAppStore, useUserStore } from '@/store';

import { getIdUserMemberData } from '@/server/api';

import useConvert from '@/common/useConvert';

import type { Decimal } from 'decimal.js';

import { computed } from 'vue';

interface DecimalType extends Decimal {}

export enum BENEFIT_TYPE {
	GOODS_DISCOUNT = '商品抵扣',
	INTEGRAL_MULTIPLE = '积分加倍',
	LOGISTICS_DISCOUNT = '物流优惠',
	PRIORITY_SHIPMENTS = '优先发货',
	QUICKNESS_AFS = '极速售后',
	EXCLUSIVE_SERVICE = '专属客服',
	USER_DEFINED = '自定义'
}

export type MemberType = 'FREE_MEMBER' | 'PAID_MEMBER';

export interface MemberBenefitItem {
	extendValue: string;
	memberRightsId: string;
	rightsName: string;
	rightsType: keyof typeof BENEFIT_TYPE;
	memberName: string;
}

export type ApiMemberInfoType = {
	memberType?: string;
	currentMemberVO?: {
		[key: string]: any;
		memberName: string;
		relevancyRights?: Record<keyof typeof BENEFIT_TYPE, MemberBenefitItem>;
	};
};

const { divThousand, divTenThousand, fixedUp } = useConvert();

function memberPrice(val: string | number | DecimalType) {
	if (includeDiscount()) {
		const memberInfo = useUserStore().userMemberData;
		const preferentialRate = memberInfo?.memberBenefit?.GOODS_DISCOUNT.extendValue;
		return fixedUp(divTenThousand(val).mul(divThousand(preferentialRate)));
	} else {
		return fixedUp(divTenThousand(val));
	}
}

const isExist = computed(() => !!useAppStore().GET_PLUGIN('addon-full-reduction'));

/**
 * 获取会员信息
 */
async function doRequest() {
	try {
		if (!useUserStore().checkLogin) return false;
		const { code, data } = await getIdUserMemberData();
		if (code === 200 && data) {
			// 更新会员缓存信息
			saveMemberInfo(data);
			return true;
		} else {
			uni.showToast({
				icon: 'none',
				title: '获取会员信息失败'
			});
			return false;
		}
	} catch (error) {
		uni.showToast({
			icon: 'none',
			title: '获取会员信息失败'
		});
		return false;
	}
}

/**
 * 存储会员信息
 */
function saveMemberInfo(data: ApiMemberInfoType) {
	useUserStore().changeUserMemberData(data);
}

/**
 * 是否是付费会员
 */
function isPaidMember() {
	if (useUserStore().userMemberData) {
		return useUserStore().userMemberData?.memberType === 'PAID_MEMBER';
	}
	return false;
}

/**
 * 判断是否存在商品折扣减免
 */
function includeDiscount() {
	const memberInfo = useUserStore().userMemberData;
	if (!memberInfo || !memberInfo.memberBenefit) {
		return false;
	}
	if (memberInfo.memberBenefit['GOODS_DISCOUNT']) {
		return true;
	} else {
		return false;
	}
}

/**
 * 判断是否受益
 */
function includeBenefit(str: keyof typeof BENEFIT_TYPE) {
	const memberInfo = useUserStore().userMemberData;
	if (memberInfo && memberInfo.memberBenefit) {
		return Boolean(memberInfo.memberBenefit[str]);
	} else {
		return false;
	}
}

export default function useMember() {
	return {
		memberPrice,
		saveMemberInfo,
		isPaidMember,
		includeDiscount,
		includeBenefit,
		doRequest,
		isExist
	};
}
