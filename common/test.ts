export const REGEX = {
	DATE: /((\d{3}[1-9]|\d{2}[1-9]\d{1}|\d{1}[1-9]\d{2}|[1-9]\d{3})-(((0[13578]|1[02])-(0[1-9]|[12]\d|3[01]))|((0[469]|11)-(0[1-9]|[12]\d|30))|(02-(0[1-9]|[1]\d|2[0-8]))))|(((\d{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29)((\d{3}[1-9]|\d{2}[1-9]\d{1}|\d{1}[1-9]\d{2}|[1-9]\d{3})-(((0[13578]|1[02])-(0[1-9]|[12]\d|3[01]))|((0[469]|11)-(0[1-9]|[12]\d|30))|(02-(0[1-9]|[1]\d|2[0-8]))))|(((\d{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29)/,
	TIME: /\d{1,2}:\d{1,2}(:\d{1,2})?/,
	TIME_DATE:
		/((\d{3}[1-9]|\d{2}[1-9]\d{1}|\d{1}[1-9]\d{2}|[1-9]\d{3})-(((0[13578]|1[02])-(0[1-9]|[12]\d|3[01]))|((0[469]|11)-(0[1-9]|[12]\d|30))|(02-(0[1-9]|[1]\d|2[0-8]))))|(((\d{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29)((\d{3}[1-9]|\d{2}[1-9]\d{1}|\d{1}[1-9]\d{2}|[1-9]\d{3})-(((0[13578]|1[02])-(0[1-9]|[12]\d|3[01]))|((0[469]|11)-(0[1-9]|[12]\d|30))|(02-(0[1-9]|[1]\d|2[0-8]))))|(((\d{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29)\s\d{1,2}:\d{1,2}(:\d{1,2})?/,
	HTTP_URL: /^((https|http|ftp|rtsp|mms)?:\/\/)[^\s]+/,
	NUMBERS: /\d+/,
	BLANK: /[\s\S]*.*[^\s][\s\S]/,
	MOBILE: /^1([35896]\d|4[5-9]|6[1-2,4-7]|7[0-8])\d{8}$/,
	CITIZEN_ID: /[1-9]\d{5}[1-2]\d{3}((0\d)|(1[0-2]))(([012]\d)|3[0-1])\d{3}(\d|X|x)/,
	// EMAIL: /^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/
	EMAIL: /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
};

/**
 * 日期
 */
const REGEX_DATE = (str: string) => REGEX.DATE.test(str);
/**
 * 时间
 */
const REGEX_TIME = (str: string) => REGEX.TIME.test(str);
/**
 * 日期 + 时间
 */
const REGEX_TIME_DATE = (str: string) => REGEX.TIME_DATE.test(str);
/**
 * 图片
 */
const REGEX_HTTP_URL = (str: string) => REGEX.HTTP_URL.test(str);
/**
 * 数字
 */
const REXGEX_NUMBERS = (str: string) => REGEX.NUMBERS.test(str);
/**
 * 文本
 */
const REGEX_BLANK = (str: string) => REGEX.BLANK.test(str);
/**
 * 手机号
 */
const REGEX_MOBILE = (str: string) => REGEX.MOBILE.test(str);
/**
 * 公民身份证
 */
const REGEX_CITIZEN_ID = (str: string) => REGEX.CITIZEN_ID.test(str);
/**
 * 邮箱
 */
const REGEX_EMAIL = (str: string) => REGEX.EMAIL.test(str);

export { REGEX_DATE, REGEX_TIME, REGEX_TIME_DATE, REGEX_HTTP_URL, REXGEX_NUMBERS, REGEX_BLANK, REGEX_MOBILE, REGEX_CITIZEN_ID, REGEX_EMAIL };
