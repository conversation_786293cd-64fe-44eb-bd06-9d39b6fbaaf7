import { useUserStore, useAppStore } from '@/store'
import { route, openWebView, goArticlePage, goArticlePageWithData } from '@/common/utils'
import debounce from '@/uni_modules/uv-ui-tools/libs/function/debounce.js'

/**
 * 登录状态检查
 */
export function useLoginCheck() {
    const userStore = useUserStore()
    const appStore = useAppStore()

    /**
     * 检查登录状态并执行回调
     * @param {Function} callback - 登录后执行的回调函数
     * @param {boolean} showModal - 是否显示登录弹窗，默认true
     * @returns {boolean} 是否已登录
     */
    const checkLoginAndExecute = (callback, showModal = true) => {
        if (userStore.checkLogin) {
            if (typeof callback === 'function') {
                callback()
            }
            return true
        } else {
            if (showModal) {
                appStore.changeShowLoginModal(true)
            }
            return false
        }
    }

    return {
        checkLoginAndExecute,
        isLoggedIn: () => userStore.checkLogin,
    }
}

/**
 * 通用URL跳转处理函数
 * @param {string} url - 跳转URL
 * @param {string} title - 页面标题
 * @param {boolean} requireLogin - 是否需要登录验证
 */
function handleUrlNavigation(url, title = '', requireLogin = false) {
    const urlString = String(url)
    if (!urlString) return

    const userStore = useUserStore()
    const appStore = useAppStore()

    if (urlString.startsWith('http://') || urlString.startsWith('https://')) {
        openWebView(urlString, title)
    } else {
        // 内部路由跳转，检查是否需要登录验证
        if (requireLogin && !userStore.checkLogin) {
            appStore.changeShowLoginModal(true)
        } else {
            route(urlString)
        }
    }
}

/**
 * Banner轮播图导航
 */
export function useBannerNavigation() {
    /**
     * 轮播图点击处理函数
     * @param {Array} bannerList - 轮播图数据列表
     * @param {number} index - 点击的轮播图索引
     * @param {boolean} requireLogin - 是否需要登录验证，默认true
     */
    const handleBannerClick = (bannerList, index, requireLogin = true) => {
        const item = bannerList[index]
        if (!item) return

        const userStore = useUserStore()
        const appStore = useAppStore()

        // 轮播图跳转策略映射
        const swiperNavigationStrategies = {
            // 内容详情跳转（广告类型）
            content: () => {
                if (requireLogin && !userStore.checkLogin) {
                    appStore.changeShowLoginModal(true)
                } else {
                    goArticlePage('advertising', item.id)
                }
            },
            // 链接跳转
            link: () => {
                handleUrlNavigation(item.skipUrl, item.title || '', requireLogin)
            },
        }

        // 根据跳转类型执行相应策略
        if (item.skipType && swiperNavigationStrategies[item.skipType]) {
            swiperNavigationStrategies[item.skipType]()
        }
    }

    return {
        handleBannerClick: (bannerList, index, requireLogin = true) => {
            debounce(() => handleBannerClick(bannerList, index, requireLogin), 300)
        },
    }
}

/**
 * 菜单导航
 */
export function useMenuNavigation() {
    /**
     * 菜单项点击处理函数
     * @param {Object} item - 菜单项数据
     * @param {boolean} requireLogin - 是否需要登录验证，默认false
     */
    const handleMenuClick = (item, requireLogin = false) => {
        // 跳转策略映射
        const navigationStrategies = {
            content: () => {
                goArticlePageWithData(item, item.id)
            },
            link: () => {
                handleUrlNavigation(item.skipUrl, item.title || '', requireLogin)
            },
            shop: () => {
                route(item.skipUrl, { categoryId: item.categoryId || '' })
            },
            serpro: () => {
                route(item.skipUrl, { categoryId: item.categoryId || '' })
            },
        }

        if (navigationStrategies[item.typeName]) {
            navigationStrategies[item.typeName]()
            return
        }

        // 默认提示
        uni.showToast({ title: '敬请期待', icon: 'none' })
    }

    return {
        handleMenuClick: (item, requireLogin = false) => {
            debounce(() => handleMenuClick(item, requireLogin), 300)
        },
    }
}
