import uvRoute from '@/uni_modules/uv-ui-tools/libs/util/route.js'

import permision from '@/js_sdk/wa-permission/permission.js'

import { sleep as uvSleep } from '@/uni_modules/uv-ui-tools/libs/function/index.js'

import useConvert from '@/common/useConvert'

import { useAppStore, useMessageStore, useArticleStore } from '@/store'

/**
 * 是否开发环境
 */
export const ENV_IS_DEV = Boolean(import.meta.env.DEV)

/**
 * 是否生产环境
 */
export const ENV_IS_PROD = Boolean(import.meta.env.PROD)

/**
 * 判断环境
 */
export function canENV(callbackDev, callbackProd) {
    if (ENV_IS_DEV) {
        if (typeof callbackDev === 'function') {
            callbackDev()
        }
    } else if (ENV_IS_PROD) {
        if (typeof callbackProd === 'function') {
            callbackProd()
        }
    }
}

/**
 * 获取 url 指定参数
 * @param {Object} url
 * @param {Object} key
 */
export function getQueryParam(url, key) {
    //使用正则表达式获取指定的查询参数
    let match = url.match(new RegExp('[?&]' + key + '=([^&]*)'))
    if (match != null) {
        //返回查询参数的值
        return decodeURIComponent(match[1])
    } else {
        //返回 null,表示未找到指定的查询参数
        return null
    }
}

export function sliceArray(array, size = 1) {
    if (array.length == 0) {
        return []
    }

    return [array.slice(0, size)].concat(sliceArray(array.slice(size), size))
}

export const route = uvRoute

export const sleep = uvSleep

export function getAppCacheSize() {
    return new Promise((resolve, reject) => {
        let cacheSizeText = '0B'
        // #ifdef APP
        plus.cache.calculate(
            (size) => {
                if (size < 1024) {
                    cacheSizeText = size + 'B'
                } else if (size / 1024 >= 1 && size / 1024 / 1024 < 1) {
                    cacheSizeText = Math.floor((size / 1024) * 100) / 100 + 'KB'
                } else if (size / 1024 / 1024 >= 1) {
                    cacheSizeText = Math.floor((size / 1024 / 1024) * 100) / 100 + 'M'
                }
                resolve(cacheSizeText)
            },
            (err) => {
                resolve(cacheSizeText)
            },
        )
        // #endif
        // #ifndef APP
        resolve(cacheSizeText)
        // #endif
    })
}

export function appClearCache() {
    return new Promise((resolve, reject) => {
        // #ifdef APP
        let osName = plus.os.name
        if (osName == 'Android') {
            function checkResolve(i, length) {
                if (i === length - 1) {
                    resolve()
                }
            }

            let main = plus.android.runtimeMainActivity()
            let sdRoot = main.getCacheDir()
            let files = plus.android.invoke(sdRoot, 'listFiles')
            let len = files.length
            for (let i = 0; i < len; i += 1) {
                let filePath = '' + files[i]
                plus.io.resolveLocalFileSystemURL(
                    filePath,
                    (entry) => {
                        if (entry.isDirectory) {
                            entry.removeRecursively(
                                (entry) => {
                                    checkResolve(i, len)
                                },
                                (error) => {
                                    checkResolve(i, len)
                                },
                            )
                        } else {
                            entry.remove(
                                (entry) => {
                                    checkResolve(i, len)
                                },
                                (error) => {
                                    checkResolve(i, len)
                                },
                            )
                        }
                    },
                    (error) => {
                        checkResolve(i, len)
                    },
                )
            }
        } else {
            // ios
            plus.cache.clear(() => {
                resolve()
            })
        }
        // #endif
        // #ifndef APP
        resolve()
        // #endif
    })
}

export function revocationPrivacyAgreement() {
    // #ifdef APP
    plus.runtime.disagreePrivacy()
    plus.runtime.quit()
    // #endif
}

export function openURLBrowser(url = '') {
    if (!url) return

    const urlText = String(url)

    // #ifdef APP-PLUS
    if (urlText.startsWith('tel:')) {
        const telText = urlText.replace('tel:', '')
        uni.makePhoneCall({
            phoneNumber: telText,
        })
    } else {
        plus.runtime.openURL(urlText)
    }
    // #endif
    // #ifdef H5
    window.open(urlText)
    // #endif
    // #ifdef MP
    uni.setClipboardData({
        data: urlText,
    })
    uni.showModal({
        content: '已复制网址，请在手机浏览器里粘贴该网址',
        showCancel: false,
    })
    // #endif
}

/**
 * 打开 WebView
 * @param {Object} url
 * @param {Object} title
 */
export function openWebView(url, title = '') {
    const appStore = useAppStore()
    appStore.changeWebViewUrl(encodeURI(url))
    uni.navigateTo({
        url: `/pages/common/pages/webView/webView?title=${title}`,
    })
}

/**
 * 前往文章页面
 * @param {string} type 文章类型
 */
export function goArticlePage(type = 'fwxy', ids = '', nav = 1, platform = 1) {
    uni.navigateTo({
        url: `/pages/common/pages/article/article?type=${type}&ids=${ids}&nav=${nav}&platform=${platform}`,
    })
}

/**
 * 前往文章页面（支持大内容数据）
 * @param {Object} articleData 文章数据对象 { content, title, icon?, id? }
 * @param {number} nav 导航栏显示 1显示 0隐藏
 */
export function goArticlePageWithData(articleData, nav = 1) {
    const articleStore = useArticleStore()

    // 存储文章数据到store
    articleStore.setArticleData(articleData)

    // 跳转页面，使用custom类型避免与现有逻辑冲突
    uni.navigateTo({
        url: `/pages/common/pages/article/article?type=custom&nav=${nav}`,
    })
}

/**
 * 打开视频
 * @param {Object} url
 * @param {Object} title
 */
export function openVideo(url, title = '') {
    const appStore = useAppStore()
    appStore.changeVideoUrl(url)
    uni.navigateTo({
        url: `/pages/common/pages/videoPlayer/videoPlayer?title=${title}`,
    })
}

/**
 * 选择上传文件
 */
export async function chooseMediaFile(options = {}) {
    const defaultOptions = {
        count: 9,
        mediaType: ['image', 'video'],
        sourceType: ['album', 'camera'],
        sizeType: ['original', 'compressed'],
        compressed: true,
        camera: 'back', // back front
        maxDuration: 30,
    }

    const apiOptions = {
        ...defaultOptions,
        ...options,
    }

    if (typeof apiOptions.mediaType === 'string') {
        apiOptions.mediaType = [apiOptions.mediaType]
    }

    if (typeof apiOptions.sizeType === 'string') {
        apiOptions.sizeType = [apiOptions.sizeType]
    }

    if (apiOptions.sizeType.includes('compressed')) {
        apiOptions.compressed = true
    }

    if (apiOptions.sizeType.includes('original')) {
        apiOptions.compressed = false
    }

    let chooseRes = {}

    try {
        // #ifdef APP || MP-WEIXIN

        // #ifdef APP

        if (false) {
            if (apiOptions.sourceType.includes('camera')) {
                const status = await permision.checkPermission('camera')
                if (!status) {
                    uni.showModal({
                        title: '授权提醒',
                        content: '拍照上传图片需要授权相机权限，是否继续？',
                        success(res) {
                            if (res.confirm) {
                                permision.requestPermission('camera')
                            }
                        },
                    })

                    if (typeof apiOptions.fail === 'function') {
                        apiOptions.fail({
                            errMsg: '暂无权限',
                        })
                    }
                    if (typeof apiOptions.complete === 'function') {
                        apiOptions.complete({
                            errMsg: '暂无权限',
                        })
                    }
                    return Promise.reject({
                        errMsg: '暂无权限',
                    })
                }
            }
            if (apiOptions.sourceType.includes('album')) {
                const status = await permision.checkPermission('photoLibrary')
                if (!status) {
                    uni.showModal({
                        title: '授权提醒',
                        content: '选择相册照片上传需要授权相册权限，是否继续？',
                        success(res) {
                            if (res.confirm) {
                                permision.requestPermission('camera')
                            }
                        },
                    })

                    if (typeof apiOptions.fail === 'function') {
                        apiOptions.fail({
                            errMsg: '暂无权限',
                        })
                    }
                    if (typeof apiOptions.complete === 'function') {
                        apiOptions.complete({
                            errMsg: '暂无权限',
                        })
                    }
                    return Promise.reject({
                        errMsg: '暂无权限',
                    })
                }
            }
        }

        // #endif
        chooseRes = await uni.chooseMedia({
            ...apiOptions,
        })
        // #endif
        // #ifndef APP || MP-WEIXIN
        // #ifdef H5
        chooseRes = await uni.chooseFile({
            ...apiOptions,
        })
        // #endif
        // #ifndef H5
        if (apiOptions.mediaType.length > 1) {
        } else {
            if (apiOptions.includes('image')) {
                chooseRes = await uni.chooseImage({
                    ...apiOptions,
                })
            }
            if (apiOptions.includes('video')) {
                chooseRes = await uni.chooseVideo({
                    ...apiOptions,
                })
            }
        }
        // #endif
        // #endif
    } catch (error) {
        //TODO handle the exception
        if (typeof apiOptions.fail === 'function') {
            apiOptions.fail(error)
        }
        if (typeof apiOptions.complete === 'function') {
            apiOptions.complete(error)
        }
        return Promise.reject(error)
    }

    // console.log(chooseRes);
}

export function checkCategoryEnable(currentLevel, records) {
    const isLastLevel = currentLevel === 3
    for (let index = 0; index < records.length; ) {
        const record = records[index]
        if (isLastLevel) {
            record.disabled = false
            index++
            continue
        }
        const children = record.children || record.secondCategoryVos || record.categoryThirdlyVos
        delete record.secondCategoryVos
        delete record.categoryThirdlyVos
        const disable = !children || children.length === 0
        record.disabled = disable
        if (disable) {
            records.splice(index, 1)
            continue
        }
        checkCategoryEnable(currentLevel + 1, children)
        if (children.length === 0) {
            records.splice(index, 1)
            continue
        }
        record.children = children
        index++
    }
    return records
}

export function getPriceInfo(price = 0, decimalLength = 2) {
    const { divTenThousand } = useConvert()

    let defaultDecimalText = ''
    for (let i = 1; i <= decimalLength; i += 1) {
        defaultDecimalText += '0'
    }

    if (price) {
        let actualPrice = divTenThousand(price).toString()

        const priceArray = actualPrice?.split('.')
        const integer = priceArray?.[0] || ''
        const decimal = (priceArray?.[1] || '').slice(0, decimalLength)
        const decimalText = !decimal ? defaultDecimalText : Number(decimal) < 10 ? `${decimal}` : `${decimal}`
        return {
            integer,
            decimal,
            decimalText,
            price: price,
        }
    }

    return {
        integer: 0,
        decimal: 0,
        decimalText: defaultDecimalText,
        price: price,
    }
}

export function getUploadSrc(fileSrc = '', type = '', addImgWatermark = false) {
    const videoCoverSrc = `?x-oss-process=video/snapshot,t_1000,f_jpg,ar_auto`

    let src = '/'

    if (!fileSrc) {
        return src
    }
    if (type && type !== 'videoCover') {
        return src
    } else if (fileSrc) {
        const configStore = {
            configInfo: {
                imgWatermark: '',
                uploadFileBaseUrl: 'https://hejiawuyou.oss-accelerate.aliyuncs.com',
            },
        }

        if (fileSrc.startsWith('http://') || fileSrc.startsWith('https://')) {
            if (type === 'videoCover') {
                src = `${fileSrc}${videoCoverSrc}`
            } else if (addImgWatermark) {
                src = `${fileSrc}${configStore.configInfo.imgWatermark}`
            } else {
                src = fileSrc
            }
        } else if (fileSrc.startsWith('/app/')) {
            if (type === 'videoCover') {
                src = `${configStore.configInfo.uploadFileBaseUrl}${fileSrc}${videoCoverSrc}`
            } else if (addImgWatermark) {
                src = `${configStore.configInfo.uploadFileBaseUrl}${fileSrc}${configStore.configInfo.imgWatermark}`
            } else {
                src = `${configStore.configInfo.uploadFileBaseUrl}${fileSrc}`
            }
        } else {
            src = fileSrc
        }
    } else {
        src = '/'
    }

    return src
}

export const salesVolumeToStr = useConvert().salesVolumeToStr

export function goShopHome(shopId) {
    route('/pages/goods/pages/shopHome/shopHome', {
        shopId,
    })
}

export function goLogin(reLaunch = false) {
    if (reLaunch) {
        uni.reLaunch({
            url: '/pages/login/pages/index/index',
        })
    } else {
        route('/pages/login/pages/index/index', {})
    }
}

export function goPlatformCustomerService() {
    useMessageStore().setImUserInfo({
        shopId: '0',
        shopLogo: '',
        shopName: '平台客服',
    })

    route('/pages/message/pages/im/im')
}

// 计算版本
export function compareVersion(v1, v2) {
    v1 = v1.split('.')
    v2 = v2.split('.')
    const len = Math.max(v1.length, v2.length)
    while (v1.length < len) {
        v1.push('0')
    }
    while (v2.length < len) {
        v2.push('0')
    }
    for (let i = 0; i < len; i++) {
        const num1 = parseInt(v1[i], 10)
        const num2 = parseInt(v2[i], 10)

        if (num1 > num2) {
            return 1
        } else if (num1 < num2) {
            return -1
        }
    }
    return 0
}
