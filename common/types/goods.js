import { getEstimateDate } from './utils.js'

export const EXPRESS_CODE = 50002 // 超出配送范围
export const INTRA_CITY_DISTRIBUTION_CODE_1 = 60001 // 超出配送范围
export const INTRA_CITY_DISTRIBUTION_CODE_2 = 60002 //不足配送金额

export const ServiceBarrier = {
    NO_FREIGHT: '全场包邮',
    SEVEN_END_BACK: '7天退换',
    TWO_DAY_SEND: '48小时发货',
    FAKE_COMPENSATE: '假一赔十',
    ALL_ENSURE: '正品保证',
}

/**
 * 配送方式
 */
export const DistributionType = {
    //商家配送
    MERCHANT: '商家配送',
    //快递配送
    EXPRESS: '快递配送',
    //同城配送
    INTRA_CITY_DISTRIBUTION: '同城配送',
    //门店配送
    SHOP_STORE: '门店配送',
    //虚拟配送 虚拟发货
    VIRTUAL: '虚拟发货',
    WITHOUT: '无需物流',
}

// 快递配送方式
export const DISTRIBUTION = {
    EXPRESS: 'EXPRESS', //快递配送
    INTRA_CITY_DISTRIBUTION: 'INTRA_CITY_DISTRIBUTION', //同城配送
    SHOP_STORE: 'SHOP_STORE', //店铺门店
    VIRTUAL: 'VIRTUAL', //无需物流
    MERCHANT: 'MERCHANT', //商家配送
}

/**
 * 库存类型
 */
export const StockType = {
    //无线库存
    UNLIMITED: 'UNLIMITED',
    //有限库存
    LIMITED: 'LIMITED',
}

/**
 * sku 限购类型
 */
export const LimitType = {
    //不限购
    UNLIMITED: 'UNLIMITED',
    //商品限购
    PRODUCT_LIMITED: 'PRODUCT_LIMITED',
    //sku 限购
    SKU_LIMITED: 'SKU_LIMITED',
}

/**
 * 预计赚类型
 */
export const EarningType = {
    //返利
    REBATE: 'REBATE',
    //分销佣金
    DISTRIBUTE: 'DISTRIBUTE',
}

/**
 * 预计赚类型
 */
export const ProductType = {
    //真实商品
    REAL_PRODUCT: 'REAL_PRODUCT',
    //虚拟商品
    VIRTUAL_PRODUCT: 'VIRTUAL_PRODUCT',
}

/**
 * 商品销售类型
 */
export const SellType = {
    //采购商品
    PURCHASE: 'PURCHASE',
    //代销商品
    CONSIGNMENT: 'CONSIGNMENT',
    //自由商品
    OWN: 'OWN',
}

/**
 * 活动类型
 */
export const ActivityType = {
    COMMON: 'COMMON',
    SPIKE: 'SPIKE',
    TEAM: 'TEAM',
    BARGAIN: 'BARGAIN',
    PACKAGE: 'PACKAGE',
}

export const DiscountType = {
    //会员
    VIP: '会员',
    // 优惠券
    // COUPON: '优惠券',
    COUPON: '领券可',
    //满减
    FULL: '满减',
}

export const serviceHandler = {
    ALL_ENSURE: {
        name: ServiceBarrier['ALL_ENSURE'],
        sendTime: () => '',
        isSendTimeService: false,
    },
    FAKE_COMPENSATE: {
        name: ServiceBarrier['FAKE_COMPENSATE'],
        sendTime: () => '',
        isSendTimeService: false,
    },
    NO_FREIGHT: {
        name: ServiceBarrier['NO_FREIGHT'],
        sendTime: () => '',
        isSendTimeService: false,
    },
    SEVEN_END_BACK: {
        name: ServiceBarrier['SEVEN_END_BACK'],
        sendTime: () => '',
        isSendTimeService: false,
    },
    TWO_DAY_SEND: {
        name: ServiceBarrier['TWO_DAY_SEND'],
        sendTime: () => getEstimateDate(2),
        isSendTimeService: true,
    },
}

export const PAY_TYPE = {
    ORDER: 'ORDER',
    BALANCE: 'BALANCE',
    MEMBER: 'MEMBER',
    INTEGRAL: 'INTEGRAL',
}

// 支付方式
export const ORDERPAYMENT = {
    WECHAT: 'WECHAT',
    ALIPAY: 'ALIPAY',
    BALANCE: 'BALANCE',
}

export const payType = {
    WECHAT: '微信',
    ALIPAY: '支付宝',
    BALANCE: '余额',
}

export const usePaymentCn = (str = 'WECHAT') => {
    return payType[str]
}

/**
 * 默认的折扣配置
 */
export const discountTypeConf = {
    PLATFORM_COUPON: {
        isShopDiscount: false,
        name: '平台优惠',
        sort: 1,
    },
    SHOP_COUPON: {
        isShopDiscount: true,
        name: '店铺优惠券',
        sort: 2,
    },
    MEMBER_DEDUCTION: {
        isShopDiscount: false,
        name: '会员折扣',
        sort: 3,
    },
    FULL_REDUCTION: {
        isShopDiscount: true,
        name: '满减',
        sort: 4,
    },
    CONSUMPTION_REBATE: {
        isShopDiscount: false,
        name: '消费返利',
        sort: 5,
    },
}

//搜索排序
export const SORT_TYPE = {
    all: 0, //综合
    price: 1, //价格
    salesVolume: 2, //销量
}
export const ORDER_TYPE = {
    ASC: 0,
    DESC: 1,
}
