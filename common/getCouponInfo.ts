import { ref, Ref, reactive, provide, computed, getCurrentInstance } from 'vue';
import { useAppStore } from '@/store';
import { getGoodsDetailsCouponPage } from '@/server/api';

export type Long = number | string;

export interface GetCouponListParams {
	shopId: Long;
	amount: Long;
	productId: Long;
}

export interface CouponDispatcherType {
	[key: string]: any;
}

export interface CartApiCouponVO {
	[key: string]: any;
}

export interface ApiCouponVO {
	[key: string]: any;
}

/**
 * 库存类型
 */
export enum StockType {
	//无线库存
	UNLIMITED = 'UNLIMITED',
	//有限库存
	LIMITED = 'LIMITED'
}

/**
 * sku 限购类型
 */
export enum LimitType {
	//不限购
	UNLIMITED = 'UNLIMITED',
	//商品限购
	PRODUCT_LIMITED = 'PRODUCT_LIMITED',
	//sku 限购
	SKU_LIMITED = 'SKU_LIMITED'
}

/**
 * 商品 sku 信息
 */
export interface StorageSku {
	activityId?: Long;
	id: Long;
	//库存类型
	stockType: keyof typeof StockType;
	//剩余库存
	stock: Long;
	//销量
	salesVolume: Long;
	// 初始销量
	initSalesVolume: Long;
	//限购类型
	limitType: keyof typeof LimitType;
	//限购数量
	limitNum: number;
	//sku 规格信息
	specs: string[];
	//sku 图片
	image: string;
	//sku 划线价
	price: Long;
	//sku 销售价
	salePrice: Long;
	// 重量
	weight: number;
	// 店铺id
	shopId: Long;
	// 商品id
	productId: Long;
	activePrice?: Long;
}

export default function hooksOfCoupon(choosedSku: Ref<StorageSku>) {
	const instance = getCurrentInstance();
	const goodsDetailCouponList = ref<CartApiCouponVO[]>([]);
	const pageConfig = reactive({ size: 5, current: 1, pages: 1 });
	const isExist = computed(() => !!useAppStore().GET_PLUGIN('addon-coupon'));
	async function doRequest() {
		// console.log('----刷新·········1·');
		let routeShopId = '';
		// #ifdef H5
		routeShopId = (instance?.attrs?.shopId as string) || '';
		// #endif
		// #ifndef H5
		const pages = getCurrentPages();
		const currentPage = pages[pages.length - 1];
		routeShopId = currentPage.options?.shopId || '';
		// #endif
		if (!choosedSku.value) return;
		const { productId, salePrice, shopId } = choosedSku.value;
		const options = {
			shopId: routeShopId || shopId,
			amount: salePrice,
			productId
		};
		await initGoodsDetailCouponList(false, options).catch(() => {});
		// 请求失败 / 成功 都会走这里
		// 商品详情通过优惠券数量决定是否计算优惠券优惠
		return goodsDetailCouponList.value[0];
	}

	//TODO:这里的 options 怎样入参合适 直接取到 choosedSku 还是作为一个单独的模块去给商品使用
	async function initGoodsDetailCouponList(isLoad = false, options: GetCouponListParams) {
		// console.log('----刷新··········');

		if (!isLoad) {
			// 刷新
			pageConfig.current = 1;
			goodsDetailCouponList.value = await getCouponList(options);
		} else if (isLoad && pageConfig.current < pageConfig.pages) {
			// 更新
			pageConfig.current++;
			goodsDetailCouponList.value = [...goodsDetailCouponList.value, ...(await getCouponList(options))];
		}
	}
	/**
	 * 获取优惠券列表
	 * @param {GetCouponListParams} options
	 * @param {number} pages
	 */
	async function getCouponList(options: GetCouponListParams) {
		if (Number(options.amount) === 0) return [];
		const { code, data } = await getGoodsDetailsCouponPage({ ...options, current: pageConfig.current, size: pageConfig.size });
		if (code !== 200) {
			return [];
		}

		pageConfig.pages = data.pages;
		return data.records.map((item: ApiCouponVO) => ({
			...item,
			watermark: false
		}));
	}

	const exportSlice: CouponDispatcherType = {
		doRequest,
		initGoodsDetailCouponList,
		goodsDetailCouponList,
		isExist
	};
	// provide('couponProvide', exportSlice);
	return exportSlice;
}
