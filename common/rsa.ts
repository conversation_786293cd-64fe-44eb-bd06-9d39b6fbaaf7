import { JSEncrypt } from 'jsencrypt'

const public_key = import.meta.env.VITE_RSA_PUBLIC_KEY

export function encryptor() {
	const encryptor = new JSEncrypt({ default_key_size: '2048' })
	encryptor.setPublicKey(public_key)
	return encryptor
}

function enc(encryptor : JSEncrypt, data : string) {
	const encrypt = encryptor.encrypt(data)
	if (!encrypt) {
		throw new Error('rsa encrypt failed')
	}
	return encrypt
}

type EncryptFunc = (enc : (data : string) => string) => void

export function encryptAccept(encryptFunc : EncryptFunc) {
	const c = encryptor()
	encryptFunc((data) => {
		return enc(c, data)
	})
}