// 基础版问卷
export interface Questionnaire {
	id: number;
	question: string;
	type: "select" | "multiselect" | "input";
	key?: string;
	required?: boolean;
	unit?: string;
	scope?: number[];
	dataType?: "integer" | "decimal"; // 数据类型：整数或小数（仅input类型使用）
	defaultValue?: number; // 默认值（仅input类型使用）
	step?: number; // 步长（仅input类型使用）
	condition?: {
		value: string | number;
		key: string;
	};
	options?: {
		label: string;
		value: string | number;
	}[];
}

export const familyDiseaseQuestionnaire: Questionnaire[] = [
	{
		id: 1,
		question: "您的亲属目前或曾经是否被医生诊断过糖尿病?",
		key: "diab",
		type: "multiselect",
		required: true,
		options: [
			{
				label: "本人",
				value: "diab_2",
			},
			{
				label: "父亲",
				value: "diab_f",
			},
			{
				label: "母亲",
				value: "diab_m",
			},
			{
				label: "兄弟",
				value: "diab_bro",
			},
			{
				label: "姐妹",
				value: "diab_sis",
			},
			{
				label: "无",
				value: "",
			},
		],
	},
	{
		id: 2,
		question: "您的亲属目前或曾经是否被医生诊断过患有高血压?",
		type: "multiselect",
		key: "hbp",
		required: true,
		options: [
			{
				label: "本人",
				value: "hbp",
			},
			{
				label: "父亲",
				value: "hbp_f",
			},
			{
				label: "母亲",
				value: "hbp_m",
			},
			{
				label: "兄弟",
				value: "hbp_bro",
			},
			{
				label: "姐妹",
				value: "hbp_sis",
			},
			{
				label: "无",
				value: "",
			},
		],
	},
	{
		id: 3,
		question: "您的亲属目前或曾经是否被医生诊断过患有脑卒中(脑中风)?",
		type: "multiselect",
		key: "stroke",
		required: true,
		options: [
			{
				label: "本人",
				value: "stroke",
			},
			{
				label: "父亲",
				value: "stroke_f",
			},
			{
				label: "母亲",
				value: "stroke_m",
			},
			{
				label: "兄弟",
				value: "stroke_bro",
			},
			{
				label: "姐妹",
				value: "stroke_sis",
			},
			{
				label: "无",
				value: "",
			},
		],
	},
	{
		id: 4,
		question: "您的亲属目前或曾经是否被医生诊断过患有慢阻肺(慢性支气管炎/肺气肿)?",
		type: "multiselect",
		key: "copd",
		required: true,
		options: [
			{
				label: "本人",
				value: "copd",
			},
			{
				label: "父亲",
				value: "chronic_f",
			},
			{
				label: "母亲",
				value: "chronic_m",
			},
			{
				label: "兄弟",
				value: "chronic_bro",
			},
			{
				label: "姐妹",
				value: "chronic_sis",
			},
			{
				label: "无",
				value: "",
			},
		],
	},
	{
		id: 5,
		question: "您的亲属目前或曾经是否被医生诊断过患有痛风(高尿酸血症)?",
		type: "multiselect",
		key: "gout",
		required: true,
		options: [
			{
				label: "本人",
				value: "gout",
			},
			{
				label: "父亲",
				value: "gout_f",
			},
			{
				label: "母亲",
				value: "gout_m",
			},
			{
				label: "兄弟",
				value: "gout_bro",
			},
			{
				label: "姐妹",
				value: "gout_sis",
			},
			{
				label: "无",
				value: "",
			},
		],
	},
	{
		id: 6,
		question: "您的亲属目前或曾经是否髋部骨折?",
		type: "multiselect",
		key: "fracture",
		required: true,
		options: [
			{
				label: "本人",
				value: "fracture",
			},
			{
				label: "父亲",
				value: "pf_f",
			},
			{
				label: "母亲",
				value: "pf_m",
			},
			{
				label: "兄弟",
				value: "pf_bro",
			},
			{
				label: "姐妹",
				value: "pf_sis",
			},
			{
				label: "无",
				value: "",
			},
		],
	},
];

export const foodQuestionnaire: Questionnaire[] = [
	{
		id: 21,
		question: "您每周有几天会吃大米、面粉类或杂粮类食物?",
		key: "cr_w",
		type: "select",
		required: true,
		options: [
			{
				label: "5-7天",
				value: 1,
			},
			{
				label: "2-4天",
				value: 2,
			},
			{
				label: "1-2天",
				value: 3,
			},
			{
				label: "<1天或不吃",
				value: 4,
			},
		],
	},
	{
		id: 22,
		question: "您每周有几天会吃肉类（猪，牛，羊，禽）?",
		key: "meat_w",
		type: "select",
		required: true,
		options: [
			{
				label: "5-7天",
				value: 1,
			},
			{
				label: "2-4天",
				value: 2,
			},
			{
				label: "1-2天",
				value: 3,
			},
			{
				label: "<1天或不吃",
				value: 4,
			},
		],
	},
	{
		id: 23,
		question: "您每周有几天会吃鱼类或其他水产品：虾、蟹?",
		key: "fish_w",
		type: "select",
		required: true,
		options: [
			{
				label: "5-7天",
				value: 1,
			},
			{
				label: "2-4天",
				value: 2,
			},
			{
				label: "1-2天",
				value: 3,
			},
			{
				label: "<1天或不吃",
				value: 4,
			},
		],
	},
	{
		id: 24,
		question: "您每周有几天会吃蛋类及其制品?",
		key: "egg_w",
		type: "select",
		required: true,
		options: [
			{
				label: "5-7天",
				value: 1,
			},
			{
				label: "2-4天",
				value: 2,
			},
			{
				label: "1-2天",
				value: 3,
			},
			{
				label: "<1天或不吃",
				value: 4,
			},
		],
	},
	{
		id: 25,
		question: "您每周有几天会吃牛奶及奶制品?",
		key: "milk_w",
		type: "select",
		required: true,
		options: [
			{
				label: "5-7天",
				value: 1,
			},
			{
				label: "2-4天",
				value: 2,
			},
			{
				label: "1-2天",
				value: 3,
			},
			{
				label: "<1天或不吃",
				value: 4,
			},
		],
	},
	{
		id: 26,
		question: "您每周有几天会吃豆类及豆制品?",
		key: "bean_w",
		type: "select",
		required: true,
		options: [
			{
				label: "5-7天",
				value: 1,
			},
			{
				label: "2-4天",
				value: 2,
			},
			{
				label: "1-2天",
				value: 3,
			},
			{
				label: "<1天或不吃",
				value: 4,
			},
		],
	},
	{
		id: 27,
		question: "您每周有几天会吃新鲜蔬菜?",
		key: "vegetable_w",
		type: "select",
		required: true,
		options: [
			{
				label: "5-7天",
				value: 1,
			},
			{
				label: "2-4天",
				value: 2,
			},
			{
				label: "1-2天",
				value: 3,
			},
			{
				label: "<1天或不吃",
				value: 4,
			},
		],
	},
	{
		id: 28,
		question: "您每周有几天会吃新鲜水果?",
		key: "ffru_w",
		type: "select",
		required: true,
		options: [
			{
				label: "5-7天",
				value: 1,
			},
			{
				label: "2-4天",
				value: 2,
			},
			{
				label: "1-2天",
				value: 3,
			},
			{
				label: "<1天或不吃",
				value: 4,
			},
		],
	},
];

export const drinkQuestionnaire: Questionnaire[] = [
	{
		id: 29,
		question: "您是否喝酒？",
		key: "drinking_status",
		type: "select",
		required: true,
		options: [
			{
				label: "从来不喝",
				value: 1,
			},
			{
				label: "喝酒",
				value: 2,
			},
		],
	},
	{
		id: 30,
		question: "您最近一周喝了多少次？",
		condition: {
			key: "drinking_status",
			value: 2,
		},
		key: "how_long_drink",
		type: "select",
		required: true,
		options: [
			{
				label: "7次以上",
				value: 1,
			},
			{
				label: "5-7次",
				value: 2,
			},
			{
				label: "3-4次",
				value: 3,
			},
			{
				label: "1-2次",
				value: 4,
			},
			{
				label: "少于1次",
				value: 5,
			},
		],
	},
];

export const sportsQuestionnaire: Questionnaire[] = [
	{
		id: 101,
		question: "你是怎么运动的?（10分钟以上的活动）",
		type: "select",
		key: "sport_type",
		required: true,
		options: [
			{
				label: "重体力(如搬运重物、赛跑、健身房阻力运动或长时间健身操等)",
				value: "heavy",
			},
			{
				label: "中体力(如骑自行车、慢跑、游泳、快步走、乒乓球、羽毛球、交谊舞等)",
				value: "medium",
			},
			{
				label: "轻体力(包括散步、慢走、您工作和出行时的步行等)",
				value: "light",
			},
		],
	},
	// 重体力运动相关问题
	{
		id: 102,
		question: "重体力运动每周几天？",
		type: "input",
		key: "h_activity_freq",
		required: true,
		scope: [0, 7],
		dataType: "integer",
		defaultValue: 3,
		step: 1,
		condition: {
			key: "sport_type",
			value: "heavy",
		},
	},
	{
		id: 103,
		question: "重体力运动每天几分钟？",
		type: "input",
		key: "h_act_tim_length",
		required: true,
		scope: [10, 1440],
		dataType: "integer",
		defaultValue: 30,
		step: 5,
		condition: {
			key: "sport_type",
			value: "heavy",
		},
	},
	// 中体力运动相关问题
	{
		id: 104,
		question: "中体力运动每周几天？",
		type: "input",
		key: "m_activity_freq",
		required: true,
		scope: [0, 7],
		dataType: "integer",
		defaultValue: 3,
		step: 1,
		condition: {
			key: "sport_type",
			value: "medium",
		},
	},
	{
		id: 105,
		question: "中体力运动每天几分钟？",
		type: "input",
		key: "m_act_tim_length",
		required: true,
		scope: [10, 1440],
		dataType: "integer",
		defaultValue: 30,
		step: 5,
		condition: {
			key: "sport_type",
			value: "medium",
		},
	},
	// 轻体力运动相关问题
	{
		id: 106,
		question: "轻体力运动每周几天？",
		type: "input",
		key: "walking_day",
		required: true,
		scope: [0, 7],
		dataType: "integer",
		defaultValue: 3,
		step: 1,
		condition: {
			key: "sport_type",
			value: "light",
		},
	},
	{
		id: 107,
		question: "轻体力运动每天几分钟？",
		type: "input",
		key: "walking_minutes",
		required: true,
		scope: [10, 1440],
		dataType: "integer",
		defaultValue: 30,
		step: 5,
		condition: {
			key: "sport_type",
			value: "light",
		},
	},
];

export const sleepQuestionnaire: Questionnaire[] = [
	{
		id: 201,
		question: "您的睡眠质量？",
		type: "select",
		key: "sleep_status",
		required: true,
		options: [
			{
				label: "非常好",
				value: 1,
			},
			{
				label: "尚好",
				value: 2,
			},
			{
				label: "不好",
				value: 3,
			},
			{
				label: "非常差",
				value: 4,
			},
		],
	},
	{
		id: 202,
		question: "最近，您每天睡多久？",
		type: "input",
		key: "sleep_time",
		unit: "小时",
		required: true,
		scope: [0, 23],
		dataType: "decimal",
		defaultValue: 8,
		step: 0.5,
	},
	{
		id: 203,
		question: "您是否要服药才能入睡？",
		type: "select",
		key: "sleep_medi",
		required: true,
		options: [
			{
				label: "不用服用",
				value: 1,
			},
			{
				label: "每周不足1次",
				value: 2,
			},
			{
				label: "每周平均1-2次",
				value: 3,
			},
			{
				label: "每周平均3次甚至更多",
				value: 4,
			},
		],
	},
];

export const healthQuestionnaire: Questionnaire[] = [
	// {
	//     id: 1001,
	//     question: '身高',
	//     type: 'input',
	//     unit: 'cm',
	//     key: 'body_height',
	//     scope: [50, 250],
	// },
	// {
	//     id: 1002,
	//     question: '体重',
	//     type: 'input',
	//     unit: 'kg',
	//     key: 'body_weight',
	//     scope: [25, 150],
	// },
	// {
	//     id: 1003,
	//     question: '臀围',
	//     type: 'input',
	//     unit: 'cm',
	//     key: 'hip_circum',
	//     scope: [50, 150],
	// },
	// {
	//     id: 1004,
	//     question: '腰围',
	//     type: 'input',
	//     unit: 'cm',
	//     key: 'waist_line',
	//     scope: [50, 150],
	// },
	// {
	// 	id: 1005,
	// 	question: "收缩压",
	// 	type: "input",
	// 	unit: "mmHg",
	// 	key: "sbp",
	// 	required: true,
	// 	scope: [60, 300],
	// },
	// {
	// 	id: 1006,
	// 	question: "舒张压",
	// 	type: "input",
	// 	unit: "mmHg",
	// 	key: "dbp",
	// 	required: true,
	// 	scope: [40, 200],
	// },
	// {
	// 	id: 1007,
	// 	question: "总胆固醇（TC）",
	// 	type: "input",
	// 	unit: "mmol/L",
	// 	key: "cholesterol",
	// 	required: true,
	// 	scope: [0.01, 20],
	// },
	// {
	// 	id: 1008,
	// 	question: "高密度脂蛋白胆固醇",
	// 	type: "input",
	// 	unit: "mmol/L",
	// 	key: "hdlc",
	// 	required: true,
	// 	scope: [0.01, 10],
	// },
	// {
	// 	id: 1009,
	// 	question: "甘油三酯（TG）",
	// 	type: "input",
	// 	unit: "mmol/L",
	// 	key: "triglycerin",
	// 	required: true,
	// 	scope: [0.01, 20],
	// },
	// {
	// 	id: 1010,
	// 	question: "低密度脂蛋白胆固醇",
	// 	type: "input",
	// 	unit: "mmol/L",
	// 	key: "ldlc",
	// 	scope: [0.01, 20],
	// },
	// {
	// 	id: 1011,
	// 	question: "空腹血糖(Glu)",
	// 	type: "input",
	// 	unit: "mmol/L",
	// 	key: "fasting_bg",
	// 	required: true,
	// 	scope: [1.1, 33.3],
	// },
	// {
	// 	id: 1012,
	// 	question: "骨密度检查指标-T值",
	// 	type: "input",
	// 	key: "t_score",
	// 	scope: [-10, 10],
	// },
	{
		id: 1013,
		question: "您上次接受乳腺钼靶X光检查的时间是？",
		condition: {
			key: "sex",
			value: 2,
		},
		type: "select",
		key: "mammography_xray",
		required: true,
		options: [
			{
				label: "一年内",
				value: 1,
			},
			{
				label: "1-2年前",
				value: 2,
			},
			{
				label: "2-3年前",
				value: 3,
			},
			{
				label: "3-4年前",
				value: 4,
			},
			{
				label: "5-6年前",
				value: 5,
			},
			{
				label: "7年前或更早",
				value: 6,
			},
			{
				label: "从来没有",
				value: 7,
			},

			{
				label: "不清楚",
				value: 8,
			},
		],
	},
	{
		id: 1014,
		question: "您经常自己检查您乳房是否有肿块吗？",
		condition: {
			key: "sex",
			value: 2,
		},
		type: "select",
		key: "breast_lumps",
		required: true,
		options: [
			{
				label: "每月",
				value: 1,
			},
			{
				label: "几个月一次",
				value: 2,
			},
			{
				label: "很少或从不",
				value: 3,
			},
		],
	},
	{
		id: 1015,
		question: "您是否有头晕、头痛、头胀、头部压迫紧箍感?",
		key: "dhhhch",
		type: "select",
		required: true,
		options: [
			{
				label: "是",
				value: 1,
			},
			{
				label: "否",
				value: 0,
			},
		],
	},
];

export const baseQuestionnaireList = [
	{
		category: "food",
		text: "饮食习惯",
		list: foodQuestionnaire,
	},
	{
		category: "drink",
		text: "饮酒情况",
		list: drinkQuestionnaire,
	},
	{
		category: "sports",
		text: "运动情况",
		list: sportsQuestionnaire,
	},
	{
		category: "sleep",
		text: "睡眠情况",
		list: sleepQuestionnaire,
	},
	{
		category: "health",
		text: "身体指标",
		list: healthQuestionnaire,
	},
	{
		category: "famiily",
		text: "家族疾病史",
		list: familyDiseaseQuestionnaire,
	},
];

export const totalBaseQuestionnaireList = [...foodQuestionnaire, ...drinkQuestionnaire, ...sportsQuestionnaire, ...sleepQuestionnaire, ...healthQuestionnaire, ...familyDiseaseQuestionnaire];
