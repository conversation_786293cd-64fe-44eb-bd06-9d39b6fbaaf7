const storageKey = 'hjwy_user';

class Storage {
	set(key: string, data: any, expired = 365) {
		uni.setStorageSync(`${storageKey}_${key}`, JSON.stringify({ data, time: +new Date(), expired: expired * 60 * 60 * 24 * 1000 }));
	}

	get(key: string): any {
		try {
			const { data, time = 0, expired = 0 } = JSON.parse(uni.getStorageSync(`${storageKey}_${key}`));

			if (!expired) return null;

			if (time + expired > +new Date()) return data;

			this.remove(key);
			return null;
		} catch {
			return null;
		}
	}

	remove(key: string) {
		return uni.removeStorageSync(`${storageKey}_${key}`);
	}

	removeAll() {
		const res = uni.getStorageInfoSync();
		res.keys.forEach((key) => {
			if (key.startsWith(storageKey)) {
				uni.removeStorageSync(key);
			}
		});
	}
}

export default new Storage();
