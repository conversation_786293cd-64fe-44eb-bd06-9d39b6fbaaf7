<script setup>
import { onLoad } from '@dcloudio/uni-app';

import { ref, reactive, getCurrentInstance } from 'vue';

import { useUserStore } from '@/store';

import { getOrderEvaluateInfo, getOrderEvaluateInfoList, likeEvaluation } from '@/server/api';

const props = defineProps({
	productId: {
		type: String,
		required: true
	},
	shopId: {
		type: String,
		required: true
	},
	serveGoods: {
		type: [String, Number],
		default: 0
	}
});

const paging = ref(null);

const pageData = reactive({
	activeTab: 0,
	tabList: [
		{
			name: '全部',
			type: '',
			count: 0,
			countKey: 'totalCount',
			show: true
		},
		{
			name: '有内容',
			type: 'CONTENT',
			count: 0,
			countKey: 'contentCount',
			show: true
		},
		{
			name: '有图片',
			type: 'IMAGE',
			count: 0,
			countKey: 'mediaCount',
			show: true
		},
		{
			name: '好评',
			type: 'PRAISE',
			count: 0,
			countKey: 'praiseCount',
			show: true
		},
		{
			name: '差评',
			type: 'CRITICIZE',
			count: 0,
			countKey: 'criticizeCount',
			show: true
		}
	],

	sort: 1,

	evaluateOverview: {
		contentCount: 0,
		mediaCount: 0,
		praiseCount: 0,
		criticizeCount: 0,
		praiseRatio: '0.00',
		totalCount: 0
	},

	dataList: []
});

function changeType(e) {
	if (pageData.activeTab !== e) {
		pageData.activeTab = e;

		if (paging.value) paging.value.reload();
	}
}

function changeSort(e) {
	if (pageData.sort !== e) {
		pageData.sort = e;
		if (paging.value) paging.value.reload();
	}
}

function queryList(pageNo, pageSize) {
	const params = {
		productId: props.productId,
		shopId: props.shopId,
		type: pageData.tabList[pageData.activeTab].type,
		current: pageNo,
		size: pageSize
	};

	getOrderEvaluateInfoList({
		...params
	})
		.then((res) => {
			if (res.apiStatus) {
				paging.value.complete(res.data.records);
			} else {
				paging.value.complete(false);
			}
		})
		.catch((err) => {
			paging.value.complete(false);
		});
}

function likeEvaluationItem(e) {
	const { item, index } = e;

	likeEvaluation({
		evaluateId: item.id,
		userId: useUserStore().userData.userId
	}).then((res) => {
		if (res.apiStatus) {
			pageData.dataList[index].userEvaluateStatus = !pageData.dataList[index].userEvaluateStatus;
			if (pageData.dataList[index].userEvaluateStatus) {
				pageData.dataList[index].likeNum += 1;
			} else {
				pageData.dataList[index].likeNum -= 1;
			}
		}
	});
}

async function loadData() {
	try {
		const { apiStatus, data } = await getOrderEvaluateInfo({
			productId: props.productId,
			shopId: props.shopId,
			type: ''
		});
		if (apiStatus) {
			pageData.evaluateOverview = data;
		}
	} catch (error) {
		//TODO handle the exception
	}
}

onLoad(() => {
	loadData();
});
</script>
<template>
	<app-layout>
		<z-paging ref="paging" v-model="pageData.dataList" @query="queryList">
			<template #top>
				<view class="w-full flex px-16 mt-20">
					<view class="w-0 flex-1">
						<scroll-view scroll-x="true">
							<view class="flex items-center">
								<template v-for="(item, index) in pageData.tabList" :key="index">
									<template v-if="item.show">
										<view
											@click="changeType(index)"
											class="bg-#F2F4F8 border-rd-8 h-60 text-center line-height-60 ml-12 flex-shrink-0"
											:class="[item.type ? 'w-178' : 'w-128']"
											:style="[pageData.activeTab == index && 'background-color: #03BAA7']"
										>
											<text class="text-26 text-#323232 font-500" :style="[pageData.activeTab == index && 'color: #fff; font-weight: bold']">{{ item.name }}</text>
											<template v-if="item.type && item.countKey">
												<template v-if="item.type === 'PRAISE'">
													<text class="text-24 text-#888 font-500 mx-10" :style="[pageData.activeTab == 2 ? 'color: #FD834F; font-weight: bold' : 'color: #FD834F;']">
														{{ pageData.evaluateOverview[item.countKey] }}
													</text>
												</template>
												<template v-else>
													<text class="text-24 text-#888 font-500 mx-10" :style="[pageData.activeTab == index && 'color: #fff; font-weight: bold']">
														{{ pageData.evaluateOverview[item.countKey] }}
													</text>
												</template>
											</template>
										</view>
									</template>
								</template>
							</view>
						</scroll-view>
					</view>
				</view>

				<!-- <view class="flex justify-start items-center pl-36 mt-40">
					<view @click="changeSort(1)" class="text-26 text-#616161 font-500" :style="[pageData.sort == 1 && 'color: #323232;font-weight: bold;']">默认排序</view>
					<view class="w-1 h-20 bg-#DFE4EA mx-14"></view>
					<view @click="changeSort(2)" class="text-26 text-#616161 font-500" :style="[pageData.sort == 2 && 'color: #323232;font-weight: bold;']">最新排序</view>
				</view> -->

				<view class="w-full h-1 bg-#EAEDF3 mt-20"></view>
			</template>
			<view class="evaluation">
				<view v-for="(item, index) in pageData.dataList" :key="index" class="">
					<page-evaluation-item :item="item" :index="index" :serveGoods="item.serveGoods" type="all" @likeEvaluation="likeEvaluationItem"></page-evaluation-item>
				</view>
			</view>
		</z-paging>
	</app-layout>
</template>
<style lang="scss" scoped>
.evaluation {
}
</style>
