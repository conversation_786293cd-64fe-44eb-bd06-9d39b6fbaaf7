<script setup>
import { onLoad } from "@dcloudio/uni-app";

import { ref, reactive } from "vue";

import { useUserStore, useOrderStore } from "@/store";

import { canENV } from "@/common/utils";

import { getGuessYouLike } from "@/server/api";

const userStore = useUserStore();

const props = defineProps({
	orderGoodsType: {
		type: String,
		default: "shop",
	},
	orderStatus: {
		type: String,
		default: "UN_DELIVERY",
	},
});

const pageData = reactive({
	dataList: [],

	list1: [],
	list2: [],
});

const waterfall = ref(null);

function changeList(e) {
	pageData[e.name].push(e.value);
}

function loadData(refresh) {
	if (refresh) {
		pageData.dataList = [];
		pageData.list1 = [];
		pageData.list2 = [];

		if (waterfall.value) waterfall.value.clear();
	}

	getGuessYouLike({
		current: 1,
		size: 30,
	}).then((res) => {
		if (res.apiStatus) {
			const list = res.data.records.map((i) => {
				if (!Number(i.serveTypeId)) {
					i.serveGoods = 0;
					i.serveTypeId = "0";
					i.serveTypeFename = "shop";
				}

				i.name = i.productName;
				i.pic = i.productAlbumPics;

				i.statistics = {
					productId: i.productId,
					lowestPrice: i.lowestPrice,
					salesVolume: i.salesVolume,
				};

				return i;
			});

			pageData.dataList = refresh ? list : [...pageData.dataList, ...list];

			// if (pageData.dataList.length >= res.data.total) {
			// 	pageData.status = 'nomore';
			// } else {
			// 	pageData.status = 'loadmore';
			// }
		}
	});
}

function goHome() {
	uni.switchTab({
		url: "/pages/tabBar/home/<USER>",
	});
}

function goOrder() {
	if (props.orderGoodsType === "integral") {
		uni.navigateTo({
			url: "/pages/order/pages/integralOrder/integralOrder",
		});
	} else {
		useOrderStore().changeGoOrderInfo({
			orderGoodsType: props.orderGoodsType,
			orderStatus: props.orderStatus,
		});

		// uni.switchTab({
		// 	url: '/pages/tabBar/order/order'
		// });
		uni.redirectTo({
			url: '/pages/tabBar/order/order'
		});
	}
}

onLoad(() => {
	loadData(true);
});
</script>

<template>
	<view class="pay-success page-main bg-#F4F5F8">
		<view class="w-full bg-#fff pb-40 header-box">
			<view class="py-30 flex flex-center">
				<app-image src="@/pages/goods/static/icon_success.png" size="106" mode=""></app-image>
			</view>
			<view class="text-center font-40 font-bold">付款成功</view>
			<view class="w-full flex items-center justify-between px-90 mt-50">
				<view class="w-246">
					<uv-button color="#F2F3F6" text="返回首页" class="w-full mt-0 flex-center" custom-style="height: 90rpx;" customTextStyle="font-size: 30rpx; font-weight: bold;" shape="circle" loadingMode="circle" @click="goHome">
						<view class="text-30 text-#828D9C font-bold">返回主页</view>
					</uv-button>
				</view>
				<view class="w-246">
					<uv-button color="#E8FAF3" text="查看订单" class="w-full mt-0 flex-center" custom-style="height: 90rpx;" customTextStyle="font-size: 30rpx; font-weight: bold;" shape="circle" loadingMode="circle" @click="goOrder">
						<view class="text-30 text-#00B698 font-bold">查看订单</view>
					</uv-button>
				</view>
			</view>
		</view>

		<view class="py-30 flex flex-center">
			<view class="">
				<app-image src="@/pages/goods/static/icon_cainixihuan_title_left.png" size="27" mode=""></app-image>
			</view>
			<view class="text-30 font-bold mx-10">您可能还会喜欢</view>
			<view class="">
				<app-image src="@/pages/goods/static/icon_cainixihuan_title_right.png" size="27" mode=""></app-image>
			</view>
		</view>
		<view class="w-full py-0">
			<view class="px-10 py-20 flex flex-wrap">
				<template v-for="(item, index) in pageData.dataList" :key="index">
					<view class="mb-20 w-345 mx-10">
						<!-- 普通商城 -->
						<template v-if="item.serveTypeFename === 'shop'">
							<page-product-card-item :item="item" :index="index"></page-product-card-item>
						</template>

						<!-- 家政服务 -->
						<template v-if="item.serveTypeFename === 'homemaking'">
							<page-jiazheng-card-item :item="item" :index="index"></page-jiazheng-card-item>
						</template>

						<!-- 陪诊服务 -->
						<template v-if="item.serveTypeFename === 'attend'">
							<page-peizhen-card-item :item="item" :index="index"></page-peizhen-card-item>
						</template>

						<!-- 护理服务 -->
						<template v-if="item.serveTypeFename === 'nurse'">
							<page-huli-card-item :item="item" :index="index"></page-huli-card-item>
						</template>
					</view>
				</template>

				<template v-if="false">
					<uv-waterfall ref="waterfall" v-model="pageData.dataList" :add-time="100" :columnCount="2" left-gap="0rpx" right-gap="0rpx" column-gap="20rpx" @changeList="changeList">
						<template #list1>
							<view>
								<template v-for="(item, index) in pageData.list1" :key="index">
									<view class="mb-20">
										<!-- 普通商城 -->
										<template v-if="item.serveTypeFename === 'shop'">
											<page-product-card-item :item="item" :index="index"></page-product-card-item>
										</template>

										<!-- 家政服务 -->
										<template v-if="item.serveTypeFename === 'homemaking'">
											<page-jiazheng-card-item :item="item" :index="index"></page-jiazheng-card-item>
										</template>

										<!-- 陪诊服务 -->
										<template v-if="item.serveTypeFename === 'attend'">
											<page-peizhen-card-item :item="item" :index="index"></page-peizhen-card-item>
										</template>

										<!-- 护理服务 -->
										<template v-if="item.serveTypeFename === 'nurse'">
											<page-huli-card-item :item="item" :index="index"></page-huli-card-item>
										</template>
									</view>
								</template>
							</view>
						</template>
						<template #list2>
							<template v-for="(item, index) in pageData.list2" :key="index">
								<view class="mb-20">
									<!-- 普通商城 -->
									<template v-if="item.serveTypeFename === 'shop'">
										<page-product-card-item :item="item" :index="index"></page-product-card-item>
									</template>

									<!-- 家政服务 -->
									<template v-if="item.serveTypeFename === 'homemaking'">
										<page-jiazheng-card-item :item="item" :index="index"></page-jiazheng-card-item>
									</template>

									<!-- 陪诊服务 -->
									<template v-if="item.serveTypeFename === 'attend'">
										<page-peizhen-card-item :item="item" :index="index"></page-peizhen-card-item>
									</template>

									<!-- 护理服务 -->
									<template v-if="item.serveTypeFename === 'nurse'">
										<page-huli-card-item :item="item" :index="index"></page-huli-card-item>
									</template>
								</view>
							</template>
						</template>
					</uv-waterfall>
				</template>
			</view>
		</view>

		<view class=""></view>
	</view>
</template>

<style lang="scss" scoped>
.pay-success {
}
.header-box {
	border-radius: 0rpx 0rpx 20rpx 20rpx;
}
</style>
