<script setup>
import { onLoad } from "@dcloudio/uni-app";

import { ref, reactive } from "vue";

import { getShopDetails, cancelAttentionAndAttention, searchProduct } from "@/server/api";

import { useUserStore, useAppStore } from "@/store";

import { canENV, salesVolumeToStr } from "@/common/utils";

import throttle from "@/uni_modules/uv-ui-tools/libs/function/throttle.js";
import { common } from "@/common/images";

const userStore = useUserStore();

const _props = defineProps({
	shopId: {
		type: String,
		required: true,
	},
});

const pagingRef = ref();

const waterfall = ref(null);

const _data = reactive({
	dataList: [],

	list1: [],
	list2: [],
});
const shopInfo = ref();

const enumSortIndex = ref(0);
const enumSortKey = [
	[
		{ order: "salesVolume", sort: "DESC" },
		{ order: "createTime", sort: "DESC" },
	],
	{ order: "salesVolume", sort: "DESC" },
	{ order: "createTime", sort: "DESC" },
	{ order: "salePrices", sort: "ASC" },
	{ order: "salePrices", sort: "DESC" },
];

const searchParams = reactive({
	keyword: null,
	orderByParams: [
		{ order: "salesVolume", sort: "DESC" },
		{ order: "createTime", sort: "DESC" },
	],
	shopId: _props.shopId || "",
});

function changeEnumSort(index) {
	if (enumSortIndex.value !== index) {
		enumSortIndex.value = index;
		searchParams.orderByParams = enumSortKey[enumSortIndex.value];
		if (pagingRef.value) pagingRef.value.reload();
	}
}

function changeList(e) {
	_data[e.name].push(e.value);
}

async function queryList(pageNo, pageSize) {
	const params = {
		page: pageNo,
		current: pageNo,
		limit: pageSize,
		searchTotalStockGtZero: true,
		status: "loadmore",
		...searchParams,
	};

	if (pageNo === 1) {
		_data.dataList = [];
		_data.list1 = [];
		_data.list2 = [];

		if (waterfall.value) waterfall.value.clear();
	}

	try {
		const res = await searchProduct(params);

		if (res.apiStatus) {
			let list = [];
			list = res.data.list.map((i) => {
				if (!Number(i.serveTypeId)) {
					i.serveGoods = 0;
					i.serveTypeId = "0";
					i.serveTypeFename = "shop";
				}

				i.name = i.productName;

				i.statistics = {
					productId: i.productId,
					lowestPrice: Math.min(...(i.salePrices || [0])),
					salesVolume: i.salesVolume,
				};

				return i;
			});
			pagingRef.value.complete(list);
		}
	} catch (error) {
		pagingRef.value.complete(false);
	}
}

/**
 * 初始化店铺数据
 * @param {String} shopId
 * @param {String} type
 */
async function initShopInfo(shopId, type = "SHOP_HOME") {
	try {
		const shopRes = await getShopDetails({
			shopId,
			type,
		});

		if (shopRes && shopRes.apiStatus) {
			const { logo: shopLogo, name: shopName } = shopRes.data;
			const info = {
				...shopRes.data,
				shopId: shopId,
				shopName,
				shopLogo,
				followCount: shopRes.data?.follow || "0",
				newTips: "",
				status: "",
			};

			shopInfo.value = info;

			if (pagingRef.value) pagingRef.value.reload();
		}
	} catch (error) {
		//TODO handle the exception
		return Promise.reject(error);
	}
}

/**
 * 关注/取消关注店铺
 */
async function changeShopAttention() {
	cancelAttentionAndAttention({
		name: shopInfo.value.name,
		shopId: _props.shopId,
		shopLogo: shopInfo.value.logo,
		isFollow: !shopInfo.value.isFollow,
	}).then((res) => {
		if (res.apiStatus) {
			shopInfo.value.isFollow = !shopInfo.value.isFollow;
			if (shopInfo.value.isFollow) {
				shopInfo.value.follow += 1;
			} else {
				shopInfo.value.follow = Math.max(shopInfo.value.follow - 1, 0);
			}
			uni.showToast({
				icon: "none",
				title: `${shopInfo.value.isFollow ? "关注" : "取消"}成功`,
			});
		}
	});
}

function canICallBack(cb, type = "") {
	throttle(() => {
		if (userStore.checkLogin) {
			if (typeof cb === "function") cb();
		} else {
			useAppStore().changeShowLoginModal(true);
		}
	});
}

function loadData() {
	if (!_props.shopId) {
		return;
	}

	initShopInfo(_props.shopId);
}

function onRefresh() {}

onLoad(() => {
	loadData();
});
</script>

<template>
	<view class="shop-home page-main bg-#F0F3F7">
		<app-layout>
			<z-paging ref="pagingRef" v-model="_data.dataList" @query="queryList" :refresher-enabled="true" :watch-touch-direction-change="false" :fixed="true" :auto="false" :refresher-vibrate="true">
				<template #top>
					<view class="w-full relative">
						<app-image src="@/pages/goods/static/bg_shop_home.png" mode="widthFix" width="750" class="absolute"></app-image>
						<view class="w-full h-full relative flex flex-col">
							<app-navBar back :backIcon="common.iconLeftBai" :showRight="false">
								<template #content>
									<view class="w-full flex justify-between items-center h-full">
										<view class="flex justify-start items-center"></view>
										<view class="flex items-center">
											<!-- <app-image src="@/pages/order/static/icon_kefu_bai.png" class="mr-24" size="40" mode=""></app-image> -->
											<!-- <app-image src="@/pages/order/static/icon_caidan_bai.png" class="mr-24" size="36" mode=""></app-image> -->
										</view>
									</view>
								</template>
							</app-navBar>

							<template v-if="shopInfo">
								<view class="w-full flex-1 flex flex-col justify-end">
									<view class="w-full flex items-center px-30 py-35">
										<view class="w-full flex items-center">
											<view class="w-110 mr-24 flex-shrink-0">
												<app-image :src="shopInfo.logo" size="110" rd="10" mode="" class="bg-#fff"></app-image>
											</view>

											<view class="flex-1 flex flex-col text-#fff flex-1">
												<view class="w-full uv-line-1 text-32 font-bold">
													{{ shopInfo.name }}
												</view>
												<view class="flex items-center mt-20">
													<view class="">
														<uv-rate :count="1" :value="1" active-color="#F8BC35" inactive-color="#E2E2E2" inactiveIcon="star-fill" size="14" gutter="0" readonly></uv-rate>
													</view>
													<view class="ml-8 text-24">
														{{ shopInfo.score }}
													</view>
													<view class="w-1 h-23 bg-#DFE4EA mx-12"></view>
													<view class="flex items-center text-24 font-500">
														<view class="text-#fff">{{ salesVolumeToStr(shopInfo.follow) }}粉丝</view>
													</view>
												</view>
											</view>
										</view>

										<view class="flex-shrink-0 ml-20">
											<template v-if="!shopInfo.isFollow">
												<view class="px-16 border-rd-8 border-1 border-solid border-#fff bg-#fff flex flex-center text-24 font-500 text-#03BAA7 h-48 ac-op" @click="canICallBack(() => changeShopAttention())">
													<app-image :src="common.iconJiaLv" size="20" mr="9" mode=""></app-image>

													<view class="">关注</view>
												</view>
											</template>

											<template v-else>
												<view class="px-16 border-rd-8 border-1 border-solid border-#fff flex flex-center text-24 font-500 text-#fff h-48 ac-op" @click="canICallBack(() => changeShopAttention())">
													<view class="">已关注</view>
												</view>
											</template>
										</view>
									</view>
									<view class="w-full h-82 bg-#fff" style="border-radius: 20rpx 20rpx 0rpx 0rpx">
										<view class="w-full h-full flex items-center justify-between font-500 text-28 text-#323232 px-30">
											<view class="" :class="enumSortIndex === 0 && 'text-main'" @click="changeEnumSort(0)">综合</view>
											<view class="" :class="enumSortIndex === 1 && 'text-main'" @click="changeEnumSort(1)">销量</view>
											<view class="" :class="enumSortIndex === 2 && 'text-main'" @click="changeEnumSort(2)">新品</view>
											<template v-if="enumSortIndex !== 3 && enumSortIndex !== 4">
												<view class="flex items-center" @click="changeEnumSort(3)">
													<view class="">价格</view>
													<view class="flex flex-col flex-center ml-5 pt-5">
														<app-image :src="common.iconPaixunWu" width="20" mode="widthFix"></app-image>
													</view>
												</view>
											</template>
											<template v-if="enumSortIndex === 3">
												<view class="flex items-center" @click="changeEnumSort(4)">
													<view class="text-main">价格</view>
													<view class="flex flex-col flex-center ml-5 pt-5">
														<app-image :src="common.iconPaixuShengxu" width="20" mode="widthFix"></app-image>
													</view>
												</view>
											</template>
											<template v-if="enumSortIndex === 4">
												<view class="flex items-center" @click="changeEnumSort(3)">
													<view class="text-main">价格</view>
													<view class="flex flex-col flex-center ml-5 pt-5">
														<app-image :src="common.iconPaixuJiangxu" width="20" mode="widthFix"></app-image>
													</view>
												</view>
											</template>
										</view>
									</view>
								</view>
							</template>
						</view>
					</view>
				</template>

				<view class="px-10 py-20 flex flex-wrap">
					<template v-for="(item, index) in _data.dataList" :key="index">
						<view class="mb-20 w-345 mx-10">
							<!-- 普通商城 -->
							<template v-if="item.serveTypeFename === 'shop'">
								<page-product-card-item :item="item" :index="index"></page-product-card-item>
							</template>

							<!-- 家政服务 -->
							<template v-if="item.serveTypeFename === 'homemaking'">
								<page-jiazheng-card-item :item="item" :index="index"></page-jiazheng-card-item>
							</template>

							<!-- 陪诊服务 -->
							<template v-if="item.serveTypeFename === 'attend'">
								<page-peizhen-card-item :item="item" :index="index"></page-peizhen-card-item>
							</template>

							<!-- 护理服务 -->
							<template v-if="item.serveTypeFename === 'nurse'">
								<page-huli-card-item :item="item" :index="index"></page-huli-card-item>
							</template>
						</view>
					</template>

					<template v-if="false">
						<uv-waterfall ref="waterfall" v-model="_data.dataList" :add-time="100" :columnCount="2" left-gap="0rpx" right-gap="0rpx" column-gap="20rpx" @changeList="changeList">
							<template #list1>
								<view>
									<template v-for="(item, index) in _data.list1" :key="index">
										<view class="mb-20">
											<!-- 普通商城 -->
											<template v-if="item.serveTypeFename === 'shop'">
												<page-product-card-item :item="item" :index="index"></page-product-card-item>
											</template>

											<!-- 家政服务 -->
											<template v-if="item.serveTypeFename === 'homemaking'">
												<page-jiazheng-card-item :item="item" :index="index"></page-jiazheng-card-item>
											</template>

											<!-- 陪诊服务 -->
											<template v-if="item.serveTypeFename === 'attend'">
												<page-peizhen-card-item :item="item" :index="index"></page-peizhen-card-item>
											</template>

											<!-- 护理服务 -->
											<template v-if="item.serveTypeFename === 'nurse'">
												<page-huli-card-item :item="item" :index="index"></page-huli-card-item>
											</template>
										</view>
									</template>
								</view>
							</template>
							<template #list2>
								<template v-for="(item, index) in _data.list2" :key="index">
									<view class="mb-20">
										<!-- 普通商城 -->
										<template v-if="item.serveTypeFename === 'shop'">
											<page-product-card-item :item="item" :index="index"></page-product-card-item>
										</template>

										<!-- 家政服务 -->
										<template v-if="item.serveTypeFename === 'homemaking'">
											<page-jiazheng-card-item :item="item" :index="index"></page-jiazheng-card-item>
										</template>

										<!-- 陪诊服务 -->
										<template v-if="item.serveTypeFename === 'attend'">
											<page-peizhen-card-item :item="item" :index="index"></page-peizhen-card-item>
										</template>

										<!-- 护理服务 -->
										<template v-if="item.serveTypeFename === 'nurse'">
											<page-huli-card-item :item="item" :index="index"></page-huli-card-item>
										</template>
									</view>
								</template>
							</template>
						</uv-waterfall>
					</template>
				</view>
			</z-paging>
		</app-layout>
	</view>
</template>

<style lang="scss" scoped>
.shop-home {
}
.text-09C1B1 {
	color: #09c1b1;
}
</style>
