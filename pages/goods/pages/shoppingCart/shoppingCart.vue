<script setup>
import { ref, reactive, getCurrentInstance, computed, nextTick, watch } from "vue";

import { onLoad, onReady, onShow, onHide } from "@dcloudio/uni-app";

import { getShopCarList, deleteShopCarData, editShopCarGood, emptyShopCarData, goodsAddAssess } from "@/server/api";

import { usePublicStore, useAppStore, useGoodsStore } from "@/store";

import { getPriceInfo, route, goShopHome } from "@/common/utils";

import { deepClone, sleep } from "@/uni_modules/uv-ui-tools/libs/function/index.js";

import { Decimal } from "decimal.js";

import useConvert from "@/common/useConvert";
import { common } from "@/common/images";

const { divTenThousand } = useConvert();

const paging = ref(null);

const skuPopup = ref();

const goodsStore = useGoodsStore();

const pageData = reactive({
	typeList: [
		{
			name: "商品",
			type: "shop",
			id: "0",
			value: 0,
		},
		{
			name: "护理",
			type: "nurse",
			id: "1904049800822345728",
			value: 3,
		},
		{
			name: "陪诊",
			type: "attend",
			id: "1904111595356377088",
			value: 2,
		},
		{
			name: "家政",
			type: "homemaking",
			id: "1904111619062583296",
			value: 1,
		},
	],
	typeCurrent: 0,

	isEditor: false,

	sort: 1,
	dataList: [],

	invalidDataList: [],

	value: 1,

	btnLoading: false,
});

const allSelect = ref([]);

pageData.typeList.map((item, index) => {
	pageData.typeList[index].value = Number(useAppStore().goodsServerTypeList[item.type].id || 0);
	pageData.typeList[index].id = useAppStore().goodsServerTypeList[item.type].trueId;
});

const checkProductValue = ref([]);

const selectGoodsList = ref([]);

// 最终价格与数量计算
const result = computed(() => {
	if (selectGoodsList.value.length) {
		const products = selectGoodsList.value.map((item) => [...item.selectGoods]).flat(1);

		const priceArr = products.map((item) => {
			return new Decimal(item?.finalPrice || item.salePrice).mul(item.num);
		});

		const price = priceArr.reduce((pre, cur) => {
			return new Decimal(pre).add(new Decimal(cur));
		});

		return {
			products,
			price,
		};
	} else {
		return {
			products: [],
			price: 0,
		};
	}
});

function submit() {
	if (result.value.products.length > 0) {
		goodsStore.goCreateOrderCart(selectGoodsList.value, 0, 0);
	}
}

function changeType(e) {
	pageData.typeCurrent = e;
	if (paging.value) paging.value.reload();
}

function changeSort(e) {
	pageData.sort = e;
}

async function openSkuProp(info) {
	uni.showLoading({
		title: "加载中...",
		mask: true,
	});
	goodsStore
		.init({
			shopId: info.shop.shopId,
			productId: info.goods.productId,
			skuId: info.goods.id,
			goodsNum: info.goods.num,
			type: "cartSku",
		})
		.then(async (res) => {
			await sleep(400);
			uni.hideLoading();
			if (goodsStore.currentChoosedSku.specs?.length) {
				goodsStore.clickBottomBtn({
					type: "CHANGE_CARD_SKU",
				});

				uni.$once("updateCartList", () => {
					if (paging.value) paging.value.reload();
				});
			}
		})
		.catch((err) => {
			uni.hideLoading();
		});
}

function clearInvalidGoods() {
	uni.showModal({
		title: "提示",
		content: "确定清空失效的商品吗？",
		success: (res) => {
			if (res.confirm) {
				emptyShopCarData().then((res) => {
					if (paging.value) paging.value.reload();
				});
			}
		},
	});
}

async function addCollection() {
	if (result.value.products.length > 0) {
		uni.showModal({
			title: "提醒",
			content: "确定要将已选商品移入收藏吗？",
			success(res) {
				if (res.confirm) {
					const items = result.value.products.map((i) => {
						return {
							shopId: i.shopId,
							productId: i.productId,
							productName: i.productName,
							productPic: i.productImage,
							productPrice: String(i.salePrice),
						};
					});
					const itemIds = items.map((i) => i.id);

					goodsAddAssess({
						userCollectDTOList: items,
						whetherCollect: true,
						serveTypeId: 0,
					}).then((res) => {
						if (res.apiStatus) {
							// resetSelect();
							openEditor(false);
							if (paging.value) paging.value.reload();
						}
					});
				}
			},
		});
	} else {
		openEditor(false);
	}
}

async function delCart() {
	const selectList = [];
	for (let i = 0; i < selectGoodsList.value.length; i++) {
		const shopItem = selectGoodsList.value[i];

		const selectShop = {
			shopId: shopItem.shopId,
			skuIds: [],
			uniqueIds: [],
		};

		for (let j = 0; j < shopItem.selectGoods.length; j++) {
			const goodsItem = shopItem.selectGoods[j];
			selectShop.skuIds.push(goodsItem.id);
			selectShop.uniqueIds.push(goodsItem.uniqueId);
		}

		selectList.push(selectShop);
	}

	if (result.value.products.length > 0) {
		uni.showModal({
			title: "提醒",
			content: "确定要删除已选商品吗？",
			success(res) {
				if (res.confirm) {
					const items = checkProductValue.value.map((i) => {
						return pageData.dataList[i];
					});
					const itemIds = items.map((i) => i.id);

					deleteShopCarData(selectList).then((res) => {
						if (res.apiStatus) {
							// resetSelect()
							openEditor(false);
							if (paging.value) paging.value.reload();
						}
					});
				}
			},
		});
	} else {
		openEditor(false);
	}
}

function resetSelect() {
	selectAll(false);
}

function openEditor(state = true) {
	// checkProductValue.value = [];
	// selectGoodsList.value = [];
	pageData.isEditor = state;
}

// 商品详情
function goGoodsDetails(goods) {
	route("/pages/goods/pages/productDetails/productDetails", {
		productId: goods.productId,
		shopId: goods.shopId,
	});
}

function queryList(pageNo, pageSize) {
	const params = {
		pageNo: pageNo,
		current: pageNo,
		pageSize: pageSize,
		// serveTypeId: pageData.typeList[pageData.typeCurrent].id
	};

	getShopCarList()
		.then((res) => {
			if (res.apiStatus) {
				checkProductValue.value = [];
				selectGoodsList.value = [];

				let list = res.data.valid;

				const newList = list.map((i, index) => {
					i.checkList = [];
					i.isCheck = [];
					i.index = index;
					return i;
				});

				pageData.invalidDataList = res.data.invalid;

				paging.value.complete(newList);
			} else {
				if (paging.value) paging.value.complete(false);
			}
		})
		.catch((err) => {
			if (paging.value) paging.value.complete(false);
		});
}

function getGoodsCountMax(goods) {
	let num = 0;
	if (!goods.skuStock.teamStock) goods.skuStock.teamStock = 0;
	const { limitType, stockType, stock, limitNum, teamStock } = goods.skuStock;
	if (limitType !== "UNLIMITED" && stockType !== "UNLIMITED") {
		// 限购 +有限库存 取最小值
		return Math.min(Number(limitNum), Number(stock));
	} else if (limitType !== "UNLIMITED") {
		// 限购 取限购的数量
		num = Number(limitNum);
	} else if (stockType !== "UNLIMITED") {
		//  不限购限制库存 取库存的数量
		num = Number(stock);
	} else {
		return Number(teamStock) > 0 ? Number(teamStock) : 100;
	}
	return num;
}

async function changeGoodsNum(shopIndex, goodsIndex, e) {
	const shopItem = pageData.dataList[shopIndex];
	const goodsItem = shopItem.products[goodsIndex];
	const maxNum = getGoodsCountMax(goodsItem);
	const olNum = goodsItem.num;
	const newNum = e.value;

	let num = e.value;

	let verify = true;

	if (goodsItem.skuStock.limitType !== "UNLIMITED" && Number(goodsItem.skuStock.limitNum) < e.value) {
		verify = false;
		num = Number(goodsItem.skuStock.limitNum);
		uni.showToast({
			icon: "none",
			title: `商品限购${num}件`,
		});
	} else if (goodsItem.skuStock.stockType !== "UNLIMITED" && Number(goodsItem.skuStock.stock) < e.value) {
		verify = false;
		num = Number(goodsItem.skuStock.stock);
		uni.showToast({
			icon: "none",
			title: `商品仅剩${num}件`,
		});
	}

	if (verify) {
		try {
			const res = await editShopCarGood({
				id: goodsItem.id,
				skuId: goodsItem.id,
				productId: goodsItem.productId,
				shopId: shopItem.shopId,
				num: newNum,
			});
			if (res.apiStatus) {
				num = newNum;
			} else {
				num = olNum;
			}
		} catch (error) {
			//TODO handle the exception
			num = olNum;
		}
	}

	goodsItem.num = newNum;

	nextTick(() => {
		pageData.dataList[shopIndex].products[goodsIndex].num = num;
		getSelectGoods();
	});
}

function selectShop(index, e) {
	const shopItem = pageData.dataList[index];
	if (e) {
		for (let j = 0; j < shopItem.products.length; j++) {
			if (!shopItem.checkList.includes(j)) {
				shopItem.checkList.push(j);
			}
		}
	} else {
		shopItem.checkList = [];
	}
	checkSelectAll();
}

function selectGoods(index, e) {
	const shopItem = pageData.dataList[index];
	if (shopItem.checkList.length === shopItem.products.length) {
		if (!checkProductValue.value.includes(index)) {
			checkProductValue.value.push(index);
		}
	} else {
		const findIndex = checkProductValue.value.findIndex((i) => i === index);
		if (findIndex >= 0) {
			checkProductValue.value.splice(findIndex, 1);
		}
	}
	checkSelectAll();
}

function selectAll(e) {
	if (!e) {
		checkProductValue.value = [];
	}

	for (let i = 0; i < pageData.dataList.length; i++) {
		const shopItem = pageData.dataList[i];
		if (!e) {
			shopItem.checkList = [];
		} else {
			if (!checkProductValue.value.includes(i)) {
				checkProductValue.value.push(i);
			}

			for (let j = 0; j < shopItem.products.length; j++) {
				if (!shopItem.checkList.includes(j)) {
					shopItem.checkList.push(j);
				}
			}
		}
	}

	getSelectGoods();
}

function checkSelectAll() {
	const allSelectArr = [];

	for (let i = 0; i < pageData.dataList.length; i++) {
		const shopItem = pageData.dataList[i];
		if (shopItem.checkList.length === shopItem.products.length) {
			allSelectArr.push(i);
		}
	}

	if (allSelectArr.length === pageData.dataList.length) {
		allSelect.value = [true];
	} else {
		allSelect.value = [];
	}

	getSelectGoods();
}

function getSelectGoods() {
	const dataList = deepClone(pageData.dataList);
	const selectList = [];
	for (let i = 0; i < dataList.length; i++) {
		const shopItem = dataList[i];

		const selectShop = {
			...shopItem,
			selectGoods: [],
		};

		if (typeof selectShop.agencyId === "undefined") {
			selectShop.agencyId = ``;
		} else {
			selectShop.agencyId = String(shopItem.agencyId);
		}
		if (typeof selectShop.agencyInfo === "undefined") selectShop.agencyInfo = {};

		for (let j = 0; j < shopItem.products.length; j++) {
			if (shopItem.checkList.includes(j)) {
				selectShop.selectGoods.push({
					shopId: selectShop.shopId,
					shopName: selectShop.shopName,
					shopLogo: selectShop.shopLogo,
					agencyId: selectShop.agencyId || "",
					agencyInfo: selectShop.agencyInfo || {},
					...shopItem.products[j],
				});
			}
		}

		if (selectShop.selectGoods.length > 0) {
			selectList.push(selectShop);
		}
	}
	selectGoodsList.value = selectList;
	return selectList;
}

onShow(() => {
	if (paging.value) paging.value.reload();
});
</script>
<template>
	<app-layout>
		<z-paging ref="paging" bg-color="#F4F5F8" v-model="pageData.dataList" @query="queryList" :refresher-enabled="false" :loading-more-enabled="false">
			<template #top>
				<app-navBar bgColor="linear-gradient(135deg, #01C8A7 33%, #14D1D5 100%)" back :backIcon="common.iconLeftBai" rightPlaceholderSize="140rpx" leftPlaceholderSize="140rpx">
					<template #content>
						<view class="w-full h-full flex justify-between items-center px-0">
							<view class="flex-1 nav-center">
								<text class="text-32 text-#fff font-bold">购物车</text>
							</view>
						</view>
					</template>
					<template #right>
						<template v-if="pageData.isEditor">
							<view class="flex items-center justify-end">
								<text class="text-30 font-500 flex-shrink-0 text-#fff" @click="openEditor(false)">完成</text>
								<!-- <text class="text-30 font-500 flex-shrink-0 ml-20 text-#FC3F33" @click="delCollection">删除</text> -->
							</view>
						</template>
						<template v-if="!pageData.isEditor">
							<view class="flex items-center justify-end">
								<text class="text-30 font-500 text-#fff" @click="openEditor(true)">管理</text>
							</view>
						</template>
					</template>
					<template #footer>
						<view class="w-full h-31 bg-#fff" style="border-radius: 30rpx 30rpx 0rpx 0rpx; margin-bottom: -1rpx"></view>
					</template>
				</app-navBar>
				<view class="w-full pb-20 bg-#fff" v-if="false">
					<view class="flex px-10 pt-20 justify-between">
						<template v-for="(type, index) in pageData.typeList" :key="index">
							<view @click="changeType(index)" class="bg-#F2F4F8 border-rd-8 flex-1 h-60 text-center flex flex-center mx-10 border-solid border-1 border-#F2F4F8" :style="[pageData.typeCurrent === index && 'background-color: #EFFAF7; border-color: #09C1B1;']">
								<text class="text-26 text-#323232 font-500" :style="[pageData.typeCurrent === index && 'color: #09C1B1; font-weight: bold']">{{ type.name }}</text>
							</view>
						</template>
					</view>
				</view>
			</template>

			<view class="absolute w-full h-354" style="background: linear-gradient(0deg, #f4f5f8 0%, #ffffff 64%)"></view>

			<view class="evaluation py-20 relative">
				<uv-checkbox-group v-model="checkProductValue" placement="column" shape="circle" size="36rpx" activeColor="#09C1B1">
					<template v-for="(shop, index) in pageData.dataList" :key="shop.shopId">
						<view class="px-20">
							<view class="w-full bg-#fff border-rd-20 py-35-n py-26 px-26 mb-20">
								<view class="w-full flex items-center" @click="goShopHome(shop.shopId)">
									<view class="mr-12">
										<!-- <uv-checkbox-group v-model="shop.isCheck"> -->
										<uv-checkbox :name="index" @change="selectShop(index, $event)"></uv-checkbox>
										<!-- </uv-checkbox-group> -->
									</view>
									<view class="text-32 font-bold">{{ shop.shopName }}</view>

									<template v-if="!!Number(shop.agencyId) && shop.agencyInfo && shop.agencyInfo.name">
										<view class="mx-10 px-10 h-30 border-0 border-solid border-#FF717B border-rd-6 flex flex-center flex-shrink-0 bg-#9b9b9b">
											<view class="text-18 text-#fff min-w-max">{{ shop.agencyInfo.name.includes('机构') ? `${shop.agencyInfo.name}` : `${shop.agencyInfo.name}机构` }}</view>
										</view>
									</template>
									<template v-else>
										<view class="mx-10 px-10 h-30 border-0 border-solid border-#FF717B border-rd-6 flex flex-center flex-shrink-0 bg-#9b9b9b">
											<view class="text-18 text-#fff min-w-max">平台机构</view>
										</view>
									</template>

									<view class="">
										<app-image :src="common.iconRightHui" size="20" ml="5" mode=""></app-image>
									</view>
								</view>

								<view class="w-full">
									<uv-checkbox-group v-model="shop.checkList" @change="selectGoods(index, $event)" placement="column" shape="circle" size="36rpx" activeColor="#09C1B1">
										<template v-for="(goods, goodsIndex) in shop.products">
											<view class="w-full flex items-center mt-30">
												<view class="mr-20">
													<!-- <template v-if="pageData.isEditor"> -->

													<view class="mr-0">
														<uv-checkbox :name="goodsIndex"></uv-checkbox>
													</view>

													<!-- </template> -->
												</view>
												<view class="">
													<view class="w-180 h-180 border-1 border-solid border-#F5F5F5 border-rd-20 relative" @click="goGoodsDetails({ ...shop, ...goods })">
														<view class="w-180 h-180 absolute z-1">
															<view class="">
																<template v-if="Array.isArray(goods.distributionMode) && goods.distributionMode.includes('VIRTUAL')">
																	<view class="text-22 h-38 bg-#FF9914 px-15 text-#fff max-w-fit" style="border-radius: 20rpx 0rpx 20rpx 0rpx">虚拟商品</view>
																</template>
															</view>
														</view>
														<view class="w-180 h-180 relative">
															<app-image :src="goods.image" size="178" rd="20" mode=""></app-image>
														</view>
													</view>
												</view>

												<view class="flex-1 ml-20 min-h-180">
													<view class="w-full flex flex-col">
														<view class="text-26 font-bold uv-line-3" @click="goGoodsDetails({ ...shop, ...goods })">{{ goods.productName }}</view>
														<view class="w-full mt-18">
															<template v-if="goods.specs?.length && shop.enable">
																<view class="w-full bg-#F8F8F8 border-rd-6 h-46 flex items-center px-10" @click="openSkuProp({ shop, goods })">
																	<view class="flex-1 text-#555555 text-24 font-500 uv-line-1">{{ goods.specs.join("/") || "默认" }}</view>

																	<view class="pl-20">
																		<app-image :src="common.iconJiantouBottomHui" size="20" mode=""></app-image>
																	</view>
																</view>
															</template>
															<template v-else>
																<view class="h-46"></view>
															</template>
														</view>
														<view class="w-full flex items-center justify-between mt-30">
															<view class="flex items-end text-#FC3F33 font-bold">
																<view class="text-30">￥</view>
																<view class="text-34">{{ getPriceInfo(goods.finalPrice).integer }}.{{ getPriceInfo(goods.finalPrice).decimalText }}</view>
															</view>

															<view class="">
																<my-uv-number-box v-model="goods.num" min="1" :max="getGoodsCountMax(goods)" :asyncChange="false" @change="changeGoodsNum(index, goodsIndex, $event)" :integer="true" inputWidth="60rpx" buttonSize="50rpx" bgColor="#F3F4F6"></my-uv-number-box>
															</view>
														</view>
													</view>
												</view>
											</view>
										</template>
									</uv-checkbox-group>
								</view>
							</view>
						</view>
					</template>
				</uv-checkbox-group>
			</view>

			<view class="px-20 relative" v-if="pageData.invalidDataList.length > 0">
				<view class="w-full bg-#fff border-rd-20 py-35-n py-26 px-26 mb-20">
					<view class="w-full flex items-center justify-between">
						<view class="text-32 font-bold">失效商品</view>

						<view class="">
							<app-image :src="common.iconDelHui" size="24" mode="" @click="clearInvalidGoods"></app-image>
						</view>
					</view>
					<view class="w-full">
						<template v-for="(shop, index) in pageData.invalidDataList" :key="shop.shopId">
							<template v-for="(goods, goodsIndex) in shop.products">
								<view class="w-full flex items-center mt-30">
									<view class="">
										<view class="w-180 h-180 border-1 border-solid border-#F5F5F5 border-rd-20 relative">
											<app-image :src="goods.image" size="178" rd="20" mode=""></app-image>
											<view class="absolute w-180 h-180 top-0 left-0 border-rd-20 bg-[rgba(0,0,0,0.4)] flex flex-center p-10">
												<view class="w-full h-full border-2 border-#fff border-solid flex flex-center text-#fff text-24 font-bold border-rd-90">商品不可用</view>
											</view>
										</view>
									</view>

									<view class="flex-1 ml-20 min-h-180">
										<view class="w-full flex flex-col">
											<view class="text-26 font-bold uv-line-3 text-#999">{{ goods.productName }}</view>
											<view class="w-full mt-18">
												<template v-if="goods.specs?.length && shop.enable">
													<view class="w-full bg-#F8F8F8 border-rd-6 h-46 flex items-center px-10">
														<view class="flex-1 text-#555555 text-24 font-500 uv-line-1">{{ goods.specs.join("/") || "默认" }}</view>

														<view class="pl-20">
															<app-image :src="common.iconJiantouBottomHui" size="20" mode=""></app-image>
														</view>
													</view>
												</template>
												<template v-else>
													<view class="h-46"></view>
												</template>
											</view>
											<view class="w-full flex items-center justify-between mt-30" v-if="false">
												<view class="flex items-end text-#FC3F33 font-bold">
													<view class="text-30">￥</view>
													<view class="text-34">{{ getPriceInfo(goods.finalPrice).integer }}.{{ getPriceInfo(goods.finalPrice).decimalText }}</view>
												</view>

												<view class="">
													<my-uv-number-box v-model="goods.num" min="1" :max="getGoodsCountMax(goods)" :asyncChange="false" @change="changeGoodsNum(index, goodsIndex, $event)" :integer="true" inputWidth="60rpx" buttonSize="50rpx" bgColor="#F3F4F6"></my-uv-number-box>
												</view>
											</view>
										</view>
									</view>
								</view>
							</template>
						</template>
					</view>
				</view>
			</view>

			<template #empty>
				<view class="w-full h-full flex flex-center flex-col bg-#fff relative">
					<app-image src="@/pages/goods/static/icon_gwc_null.png" size="340" mode=""></app-image>
					<view class="w-full text-center text-#888888 text-28 font-500">购物暂无商品</view>
				</view>
			</template>

			<template #bottom>
				<view class="w-full bg-#fff">
					<view class="w-full h-1 bg-#EFF2F9"></view>
					<view class="w-full flex items-center justify-between min-h-85 py-15">
						<view class="pl-30 flex items-center">
							<uv-checkbox-group v-model="allSelect">
								<uv-checkbox @change="selectAll" shape="circle" :name="true" activeColor="#09C1B1" label="全选" labelSize="30rpx" labelColor="#0F1B34" size="36rpx"></uv-checkbox>
							</uv-checkbox-group>
						</view>

						<view class="pr-40 h-84">
							<template v-if="!pageData.isEditor">
								<view class="flex items-center">
									<view class="">
										<view class="flex items-end">
											<view class="text-30 mr-10">合计</view>
											<view class="flex items-end">
												<view class="text-30 font-500 text-#FC3F33 pb-2">¥</view>
												<view class="text-36 font-500 text-#FC3F33">{{ getPriceInfo(result.price).integer }}</view>
												<view class="text-30 font-500 text-#FC3F33 pb-2">.{{ getPriceInfo(result.price).decimalText }}</view>
											</view>
										</view>
									</view>
									<view class="w-220 ml-24">
										<uv-button color="#00B496" :text="`结算(${result.products.length})`" loadingText="加载中..." class="w-full mt-0 flex-center" custom-style="height: 84rpx;" customTextStyle="font-size: 30rpx; font-weight: bold;" shape="circle" loadingMode="circle" :disabled="result.products.length === 0" :loading="pageData.btnLoading" @click="submit"></uv-button>
									</view>
								</view>
							</template>
							<template v-else>
								<view class="flex items-center">
									<view class="w-220 ml-10">
										<uv-button color="#FFF5E8" :text="`移入收藏夹`" loadingText="加载中..." class="w-full mt-0 flex-center" custom-style="height: 84rpx;" customTextStyle="font-size: 30rpx; font-weight: bold; color: #FF9914;" shape="circle" loadingMode="circle" :disabled="result.products.length === 0" :loading="pageData.btnLoading" @click="addCollection"></uv-button>
									</view>
									<view class="w-220 ml-10">
										<uv-button color="#FC3F33" :text="`删除`" loadingText="加载中..." class="w-full mt-0 flex-center" custom-style="height: 84rpx;" customTextStyle="font-size: 30rpx; font-weight: bold;" shape="circle" loadingMode="circle" :disabled="result.products.length === 0" :loading="pageData.btnLoading" @click="delCart"></uv-button>
									</view>
								</view>
							</template>
						</view>
					</view>
					<app-safeAreaBottom></app-safeAreaBottom>
				</view>
			</template>
		</z-paging>

		<page-sku-popup ref="skuPopup" listStyle="card"></page-sku-popup>
	</app-layout>
</template>
<style lang="scss" scoped>
.evaluation {
}
</style>
