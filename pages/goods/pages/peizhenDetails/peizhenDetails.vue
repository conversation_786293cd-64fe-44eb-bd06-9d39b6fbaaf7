/** * 约会详情 */
<script setup>
import { reactive, ref, computed, nextTick } from "vue";

import { onLoad } from "@dcloudio/uni-app";

import { useUserStore } from "@/store";
import { common, logo } from "@/common/images";

const userStore = useUserStore();

const pageData = reactive({
	swiperList: ["https://img1.baidu.com/it/u=2840899525,3798641684&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=281", "https://img2.baidu.com/it/u=3586956883,1714965503&fm=253&fmt=auto&app=138&f=JPEG?w=787&h=500", "https://img2.baidu.com/it/u=44170729,3837562321&fm=253&fmt=auto&app=138&f=JPEG?w=787&h=500"],

	mark: 4.8,
});
</script>
<template>
	<app-layout>
		<view class="rendezvous page bg-#F1F3F7 pb-0">
			<app-navBar :back="true" :fixed="true" :backIcon="common.iconLeftHeidi" backIconSize="56rpx">
				<template v-slot:content>
					<view class="w-full flex justify-between items-center pr-30">
						<view class=""></view>
						<view class="flex items-center">
							<app-image src="@/pages/goods/static/icon_shoucang_heidi.png" width="56rpx" mode="widthFix"></app-image>
							<app-image src="@/pages/goods/static/icon_gengduo_heidi.png" width="56rpx" ml="22rpx" mode="widthFix"></app-image>
						</view>
					</view>
				</template>
			</app-navBar>
			<view class="w-full">
				<page-swiper height="560rpx" :videos="''" :images="pageData.swiperList.join(',')"></page-swiper>
			</view>

			<!-- <view class="w-full pt-472"></view> -->

			<view class="px-20 mt-24">
				<view class="w-full px-32 py-36 bg-#fff border-rd-20">
					<view class="w-full flex items-center justify-between">
						<view class="flex items-end">
							<view class="text-30 font-500 text-#FC3F33 pb-10"> ¥ </view>
							<view class="text-50 font-500 text-#FC3F33"> 1188 </view>
							<view class="text-24 font-500 text-#FC3F33 pb-12"> 起 </view>
						</view>

						<view class="text-#616161 text-26"> 已售出900+ </view>
					</view>

					<view class="flex items-center justify-between mt-20">
						<view class="flex items-center">
							<view class="px-10 h-40 border-1 border-solid border-#FF717B border-rd-6 flex flex-center mr-10">
								<view class="text-24 text-#F93946"> 新人劵无门槛 </view>
							</view>
							<view class="px-10 h-40 border-1 border-solid border-#FF717B border-rd-6 flex flex-center mr-10">
								<view class="text-24 text-#F93946"> 满5000减100 </view>
							</view>
						</view>

						<view class="">
							<app-image :src="common.iconRightHui" size="20rpx" mode=""></app-image>
						</view>
					</view>

					<view class="text-#222222 text-32 font-bold mt-29"> 睡眠呼吸机 </view>
				</view>
			</view>

			<view class="px-20 mt-24">
				<view class="w-full px-32 py-36 bg-#fff border-rd-20">
					<view class="flex items-center">
						<view class="w-100 text-26rpx font-bold"> 规格 </view>
						<view class="flex-1 flex items-center justify-between">
							<view class="text-26 text-#323232"> 选择分类 </view>

							<view class="">
								<app-image :src="common.iconRightHui" size="20rpx" mode=""></app-image>
							</view>
						</view>
					</view>

					<view class="flex items-center mt-40">
						<view class="w-100 text-26rpx font-bold"> 发货 </view>
						<view class="flex-1 flex items-center justify-between">
							<view class="flex items-center">
								<view class="text-26 text-#323232"> 江苏苏州 </view>

								<view class="w-1 h-23 bg-#DFE4EA mx-20"></view>

								<view class="text-26 text-#323232"> 快递：免运费 </view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<view class="px-20 mt-24">
				<view class="w-full bg-#fff border-rd-20 overflow-hidden">
					<view class="w-full h-85 flex items-center justify-between px-30 border-b-1 border-b-solid border-b-#EAEDF3">
						<view class="text-30 font-bold"> 用户点评(100+) </view>

						<view class="flex items-center">
							<view class="text-#999999 font-500 text-26"> 全部 </view>
							<app-image :src="common.iconRightHui" size="20rpx" ml="4" mode=""></app-image>
						</view>
					</view>

					<view class="w-full p-30">
						<view class="w-full flex items-center justify-between">
							<view class="flex items-center">
								<app-image :src="logo" size="36rpx" rd="50%" mr="14" mode=""></app-image>

								<view class="text-26 font-bold"> 豆腐 </view>
							</view>

							<view class="">
								<view class="text-26 text-#888888 font-500"> 服务：加时(每1小时) </view>
							</view>
						</view>

						<view class="w-full flex mt-20">
							<view class="flex-1 text-26 text-#323232 font-500"> 很专业的陪诊服务熟知流程效率很快，平常不熟悉医院爬上爬下取药找医生要很久，现在一两个小时就看完医生了。 </view>

							<view class="w-100 flex-shrink-0 ml-24">
								<app-image :src="logo" size="100" rd="10" mode=""></app-image>
							</view>
						</view>
					</view>
				</view>
			</view>

			<view class="px-20 mt-24">
				<view class="w-full px-32 py-36 bg-#fff border-rd-20">
					<view class="flex items-center">
						<view class="flex items-center flex-1">
							<view class="w-110 mr-24 flex-shrink-0">
								<app-image :src="logo" size="110" rd="10" mode=""></app-image>
							</view>

							<view class="flex-1 flex flex-col">
								<view class="w-full uv-line-1 text-32 font-bold"> 和家无忧 </view>
								<view class="flex items-center mt-20">
									<view class="">
										<uv-rate :count="5" v-model="pageData.mark" active-color="#F8BC35" inactive-color="#E2E2E2" inactiveIcon="star-fill" size="14" gutter="0" readonly></uv-rate>
									</view>
									<view class="ml-8 text-24"> 4.8 </view>
									<view class="w-1 h-23 bg-#DFE4EA mx-12"></view>
									<view class="flex items-center text-24 font-500">
										<view class=""> 累计服务 </view>
										<view class="text-#FF8726"> 2w+ </view>
									</view>
								</view>
							</view>
						</view>

						<view class="flex-shrink-0 ml-20">
							<template v-if="true">
								<view class="px-16 border-rd-8 bg-#03BAA7 flex flex-center text-24 font-500 text-#fff h-48 ac-op">
									<app-image :src="common.iconJiaBai" size="20" mr="9" mode=""></app-image>

									<view class=""> 关注 </view>
								</view>
							</template>

							<template v-else>
								<view class="px-16 border-rd-8 bg-#BCBCC0 flex flex-center text-24 font-500 text-#fff h-48 ac-op">
									<view class=""> 已关注 </view>
								</view>
							</template>
						</view>
					</view>

					<view class="mt-50 w-full h-70 border-rd-70 border-2 border-solid border-#00B496 flex flex-center ac-op">
						<view class="text-#00B496 text-26 font-500"> 进店逛逛 </view>
					</view>
				</view>
			</view>

			<view class="px-20 mt-24 pb-50">
				<uv-parse class="mt-10" :content="'商品详情'"></uv-parse>
			</view>

			<view class="w-full sticky bottom-0 h-150 px-20 pt-14 flex bg-#fff">
				<view class="w-full flex h-84">
					<view class="flex flex-1 justify-around">
						<view class="flex flex-col flex-center ac-op">
							<app-image src="@/pages/goods/static/icon_dianpu_hei.png" size="40"></app-image>

							<view class="text-center text-22 mt-14 font-500"> 店铺 </view>
						</view>

						<view class="flex flex-col flex-center ac-op">
							<app-image :src="common.iconKefuHei" size="40"></app-image>

							<view class="text-center text-22 mt-14 font-500"> 客服 </view>
						</view>

						<view class="flex flex-col flex-center ac-op">
							<app-image :src="common.iconGouwucheHei" size="40"></app-image>

							<view class="text-center text-22 mt-14 font-500"> 购物车 </view>
						</view>
					</view>

					<view class="w-440 h-84 flex-shrink-0 ml-24 border-rd-84 overflow-hidden flex flex-center text-30 font-bold text-#fff">
						<view class="flex-1 h-full flex flex-center ac-op" style="background: linear-gradient(-65deg, #ffaf31 0%, #f79b2c 100%)"> 加入购物车 </view>

						<view class="flex-1 h-full flex flex-center ac-op" style="background: linear-gradient(-65deg, #00b496 0%, #02c9a8 100%)"> 立即下单 </view>
					</view>
				</view>
			</view>
		</view>
	</app-layout>
</template>

<style lang="scss" scoped>
@mixin flex($direction: row) {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: $direction;
}

.page {
	min-height: 100vh;
}

.indicator {
	@include flex(row);
	justify-content: center;

	&__dot {
		height: 10rpx;
		width: 10rpx;
		border-radius: 50%;
		background-color: #cbcbcb;
		margin: 0 4rpx;
		transition: background-color 0.3s;

		&--active {
			background-color: #6429e3;
		}
	}
}
</style>
