<script setup>
import { reactive, ref, computed, nextTick } from 'vue';

import { EXPRESS_CODE, INTRA_CITY_DISTRIBUTION_CODE_1, INTRA_CITY_DISTRIBUTION_CODE_2, serviceHandler } from '@/common/types/goods';

import { goodsDetailsContent, goodsDetailsFooter } from '@/pages/goods/common/skeleton.data';

import { onLoad, onReady, onHide } from '@dcloudio/uni-app';

import useConvert from '@/common/useConvert';

import { useUserStore, useGoodsStore } from "@/store";
import { canENV, route } from "@/common/utils";
import { common } from "@/common/images";

const { divTenThousand } = useConvert();

const userStore = useUserStore();
const goodsStore = useGoodsStore();

const props = defineProps({
	productId: {
		type: String,
		required: true
	},
	shopId: {
		type: String,
		required: true
	}
});

const pageData = reactive({
	pageLoading: true,

	swiperList: [],

	mark: 4.8,

	shopData: null,

	productData: null
});

// 商品信息
const info = computed(() => goodsStore.goodInfo);

// 店铺信息
const shopData = computed(() => goodsStore.shopInfo);

// 付费会员折扣信息
const memberDiscount = computed(() => goodsStore.discountMap.value?.VIP);

// 商品参数弹窗
const featureNamePopup = ref();

// SKU 弹窗
const skuPopup = ref();

/**
 * 会员优惠
 */
function jumpToMember() {
	const curmemberDiscount = memberDiscount.value;
	//如果已经是会员 则不需要跳转开通付费会员
	if (curmemberDiscount?.data?.isVip) {
		//如果有折扣 则查看优惠
		if (curmemberDiscount.discount || discountList.value.length) {
			return handleReceiveCoupon();
		}
		return;
	}
	if (!userStore.checkLogin) {
		// $useUserStore.PUT_AUTH_TYPE(true);
		return;
	}

	// handleReceiveCoupon();
	route('/pages/my/pages/memberCenter/memberCenter');
}

const isPreferentialPopup = ref();
/**
 * 点击查看优惠
 */
function handleReceiveCoupon() {
	if (isPreferentialPopup.value) isPreferentialPopup.value.openPopup();
}

/**
 * 打开参数
 */
function openGoodsParames() {
	if (featureNamePopup.value) featureNamePopup.value.open();
}

/**
 * 收藏\取消收藏商品
 */
function changeGoodsCollect() {
	goodsStore.changeGoodsCollect();
}

/**
 *  优惠券规则格式化
 */
const formattingCouponRules = (cou, autocomplete = true) => {
	let text;
	if (!cou || !Object.values(cou).length) {
		text = '';
		return text;
	}
	switch (cou.type) {
		case 'PRICE_REDUCE':
			text = autocomplete ? `现金券减${cou.amount && divTenThousand(cou.amount)}元` : `无门槛现金券`;
			break;
		case 'PRICE_DISCOUNT':
			text = autocomplete ? `无门槛折扣券${cou.discount}折` : `无门槛折扣券`;
			break;
		case 'REQUIRED_PRICE_REDUCE':
			text = autocomplete
				? `满${divTenThousand(cou.requiredAmount || '0')}元减${cou.amount && divTenThousand(cou.amount)}元`
				: `满${divTenThousand(cou.requiredAmount || '0')}元可用`;
			break;
		case 'REQUIRED_PRICE_DISCOUNT':
			text = autocomplete ? `满${divTenThousand(cou.requiredAmount || '0')}元${cou.discount}折` : `满${divTenThousand(cou.requiredAmount || '0')}元可用`;
			break;
		default:
			break;
	}
	return text;
};

/**
 * 获取评价数量 num
 */
function evaluateTotalNumToText(num = 0) {
	let numText = ``;
	let strNum = `${num}`;

	if (num >= 0 && num <= 100) {
		numText = `${num}`;
	}

	if (num > 100 && num < 1000) {
		numText = strNum[0];
		for (let i = 0; i < strNum.length - 1; i += 1) {
			numText += '0';
		}
	}

	if (num >= 1000 && num < 10000) {
		numText = strNum[0];
		numText += strNum[1];
		for (let i = 0; i < strNum.length - 2; i += 1) {
			numText += '0';
		}
	}

	if (num >= 10000) {
		numText = strNum[0];
		if (Number(strNum[1]) > 0) {
			numText += `.${strNum[1]}`;
		}
		numText += `万`;
	}

	return numText;
}
// 获取评价数量
const evaluateTotalText = computed(() => {
	const totalCount = goodsStore.evaluateInfo?.totalCount;

	const numText = evaluateTotalNumToText(totalCount);

	if (numText) {
		if (totalCount >= 100) {
			return `(${numText}+)`;
		} else {
			return `(${numText})`;
		}
	} else {
		return '';
	}
});

/**
 * 前往商品评价
 */
function clickToEvaluation() {
	route('/pages/goods/pages/evaluation/evaluation', {
		productId: props.productId,
		shopId: props.shopId,
		serveGoods: info.value.serveGoods
	});
}

/**
 * 加入购物车、立即下单
 * @param {Object} item
 */
function clickBottomBtn(item) {
	goodsStore.clickBottomBtn(item);
}

function openSkuPopup() {
	if (goodsStore.currentChoosedSku.specs?.length) {
		goodsStore.clickBottomBtn({
			type: 'CHANGE_SKU'
		});
	}
}

/**
 * 加载数据
 */
async function loadData() {
	if (props.productId && props.shopId) {
		goodsStore
			.init({ ...props, type: 'goodsDetails' })
			.then((res) => {
				canENV(() => {
					console.log('goodsStore =>', goodsStore);
				});
				pageData.pageLoading = false;
			})
			.catch((err) => {
				canENV(() => {
					console.log(err);
				});
				uni.showToast({
					title: `商品不可用`,
					icon: 'none',
					success: () => {
						const time = setTimeout(() => {
							// uni.redirectTo({ url: '/basePackage/pages/abnormalGoods/AbnormalGoods' })
							// uni.navigateBack();
							clearTimeout(time);
						}, 1500);
					}
				});
			});
	}
}

onLoad(() => {
	loadData();
});

onReady(() => {});

onHide(() => {
	goodsStore.changeSetOperation({
		control: false
	});

	isPreferentialPopup.value?.closePopup();
	featureNamePopup.value?.close();
});
</script>
<template>
	<app-layout>
		<view class="rendezvous page bg-#F1F3F7 pb-180">
			<app-navBar :back="true" :fixed="true" :backIcon="common.iconLeftHeidi" backIconSize="56rpx" :showRight="false">
				<template v-slot:content>
					<view class="w-full flex justify-between items-center pr-30">
						<view class=""></view>
						<view class="flex items-center">
							<template v-if="info.whetherCollect">
								<app-image src="@/pages/goods/static/icon_shoucang_heidi_active.png" width="56rpx" mode="widthFix" @click="changeGoodsCollect"></app-image>
							</template>
							<template v-else>
								<app-image src="@/pages/goods/static/icon_shoucang_heidi.png" width="56rpx" mode="widthFix" @click="changeGoodsCollect"></app-image>
							</template>

							<app-image src="@/pages/goods/static/icon_gengduo_heidi.png" width="56rpx" ml="22rpx" mode="widthFix"></app-image>
						</view>
					</view>
				</template>
			</app-navBar>

			<view class="w-full" v-if="!pageData.pageLoading">
				<view class="w-full relative">
					<page-swiper height="560rpx" :videos="info.videoUrl" :images="`${goodsStore.swiperList.mainList.join(',')}`"></page-swiper>
					<template v-if="goodsStore.isSellOut">
						<view class="w-full h-560 absolute top-0 left-0 flex flex-center bg-[bg-[rgba(0,0,0,0.4)]]">
							<app-image src="@/pages/goods/static/icon_yishouqin.png" width="200" mode="widthFix"></app-image>
						</view>
					</template>
				</view>

				<!-- <view class="w-full pt-472"></view> -->

				<view class="px-20 mt-24">
					<view class="w-full px-32 py-36 bg-#fff border-rd-20">
						<view class="w-full flex items-center justify-between">
							<view class="flex items-end">
								<view class="flex items-end">
									<view class="text-24 font-500 text-#FC3F33 pb-12">{{ info.serveGoods === 0 ? "预估到手" : "预估价" }}</view>
									<view class="text-30 font-500 text-#FC3F33 pb-8">¥</view>
									<view class="text-50 font-500 text-#FC3F33">
										{{ goodsStore.computedPrice.integer }}
									</view>
									<view class="text-30 font-500 text-#FC3F33 pb-8">.{{ goodsStore.computedPrice.decimalText }}</view>
									<!-- <view class="text-24 font-500 text-#FC3F33 pb-12">起</view> -->
								</view>
								<view class="ml-20">
									<view class="text-24 font-500 text-#888 pb-8" style="text-decoration: line-through">￥{{ goodsStore.computedOriginalPrice }}</view>
								</view>
							</view>

							<template v-if="info.serveGoods === 0">
								<view class="flex items-center">
									<view class="text-#616161 text-26">已售{{ goodsStore.skuSale }}件</view>

									<template v-if="goodsStore.currentChoosedSku.limitType !== 'UNLIMITED'">
										<view class="text-#616161 text-26 ml-20">限购{{ goodsStore.currentChoosedSku.limitNum }}件</view>
									</template>
								</view>
							</template>
							<template v-if="info.serveGoods === 1">
								<view class="flex items-center">
									<view class="text-#616161 text-26">已服务{{ goodsStore.skuSale }}客户</view>

									<!-- <template v-if="goodsStore.currentChoosedSku.limitType !== 'UNLIMITED'">
										<view class="text-#616161 text-26 ml-20">限购{{ goodsStore.currentChoosedSku.limitNum }}件</view>
									</template> -->
								</view>
							</template>
						</view>

						<!-- 优惠券 -->
						<template v-if="goodsStore.discountMap.COUPON?.data.filter((i) => i.couponUserId).length > 0">
							<view class="flex items-center justify-between mt-20" @click="handleReceiveCoupon">
								<view class="flex-1 w-0">
									<scroll-view scroll-x="true">
										<view class="flex items-center">
											<template v-for="(item, index) in goodsStore.discountMap.COUPON?.data" :key="index">
												<template v-if="item.couponUserId">
													<view class="px-10 h-40 border-1 border-solid border-#FF717B border-rd-6 flex flex-center mr-10 flex-1 max-w-fit">
														<view class="text-24 text-#F93946 min-w-max">{{ formattingCouponRules(item, true) }}</view>
													</view>
												</template>
											</template>
										</view>
									</scroll-view>
								</view>

								<view class="ml-15">
									<app-image :src="common.iconRightHui" size="20rpx" mode=""></app-image>
								</view>
							</view>
						</template>

						<view class="text-#222222 text-32 font-bold mt-20">
							{{ info.name }}
						</view>

						<view v-if="info.saleDescribe" class="w-full mt-20 px-10 py-5 text-24 text-#323232 bg-#F1F3F7 border-rd-6">
							{{ info.saleDescribe }}
						</view>
					</view>
				</view>

				<view class="px-20 mt-24">
					<view class="w-full px-30 py-25 bg-#fff border-rd-20">
						<view class="flex items-center mt-0">
							<view class="w-100 text-26rpx font-bold">会员</view>
							<view class="flex-1 flex items-center justify-between" @click="jumpToMember">
								<view class="flex items-center">
									<template v-if="memberDiscount?.data?.discountDesc">
										<view class="text-26 text-#323232">{{ memberDiscount?.data?.discountDesc + " 优惠" + divTenThousand(memberDiscount?.discount).toFixed(2) }}</view>
									</template>
									<template v-else-if="memberDiscount?.data?.isVip">
										<view class="text-26 text-#323232">暂无优惠</view>
									</template>
									<template v-else>
										<view class="text-26 text-#323232">开通会员获得更多优惠</view>
									</template>
								</view>

								<view class="flex flex-center">
									<view class="text-#FC3F33 mr-10 text-26" v-if="!memberDiscount?.data?.isVip">去开通</view>
									<app-image :src="common.iconRightHui" size="20rpx" mode=""></app-image>
								</view>
							</view>
						</view>

						<template v-if="goodsStore.discountList.length > 0">
							<view class="flex items-center mt-40">
								<view class="w-100 text-26rpx font-bold">优惠</view>
								<view class="flex-1 flex items-center justify-between" @click="handleReceiveCoupon">
									<view class="flex items-center">
										<view class="text-26 text-#FC3F33">{{ goodsStore.discountList.join(",") }}</view>
									</view>

									<view class="flex flex-center">
										<app-image :src="common.iconRightHui" size="20rpx" mode=""></app-image>
									</view>
								</view>
							</view>
						</template>

						<template v-if="goodsStore.bonusPrice !== '0.00' || goodsStore.rebateAmount !== '0.00'">
							<view class="flex items-center mt-40">
								<view class="w-100 text-26rpx font-bold">赚钱</view>
								<view class="flex-1 flex items-center justify-between" @click="handleReceiveCoupon">
									<view class="flex items-center">
										<template v-if="goodsStore.bonusPrice !== '0.00'">
											<view class="text-26 text-#FC3F33">预计赚：{{ goodsStore.bonusPrice }}</view>
										</template>
										<template v-if="goodsStore.bonusPrice !== '0.00' && goodsStore.rebateAmount !== '0.00'">
											<view class="w-1 h-23 bg-#DFE4EA mx-20"></view>
										</template>
										<template v-if="goodsStore.rebateAmount !== '0.00'">
											<view class="text-26 text-#FC3F33">预计反：{{ goodsStore.rebateAmount }}</view>
										</template>
									</view>

									<view class="flex flex-center">
										<!-- <app-image :src="common.iconRightHui" size="20rpx" mode=""></app-image> -->
									</view>
								</view>
							</view>
						</template>
					</view>
				</view>

				<view class="px-20 mt-24">
					<view class="w-full px-30 py-25 bg-#fff border-rd-20">
						<template>
							<view></view>
						</template>
						<view class="flex items-center">
							<view class="w-100 text-26rpx font-bold">规格</view>
							<view class="flex-1 flex items-center justify-between" @click="openSkuPopup">
								<view class="flex flex-center">
									<template v-if="goodsStore.currentChoosedSku.specs?.length">
										<view class="text-26 text-#323232 mr-10">{{ [...goodsStore.currentChoosedSku.specs, ...goodsStore.currentGoodsExtraData.currentParams].join(" , ") }} ,</view>
									</template>
									<view class="text-26 text-#323232">{{ goodsStore.goodsCount }}件</view>
								</view>
								<template v-if="goodsStore.currentChoosedSku.specs?.length">
									<view class="">
										<app-image :src="common.iconRightHui" size="20rpx" mode=""></app-image>
									</view>
								</template>
							</view>
						</view>

						<template v-if="info.serveGoods === 0">
							<template v-if="goodsStore.defaultAddress?.area">
								<view class="flex items-center mt-40">
									<view class="w-100 text-26rpx font-bold">送至</view>
									<view class="flex-1 flex items-center justify-between">
										<view class="flex items-center">
											<view class="text-26 text-#323232">{{ goodsStore.defaultAddress.area.join(" ") }}</view>
										</view>
									</view>
								</view>
							</template>
						</template>

						<template v-if="!info.distributionMode.includes('VIRTUAL') && goodsStore.serveGoods === 0">
							<view class="flex items-center mt-40">
								<view class="w-100 text-26rpx font-bold">运费</view>
								<view class="flex-1 flex items-center justify-between">
									<view class="flex items-center">
										<template v-for="item in info.distributionMode" :key="item">
											<view class="text-26 text-#323232 flex items-center" v-if="goodsStore.interestsConfig.freight[item].label && goodsStore.interestsConfig.freight[item].code !== INTRA_CITY_DISTRIBUTION_CODE_2">
												<view class="">{{ goodsStore.interestsConfig.freight[item].label }}</view>
												<view
													:class="{
														'text-#323232!': goodsStore.freightPriceFormat(item) === '免费',
														'text-#FC3F33': goodsStore.freightPriceFormat(item) === '超出配送范围',
													}"
													class="text-#FC3F33">
													{{ goodsStore.freightPriceFormat(item) }}
												</view>
											</view>
										</template>
									</view>
								</view>
							</view>
						</template>

						<template v-if="goodsStore.interestsConfigService">
							<view class="flex items-center mt-40">
								<view class="w-100 text-26rpx font-bold">服务</view>
								<view class="flex-1 flex items-center justify-between">
									<view class="flex items-center">
										<view class="text-26 text-#323232">{{ goodsStore.interestsConfigService }}</view>
									</view>
								</view>
							</view>
						</template>

						<template v-if="goodsStore.goodsParames.length > 0">
							<view class="flex items-center mt-40" @click="openGoodsParames">
								<view class="w-100 text-26 font-bold">参数</view>
								<view class="flex-1 flex items-center justify-between">
									<view class="flex items-center">
										<view class="text-26 text-#323232">{{ goodsStore.goodsParames.map((i) => i.featureName).join(" ") }}</view>
									</view>

									<view class="">
										<app-image :src="common.iconRightHui" size="20rpx" mode=""></app-image>
									</view>
								</view>
							</view>
						</template>

						<!-- <view class="flex items-center mt-40">
							<view class="w-100 text-26rpx font-bold">发货</view>
							<view class="flex-1 flex items-center justify-between">
								<view class="flex items-center">
									<view class="text-26 text-#323232">江苏苏州</view>

									<view class="w-1 h-23 bg-#DFE4EA mx-20"></view>

									<view class="text-26 text-#323232">快递：免运费</view>
								</view>
							</view>
						</view> -->
					</view>
				</view>

				<view class="px-20 mt-24">
					<view class="w-full bg-#fff border-rd-20 overflow-hidden">
						<view class="w-full h-85 flex items-center justify-between px-30 border-b-1 border-b-solid border-b-#EAEDF3 ac-op">
							<view class="text-30 font-bold">用户点评{{ evaluateTotalText }}</view>

							<view class="flex items-center" @click="clickToEvaluation">
								<view class="text-#999999 font-500 text-26">全部</view>
								<app-image :src="common.iconRightHui" size="20rpx" ml="4" mode=""></app-image>
							</view>
						</view>

						<template v-if="goodsStore.evaluateInfo && goodsStore.evaluateInfo.evaluate && goodsStore.evaluateInfo.evaluate.id">
							<view class="w-full p-30">
								<view class="w-full flex items-center justify-between">
									<view class="flex items-center">
										<app-image :src="goodsStore.evaluateInfo.evaluate.avatar" size="36rpx" rd="50%" mr="14" mode=""></app-image>

										<view class="text-26 font-bold">{{ goodsStore.evaluateInfo.evaluate.nickname }}</view>
									</view>

									<view class="">
										<!-- <view class="text-26 text-#888888 font-500">服务：加时(每1小时)</view> -->
										<view class="text-26 text-#888888 font-500">{{ goodsStore.evaluateInfo.evaluate.specs.join(",") || "" }}</view>
									</view>
								</view>

								<view class="w-full flex mt-20" @click="clickToEvaluation">
									<view class="flex-1 text-26 text-#323232 font-500">{{ goodsStore.evaluateInfo.evaluate.comment }}</view>

									<template v-if="goodsStore.evaluateInfo.evaluate.medias > 0">
										<view class="w-100 flex-shrink-0 ml-24">
											<app-image :src="goodsStore.evaluateInfo.evaluate.medias[0]" size="100" rd="10" mode=""></app-image>
										</view>
									</template>
								</view>
							</view>
						</template>
					</view>
				</view>

				<view class="px-20 mt-24">
					<view class="w-full px-32 py-36 bg-#fff border-rd-20">
						<view class="flex items-center">
							<view class="flex items-center flex-1">
								<view class="w-110 mr-24 flex-shrink-0">
									<app-image :src="shopData.logo" size="110" rd="10" mode=""></app-image>
								</view>

								<view class="flex-1 flex flex-col">
									<view class="w-full uv-line-1 text-32 font-bold">
										{{ shopData.name }}
									</view>
									<view class="flex items-center mt-20">
										<view class="">
											<uv-rate :count="5" v-model="shopData.score" active-color="#F8BC35" inactive-color="#E2E2E2" inactiveIcon="star-fill" size="14" gutter="0" readonly></uv-rate>
										</view>
										<view class="ml-8 text-24">
											{{ shopData.score }}
										</view>
										<view class="w-1 h-23 bg-#DFE4EA mx-12"></view>
										<view class="flex items-center text-24 font-500">
											<view class="">粉丝数：</view>
											<view class="text-#FF8726">
												{{ shopData.follow }}
											</view>
										</view>
									</view>
								</view>
							</view>

							<view class="flex-shrink-0 ml-20">
								<template v-if="!shopData.isFollow">
									<view class="px-16 border-rd-8 bg-#03BAA7 flex flex-center text-24 font-500 text-#fff h-48 ac-op" @click="goodsStore.changeShopAttention()">
										<app-image :src="common.iconJiaBai" size="20" mr="9" mode=""></app-image>

										<view class="">关注</view>
									</view>
								</template>

								<template v-else>
									<view class="px-16 border-rd-8 bg-#BCBCC0 flex flex-center text-24 font-500 text-#fff h-48 ac-op" @click="goodsStore.changeShopAttention()">
										<view class="">已关注</view>
									</view>
								</template>
							</view>
						</view>

						<view class="mt-50 w-full h-70 border-rd-70 border-2 border-solid border-#00B496 flex flex-center ac-op">
							<view class="text-#00B496 text-26 font-500">进店逛逛</view>
						</view>
					</view>
				</view>

				<view class="px-20 mt-24 pb-50">
					<uv-parse class="mt-10" :content="info.detail"></uv-parse>
				</view>
			</view>

			<view class="w-full fixed bottom-0 h-150 px-20 pt-14 flex bg-#fff">
				<template v-if="!pageData.pageLoading">
					<view class="w-full flex h-84">
						<view class="flex flex-1 justify-around">
							<view class="flex flex-col flex-center ac-op">
								<app-image src="@/pages/goods/static/icon_dianpu_hei.png" size="40"></app-image>

								<view class="text-center text-22 mt-14 font-500">店铺</view>
							</view>

							<view class="flex flex-col flex-center ac-op">
								<app-image :src="common.iconKefuHei" size="40"></app-image>

								<view class="text-center text-22 mt-14 font-500">客服</view>
							</view>

							<template v-if="goodsStore.serveGoods === 0">
								<view class="flex flex-col flex-center ac-op">
									<app-image :src="common.iconGouwucheHei" size="40"></app-image>

									<view class="text-center text-22 mt-14 font-500">购物车</view>
								</view>
							</template>
						</view>

						<view class="w-440 h-84 flex-shrink-0 ml-24 border-rd-84 overflow-hidden flex flex-center text-30 font-bold text-#fff">
							<template v-if="goodsStore.isSellOut">
								<view class="flex-1 w-full h-full flex flex-center" :style="{ background: '#ccc', color: '#fff' }">
									<uv-button
										color="#ccc"
										:customStyle="{
											width: '100%',
											height: '84rpx',
										}"
										:disabled="true">
										<view class="text-30" :style="{ color: '#fff' }">已售罄</view>
									</uv-button>
								</view>
							</template>
							<template v-else>
								<template v-for="(btn, index) in goodsStore.btnOptions" :key="index">
									<view class="flex-1 w-full h-full flex flex-center" :style="{ background: btn.background, color: btn.color }">
										<uv-button
											:color="btn.background"
											:customStyle="{
												width: '100%',
												height: '84rpx',
											}"
											:disabled="goodsStore.isSellOut"
											@click="clickBottomBtn(btn)">
											<view class="text-30" :style="{ color: btn.color }">
												{{ btn.text }}
											</view>
										</uv-button>
									</view>
								</template>
							</template>
						</view>
					</view>
				</template>

				<uv-skeletons :loading="pageData.pageLoading" :skeleton="goodsDetailsFooter"></uv-skeletons>
			</view>

			<page-preferential-popup ref="isPreferentialPopup" title="优惠" :shopInfo="goodsStore.shopInfo" :sku="goodsStore.currentChoosedSku" :couponList="goodsStore.discountMap.COUPON?.data"></page-preferential-popup>

			<page-sku-popup ref="skuPopup" :listStyle="info.serveGoods === 0 ? 'card' : 'line'"></page-sku-popup>

			<my-uv-popup mode="bottom" ref="featureNamePopup" bgColor="#fff" round="20rpx" class="" closeable close-on-click-overlay>
				<view class="w-full bg-#fff border-top-left-rd-20">
					<view class="w-full py-40 text-center text-34 font-bold">商品参数</view>
					<view class="px-26 pb-50 h-800">
						<scroll-view scroll-y="true" class="w-full h-full">
							<view class="w-full h-full">
								<template v-for="(item, index) in goodsStore.goodsParames" :key="index">
									<view class="w-full flex flex-col mb-25">
										<view class="text-28 font-bold">{{ item.featureName }}</view>
										<view class="mt-10 text-26 text-#888">
											{{ item.featureValues.map((i) => i.firstValue).join(",") }}
										</view>
									</view>
								</template>
							</view>
						</scroll-view>
					</view>
				</view>
			</my-uv-popup>

			<uv-skeletons :loading="pageData.pageLoading" :skeleton="goodsDetailsContent"></uv-skeletons>
		</view>
	</app-layout>
</template>

<style lang="scss" scoped>
@mixin flex($direction: row) {
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: $direction;
}

.page {
	min-height: 100vh;
}

.indicator {
	@include flex(row);
	justify-content: center;

	&__dot {
		height: 10rpx;
		width: 10rpx;
		border-radius: 50%;
		background-color: #cbcbcb;
		margin: 0 4rpx;
		transition: background-color 0.3s;

		&--active {
			background-color: #6429e3;
		}
	}
}
</style>
