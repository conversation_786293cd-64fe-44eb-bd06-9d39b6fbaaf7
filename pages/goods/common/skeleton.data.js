// 主内容
export const goodsDetailsContent = [{
		type: 'flex',
		num: 1,
		style: 'height: 750rpx; background: #FFFFFF; padding: 5rpx;',
		children: [{
			type: 'custom',
			num: 1,
			style: 'width: 740rpx;height: 740rpx;'
		}]
	},
	{
		type: 'flex',
		num: 1,
		style: 'height: 236rpx; background: #FFFFFF; border-radius: 20rpx; margin: 25rpx 30rpx 0rpx; padding: 40rpx 32rpx;  justify-content: space-between; align-items: start; flex-direction: column;',
		children: [{
				type: 'custom',
				num: 1,
				style: 'width: 172rpx;height: 40rpx;'
			},
			{
				type: 'custom',
				num: 1,
				style: 'width: 600rpx; height: 40rpx;'
			}
		]
	},
	{
		type: 'flex',
		num: 1,
		style: 'background: #FFFFFF; border-radius: 20rpx; margin: 25rpx 30rpx 0rpx; padding: 40rpx 32rpx;  justify-content: space-between; align-items: start; flex-direction: column;',
		children: [{
				type: 'custom',
				num: 1,
				style: 'width: 600rpx;height: 40rpx;'
			},
			{
				type: 'custom',
				num: 1,
				style: 'width: 600rpx; height: 40rpx;margin: 20rpx 0rpx 0rpx;'
			},
			{
				type: 'custom',
				num: 1,
				style: 'width: 600rpx; height: 40rpx;margin: 20rpx 0rpx 0rpx;'
			},
			{
				type: 'custom',
				num: 1,
				style: 'width: 600rpx; height: 40rpx;margin: 20rpx 0rpx 0rpx;'
			}
		]
	},
	{
		type: 'flex',
		num: 1,
		style: 'height: 170rpx; background: #FFFFFF; border-radius: 20rpx; margin: 25rpx 30rpx 0rpx; padding: 40rpx 32rpx;',
		children: [{
				type: 'custom',
				num: 1,
				style: 'width: 110rpx;height: 110rpx; border-radius: 10rpx;'
			},
			{
				type: 'custom',
				num: 2,
				style: 'width: 500rpx; height: 40rpx; marginLeft: 25rpx;'
			}
		]
	},
	{
		type: 'flex',
		num: 1,
		style: 'height: 183rpx; background: #FFFFFF; border-radius: 20rpx; margin: 25rpx 30rpx 0rpx; padding: 40rpx 32rpx;  justify-content: space-between; align-items: start; flex-direction: column;',
		children: [{
				type: 'custom',
				num: 1,
				style: 'width: 600rpx;height: 40rpx;'
			},
			{
				type: 'custom',
				num: 1,
				style: 'width: 600rpx; height: 40rpx;'
			}
		]
	}
];

// 底部
export const goodsDetailsFooter = [{
	type: 'flex',
	num: 1,
	style: '',
	children: [{
			type: 'custom',
			num: 1,
			style: 'width:40rpx; height:76rpx; margin: 0 23rpx 0 0;'
		},
		{
			type: 'custom',
			num: 1,
			style: 'width:40rpx; height:76rpx; margin: 0 23rpx;'
		},
		{
			type: 'custom',
			num: 1,
			style: 'width:40rpx; height:76rpx; margin: 0 0rpx 0 23rpx;'
		},
		{
			type: 'custom',
			num: 1,
			style: 'width:220rpx; height:84rpx;border-radius: 42rpx 0rpx 0rpx 42rpx; margin: 0 0 0 50rpx;'
		},
		{
			type: 'custom',
			num: 1,
			style: 'width:220rpx; height:84rpx;border-radius: 0rpx 42rpx 42rpx 0rpx;'
		}
	]
}];