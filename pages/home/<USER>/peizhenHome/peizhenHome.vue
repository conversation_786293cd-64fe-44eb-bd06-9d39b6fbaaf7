<script setup>
import { onLoad, onReady, onPageScroll, onReachBottom } from "@dcloudio/uni-app";

import { ref, reactive, nextTick } from "vue";

const route = uni.$uv.route;

import useZPaging from "@/uni_modules/z-paging/components/z-paging/js/hooks/useZPaging.js";

import throttle from "@/uni_modules/uv-ui-tools/libs/function/throttle.js";
import { logo } from "@/common/images";

const pageHeight = ref(0);
const scrollable = ref(true);
const headerHeight = ref(9999);
const navHeader = ref(0);

const pagingRef = ref(null);
const tabs = ref(null);
const swiperItemListRef = ref();
const waterfall = ref(null);

useZPaging(pagingRef);

const searchHeight = ref(0);

const pageData = reactive({
	opacityValue: 0,
	searchTop: 0,
	searchWidth: 690,

	pageLoading: false,
	firstLoaded: false,

	dataList: [],
});

const kongList = ref([
	{
		icon: "@/pages/home/<USER>/huliHome/icon_changqihuli.png",
		name: "长期护理",
		data: {
			type: "goPage",
			url: "",
		},
	},
	{
		icon: "@/pages/home/<USER>/huliHome/icon_zhongchangqihuli.png",
		name: "中长期护理",
		data: {
			url: "",
		},
	},
	{
		icon: "@/pages/home/<USER>/huliHome/icon_duanqihuli.png",
		name: "短期护理",
		data: {
			url: "",
		},
	},
]);

function clickKongItem(item) {}

function onscroll(e) {
	const scrollTop = e.detail.scrollTop;

	// throttle(() => {
	pageData.opacityValue = scrollTop / 60;
	// }, 10);
}

function queryList(pageNo, pageSize) {
	const params = {
		pageNo: pageNo,
		pageSize: pageSize,
	};

	pageData.pageLoading = true;

	setTimeout(() => {
		let list = [];

		for (let i = 0; i < 10; i++) {
			list.push({
				id: pageData.dataList.length + 1,
				name: "居家陪护-长期护理",
			});
		}

		if (pagingRef.value) pagingRef.value.complete(list);

		pageData.firstLoaded = true;
		pageData.pageLoading = false;
	}, 1000);
}

function loadData() {}

function readData() {
	// #ifdef MP-WEIXIN
	let menuButtonInfo = uni.getMenuButtonBoundingClientRect();

	searchHeight.value = menuButtonInfo.height;
	// #endif
	searchHeight.value = uni.upx2px(74);
}

onReady(() => {
	readData();
});

onLoad(() => {
	loadData();
});
</script>
<template>
	<app-layout>
		<z-paging ref="pagingRef" @scroll="onscroll" :scrollable="scrollable" v-model="pageData.dataList" @query="queryList" :refresher-enabled="true" refresher-theme-style="black" :refresher-only="false" use-refresher-status-bar-placeholder :use-page-scroll="false">
			<view class="home_content relative">
				<app-image src="@/pages/home/<USER>/bg_item_home_top.png" class="absolute w-full" mode="widthFix"></app-image>

				<app-navBar :bgColor="`rgba(255,255,255,${pageData.opacityValue})`" height="88rpx" back>
					<template #content class="w-full">
						<view class="relative w-full h-full flex justify-between items-center nav-box">
							<view class="absolute-n w-710-n w-full px-20-n pr-20 pb-16-n flex items-center box-border" :style="[`top: ${pageData.searchTop}rpx`, `background: rgba(255,255,255,${pageData.opacityValue})`]">
								<view class="flex justify-between items-center flex-1 h-74-n border-rd-37 bg-#fff px-8 border-solid border-1 border-#09C1B1 ac-op" :style="[`width: ${pageData.searchWidth}rpx`, `height: ${searchHeight}px`]" @click="route('/pages/home/<USER>/search/search')">
									<view class="flex justify-start items-center">
										<app-image class="w-26 h-26 ml-20" :src="common.iconSearchQianhui" size="26" mode=""></app-image>
										<text class="text-#98A2B4 text-28 font-500 ml-14">长期护理</text>
									</view>
									<view class="flex justify-end items-center">
										<view class="w-126 h-58 bg-#00B496 border-rd-29 text-#fff text-28 font-bold flex justify-center items-center">搜索</view>
									</view>
								</view>

								<app-image src="@/pages/home/<USER>/icon_dingdan_hei.png" mode="" size="52" ml="20"></app-image>
							</view>
						</view>
					</template>
				</app-navBar>

				<view class="w-full relative">
					<view class="pl-20 pr-20 mt-24 relative">
						<view class="py-35 bg-#fff border-rd-20">
							<view class="grid gap-y-35rpx" :style="{ 'grid-template-columns': `repeat(${Math.min(Math.max(kongList.length, 1), 4)}, minmax(0, 1fr))` }">
								<template v-for="(item, index) in kongList" :key="index">
									<view class="flex flex-col items-center ac-op" @click="clickKongItem(item)">
										<app-image :src="item.icon" size="88" mode=""></app-image>
										<view class="mt-15 text-center text-#343434 text-26">
											{{ item.name }}
										</view>
									</view>
								</template>
							</view>
						</view>
					</view>

					<view class="px-20 mt-24 pb-0 flex items-center justify-between">
						<view class="flex-1 p-8 box-border mr-8 border-rd-20" style="background: linear-gradient(-30deg, #2cdee2 0%, #01c8a7 100%)">
							<view class="py-13 px-19 box-border">
								<view class="text-30 text-#fff font-bold">照护品牌馆</view>
							</view>

							<view class="flex items-center justify-between py-24 px-35 box-border border-rd-20 bg-#fff mt-0 h-180">
								<template v-for="i in 2" :key="i">
									<view class="flex flex-col">
										<view class="w-116 h-116 border-4 border-solid border-#C4FFF5 rd-100 flex flex-center">
											<app-image :src="logo" size="98" rd="50%" mode=""></app-image>
										</view>
										<view class="w-116 box-border relative">
											<view class="w-full h-34 bg-#00B698 uv-line-1 text-22 line-height-34 text-#fff font-500 flex flex-center border-rd-18 px-10" style="margin-top: -15rpx"> 商家商家 </view>
										</view>
									</view>
								</template>
							</view>
						</view>

						<view class="flex-1 p-8 box-border ml-8 border-rd-20" style="background: linear-gradient(-30deg, #2cdee2 0%, #01c8a7 100%)">
							<view class="py-13 px-19 box-border">
								<view class="text-30 text-#fff font-bold">居家照护服务推荐</view>
							</view>

							<view class="flex items-center justify-between py-24 px-20 box-border border-rd-20 bg-#fff mt-0 h-180">
								<template v-for="i in 2" :key="i">
									<view class="flex flex-col">
										<view class="w-134 h-100">
											<app-image :src="logo" width="134" height="100" rd="10" mode=""></app-image>
										</view>
										<view class="w-134">
											<view class="w-full text-18 text-#222222 font-500 uv-line-1">住家陪护-自理自理自理</view>
											<view class="w-full text-18 text-#FC3F33 font-500 uv-line-1 text-center">低至6480起</view>
										</view>
									</view>
								</template>
							</view>
						</view>
					</view>

					<view class="px-20 py-20">
						<view class="flex flex-wrap justify-between">
							<template v-for="(item, index) in pageData.dataList" :key="index">
								<view class="mb-20 w-346">
									<page-huli-card-item :item="item" :index="index"></page-huli-card-item>
								</view>
							</template>
						</view>

						<uv-load-more :status="'loading'" v-show="pageData.pageLoading && !pageData.firstLoaded" />
					</view>
				</view>
			</view>
		</z-paging>
	</app-layout>
</template>

<style>
page {
	background-color: #f0f3f7;
}
</style>

<style lang="scss" scoped>
.page-body {
	width: 100%;
	min-height: 100vh;
	background-color: #f0f3f7;
}

.home_content {
	// background-repeat: no-repeat;
	// background-size: 100% 521rpx;
}

.paging-content {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.swiper {
	flex: 1;
}
</style>
