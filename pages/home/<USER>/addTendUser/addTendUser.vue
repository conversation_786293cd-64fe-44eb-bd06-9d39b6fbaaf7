<script setup>
import { onLoad, onUnload } from "@dcloudio/uni-app";
import { ref, reactive, getCurrentInstance, watch, computed } from "vue";
import { useMerchantStore, useUserStore } from "@/store";
import { canENV } from "@/common/utils";

import merchantPopupVue from "./components/merchantPopup.vue";
import workPopupVue from "./components/workPopup.vue";

import { doGetServiceType, doGetMasterEnum, doApplyMaster, upload, getMasterDetailInMasterId, getMasterDetailInUser } from "@/server/api";
import { REGEX_CITIZEN_ID, REGEX_MOBILE, REXGEX_NUMBERS } from "@/common/test";
import { common } from "@/common/images";

const { ctx } = getCurrentInstance();
const merchantStore = useMerchantStore();
const formData = reactive(merchantStore.enterMasterInfo);
const pageData = reactive({
	rules: {
		shopId: { rules: [{ required: true, errorMessage: "所属商家不能为空" }] },
		name: { rules: [{ required: true, errorMessage: "姓名不能为空" }] },
		mobile: {
			rules: [
				{ required: true, errorMessage: "手机号不能为空" },
				{
					validateFunction: (rule, value, data, callback) => {
						if (!REGEX_MOBILE(value)) {
							callback("请输入正确的手机号");
						}
						return true;
					},
				},
			],
		},
		sex: {
			rules: [{ required: true, errorMessage: "性别不能为空" }],
		},
		age: {
			rules: [
				{ required: true, errorMessage: "年龄不能为空" },
				{
					validateFunction: (rule, value, data, callback) => {
						if (!REXGEX_NUMBERS(value)) {
							callback("请输入正确的年龄");
						} else {
							if (value < 18 || value >= 80) {
								callback("请输入正确的年龄");
							}
						}
						return true;
					},
				},
			],
		},
		serveTypeId: { rules: [{ required: true, errorMessage: "服务类型不能为空" }] },
		masterWork: { rules: [{ required: true, errorMessage: "从事工作不能为空" }] },
		culture: { rules: [{ required: true, errorMessage: "文化水平不能为空" }] },
		idNo: {
			rules: [
				{ required: true, errorMessage: "身份证号码不能为空" },
				{
					validateFunction: (rule, value, data, callback) => {
						if (!REGEX_CITIZEN_ID(value)) {
							callback("请输入正确的身份证号");
						}
						return true;
					},
				},
			],
		},
		idFront: { rules: [{ required: true, errorMessage: "身份证正面不能为空" }] },
		idBack: { rules: [{ required: true, errorMessage: "身份证反面不能为空" }] },
		nativePlace: { rules: [{ required: true, errorMessage: "籍贯不能为空" }] },
		placeAbode: { rules: [{ required: true, errorMessage: "现居住地不能为空" }] },
	},
	checkMerchant: {}, // 选择的商家
	serverTypeList: [], // 服务类型
	cultureList: [], // 文化水平列表
	checkWorkList: [], // 选择的从事工作
});
const formRef = ref();
const subFormRef = ref();
const merchantPopupRef = ref();
const wordPopupRef = ref();
const submitLoading = ref(true);
const applyStatus = ref(-10); // 0->审核中 1->审核通过 -1->禁用 -2->审核拒绝
const formDisable = computed(() => {
	return applyStatus.value !== -10 && applyStatus.value !== -2;
});
watch(formData, (newVal) => {
	merchantStore.SET_MASTER_INFO(newVal);
});
onLoad(async () => {
	if (useUserStore().checkLogin) {
		uni.showLoading({
			title: "加载中...",
		});
		const res = await getMasterDetailInUser();
		if (res && res.apiStatus) {
			if (typeof res.data === "object") {
				for (let [key, value] of Object.entries(res.data)) {
					if (key === "masterWorkVO") {
						formData.masterWork = value;
					} else if (key === "masterBasicsVO") {
						formData.masterBasicsDtoList = value;
					} else if (key === "shopVO") {
						formData.checkMerchant = value;
					} else {
						formData[key] = value;
					}
				}
				applyStatus.value = res.data.status;
			}
		}
		uni.hideLoading();
	}

	getServerTypeList();
});
function chooseImage(key) {
	if (formDisable.value) return;
	// uni.chooseImage({
	// 	count: 1, //默认9
	// 	sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
	// 	sourceType: ['album'], //从相册选择
	// 	success: (res)=>{
	// 		alUploadFile(res.tempFilePaths[0], true).then(res=>{
	// 			if(key == 'idFront'){
	// 				formData.masterBasicsDtoList.idFront = res.file
	// 			}else if(key == 'idBack'){
	// 				formData.masterBasicsDtoList.idBack = res.file
	// 			}else{
	// 				formData[key] = res.file
	// 			}
	// 		})
	// 	}
	// });
	// #ifndef H5
	uni.chooseMedia({
		count: 1,
		mediaType: ["image"],
		sourceType: ["album", "camera"],
		sizeType: ["original", "compressed"],
		success(res) {
			const tempFilePaths = res.tempFiles.map((i) => i.tempFilePath);

			uni.showLoading({
				title: "加载中...",
			});
			upload(tempFilePaths[0], {
				formData: tempFilePaths,
			})
				.then((res) => {
					uni.hideLoading();
					if (res.apiStatus) {
						if (key == "idFront") {
							formData.masterBasicsDtoList.idFront = res.data;
						} else if (key == "idBack") {
							formData.masterBasicsDtoList.idBack = res.data;
						} else {
							formData[key] = res.data;
						}
					}
				})
				.catch((err) => {
					uni.hideLoading();
				});
		},
	});
	// #endif
	// #ifdef H5
	uni.chooseImage({
		count: 1,
		sourceType: ["album", "camera"],
		sizeType: ["original", "compressed"],
		success(res) {
			const tempFilePaths = res.tempFilePaths;

			uni.showLoading({
				title: "加载中...",
			});
			upload(tempFilePaths[0], {
				formData: tempFilePaths,
			})
				.then((res) => {
					uni.hideLoading();
					if (res.apiStatus) {
						if (key == "idFront") {
							formData.masterBasicsDtoList.idFront = res.data;
						} else if (key == "idBack") {
							formData.masterBasicsDtoList.idBack = res.data;
						} else {
							formData[key] = res.data;
						}
					}
				})
				.catch((err) => {
					uni.hideLoading();
				});
		},
	});
	// #endif
}
function merchantPopupConfirm(e) {
	formData.shopId = e.id;
	formData.checkMerchant = e;
}
function wordPopupConfirm(e) {
	formData.masterWork = e;
}
function getServerTypeList() {
	doGetServiceType().then((res) => {
		if (res.code == 200) {
			pageData.serverTypeList = res.data.records.map((elem) => {
				return {
					value: elem.id,
					text: elem.name,
				};
			});
		}
	});
	doGetMasterEnum().then((res) => {
		if (res.code == 200) {
			pageData.cultureList = res.data.map((elem) => {
				return {
					value: elem.value,
					text: elem.name,
				};
			});
		}
	});
}
function handleSubmit() {
	let promiseList = [formRef.value.validate(), subFormRef.value.validate()];
	Promise.all(promiseList)
		.then((res) => {
			const params = JSON.parse(JSON.stringify(formData));
			params.age = Number(params.age);
			delete params.checkMerchant;
			delete params.checkWorkList;
			console.log(JSON.stringify(params));
			uni.showLoading({
				title: "加载中...",
				mask: true,
			});
			doApplyMaster(params).then((res) => {
				uni.hideLoading();
				if (res.code == 200) {
					merchantStore.CLEAR_MASTER_INFO();
					uni.showModal({
						title: "提交成功",
						content: "请耐心等待审核",
						showCancel: false,
						success: (res) => {
							if (res.confirm) {
								uni.navigateBack({
									delta: 1,
								});
							} else if (res.cancel) {
								console.log("用户点击取消");
							}
						},
					});
				}
			});
		})
		.catch((err) => {
			console.log(err);
		});
}

onUnload(() => {
	merchantStore.CLEAR_MASTER_INFO();
});
</script>

<template>
	<app-layout>
		<z-paging
			ref="pagePaging"
			:paging-style="{
				backgroundImage: 'url(https://hejiawuyou.oss-cn-shanghai.aliyuncs.com/weixin/storeInfo_top_bg.png)',
				backgroundColor: '#fff',
				backgroundRepeat: 'no-repeat',
				backgroundSize: '100% 460rpx',
				top: '0',
				bottom: '0',
			}"
			refresher-theme-style="white">
			<template #top>
				<app-navBar :has-right="false">
					<template v-slot:content>
						<view class="w-full flex justify-between items-center h-full">
							<!-- #ifdef MP-WEIXIN -->
							<view class="w-36 h-36 ml-24">
								<image @click="ctx.$uv.route({ type: 'back' })" :src="common.iconLeftBai" class="w-36 h-36" mode=""></image>
							</view>
							<!-- #endif -->

							<!-- #ifndef MP-WEIXIN -->
							<view class="w-120 h-36 ml-24">
								<image @click="ctx.$uv.route({ type: 'back' })" :src="common.iconLeftBai" class="w-36 h-36" mode=""></image>
							</view>
							<!-- #endif -->
							<view class="text-34 text-#fff font-bold">入驻服务人员申请</view>

							<!-- #ifdef MP-WEIXIN -->
							<view class="w-36 h-36 ml-24"></view>
							<!-- #endif -->

							<!-- #ifndef MP-WEIXIN -->
							<view class="w-120 h-36 ml-24"></view>
							<!-- #endif -->
						</view>
					</template>
				</app-navBar>
			</template>
			<template v-if="applyStatus === 1">
				<view class="w-full py-24 px-30 bg-[rgba(219,255,249,1)] text-#00B496 text-24 font-bold mb-0">
					<view class="mt-5">
						<text>您的申请</text>
						<text>已通过！</text>
					</view>
				</view>
			</template>
			<template v-if="applyStatus === -2">
				<view class="w-full py-24 px-30 bg-[rgba(245,230,230,1)] text-#e45656 text-24 font-bold mb-0">
					<view class="mt-5">
						<text>您上次提交的</text>
						<text>信息</text>
						<text>未通过审核！</text>
					</view>
					<view class="mt-5" v-if="formData.masterAuidLogVO.remark">
						<text>拒绝原因：</text>
						<text>{{ formData.masterAuidLogVO.remark }}</text>
					</view>
				</view>
			</template>
			<view class="bg-#fff border-rd-t-l-20 border-rd-t-r-20 pb-20">
				<uni-forms ref="formRef" :modelValue="formData" :rules="pageData.rules" :label-width="80">
					<uni-forms-item label="" name="avatar" label-position="top">
						<view @click="chooseImage('avatar')" class="text-center">
							<image v-if="formData.avatar" :src="formData.avatar" class="w-140 h-140 border-rd-70" mode="aspectFill"></image>
							<image v-else src="@/pages/home/<USER>/icon_upload_avatar.png" class="w-140 h-140" mode=""></image>
							<view class="text-30 text-#A7ACB7 font-500">上传头像</view>
						</view>
					</uni-forms-item>
					<view class="px-20">
						<uni-forms-item label="所属商家" name="shopId" required>
							<app-easyinput @click="!formDisable && merchantPopupRef.open()" :text-value="formData.checkMerchant.name" placeholder="请选择所属商家"></app-easyinput>
						</uni-forms-item>
						<uni-forms-item label="姓名" name="name" required>
							<uni-easyinput type="text" v-model="formData.name" placeholder="请输入姓名" :disabled="formDisable" />
						</uni-forms-item>
						<uni-forms-item label="手机号" name="mobile" required>
							<uni-easyinput type="text" v-model="formData.mobile" maxlength="11" placeholder="请输入手机号" :disabled="formDisable" />
						</uni-forms-item>
						<uni-forms-item label="性别" name="sex" required class="flex justify-start items-center">
							<uv-radio-group v-model="formData.sex" active-color="#00B496" :disabled="formDisable">
								<view class="flex items-center">
									<view class="mr-20">
										<uv-radio name="MALE" label="男"></uv-radio>
									</view>
									<view class="">
										<uv-radio name="FEMALE" label="女"></uv-radio>
									</view>
								</view>
							</uv-radio-group>
						</uni-forms-item>
						<uni-forms-item label="年龄" name="age" required>
							<uni-easyinput type="number" v-model="formData.age" placeholder="请输入年龄" :disabled="formDisable" />
						</uni-forms-item>
						<uni-forms-item label="服务类型" name="serveTypeId" required>
							<uni-data-select v-model="formData.serveTypeId" :localdata="pageData.serverTypeList" border-color="rgba(0,0,0,0)" text-align="right" style="width: 100%" :disabled="formDisable"></uni-data-select>
						</uni-forms-item>
						<uni-forms-item label="从事工作" name="masterWork" required>
							<app-easyinput @click="!formDisable && wordPopupRef.open()" :text-value="formData.masterWork.map((elem) => elem.workName).join(';')" placeholder="请选择从事工作" :disabled="formDisable"></app-easyinput>
						</uni-forms-item>
						<uni-forms ref="subFormRef" :modelValue="formData.masterBasicsDtoList" :rules="pageData.rules" :label-width="80">
							<uni-forms-item label="文化水平" name="culture" required>
								<uni-data-select v-model="formData.masterBasicsDtoList.culture" :localdata="pageData.cultureList" border-color="rgba(0,0,0,0)" text-align="right" style="width: 100%" :disabled="formDisable"></uni-data-select>
							</uni-forms-item>
							<uni-forms-item label="身份证号" name="idNo" required>
								<uni-easyinput type="text" v-model="formData.masterBasicsDtoList.idNo" maxlength="18" placeholder="请输入身份证号码" :disabled="formDisable" />
							</uni-forms-item>
							<uv-row customStyle="margin-bottom: 10px">
								<uv-col span="6">
									<uni-forms-item label="身份证" name="idFront" label-position="top" required>
										<view class="pl-10">
											<view @click="chooseImage('idFront')">
												<image v-if="formData.masterBasicsDtoList.idFront" :src="formData.masterBasicsDtoList.idFront" class="w-315 h-186 border-rd-20" mode="aspectFit"></image>
												<image v-else src="@/pages/home/<USER>/upload_sfzz.png" class="w-315 h-186" mode=""></image>
											</view>
										</view>
									</uni-forms-item>
								</uv-col>
								<uv-col span="6">
									<uni-forms-item label="" name="idBack" label-position="top">
										<view @click="chooseImage('idBack')">
											<image v-if="formData.masterBasicsDtoList.idBack" :src="formData.masterBasicsDtoList.idBack" class="w-315 h-186 border-rd-20" mode="aspectFit"></image>
											<image v-else src="@/pages/home/<USER>/upload_sfzf.png" class="w-315 h-186" mode=""></image>
										</view>
									</uni-forms-item>
								</uv-col>
							</uv-row>
							<uni-forms-item label="籍贯" name="nativePlace" required>
								<uni-easyinput type="text" v-model="formData.masterBasicsDtoList.nativePlace" placeholder="请输入籍贯" :disabled="formDisable" />
							</uni-forms-item>
							<uni-forms-item label="现居住地" name="placeAbode" required>
								<uni-easyinput type="text" v-model="formData.masterBasicsDtoList.placeAbode" placeholder="请输入现居住地" :disabled="formDisable" />
							</uni-forms-item>
							<uni-forms-item label="简介" name="intro">
								<uni-easyinput type="textarea" v-model="formData.masterBasicsDtoList.intro" placeholder="请输入简介" :disabled="formDisable" />
							</uni-forms-item>
						</uni-forms>
					</view>
				</uni-forms>
			</view>
			<template #bottom>
				<template v-if="applyStatus === -10">
					<view class="py-14 px-40 border-t-solid border-1 border-#EFF2F9">
						<uv-button @click="handleSubmit()" color="#00B496" shape="circle" text="提交" customTextStyle="font-size: 30rpx;font-weight: bold;" customStyle="height: 84rpx;"></uv-button>
					</view>
				</template>

				<template v-if="applyStatus === 0">
					<view class="py-14 px-40 border-t-solid border-1 border-#EFF2F9">
						<uv-button color="#00B496" shape="circle" text="审核中，请等待审核..." customTextStyle="font-size: 30rpx;font-weight: bold;" customStyle="height: 84rpx;" disabled></uv-button>
					</view>
				</template>

				<template v-if="applyStatus === 1">
					<!-- <view class="py-14 px-40 border-t-solid border-1 border-#EFF2F9">
						<uv-button color="#00B496" shape="circle" text="审核已通过" customTextStyle="font-size: 30rpx;font-weight: bold;" customStyle="height: 84rpx;" disabled></uv-button>
					</view> -->
				</template>

				<template v-if="applyStatus === -1">
					<view class="py-14 px-40 border-t-solid border-1 border-#EFF2F9">
						<uv-button color="#00B496" shape="circle" text="已被禁用，请联系客服解除" customTextStyle="font-size: 30rpx;font-weight: bold;" customStyle="height: 84rpx;" disabled></uv-button>
					</view>
				</template>

				<template v-if="applyStatus === -2">
					<view class="py-14 px-40 border-t-solid border-1 border-#EFF2F9">
						<uv-button @click="handleSubmit()" color="#00B496" shape="circle" text="再次提交" customTextStyle="font-size: 30rpx;font-weight: bold;" customStyle="height: 84rpx;"></uv-button>
					</view>
				</template>

				<app-safeAreaBottom></app-safeAreaBottom>
			</template>
		</z-paging>
		<merchant-popup-vue ref="merchantPopupRef" :model-value="formData.shopId" @confirm="merchantPopupConfirm"></merchant-popup-vue>
		<work-popup-vue ref="wordPopupRef" :model-value="formData.masterWork" @confirm="wordPopupConfirm"></work-popup-vue>
	</app-layout>
</template>

<style lang="scss" scoped></style>
