<script setup>
import "uno.css";
import { ref, reactive } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { doGetWorkList } from "@/server/api";
import { common } from "@/common/images";
const emit = defineEmits(["confirm"]);
const props = defineProps({
	modelValue: {
		type: Array,
		default: () => [],
	},
});
const pageData = reactive({
	workList: [],
	checkWorkList: [],
});
const popupRef = ref();
function open() {
	pageData.checkWorkList = JSON.parse(JSON.stringify(props.modelValue));
	popupRef.value.open();
	if (pageData.workList.length == 0) {
		getWorkList();
	}
}
function close() {
	popupRef.value.close();
}
function getWorkList() {
	doGetWorkList().then((res) => {
		if (res.code == 200) {
			pageData.workList = res.data.records.map((elem) => ({
				workId: elem.id,
				workName: elem.name,
			}));
		}
	});
}
function handleSubmit() {
	emit("confirm", pageData.checkWorkList);
	close();
}
function checkWork(item) {
	const index = pageData.checkWorkList.findIndex((elem) => elem.workId == item.workId);
	if (index > -1) {
		pageData.checkWorkList.splice(index, 1);
	} else {
		pageData.checkWorkList.push(item);
	}
}
defineExpose({
	open,
	close,
});
</script>

<template>
	<app-layout>
		<uni-popup ref="popupRef" type="bottom" border-radius="10px 10px 0 0" background-color="#fff">
			<view class="max-h-70vh flex flex-col">
				<view class="flex justify-between items-center px-30 h-100 border-b-solid border-1 border-#EAEDF3">
					<image src="" class="w-30 h-30" mode=""></image>
					<text class="text-30 text-#000000 font-bold">从事工作(可多选)</text>
					<image @click="close()" :src="common.iconCloseHui" class="w-30 h-30" mode=""></image>
				</view>
				<view class="grid grid-cols-2 gap-20 px-40 py-20">
					<view @click="checkWork(item)" class="h-80 line-height-72 border-rd-16 text-center box-border border-solid border-4 relative" v-for="(item, index) in pageData.workList" :key="index" :class="[pageData.checkWorkList.findIndex((elem) => elem.workId == item.workId) > -1 ? 'border-#66C5B5 bg-#F0FCFC' : 'border-#F2F3F6 bg-#F2F3F6']">
						<text class="text-30 text-#323232 font-500">{{ item.workName }}</text>
						<image v-if="pageData.checkWorkList.findIndex((elem) => elem.workId == item.workId) > -1" src="@/pages/home/<USER>/right_top_checked.png" class="absolute top--4 right--4 w-30 h-30" mode=""></image>
					</view>
				</view>
				<view class="py-14 px-40 border-t-solid border-1 border-#EFF2F9">
					<uv-button @click="handleSubmit()" color="#00B496" shape="circle" text="提交" customTextStyle="font-size: 30rpx;font-weight: bold;" customStyle="height: 84rpx;"></uv-button>
				</view>
				<app-safeAreaBottom></app-safeAreaBottom>
			</view>
		</uni-popup>
	</app-layout>
</template>

<style lang="scss" scoped></style>
