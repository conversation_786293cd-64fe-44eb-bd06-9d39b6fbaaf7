<script setup>
import "uno.css";
import { ref, reactive, nextTick } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { doGetShopList } from "@/server/api";
import { common } from "@/common/images";
const emit = defineEmits(["confirm"]);
const props = defineProps({
	modelValue: {
		type: [String, Number],
		default: "",
	},
});
const pageData = reactive({
	dataList: [],
	checkMerchant: {
		id: props.modelValue,
	},
});
const popupRef = ref();
const popupPagingRef = ref();
function queryList(pageNo, pageSize) {
	const params = {
		current: pageNo,
		size: pageSize,
		shopModes: "O2O",
	};
	uni.showLoading({
		title: "加载中...",
		mask: true,
	});
	doGetShopList(params)
		.then((res) => {
			uni.hideLoading();
			if (res.code == 200) {
				popupPagingRef.value.complete(res.data.records);
			}
		})
		.catch((err) => {
			uni.hideLoading();
			popupPagingRef.value.complete(false);
		});
}
function open() {
	popupRef.value.open();
	nextTick(() => {
		if (pageData.dataList.length == 0) {
			setTimeout(() => {
				popupPagingRef.value.reload();
			}, 100);
		}
	});
}
function close() {
	popupRef.value.close();
}
function handleSubmit() {
	if (pageData.checkMerchant.id) {
		emit("confirm", pageData.checkMerchant);
		close();
	}
}
defineExpose({
	open,
	close,
});
</script>

<template>
	<app-layout>
		<uni-popup ref="popupRef" type="bottom" border-radius="10px 10px 0 0" background-color="#fff">
			<view class="h-70vh">
				<z-paging ref="popupPagingRef" :auto="false" v-model="pageData.dataList" @query="queryList">
					<template #top>
						<view class="flex justify-between items-center px-30 h-100 border-b-solid border-1 border-#EAEDF3">
							<image src="" class="w-30 h-30" mode=""></image>
							<text class="text-30 text-#000000 font-bold">商家引荐</text>
							<image @click="close()" :src="common.iconCloseHui" class="w-30 h-30" mode=""></image>
						</view>
					</template>
					<view class="px-36">
						<view class="relative" @click="pageData.checkMerchant = item" v-for="(item, index) in pageData.dataList" :key="index">
							<view class="flex justify-start py-20 border-b-solid border-1 border-#EAEDF3">
								<image :src="item.logo" mode="" class="w-120 h-120 border-rd-60 min-w-120 mr-20"></image>
								<view class="flex-1 text-30 text-#222222 font-500">
									<view class="">名称: {{ item.name }}</view>
									<view class="flex justify-start items-center">
										评分:
										<uv-rate :count="5" v-model="item.score" :size="12" readonly :allowHalf="true"></uv-rate>
									</view>
									<view class="text-28 text-#222222 font-500">地址: {{ item.address }}</view>
								</view>
							</view>
							<view class="w-30 h-30 absolute right-20 top-20">
								<image v-if="pageData.checkMerchant.id == item.id" class="w-30 h-30" :src="common.iconXuanzeXhuanzhong" mode=""></image>
								<image v-else class="w-30 h-30" :src="common.iconXuianzeWeixuanzhong" mode=""></image>
							</view>
						</view>
					</view>
					<template #bottom>
						<view class="py-14 px-40 border-t-solid border-1 border-#EFF2F9">
							<uv-button @click="handleSubmit()" color="#00B496" shape="circle" text="提交" customTextStyle="font-size: 30rpx;font-weight: bold;" customStyle="height: 84rpx;"></uv-button>
						</view>
						<app-safeAreaBottom></app-safeAreaBottom>
					</template>
				</z-paging>
			</view>
		</uni-popup>
	</app-layout>
</template>

<style lang="scss" scoped></style>
