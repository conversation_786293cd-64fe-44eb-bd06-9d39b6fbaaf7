<script setup>
import { onLoad, onReady, onReachBottom, onShow, onUnload } from "@dcloudio/uni-app";

import { canENV, getPriceInfo } from "@/common/utils";

import { getSearchHistoriesAndHotWords, getSearchSuggest, delSearchHistory, searchProduct, speechToText } from "@/server/api";

import permision from "@/js_sdk/wa-permission/permission.js";

import { useUserStore } from "@/store";

import { ref, reactive, watch, computed, onUnmounted } from "vue";
import { sleep } from "@/uni_modules/uv-ui-tools/libs/function";
import { common } from "@/common/images";

const searchHeight = ref(0);

const pagePaging = ref();

const waterfall = ref(null);

const searchKeyword = ref("");

const searchList = ref([]);

const _props = defineProps({
	shopId: {
		type: String,
		default: "",
	},
});

const pageData = reactive({
	page: 0,
	limit: 30,
	status: "loading",

	searchInputFocus: true,

	searchSuggestList: [],

	dataList: [],

	list1: [],
	list2: [],
});

const navRef = ref();
const isSearchStatus = ref(false);

watch(isSearchStatus, () => {
	if (navRef.value) navRef.value.refreshNavFixedPlaceholderHeight();
});

const enumSortIndex = ref(0);
const enumSortKey = [
	[
		{ order: "salesVolume", sort: "DESC" },
		{ order: "createTime", sort: "DESC" },
	],
	{ order: "salesVolume", sort: "DESC" },
	{ order: "createTime", sort: "DESC" },
	{ order: "salePrices", sort: "ASC" },
	{ order: "salePrices", sort: "DESC" },
];

const searchParams = reactive({
	orderByParams: [
		{ order: "salesVolume", sort: "DESC" },
		{ order: "createTime", sort: "DESC" },
	],
	shopId: _props.shopId || "",
});

function changeEnumSort(index) {
	if (enumSortIndex.value !== index) {
		enumSortIndex.value = index;
		searchParams.orderByParams = enumSortKey[enumSortIndex.value];
		loadData(true);
	}
}

function changeList(e) {
	pageData[e.name].push(e.value);
}

const historySearchList = ref([]);
const hotSearchList = ref([]);

function clearSearchKeyword() {
	pageData.searchSuggestList = [];
	searchKeyword.value = "";
	searchList.value = [];
	isSearchStatus.value = false;
}

function loadData(refresh = false) {
	if (!searchKeyword.value) return;

	if (refresh) {
		pageData.page = 0;

		searchList.value = [];
		pageData.list1 = [];
		pageData.list2 = [];

		if (waterfall.value) waterfall.value.clear();
	}

	pageData.page += 1;
	pageData.status = "loading";

	uni.showLoading({
		title: "搜索中...",
	});

	searchProduct({
		page: pageData.page,
		limit: pageData.limit,
		current: pageData.page,
		keyword: searchKeyword.value,
		searchTotalStockGtZero: true,
		status: "loadmore",
		...searchParams,
	})
		.then((res) => {
			uni.hideLoading();
			if (res.apiStatus) {
				const list = res.data.list.map((i) => {
					if (!Number(i.serveTypeId)) {
						i.serveGoods = 0;
						i.serveTypeId = "0";
						i.serveTypeFename = "shop";
					}

					i.name = i.productName;

					i.statistics = {
						productId: i.productId,
						lowestPrice: Math.min(...(i.salePrices || [0])),
						salesVolume: i.salesVolume,
					};

					return i;
				});

				searchList.value = refresh ? list : [...searchList.value, ...list];

				isSearchStatus.value = true;

				if (searchList.value.length >= res.data.total) {
					pageData.status = "nomore";
				} else {
					pageData.status = "loadmore";
				}

				loadHistorySearch();
			}
		})
		.catch((err) => {
			uni.hideLoading();
		});
}

function clickSearchItem(text) {
	searchKeyword.value = text.text;
	loadData(true);
}

function delHistoryItem(index) {
	historySearchList.value.splice(index, 1);
}

function clearHistoryItem() {
	// historySearchList.value = [];
	uni.showModal({
		title: "提醒",
		content: "是否清空搜索历史？",
		success(res) {
			if (res.confirm) {
				delSearchHistory().then((res) => {
					// loadHistorySearch();

					historySearchList.value = [];
				});
			}
		},
	});
}

function addHistoryItem(text) {
	if (typeof text === "undefined" || text === "") return;
	const textStr = String(text);
	const findIdex = historySearchList.value.findIndex((i) => i.text === textStr);
	if (findIdex > 0) {
		historySearchList.value.splice(findIdex, 1);
		historySearchList.value.unshift({
			text: textStr,
		});
	} else if (findIdex < 0) {
		historySearchList.value.unshift({
			text: textStr,
		});
	}

	if (historySearchList.value.length > 30) {
		historySearchList.value.splice(historySearchList.value.length - 1, 1);
	}
}

function loadHotSearch() {
	// this.$http.post('/api/Keywords/list').then(res => {
	// 	if (Array.isArray(res.data)) {
	// 		hotSearchList.value = res.data.map(i => {
	// 			return {
	// 				text: i.name
	// 			}
	// 		})
	// 	}
	// })
}

function loadHistorySearch() {
	getSearchHistoriesAndHotWords().then((res) => {
		if (res.apiStatus) {
			historySearchList.value = res.data.histories.map((i) => {
				return {
					text: i,
				};
			});

			hotSearchList.value = res.data.hotWords.map((i) => {
				return {
					text: i,
				};
			});
		}
	});

	// const historySearch = uni.getStorageSync('historySearchList');
	// if (Array.isArray(historySearch)) {
	// 	historySearchList.value = historySearch;
	// }
}

function changeSearchKeyword(e) {
	getSearchSuggest({ keyword: searchKeyword.value }).then((res) => {
		if (res.apiStatus) {
			pageData.searchSuggestList = res.data;
		}
	});
}

function selectSuggest(suggest) {
	searchKeyword.value = suggest.productName;
	loadData(true);
}

let recorderManager = null;
let innerAudioContext = null;
// #ifdef APP || MP-WEIXIN
recorderManager = uni.getRecorderManager();
innerAudioContext = uni.createInnerAudioContext();
// #endif

const showVoiceSearchBtn = ref(false);
const keyboardHeight = ref(0);
const voiceSearch = ref(false);
const voicePopup = ref();
const recordVoiceSatate = ref("start"); // start -> 开始 | record -> 录制中 | loading -> 加载中 | error -> 错误 | success -> 正常
const recordVoiceText = ref("");
const recordErrorText = ref("网络异常，请稍后再试");

// canENV(() => {
// 	keyboardHeight.value = 1;
// });

watch(keyboardHeight, () => {
	if (keyboardHeight.value > 0) {
		if (useUserStore().checkLogin) {
			showVoiceSearchBtn.value = true;
		}
	} else {
		sleep(10).then((res) => {
			showVoiceSearchBtn.value = false;
		});
	}
});

const recordVoiceTip = computed(() => {
	let text = "";
	switch (recordVoiceSatate.value) {
		case "start":
			text = "点击图标开始";
			break;
		case "record":
			text = "正在听，您请说";
			break;
		case "loading":
			text = "识别中，请稍后...";
			break;
		case "success":
			text = recordVoiceText.value;
			break;
		case "error":
			text = recordErrorText.value;
			break;
		default:
			break;
	}

	return text;
});

function openVoiceSearch(focus = true) {
	if (focus) pageData.searchInputFocus = false;
	uni.hideKeyboard();
	recordVoiceSatate.value = "start";
	searchKeyword.value = "";
	recordErrorText.value = "网络异常，请稍后再试";
	if (voicePopup.value) voicePopup.value.open();
}

function closeVoiceSearch(focus = true) {
	if (recordVoiceSatate.value === "record") {
		recordVoiceSatate.value = "start";
		onEndRecordVoice();
	}
	if (voicePopup.value) voicePopup.value.close();
	if (focus) pageData.searchInputFocus = true;
}

async function onSartRecordVoice() {
	// #ifdef APP || MP-WEIXIN
	// #ifdef APP
	const status = await permision.checkPermission("record");
	// #endif
	// #ifdef MP-WEIXIN
	const status = true;
	// #endif

	if (!status) {
		uni.showModal({
			title: "授权提醒",
			content: "语音搜索需要您授权麦克风权限，是否继续？",
			success(res) {
				if (res.confirm) {
					permision.requestPermission("record");
				}
			},
		});
	} else {
		recordVoiceSatate.value = "record";
		recorderManager.start({
			duration: 10000,
			sampleRate: 16000,
			// #ifdef APP
			format: "mp3",
			numberOfChannels: 1,
			// #endif
			// #ifdef MP-WEIXIN
			format: "mp3",
			numberOfChannels: 1,
			// #endif
		});
	}
	// #endif
	// #ifdef H5
	recordVoiceSatate.value = "record";
	// #endif
}

async function onEndRecordVoice() {
	// #ifdef APP || MP-WEIXIN
	recordVoiceSatate.value = "loading";
	recorderManager.stop();
	// #endif

	// #ifdef H5
	recordVoiceSatate.value = "loading";

	await sleep(1000);

	recordVoiceText.value = "测试内容";
	recordVoiceSatate.value = "success";

	// recordVoiceSatate.value = 'error';
	// #endif
}

let audioUrl = "";
let audioDuration = 0;

async function recorderEnd(res) {
	canENV(() => {
		console.log("录音 -> ", res);
	});

	// #ifdef APP || MP-WEIXIN
	recordVoiceSatate.value = "loading";
	// #endif

	audioUrl = res.tempFilePath;

	ocrRecordVoice(audioUrl, 0);
	// innerAudioContext.src = audioUrl

	// #ifdef APP
	// const fileInfo = await uni.getFileInfo({
	// 	filePath: audioUrl
	// });
	// ocrRecordVoice(audioUrl, fileInfo.size);
	// #endif

	// #ifdef MP-WEIXIN
	// const wxfsm = wx.getFileSystemManager();

	// const fileState = wxfsm.fstatSync({
	// 	fd: wxfsm.openSync({
	// 		filePath: audioUrl
	// 	})
	// });
	// ocrRecordVoice(audioUrl, fileState.size);
	// #endif
}

function recorderError() {
	// #ifdef APP || MP-WEIXIN
	recorderManager.stop();
	// #endif
}

async function ocrRecordVoice(fileUrl, duration = 0) {
	if (recordVoiceSatate.value === "loading") {
		if (!fileUrl) {
			recordVoiceSatate.value = "error";
			return;
		}

		// upload(fileUrl).then((res) => {
		// 	console.log(res);
		// });

		const params = {
			// #ifdef APP
			format: "MP3",
			// #endif
			// #ifdef MP-WEIXIN
			format: "MP3",
			// #endif
			sampleRate: 16000,
			sample_rate: 16000,
			enable_inverse_text_normalization: true,
			enable_voice_detection: true,
			disfluency: true,
		};

		try {
			const res = await speechToText(fileUrl, {
				formData: params,
			});
			canENV(() => {
				console.log("识别结果 -> ", res, { fileUrl, params });
			});

			if (res.apiStatus) {
				if (res.data && res.data.name) {
					recordVoiceText.value = res.data.name;
					recordVoiceSatate.value = "success";
					await sleep(800);
					submitRecordVoiceSearch();
				} else {
					recordErrorText.value = "未识别到内容，请点击重试";
					recordVoiceSatate.value = "error";
				}
				return;
			}
		} catch (error) {
			//TODO handle the exception
		}

		recordVoiceSatate.value = "error";
	}
}

function audioOnCanplay(e) {
	audioDuration = innerAudioContext.duration;
	ocrRecordVoice(audioUrl, audioDuration);
}

onReachBottom(() => {
	if (pageData.status === "loadmore") {
		loadData();
	}
});

// #ifndef H5
uni.onKeyboardHeightChange((res) => {
	keyboardHeight.value = res.height;
	if (keyboardHeight.value > 0) {
		closeVoiceSearch(false);
	}
});
// #endif

watch(
	historySearchList,
	() => {
		uni.setStorageSync("historySearchList", historySearchList.value);
	},
	{
		deep: true,
	}
);

const voiceAnimationBox = ref([]);

function loadVoiceAnimation() {
	const boxW = 8;
	const boxH = 74;
	const hStep = boxH / 10;
	const heightScale = [
		1, 1, 1, 1,

		2, 4, 5.5, 3.5,

		2, 3.5, 4, 5,

		10,

		5, 4, 3.5, 2,

		3.5, 5.5, 4, 2,

		1, 1, 1, 1,
	];

	const initHeightArr = [];

	for (let i of heightScale) {
		initHeightArr.push(Math.max(hStep * i, boxW));
	}

	for (let i = 0; i < 25; i += 1) {
		voiceAnimationBox.value.push({
			w: `${boxW}rpx`,
			// rd: `${initHeightArr[i] / 2}rpx`,
			rd: `${boxW / 2}rpx`,
			h: `${initHeightArr[i]}rpx`,
		});
	}
}

function submitRecordVoiceSearch() {
	searchKeyword.value = recordVoiceText.value;
	closeVoiceSearch(false);
	loadData(true);
}

onLoad((options) => {
	loadHotSearch();
	loadHistorySearch();
	loadVoiceAnimation();
});

function readData() {
	// #ifdef MP-WEIXIN
	let menuButtonInfo = uni.getMenuButtonBoundingClientRect();

	searchHeight.value = menuButtonInfo.height;
	// #endif
	searchHeight.value = uni.upx2px(74);

	// openVoiceSearch()
}

onShow(() => {
	loadHistorySearch();
});

onReady(() => {
	readData();

	if (recorderManager && typeof recorderManager.onStop === "function") recorderManager.onStop(recorderEnd);
	if (recorderManager && typeof recorderManager.onError === "function") recorderManager.onError(recorderError);

	if (innerAudioContext && typeof innerAudioContext.onCanplay === "function") innerAudioContext.onCanplay(audioOnCanplay);
});

onUnload(() => {
	if (recorderManager && typeof recorderManager.offStop === "function") recorderManager.offStop(recorderEnd);
	if (recorderManager && typeof recorderManager.offError === "function") recorderManager.offError(recorderError);

	if (innerAudioContext && typeof innerAudioContext.offCanplay === "function") innerAudioContext.offCanplay(audioOnCanplay);
});

function upx2px(rpx) {
	return uni.upx2px(rpx);
}
</script>

<template>
	<app-layout :class="keyboardHeight > 0 ? 'no-scroll' : ''">
		<view class="search-user page-main bg-#F0F3F7 flex flex-col" :class="keyboardHeight > 0 ? 'no-scroll' : ''">
			<!-- <view class="w-full"> -->
			<app-navBar back bg-color="#fff" :showRight="false" :fixed="true" ref="navRef">
				<template #content>
					<view class="w-full h-full flex flex-center">
						<view class="w-full flex items-center pt-0 pl-0 pr-35">
							<view class="flex-1">
								<view class="flex items-center justify-between border-rd-100 border-1 border-solid border-#09C1B1 bg-#fff pl-20" :style="{ height: `${searchHeight}px`, paddingRight: `${(searchHeight - upx2px(56 + 2 + 4)) / 2}px` }">
									<view class="flex items-center flex-1">
										<app-image :src="common.iconSousuoHui" mode="widthFix" size="26"></app-image>
										<view class="text-26 font-500 ml-10 flex-1">
											<input class="text-26" type="text" placeholder="请输入搜索内容" placeholder-class="text-#999999" v-model="searchKeyword" :focus="pageData.searchInputFocus" @blur="() => (pageData.searchInputFocus = false)" @input="changeSearchKeyword" />
										</view>
									</view>
									<view class="px-26">
										<view class="w-28 h-28">
											<app-image :src="common.iconClearHui" size="28rpx" v-show="isSearchStatus" @click="clearSearchKeyword"></app-image>
										</view>
									</view>
									<view class="w-120 h-56 b-rd-28 bg-#00B496 text-#fff text-26 font-500 flex flex-center ac-op" @click="loadData(true)">搜索</view>
								</view>
							</view>
						</view>
					</view>
				</template>

				<template #footer>
					<view class="w-full h-82 bg-#fff" style="border-radius: 20rpx 20rpx 0rpx 0rpx" v-if="isSearchStatus">
						<view class="w-full h-1 bg-#EDEEF0"></view>
						<view class="w-full h-full flex items-center justify-between font-500 text-28 text-#323232 px-30">
							<view class="" :class="enumSortIndex === 0 && 'text-main'" @click="changeEnumSort(0)">综合</view>
							<view class="" :class="enumSortIndex === 1 && 'text-main'" @click="changeEnumSort(1)">销量</view>
							<view class="" :class="enumSortIndex === 2 && 'text-main'" @click="changeEnumSort(2)">新品</view>
							<template v-if="enumSortIndex !== 3 && enumSortIndex !== 4">
								<view class="flex items-center" @click="changeEnumSort(3)">
									<view class="">价格</view>
									<view class="flex flex-col flex-center ml-5 pt-5">
										<app-image :src="common.iconPaixunWu" width="20" mode="widthFix"></app-image>
									</view>
								</view>
							</template>
							<template v-if="enumSortIndex === 3">
								<view class="flex items-center" @click="changeEnumSort(4)">
									<view class="text-main">价格</view>
									<view class="flex flex-col flex-center ml-5 pt-5">
										<app-image :src="common.iconPaixuShengxu" width="20" mode="widthFix"></app-image>
									</view>
								</view>
							</template>
							<template v-if="enumSortIndex === 4">
								<view class="flex items-center" @click="changeEnumSort(3)">
									<view class="text-main">价格</view>
									<view class="flex flex-col flex-center ml-5 pt-5">
										<app-image :src="common.iconPaixuJiangxu" width="20" mode="widthFix"></app-image>
									</view>
								</view>
							</template>
						</view>
					</view>
				</template>
			</app-navBar>
			<!-- </view> -->

			<template v-if="searchKeyword.length === 0 && !isSearchStatus">
				<view class="w-full h-full bg-#fff flex-1">
					<view class="px-30 py-50">
						<view class="hot-search-box" v-if="historySearchList.length > 0">
							<view class="hot-search-title">
								<view class="flex items-center">
									<!-- <view class="w-6 h-24 border-rd-2 bg-main mr-15 mt-8"></view> -->
									<view class="hot-search-title-text">历史搜索</view>
								</view>

								<view class="">
									<app-image :src="common.iconDelHui" size="24" mode="" @click="clearHistoryItem()"></app-image>
								</view>
							</view>
							<view class="hot-search-content">
								<template v-for="(item, index) in historySearchList" :key="index">
									<view class="hot-search-item" v-if="item.text">
										<view class="bg-#F5F6FA pt-12 pl-15 pr-15 pb-12 border-rd-10" @click="clickSearchItem(item)" @close="delHistoryItem(index)">
											<view class="text-24 font-500 text-#333333">
												{{ item.text }}
											</view>
										</view>
									</view>
								</template>
							</view>
						</view>
						<view class="hot-search-box" v-if="hotSearchList.length > 0">
							<view class="hot-search-title">
								<view class="flex items-center">
									<view class="w-6 h-24 border-rd-2 bg-main mr-15 mt-8"></view>
									<view class="hot-search-title-text">热门搜索</view>
								</view>
							</view>
							<view class="hot-search-content">
								<template v-for="(item, index) in hotSearchList" :key="index">
									<view class="hot-search-item" v-if="item.text">
										<view class="bg-#F5F6FA pt-12 pl-15 pr-15 pb-12 border-rd-10" @click="clickSearchItem(item)" @close="delHistoryItem(index)">
											<view class="text-24 font-500 text-#333333">
												{{ item.text }}
											</view>
										</view>
									</view>
								</template>
							</view>
						</view>
					</view>
				</view>
			</template>

			<template v-if="searchKeyword.length > 0 && !isSearchStatus">
				<view class="w-full h-full bg-#fff flex-1">
					<uv-cell-group>
						<template v-for="(suggest, index) in pageData.searchSuggestList" :key="index">
							<uv-cell :title="suggest.productName" @click="selectSuggest(suggest)"></uv-cell>
						</template>
					</uv-cell-group>
				</view>
			</template>

			<template v-if="isSearchStatus">
				<view class="w-full py-0">
					<view class="px-10 py-20 flex flex-wrap">
						<template v-for="(item, index) in searchList" :key="index">
							<view class="mb-20 w-345 mx-10">
								<!-- 普通商城 -->
								<template v-if="item.serveTypeFename === 'shop'">
									<page-product-card-item :item="item" :index="index"></page-product-card-item>
								</template>

								<!-- 家政服务 -->
								<template v-if="item.serveTypeFename === 'homemaking'">
									<page-jiazheng-card-item :item="item" :index="index"></page-jiazheng-card-item>
								</template>

								<!-- 陪诊服务 -->
								<template v-if="item.serveTypeFename === 'attend'">
									<page-peizhen-card-item :item="item" :index="index"></page-peizhen-card-item>
								</template>

								<!-- 护理服务 -->
								<template v-if="item.serveTypeFename === 'nurse'">
									<page-huli-card-item :item="item" :index="index"></page-huli-card-item>
								</template>
							</view>
						</template>

						<template v-if="false">
							<uv-waterfall ref="waterfall" v-model="searchList" :add-time="100" :columnCount="2" left-gap="0rpx" right-gap="0rpx" column-gap="20rpx" @changeList="changeList">
								<template #list1>
									<view>
										<template v-for="(item, index) in pageData.list1" :key="index">
											<view class="mb-20">
												<!-- 普通商城 -->
												<template v-if="item.serveTypeFename === 'shop'">
													<page-product-card-item :item="item" :index="index"></page-product-card-item>
												</template>

												<!-- 家政服务 -->
												<template v-if="item.serveTypeFename === 'homemaking'">
													<page-jiazheng-card-item :item="item" :index="index"></page-jiazheng-card-item>
												</template>

												<!-- 陪诊服务 -->
												<template v-if="item.serveTypeFename === 'attend'">
													<page-peizhen-card-item :item="item" :index="index"></page-peizhen-card-item>
												</template>

												<!-- 护理服务 -->
												<template v-if="item.serveTypeFename === 'nurse'">
													<page-huli-card-item :item="item" :index="index"></page-huli-card-item>
												</template>
											</view>
										</template>
									</view>
								</template>
								<template #list2>
									<template v-for="(item, index) in pageData.list2" :key="index">
										<view class="mb-20">
											<!-- 普通商城 -->
											<template v-if="item.serveTypeFename === 'shop'">
												<page-product-card-item :item="item" :index="index"></page-product-card-item>
											</template>

											<!-- 家政服务 -->
											<template v-if="item.serveTypeFename === 'homemaking'">
												<page-jiazheng-card-item :item="item" :index="index"></page-jiazheng-card-item>
											</template>

											<!-- 陪诊服务 -->
											<template v-if="item.serveTypeFename === 'attend'">
												<page-peizhen-card-item :item="item" :index="index"></page-peizhen-card-item>
											</template>

											<!-- 护理服务 -->
											<template v-if="item.serveTypeFename === 'nurse'">
												<page-huli-card-item :item="item" :index="index"></page-huli-card-item>
											</template>
										</view>
									</template>
								</template>
							</uv-waterfall>
						</template>
					</view>
				</view>
			</template>
		</view>

		<view class="w-full py-14 px-34 flex border-solid border-x-0 border-t-1 border-b-0 border-#EDEEF0 fixed l-0 bg-#fff" :style="{ bottom: `${keyboardHeight}px` }" v-show="showVoiceSearchBtn">
			<view class="py-14 px-20 border-2 border-solid border-#E7E7E7 border-rd-31 flex items-center ac-op" @click="openVoiceSearch">
				<app-image src="@/pages/home/<USER>/icon_yuyinsousuo_lv.png" size="34" mr="13" mode=""></app-image>

				<view class="text-26 text-#333333 font-500">点击使用语音搜索</view>
			</view>
		</view>

		<uv-popup ref="voicePopup" mode="bottom" :overlay="true" :closeOnClickOverlay="false" :overlayStyle="{ background: 'rgba(0, 0, 0, 0.3)' }" bgColor="none" zIndex="9999999" :safeAreaInsetBottom="false">
			<view class="bd-box w-full border-solid border-x-0 border-t-1 border-b-0 border-#EDEEF0 bg-#fff pl-34 pr-34 pt-75 pb-50">
				<view class="w-full">
					<view class="flex flex-center font-bold text-36" :class="recordVoiceSatate === 'error' ? 'text-#959595' : 'text-#101010'">
						{{ recordVoiceTip }}
					</view>

					<view class="w-full bd-box px-50 mt-88">
						<view class="bd-box w-full h-98 border-rd-79 flex flex-center">
							<view class="bd-box w-full h-98 flex flex-center px-78 py-12 voice-animation-box border-rd-49" :class="[recordVoiceSatate === 'record' ? 'active' : 'norm']">
								<!-- <view class="audio-animation-box h-98"></view> -->

								<view class="bd-box w-full flex items-center justify-between h-full">
									<template v-for="(item, index) in voiceAnimationBox" :key="index">
										<view :style="{ width: item.w, height: item.h, borderRadius: item.rd }" class="audio-animation-item"></view>
									</template>
								</view>
							</view>
						</view>
					</view>

					<view class="flex mt-92 items-center justify-between">
						<view class="w-62 h-62">
							<app-image src="@/pages/home/<USER>/icon_jianpan_btn.png" size="62" @click="closeVoiceSearch(true)" mode=""></app-image>
						</view>

						<view class="">
							<template v-if="recordVoiceSatate === 'record'">
								<view class="w-130 h-130">
									<app-image src="@/pages/home/<USER>/icon_jieshuluyin_btn.png" @click="onEndRecordVoice" size="130" mode=""></app-image>
								</view>
							</template>
							<template v-else-if="recordVoiceSatate === 'loading'">
								<view class="w-130 h-130">
									<view class="w-130 h-130 border-rd-65 bg-#EDF0F2 flex flex-center">
										<view class="btn-loader loader-2 flex flex-center"></view>
									</view>
								</view>
							</template>
							<template v-else-if="recordVoiceSatate === 'success'">
								<view class="w-130 h-130">
									<app-image src="@/pages/home/<USER>/icon_jieshuluyin_btn.png" @click="submitRecordVoiceSearch" size="130" mode=""></app-image>
								</view>
							</template>
							<template v-else>
								<view class="w-130 h-130">
									<app-image src="@/pages/home/<USER>/icon_kaishiluyin_btn.png" @click="onSartRecordVoice" size="130" mode=""></app-image>
								</view>
							</template>
						</view>

						<view class="w-62 h-62"></view>
					</view>

					<view class="w-full flex flex-center mt-20 h-30">
						<view class="text-#888888 text-26 font-500">
							<template v-if="recordVoiceSatate === 'record'">
								<view>点击结束</view>
							</template>
						</view>
					</view>

					<app-safeAreaBottom></app-safeAreaBottom>
				</view>
			</view>
		</uv-popup>
	</app-layout>
</template>

<style scoped lang="scss">
.search-user {
}

.hot-search-box {
	width: 100%;
	margin: 0rpx 0rpx 0rpx;

	.hot-search-title {
		display: flex;
		align-items: center;
		justify-content: space-between;

		.hot-search-title-text {
			font-size: 30rpx;
			font-weight: bold;
			color: #101010;
		}
	}

	.hot-search-content {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		padding: 30rpx 0;

		.hot-search-item {
			margin: 0rpx 15rpx 20rpx 0;
		}
	}
}

.voice-animation-box {
	background-color: #edf0f2;

	.audio-animation-item {
		background-color: #00b496;
		transition: height 800ms;
	}

	&.active {
		background-color: #00b496;

		.audio-animation-item {
			background-color: #ffffff;
		}
	}
}

/* HTML: <div class="loader"></div> */
.loader {
	width: 90rpx;
	aspect-ratio: 6;
	--c: #00b496 90%, #0000;
	background: radial-gradient(circle closest-side at left 10rpx top 50%, var(--c)), radial-gradient(circle closest-side, var(--c)), radial-gradient(circle closest-side at right 10rpx top 50%, var(--c));
	background-size: 100% 100%;
	background-repeat: no-repeat;
	animation: l4 0.8s infinite alternate;
}
@keyframes l4 {
	to {
		width: 40rpx;
		aspect-ratio: 1;
	}
}

/* HTML: <div class="loader"></div> */
.loader-2 {
	color: #00b496;
	width: 6rpx;
	aspect-ratio: 1;
	border-radius: 50%;
	animation: l37-1 0.75s infinite linear alternate, l37-2 1.5s infinite linear;
}
@keyframes l37-1 {
	0%,
	20% {
		box-shadow: 30rpx 0 0 3rpx, 10rpx 0 0 3rpx, -10rpx 0 0 3rpx, -30rpx 0 0 3rpx;
	}
	60%,
	100% {
		box-shadow: 12rpx 0 0 3rpx, 14rpx 0 0 6rpx, -14rpx 0 0 6rpx, -12rpx 0 0 3rpx;
	}
}

@keyframes l37-2 {
	0%,
	25% {
		transform: rotate(0);
	}
	50%,
	100% {
		transform: rotate(0.5turn);
	}
}
</style>
