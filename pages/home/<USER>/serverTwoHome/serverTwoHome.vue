<script setup>
import { onLoad, onReady, onPageScroll, onReachBottom } from "@dcloudio/uni-app";

import { ref, reactive, nextTick, getCurrentInstance } from "vue";

import { goArticlePage, openWebView } from "@/common/utils";

import { getCategoryLevelFromPlatform, getTypeGoodsList } from "@/server/api";

import { useAppStore, usePublicStore, useUserStore } from "@/store";

const route = uni.$uv.route;

import useZPaging from "@/uni_modules/z-paging/components/z-paging/js/hooks/useZPaging.js";

import throttle from "@/uni_modules/uv-ui-tools/libs/function/throttle.js";

import { deepClone, sleep } from "@/uni_modules/uv-ui-tools/libs/function/index.js";
import { logo } from "@/common/images";

const userStore = useUserStore();

const pageHeight = ref(0);
const scrollable = ref(true);
const headerHeight = ref(9999);
const navHeader = ref(0);

const pagingRef = ref(null);
const tabs = ref(null);
const swiperItemListRef = ref();
const waterfall = ref(null);

useZPaging(pagingRef);

const searchHeight = ref(0);

const props = defineProps({
	categoryId: {
		type: [String, Number],
		required: true,
	},
});

const pageData = reactive({
	opacityValue: 0,
	searchTop: 0,
	searchWidth: 690,

	pageLoading: false,
	firstLoaded: false,

	typeList: [],

	dataList: [],

	list1: [],
	list2: [],
});

const kongList = ref([
	{
		icon: "@/pages/home/<USER>/huliHome/icon_changqihuli.png",
		name: "半天陪诊",
		data: {
			type: "goPage",
			url: "",
		},
	},
	{
		icon: "@/pages/home/<USER>/huliHome/icon_zhongchangqihuli.png",
		name: "全天陪诊",
		data: {
			url: "",
		},
	},
	{
		icon: "@/pages/home/<USER>/huliHome/icon_duanqihuli.png",
		name: "特需陪诊",
		data: {
			url: "",
		},
	},
]);

function clickKongItem(item) {}

function onscroll(e) {
	const scrollTop = e.detail.scrollTop;

	throttle(() => {
		pageData.opacityValue = scrollTop / 60;
	}, 10);
}

function changeList(e) {
	pageData[e.name].push(e.value);
}

async function queryList(pageNo, pageSize) {
	const params = {
		current: pageNo,
		pageNo: pageNo,
		pageSize: pageSize,
	};

	if (pageNo === 1) {
		pageData.dataList = [];
		pageData.list1 = [];
		pageData.list2 = [];

		if (waterfall.value) waterfall.value.clear();
	}

	pageData.pageLoading = true;

	let list = [];

	try {
		if (props.categoryId) {
			params.platformCategoryId = props.categoryId;
			params.area = userStore.userData.area[2] ? userStore.userData.area.join("/") : "";
			const listRes = await getTypeGoodsList(params);
			if (listRes.apiStatus) {
				list = listRes.data.records.map((i) => {
					if (!Number(i.serveTypeId)) {
						i.serveGoods = 0;
						i.serveTypeId = "0";
						i.serveTypeFename = "shop";
					}

					return i;
				});
			}
		}
	} catch (error) {
		//TODO handle the exception

		if (pagingRef.value) pagingRef.value.complete(false);

		pageData.firstLoaded = true;
		pageData.pageLoading = false;

		return;
	}

	if (pagingRef.value) pagingRef.value.complete(list);

	pageData.firstLoaded = true;
	pageData.pageLoading = false;

	readData(true);
}

function loadTypeList() {
	return new Promise((resolve, reject) => {
		getCategoryLevelFromPlatform({ current: 1, size: 999 })
			.then((res) => {
				let list = [];
				let childList = [];
				if (res.apiStatus) {
					res.data.records.map((i) => {
						// if (i.categoryType === 0) {
						list.push({
							...i,
						});
						// }
					});
				}

				if (props.categoryId) {
					const findIndex = list.findIndex((i) => i.id === props.categoryId);
					if (findIndex >= 0) {
						childList = list[findIndex].secondCategoryVos;
					}
				}
				pageData.typeList = childList;

				if (pagingRef.value) pagingRef.value.reload();

				resolve();
			})
			.catch((err) => {
				reject(err);
			});
	});
}

function loadData() {
	loadTypeList();
}

function readData() {
	// #ifdef MP-WEIXIN
	let menuButtonInfo = uni.getMenuButtonBoundingClientRect();

	searchHeight.value = menuButtonInfo.height;
	// #endif
	searchHeight.value = uni.upx2px(74);
}

function goToOrderList() {
	uni.switchTab({
		url: "/pages/tabBar/order/order",
	});
}

onReady(() => {
	readData();
});

onLoad(() => {
	loadData();
});
</script>
<template>
	<app-layout>
		<z-paging ref="pagingRef" @scroll="onscroll" :scrollable="scrollable" v-model="pageData.dataList" @query="queryList" :refresher-enabled="true" refresher-theme-style="black" :refresher-only="false" use-refresher-status-bar-placeholder :use-page-scroll="false" :auto="false">
			<view class="home_content relative">
				<app-image src="@/pages/home/<USER>/bg_item_home_top.png" class="absolute w-full" mode="widthFix"></app-image>
				<app-navBar :bgColor="`rgba(255,255,255,${pageData.opacityValue})`" height="88rpx" :showRight="false" back>
					<template #content class="w-full">
						<view class="relative w-full h-full flex justify-between items-center nav-box">
							<view class="absolute-n w-710-n w-full px-20-n pr-20 pb-16-n flex items-center box-border" :style="[`top: ${pageData.searchTop}rpx`, `background: rgba(255,255,255,${pageData.opacityValue})`]">
								<view class="flex justify-between items-center flex-1 h-74-n border-rd-37 bg-#fff px-8 border-solid border-1 border-#09C1B1 ac-op" :style="[`width: ${pageData.searchWidth}rpx`, `height: ${searchHeight}px`]" @click="route('/pages/home/<USER>/search/search')">
									<view class="flex justify-start items-center">
										<app-image class="w-26 h-26 ml-20" :src="common.iconSearchQianhui" size="26" mode=""></app-image>
										<!-- <text class="text-#98A2B4 text-28 font-500 ml-14">半天陪诊</text> -->
										<text class="text-#98A2B4 text-28 font-500 ml-14">请输入搜索内容</text>
									</view>
									<view class="flex justify-end items-center">
										<view class="w-126 h-58 bg-#00B496 border-rd-29 text-#fff text-28 font-bold flex justify-center items-center">搜索</view>
									</view>
								</view>

								<app-image src="@/pages/home/<USER>/icon_dingdan_hei.png" @click="goToOrderList" mode="" size="52" ml="20"></app-image>
							</view>
						</view>
					</template>
					<template #right></template>
				</app-navBar>

				<view class="w-full relative">
					<template v-if="pageData.typeList.length > 0">
						<view class="pl-20 pr-20 mt-24 relative">
							<view class="py-35 bg-#fff border-rd-20">
								<view class="grid gap-y-35rpx" :style="{ 'grid-template-columns': `repeat(${Math.min(Math.max(kongList.length, 1), 4)}, minmax(0, 1fr))` }">
									<template v-for="(item, index) in pageData.typeList" :key="index">
										<view class="flex flex-col items-center ac-op" @click="clickKongItem(item)">
											<app-image :src="item.categoryImg" size="88" mode=""></app-image>
											<view class="mt-15 text-center text-#343434 text-26">
												{{ item.name }}
											</view>
										</view>
									</template>
								</view>
							</view>
						</view>
					</template>

					<view class="px-20 mt-24 pb-0 flex items-center justify-between" v-if="false">
						<view class="flex-1 p-8 box-border mr-8 border-rd-20" style="background: linear-gradient(-30deg, #2cdee2 0%, #01c8a7 100%)">
							<view class="py-13 px-19 box-border">
								<view class="text-30 text-#fff font-bold">品牌馆</view>
							</view>

							<view class="flex items-center justify-between py-24 px-35 box-border border-rd-20 bg-#fff mt-0 h-180">
								<template v-for="i in 2" :key="i">
									<view class="flex flex-col">
										<view class="w-116 h-116 border-4 border-solid border-#C4FFF5 rd-100 flex flex-center">
											<app-image :src="logo" size="98" rd="50%" mode=""></app-image>
										</view>
										<view class="w-116 box-border relative">
											<view class="w-full h-34 bg-#00B698 uv-line-1 text-22 line-height-34 text-#fff font-500 flex flex-center border-rd-18 px-10" style="margin-top: -15rpx"> 商家商家 </view>
										</view>
									</view>
								</template>
							</view>
						</view>

						<view class="flex-1 p-8 box-border ml-8 border-rd-20" style="background: linear-gradient(-30deg, #2cdee2 0%, #01c8a7 100%)">
							<view class="py-13 px-19 box-border">
								<view class="text-30 text-#fff font-bold">服务推荐</view>
							</view>

							<view class="flex items-center justify-between py-24 px-20 box-border border-rd-20 bg-#fff mt-0 h-180">
								<template v-for="i in 2" :key="i">
									<view class="flex flex-col">
										<view class="w-134 h-100">
											<app-image :src="logo" width="134" height="100" rd="10" mode=""></app-image>
										</view>
										<view class="w-134">
											<view class="w-full text-18 text-#222222 font-500 uv-line-1">门诊陪诊-半天</view>
											<view class="w-full text-18 text-#FC3F33 font-500 uv-line-1 text-center">低至30元起</view>
										</view>
									</view>
								</template>
							</view>
						</view>
					</view>

					<view class="px-10 py-20 flex flex-wrap">
						<template v-for="(item, index) in pageData.dataList" :key="index">
							<view class="mb-20 w-345 mx-10">
								<!-- 普通商城 -->
								<template v-if="item.serveTypeFename === 'shop'">
									<page-product-card-item :item="item" :index="index"></page-product-card-item>
								</template>

								<!-- 家政服务 -->
								<template v-if="item.serveTypeFename === 'homemaking'">
									<page-jiazheng-card-item :item="item" :index="index"></page-jiazheng-card-item>
								</template>

								<!-- 陪诊服务 -->
								<template v-if="item.serveTypeFename === 'attend'">
									<page-peizhen-card-item :item="item" :index="index"></page-peizhen-card-item>
								</template>

								<!-- 护理服务 -->
								<template v-if="item.serveTypeFename === 'nurse'">
									<page-huli-card-item :item="item" :index="index"></page-huli-card-item>
								</template>
							</view>
						</template>

						<template v-if="false">
							<uv-waterfall
								ref="waterfall"
								v-model="pageData.dataList"
								:add-time="100"
								:columnCount="2"
								left-gap="0rpx"
								right-gap="0rpx"
								column-gap="20rpx"
								@changeList="changeList"
							>
								<template #list1>
									<view>
										<template v-for="(item, index) in pageData.list1" :key="index">
											<view class="mb-20">
												<!-- 普通商城 -->
												<template v-if="item.serveTypeFename === 'shop'">
													<page-product-card-item :item="item" :index="index"></page-product-card-item>
												</template>

												<!-- 家政服务 -->
												<template v-if="item.serveTypeFename === 'homemaking'">
													<page-jiazheng-card-item :item="item" :index="index"></page-jiazheng-card-item>
												</template>

												<!-- 陪诊服务 -->
												<template v-if="item.serveTypeFename === 'attend'">
													<page-peizhen-card-item :item="item" :index="index"></page-peizhen-card-item>
												</template>

												<!-- 护理服务 -->
												<template v-if="item.serveTypeFename === 'nurse'">
													<page-huli-card-item :item="item" :index="index"></page-huli-card-item>
												</template>
											</view>
										</template>
									</view>
								</template>
								<template #list2>
									<template v-for="(item, index) in pageData.list2" :key="index">
										<view class="mb-20">
											<!-- 普通商城 -->
											<template v-if="item.serveTypeFename === 'shop'">
												<page-product-card-item :item="item" :index="index"></page-product-card-item>
											</template>

											<!-- 家政服务 -->
											<template v-if="item.serveTypeFename === 'homemaking'">
												<page-jiazheng-card-item :item="item" :index="index"></page-jiazheng-card-item>
											</template>

											<!-- 陪诊服务 -->
											<template v-if="item.serveTypeFename === 'attend'">
												<page-peizhen-card-item :item="item" :index="index"></page-peizhen-card-item>
											</template>

											<!-- 护理服务 -->
											<template v-if="item.serveTypeFename === 'nurse'">
												<page-huli-card-item :item="item" :index="index"></page-huli-card-item>
											</template>
										</view>
									</template>
								</template>
							</uv-waterfall>
						</template>

						<uv-load-more :status="'loading'" v-show="pageData.pageLoading && !pageData.firstLoaded" />
					</view>
				</view>
			</view>
		</z-paging>
	</app-layout>
</template>

<style>
page {
	background-color: #f0f3f7;
}
</style>

<style lang="scss" scoped>
.page-body {
	width: 100%;
	min-height: 100vh;
	background-color: #f0f3f7;
}

.home_content {
	// background-repeat: no-repeat;
	// background-size: 100% 521rpx;
}

.paging-content {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.swiper {
	flex: 1;
}
</style>
