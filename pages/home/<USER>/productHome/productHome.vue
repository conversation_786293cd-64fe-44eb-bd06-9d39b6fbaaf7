<script setup>
import { onLoad, onReady } from '@dcloudio/uni-app'

import { ref, reactive, nextTick, getCurrentInstance } from 'vue'

import { goArticlePage, openWebView } from '@/common/utils'

import { getBannerList, getBannerDetails, getCategoryLevelFromPlatform, getTypeGoodsList, getWinnowGoodsList } from '@/server/api'

import { useAppStore, usePublicStore, useUserStore } from '@/store'

const route = uni.$uv.route

import useZPaging from '@/uni_modules/z-paging/components/z-paging/js/hooks/useZPaging.js'

import { deepClone, sleep } from '@/uni_modules/uv-ui-tools/libs/function/index.js'
import { common, logo } from '@/common/images'
import SortOrder from '@/components/SortOrder/SortOrder.vue'
import { SORT_TYPE } from '@/common/types/goods'

const userStore = useUserStore()

const pageHeight = ref(0)
const scrollable = ref(true)
const headerHeight = ref(9999)
const navHeader = ref(0)

const pagingRef = ref(null)
const tabs = ref(null)
const swiperItemListRef = ref()

const waterfall = ref(null)

useZPaging(pagingRef)

const searchHeight = ref(0)

const popupTypesRef = ref()

const props = defineProps({
    categoryId: {
        type: [String, Number],
        default: '',
    },
})

const pageData = reactive({
    opacityValue: 0,
    searchTop: 0,
    searchWidth: 690,

    scrollTop: 0,

    typePopupTop: 0,

    swiperList: [],

    typeList: [],
    typeIndex: 0,

    pageLoading: false,
    firstLoaded: false,

    dataList: [],

    list1: [],
    list2: [],
})
const sortBarOpacity = ref(0)
function onscroll(e) {
    const scrollTop = e.detail.scrollTop

    // ortBarOpacity计算
    const triggerPoint = pageData.typePopupTop - 50 // 提前50px开始显示
    if (scrollTop > triggerPoint) {
        // 渐进式透明度变化
        const fadeDistance = 50 // 50px的渐变距离
        const progress = Math.min((scrollTop - triggerPoint) / fadeDistance, 1)
        sortBarOpacity.value = progress
    } else {
        sortBarOpacity.value = 0
    }

    pageData.scrollTop = scrollTop

    // throttle(() => {
    if (popupTypesOpen.value) return
    pageData.opacityValue = scrollTop / 60

    // }, 10);
}

function clickSwiperItem(index) {
    const item = pageData.swiperList[index]

    if (item.skipType === 'content') {
        goArticlePage('advertising', item.id)
        return
    }

    if (item.skipType === 'link') {
        const url = String(item.skipUrl)
        if (url) {
            if (url.startsWith('http://') || url.startsWith('https://')) {
                openWebView(url, item.title || '')
            } else {
                route(url)
            }
        }
    }
}

function changeTypeList(e) {
    if (pageData.typeIndex === e.index) return
    pageData.typeIndex = e.index
    // 使用refreshToPage(1)并清空数据，避免滚动到顶部
    if (pagingRef.value) {
        pageData.dataList = []
        pagingRef.value.refreshToPage(1)
    }

    if (popupTypesOpen.value) {
        if (popupTypesRef.value) popupTypesRef.value.close()
    }
}

const olNavOpacityValue = ref()
const popupTypesOpen = ref(false)
function openTypeList() {
    if (!popupTypesOpen.value) {
        olNavOpacityValue.value = pageData.opacityValue
        pageData.opacityValue = 1
        if (popupTypesRef.value) popupTypesRef.value.open()
    } else {
        if (popupTypesRef.value) popupTypesRef.value.close()
    }
}
async function changePopupTypes(e) {
    if (!e.show) {
        await sleep(300)
        popupTypesOpen.value = e.show
        pageData.opacityValue = olNavOpacityValue.value
        if (pageData.searchTop === 0) {
            pageData.opacityValue = 0
        }
    } else {
        popupTypesOpen.value = e.show
    }
}

function changeList(e) {
    pageData[e.name].push(e.value)
}
const activeSortType = ref(SORT_TYPE.all)
const activeSortOrder = ref()
const changeSortType = ({ sortOrder, sortType }) => {
    activeSortType.value = sortType
    activeSortOrder.value = sortOrder
    pagingRef.value.refreshToPage(1)
}
async function queryList(pageNo, pageSize) {
    const params = {
        current: pageNo,
        pageNo: pageNo,
        pageSize: pageSize,
        sortType: activeSortType.value,
        sortOrder: activeSortOrder.value,
    }

    if (pageNo === 1) {
        pageData.dataList = []
        pageData.list1 = []
        pageData.list2 = []

        if (waterfall.value) waterfall.value.clear()
    }

    pageData.pageLoading = true

    const typeItem = pageData.typeList[pageData.typeIndex]

    let list = []

    try {
        if (typeItem.id) {
            params.platformCategoryId = typeItem.id
            // params.area = userStore.userData.area[2] ? userStore.userData.area.join('/') : '';
            const listRes = await getTypeGoodsList(params)
            if (listRes.apiStatus) {
                list = listRes.data.records.map((i) => {
                    if (!Number(i.serveTypeId)) {
                        i.serveGoods = 0
                        i.serveTypeId = '0'
                        i.serveTypeFename = 'shop'
                    }

                    return i
                })
            }
        } else {
            params.serverGoods = 0
            params.choice = 1
            const listRes = await getWinnowGoodsList(params)
            if (listRes.apiStatus) {
                list = listRes.data.records.map((i) => {
                    if (!Number(i.serveTypeId)) {
                        i.serveGoods = 0
                        i.serveTypeId = '0'
                        i.serveTypeFename = 'shop'
                    }

                    return i
                })
            }
        }
    } catch (error) {
        //TODO handle the exception

        if (pagingRef.value) pagingRef.value.complete(false)

        pageData.firstLoaded = true
        pageData.pageLoading = false

        return
    }

    if (pagingRef.value) pagingRef.value.complete(list)

    pageData.firstLoaded = true
    pageData.pageLoading = false

    readData(true)
}

async function loadData() {
    loadBannerList()
    loadTypeList()
}

function loadBannerList() {
    return new Promise((resolve, reject) => {
        // 获取 Banner
        getBannerList({
            current: 1,
            size: 10,
            status: 'STATUSN', // STATUSY -> 是 | STATUSN -> 否
            type: 'SHOPBANNER', // IbANNER -> 首页轮播 | INDRE -> 首页招募图 | SHOPBANNER -> 无忧商城轮播图
        })
            .then((res) => {
                if (res.apiStatus) {
                    pageData.swiperList = res.data.records
                }
                resolve()
            })
            .catch((err) => {
                reject(err)
            })
    })
}

function loadTypeList() {
    return new Promise((resolve, reject) => {
        getCategoryLevelFromPlatform({ current: 1, size: 999 })
            .then((res) => {
                let list = [
                    {
                        name: '推荐',
                        id: 0,
                    },
                ]
                if (res.apiStatus) {
                    res.data.records.map((i) => {
                        if (i.categoryType === 0) {
                            list.push({
                                ...i,
                            })
                        }
                    })
                }
                if (props.categoryId) {
                    const findIndex = list.findIndex((i) => i.id === props.categoryId)
                    if (findIndex >= 0) {
                        pageData.typeIndex = findIndex
                    }
                }
                pageData.typeList = list

                if (pagingRef.value) pagingRef.value.reload()

                resolve()
            })
            .catch((err) => {
                reject(err)
            })
    })
}

const instance = getCurrentInstance()
const query = uni.createSelectorQuery().in(instance.proxy)
function readData(loadType = false) {
    // #ifdef MP-WEIXIN
    let menuButtonInfo = uni.getMenuButtonBoundingClientRect()

    searchHeight.value = menuButtonInfo.height
    // #endif
    searchHeight.value = uni.upx2px(74)

    nextTick(() => {
        if (loadType) {
            query
                .select('#myTabsBox')
                .boundingClientRect((data) => {
                    pageData.typePopupTop = data.bottom
                })
                .exec()
        }
    })
}

function canICallBack(cb, type = '') {
    if (userStore.checkLogin) {
        if (typeof cb === 'function') cb()
    } else {
        useAppStore().changeShowLoginModal(true)
    }
}

onReady(() => {
    readData()
})

onLoad(() => {
    loadData()
})
</script>
<template>
    <app-layout>
        <z-paging
            ref="pagingRef"
            @scroll="onscroll"
            :scrollable="scrollable"
            v-model="pageData.dataList"
            @query="queryList"
            :refresher-enabled="true"
            refresher-theme-style="black"
            :refresher-only="false"
            use-refresher-status-bar-placeholder
            :use-page-scroll="false"
            :auto="false"
        >
            <view class="home_content relative">
                <app-image src="@/pages/home/<USER>/bg_shangcheng_header.png" class="absolute w-full" mode="widthFix"></app-image>

                <app-navBar
                    :bgColor="`rgba(5, 201, 172,${pageData.opacityValue})`"
                    :height="`${searchHeight}px`"
                    back
                    :backIcon="common.iconLeftBai"
                    style="z-index: 10078"
                    zIndex="10078"
                    rightPlaceholderSize="80rpx"
                >
                    <template #content class="w-full">
                        <view class="relative w-full h-full flex justify-between items-center nav-box">
                            <view
                                class="absolute-n w-710-n w-full px-20-n pr-20 pb-16-n flex items-center justify-between box-border"
                                :style="[`top: ${pageData.searchTop}rpx`, `background: rgba(5, 201, 172,${pageData.opacityValue})`]"
                            >
                                <view class="text-34 text-#fff font-bold flex-1 nav-center">无忧商城</view>
                            </view>
                        </view>
                    </template>
                    <template #right>
                        <view class="">
                            <app-image
                                :src="common.iconCarBai"
                                mode=""
                                size="40"
                                @click="canICallBack(() => route('/pages/goods/pages/shoppingCart/shoppingCart'))"
                            ></app-image>
                        </view>
                    </template>
                    <template #footer>
                        <view class="w-full box-border">
                            <view class="w-full flex flex-center px-20 pt-10 box-border">
                                <view
                                    class="flex justify-between items-center flex-1 h-74-n border-rd-37 bg-#fff px-8 border-solid border-1 border-#09C1B1 ac-op"
                                    :style="[`width: ${pageData.searchWidth}rpx`, `height: ${searchHeight}px`]"
                                    @click="route('/pages/home/<USER>/search/search')"
                                >
                                    <view class="flex justify-start items-center">
                                        <app-image class="w-26 h-26 ml-20" :src="common.iconSearchQianhui" size="26" mode=""></app-image>
                                        <text class="text-#98A2B4 text-28 font-500 ml-14">热门搜索</text>
                                    </view>
                                    <view class="flex justify-end items-center pr-18">
                                        <!-- <view
											class="w-126 h-58 bg-#00B496 border-rd-29 text-#fff text-28 font-bold flex justify-center items-center">
											搜索
										</view> -->

                                        <!-- <app-image src="@/pages/home/<USER>/icon_paizhao_hei.png" size="40" mode=""></app-image> -->

                                        <!-- <view class="w-2 h-34 mx-16" style="background: linear-gradient(0deg, #ffffff 0%, #eeeced 35%, #eeeced 68%, #ffffff 100%)"></view> -->

                                        <!-- <app-image src="@/pages/home/<USER>/icon_saoma_hei.png" size="40" mode=""></app-image> -->
                                    </view>
                                </view>
                            </view>

                            <view class="w-full px-20 my-tabs-box box-border" id="myTabsBox">
                                <uv-tabs
                                    :list="pageData.typeList"
                                    :current="pageData.typeIndex"
                                    :activeStyle="{
                                        height: '31rpx',
                                        'font-weight': 'bold',
                                        'font-size': '32rpx',
                                        color: '#FFFFFF',
                                    }"
                                    :inactiveStyle="{
                                        height: '31rpx',
                                        'font-weight': '500',
                                        'font-size': '30rpx',
                                        color: '#FFFFFF',
                                    }"
                                    lineWidth="50rpx"
                                    lineHeight="10rpx"
                                    :lineColor="'#009686'"
                                    @change="changeTypeList"
                                >
                                    <template #right>
                                        <template v-if="pageData.typeList.length > 3 && pageData.typePopupTop > 0">
                                            <view class="w-64 h-42 flex flex-center pt-10">
                                                <!-- <app-image src="@/pages/home/<USER>/icon_nav_caidan_bai.png" width="64" height="42"
													mode=""></app-image> -->
                                                <uv-icon name="list" size="42rpx" color="#fff" bold @click="openTypeList"></uv-icon>
                                            </view>
                                        </template>
                                    </template>
                                </uv-tabs>
                            </view>
                        </view>
                    </template>
                </app-navBar>

                <uv-popup
                    ref="popupTypesRef"
                    mode="top"
                    :customStyle="{ top: `${pageData.typePopupTop}px` }"
                    bg-color="#F1F3F7"
                    round="20rpx"
                    @change="changePopupTypes"
                    :safeAreaInsetBottom="false"
                >
                    <view class="w-full px-30 pt-24 mb-34">
                        <view class="w-full grid grid-cols-3 gap-20">
                            <template v-for="(item, index) in pageData.typeList" :key="index">
                                <view
                                    class="w-full h-66 bg-#fff flex flex-center font-500 text-26 border-rd-10 ac-op"
                                    :class="index === pageData.typeIndex && 'text-main'"
                                    @click="changeTypeList({ index })"
                                >
                                    {{ item.name }}
                                </view>
                            </template>
                        </view>
                    </view>
                </uv-popup>

                <view class="px-20">
                    <view class="mt-20">
                        <uv-swiper
                            :list="pageData.swiperList"
                            keyName="image"
                            @click="clickSwiperItem"
                            indicatorMode="dot"
                            height="320rpx"
                            radius="20rpx"
                            indicator
                            circular
                        ></uv-swiper>
                    </view>
                </view>

                <!-- 排序组件容器 - 优化sticky定位 -->

                <SortOrder
                    @onChange="changeSortType"
                    class="sticky z-99999"
                    :style="{
                        top: `${pageData.typePopupTop}px`,
                        background: `rgba(255, 255, 255, ${sortBarOpacity})`,
                    }"
                />

                <!-- 推荐商品 -->
                <view class="px-20 mt-24 pb-0 flex items-center justify-between relative" v-if="false">
                    <view class="flex-1 p-0 box-border mr-9 border-rd-20 bg-#fff">
                        <view
                            class="px-18 pt-24 pb-20 box-border flex items-center"
                            style="background: linear-gradient(180deg, #b8f2e9 0%, #ffffff 100%); border-radius: 20rpx 20rpx 0 0"
                        >
                            <view class="text-30 text-#222222 font-bold">标题标题</view>
                            <view
                                class="h-34 bg-#00B698 uv-line-1 text-22 line-height-34 text-#fff font-500 flex flex-center border-rd-18 px-10 ml-10"
                                >精选HOT</view
                            >
                        </view>

                        <view class="flex items-center justify-between pt-0 pb-24 px-20 box-border border-rd-20 bg-#fff mt-0 h-250">
                            <template v-for="i in 2" :key="i">
                                <view class="flex flex-col">
                                    <view class="w-150 h-150">
                                        <app-image :src="logo" width="150" height="150" rd="10" mode=""></app-image>
                                    </view>
                                    <view class="w-150 mt-8">
                                        <view class="w-full text-28 text-#222222 font-500 uv-line-1 text-center">护腰带</view>
                                        <view class="flex items-end justify-center">
                                            <view class="text-24 text-#FC3F33 font-500 uv-line-1 text-center pb-4">￥</view>
                                            <view class="text-34 text-#FC3F33 font-500 uv-line-1 text-center">299</view>
                                        </view>
                                    </view>
                                </view>
                            </template>
                        </view>
                    </view>

                    <!-- 推荐商品 -->
                    <view class="flex-1 p-0 box-border ml-9 border-rd-20 bg-#fff">
                        <view
                            class="px-18 pt-24 pb-20 box-border flex items-center"
                            style="background: linear-gradient(180deg, #b8f2e9 0%, #ffffff 100%); border-radius: 20rpx 20rpx 0 0"
                        >
                            <view class="text-30 text-#222222 font-bold">标题标题</view>
                            <view
                                class="h-34 bg-#00B698 uv-line-1 text-22 line-height-34 text-#fff font-500 flex flex-center border-rd-18 px-10 ml-10"
                                >精选HOT</view
                            >
                        </view>

                        <view class="flex items-center justify-between pt-0 pb-24 px-20 box-border border-rd-20 bg-#fff mt-0 h-250">
                            <template v-for="i in 2" :key="i">
                                <view class="flex flex-col">
                                    <view class="w-150 h-150">
                                        <app-image :src="logo" width="150" height="150" rd="10" mode=""></app-image>
                                    </view>
                                    <view class="w-150 mt-8">
                                        <view class="w-full text-28 text-#222222 font-500 uv-line-1 text-center">鼻子喷雾剂</view>
                                        <view class="flex items-end justify-center">
                                            <view class="text-24 text-#FC3F33 font-500 uv-line-1 text-center pb-4">￥</view>
                                            <view class="text-34 text-#FC3F33 font-500 uv-line-1 text-center">12.9</view>
                                        </view>
                                    </view>
                                </view>
                            </template>
                        </view>
                    </view>
                </view>

                <view class="px-10 py-20 flex flex-wrap">
                    <template v-for="(item, index) in pageData.dataList" :key="index">
                        <view class="mb-20 w-345 mx-10">
                            <!-- 普通商城 -->
                            <template v-if="item.serveTypeFename === 'shop'">
                                <page-product-card-item :item="item" :index="index"></page-product-card-item>
                            </template>

                            <!-- 家政服务 -->
                            <template v-if="item.serveTypeFename === 'homemaking'">
                                <page-jiazheng-card-item :item="item" :index="index"></page-jiazheng-card-item>
                            </template>

                            <!-- 陪诊服务 -->
                            <template v-if="item.serveTypeFename === 'attend'">
                                <page-peizhen-card-item :item="item" :index="index"></page-peizhen-card-item>
                            </template>

                            <!-- 护理服务 -->
                            <template v-if="item.serveTypeFename === 'nurse'">
                                <page-huli-card-item :item="item" :index="index"></page-huli-card-item>
                            </template>
                        </view>
                    </template>

                    <template v-if="false">
                        <uv-waterfall
                            ref="waterfall"
                            v-model="pageData.dataList"
                            :add-time="100"
                            :columnCount="2"
                            left-gap="0rpx"
                            right-gap="0rpx"
                            column-gap="20rpx"
                            @changeList="changeList"
                        >
                            <template #list1>
                                <view>
                                    <template v-for="(item, index) in pageData.list1" :key="index">
                                        <view class="mb-20">
                                            <!-- 普通商城 -->
                                            <template v-if="item.serveTypeFename === 'shop'">
                                                <page-product-card-item :item="item" :index="index"></page-product-card-item>
                                            </template>

                                            <!-- 家政服务 -->
                                            <template v-if="item.serveTypeFename === 'homemaking'">
                                                <page-jiazheng-card-item :item="item" :index="index"></page-jiazheng-card-item>
                                            </template>

                                            <!-- 陪诊服务 -->
                                            <template v-if="item.serveTypeFename === 'attend'">
                                                <page-peizhen-card-item :item="item" :index="index"></page-peizhen-card-item>
                                            </template>

                                            <!-- 护理服务 -->
                                            <template v-if="item.serveTypeFename === 'nurse'">
                                                <page-huli-card-item :item="item" :index="index"></page-huli-card-item>
                                            </template>
                                        </view>
                                    </template>
                                </view>
                            </template>
                            <template #list2>
                                <template v-for="(item, index) in pageData.list2" :key="index">
                                    <view class="mb-20">
                                        <!-- 普通商城 -->
                                        <template v-if="item.serveTypeFename === 'shop'">
                                            <page-product-card-item :item="item" :index="index"></page-product-card-item>
                                        </template>

                                        <!-- 家政服务 -->
                                        <template v-if="item.serveTypeFename === 'homemaking'">
                                            <page-jiazheng-card-item :item="item" :index="index"></page-jiazheng-card-item>
                                        </template>

                                        <!-- 陪诊服务 -->
                                        <template v-if="item.serveTypeFename === 'attend'">
                                            <page-peizhen-card-item :item="item" :index="index"></page-peizhen-card-item>
                                        </template>

                                        <!-- 护理服务 -->
                                        <template v-if="item.serveTypeFename === 'nurse'">
                                            <page-huli-card-item :item="item" :index="index"></page-huli-card-item>
                                        </template>
                                    </view>
                                </template>
                            </template>
                        </uv-waterfall>
                    </template>

                    <!-- <uv-load-more :status="'loading'" v-show="pageData.pageLoading && !pageData.firstLoaded" /> -->
                </view>
            </view>
        </z-paging>
    </app-layout>
</template>

<style>
page {
    background-color: #f0f3f7;
}
</style>

<style lang="scss" scoped>
.page-body {
    width: 100%;
    min-height: 100vh;
    background-color: #f0f3f7;
}

.home_content {
    // background-repeat: no-repeat;
    // background-size: 100% 521rpx;
}

.paging-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.swiper {
    flex: 1;
}

.my-tabs-box :deep(.uv-tabs__wrapper__nav__item) {
    position: relative;
    z-index: 1;
}

.my-tabs-box :deep(.uv-tabs__wrapper__nav__line) {
    bottom: 14rpx;
}
</style>
