<script setup>
import { reactive } from 'vue';

import { onLoad, onReady } from '@dcloudio/uni-app';

import { canENV } from '@/common/utils';

import { useUserStore, useAppStore, useLoginStore } from '@/store';

const appStore = useAppStore();
const userStore = useUserStore();
const loginStore = useLoginStore();

const pageData = reactive({});

onReady(() => {
	// #ifdef APP
	plus.navigator.closeSplashscreen();
	// #endif
});

async function loadData() {
	if (userStore.checkLogin) {
		try {
			await userStore.getAllUserInfo();
		} catch (error) {
			//TODO handle the exception
			canENV(() => {
				console.log('初始化失败！！！！！！', error);
			});
			loginStore.logOut();
		}

		userStore.goNextPage(false, true);
	} else {
		userStore.goNextPage(true, true);
	}
}

onLoad(() => {
	loadData();
});
</script>
<template>
	<app-layout>
		<view class="content"></view>
	</app-layout>
</template>

<style lang="scss" scoped></style>
