<!-- 我的养老社区 -->
<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getBannerList, getKingKongList } from '@/server/api'
import { home } from '@/common/images'
import debounce from '@/uni_modules/uv-ui-tools/libs/function/debounce.js'
import { route, openWebView, goArticlePage, goArticlePageWithData, sliceArray } from '@/common/utils'

// 社区数据
const communityData = ref({
    healthScore: 653,
    serviceCount: 10235,
    name: '武汉市悦年华颐养中心（武汉石桥）',
    address: '地址：湖北省武汉市江岸区兴业路33号青松路与兴业路交汇处',
})

// 点击进入社区
const handleEnterCommunity = () => {
    console.log('进入我的社区')
    // TODO: 跳转到社区详情页
}

// 点击社区卡片
const handleCommunityClick = () => {
    console.log('点击社区卡片')
    // TODO: 跳转到社区详情页
}

const bannerList = ref([])

const menuCurrent = ref(0)
const menuList = ref([])

/**
 * 处理URL跳转逻辑
 * @param {string} url - 跳转URL
 * @param {string} title - 页面标题
 * @param {boolean} requireLogin - 是否需要登录验证
 */
function handleUrlNavigation(url, title = '', requireLogin = false) {
    const urlString = String(url)
    if (!urlString) return

    if (urlString.startsWith('http://') || urlString.startsWith('https://')) {
        openWebView(urlString, title)
    } else {
        // 内部路由跳转，检查是否需要登录验证
        if (requireLogin && !userStore.checkLogin) {
            appStore.changeShowLoginModal(true)
        } else {
            route(urlString)
        }
    }
}

/**
 * 菜单跳转逻辑：
 * 1. 优先判断skipType: content(详情) | link(外链或页面)
 * 2. 接着判断typeName: shop(商品) | serpro(服务)
 * 3. 最后匹配不到的都提示【敬请期待】
 * 网上医院https://hd.guahao.com/u/33076?utm_trace=6nYAFXX-Tf&_cp=6nYAFXX-Tf
 * 名医在线https://wy.guahao.com/search?tab=doctor
 */
function clickKongItem(item) {
    debounce(() => {
        // 跳转策略映射
        const navigationStrategies = {
            // skipType 优先级处理
            content: () => {
                goArticlePageWithData(item, item.id)
            },
            link: () => {
                handleUrlNavigation(item.skipUrl, item.title || '')
            },
            // typeName 次级处理
            shop: () => {
                route('/pages/home/<USER>/productHome/productHome', {
                    categoryId: item.categoryId || '',
                })
            },
            serpro: () => {
                route('/pages/home/<USER>/serverHome/serverHome', {
                    categoryId: item.categoryId || '',
                })
            },
        }

        // 优先处理 skipType
        if (item.skipType && navigationStrategies[item.skipType]) {
            navigationStrategies[item.skipType]()
            return
        }

        // 其次处理 typeName
        if (item.typeName && navigationStrategies[item.typeName]) {
            navigationStrategies[item.typeName]()
            return
        }

        // 默认提示
        uni.showToast({ title: '敬请期待', icon: 'none' })
    }, 300)
}

function menuListSwipeChange(e) {
    menuCurrent.value = e.detail.current
}

async function loadData() {
    // 获取 Banner
    const res = await getBannerList({
        current: 1,
        size: 10,
        status: 'STATUSN', // STATUSY -> 是 | STATUSN -> 否
        type: 'IbANNER', // IbANNER -> 首页轮播 | INDRE -> 首页招募图
    })
    if (res.apiStatus) {
        bannerList.value = res.data.records
    }

    // 获取菜单区
    const resMenu = await getKingKongList({ pageType: 1 })
    if (resMenu.apiStatus) {
        const allData = [...resMenu.data]
        menuList.value = sliceArray(allData, 8)
    }
}

const swiperHeightClass = computed(() => {
    if (menuList.value.length > 0) {
        if (menuList.value[0].length > 8) {
            return 'h-500'
        } else if (menuList.value[0].length > 4) {
            return 'h-320'
        } else {
            return 'h-140'
        }
    }
})

onLoad(() => {
    loadData()
})
</script>

<template>
    <view>
        <view class="mx-20 rd-20 overflow-hidden community-main-card">
            <!-- 绿色渐变区域 -->
            <view class="community-gradient p-20 relative">
                <!-- 标题区域 -->
                <view class="flex justify-between items-start mb-40 relative">
                    <view class="text-48 font-bold text-white">我的养老社区</view>
                    <view class="community-float-btn" @click="handleEnterCommunity">
                        <view class="flex items-center">
                            <text class="text-30 text-#319176 font-bold">进入我的社区</text>
                            <uv-icon name="arrow-right" size="16" color="#319176" />
                        </view>
                    </view>
                </view>

                <!-- 数据区域 -->
                <view class="flex justify-start items-center gap-60">
                    <view class="flex flex-col items-center">
                        <view class="text-64 font-bold text-white">{{ communityData.healthScore }}</view>
                        <view class="community-data-label">社区健康分</view>
                    </view>
                    <view class="flex flex-col items-center">
                        <view class="text-64 font-bold text-white">{{ communityData.serviceCount }}</view>
                        <view class="community-data-label">总服务人次</view>
                    </view>
                </view>

                <!-- 信息区域 -->
                <view class="my-20 rd-20 overflow-hidden bg-white" @click="handleCommunityClick">
                    <view class="flex">
                        <view class="w-200 h-200 rd-20 overflow-hidden">
                            <image :src="home.myCommunity" mode="cover" class="w-full h-full" />
                        </view>
                        <view class="flex-1 flex flex-col gap-16 py-10 px-20">
                            <view class="text-30 font-bold text-#319176">{{ communityData.name }}</view>
                            <view class="text-24 text-#319176">{{ communityData.address }}</view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <!-- 活动通知 -->
        <view class="mx-40 my-20 text-40 font-bold text-#46c29e">活动通知</view>
        <view class="mx-20 mt-20">
            <uv-swiper :list="bannerList" keyName="image" indicatorMode="dot" height="200rpx" radius="20rpx" indicator circular></uv-swiper>
        </view>

        <view class="mx-40 my-20 text-40 font-bold text-#fb7e04">社区服务</view>
        <view class="mx-20 mt-20 p-20 bg-white rd-10 overflow-hidden">
            <swiper
                :class="swiperHeightClass"
                :current="menuCurrent"
                :indicator-dots="true"
                @change="menuListSwipeChange"
                indicator-color="rgba(221, 221, 221, .8)"
                indicator-active-color="rgba(64, 201, 169, .8)"
            >
                <template v-for="swiperItem in menuList" :key="swiperItem.id">
                    <swiper-item>
                        <view class="grid grid-cols-4 gap-y-35rpx">
                            <template v-for="item in swiperItem" :key="item.id">
                                <view class="flex flex-col items-center" @click="clickKongItem(item)">
                                    <app-image :src="item.icon" size="88" mode=""></app-image>
                                    <view class="mt-15 text-#343434 text-26 h-30">
                                        {{ item.title }}
                                    </view>
                                </view>
                            </template>
                        </view>
                    </swiper-item>
                </template>
            </swiper>
        </view>
    </view>
</template>

<style>
page {
    background-color: #f0f3f7;
}
</style>
<style scoped>
.community-main-card {
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
}

.community-gradient {
    background: linear-gradient(135deg, #56e3c0 0%, #46c29e 100%);
}

.community-float-btn {
    position: absolute;
    top: 0;
    right: 0;
    background: linear-gradient(to bottom, #f9f9f7 0%, #ebdabc 100%);
    border-radius: 50rpx;
    padding: 16rpx 28rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
    z-index: 10;
    transition: all 0.2s ease;
}

.community-float-btn:active {
    transform: scale(0.95);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.16);
}

.community-data-label {
    background: rgba(0, 0, 0, 0.2);
    color: #ffffff;
    font-size: 28rpx;
    padding: 4rpx 24rpx;
    border-radius: 20rpx;
    font-weight: 500;
}
</style>
