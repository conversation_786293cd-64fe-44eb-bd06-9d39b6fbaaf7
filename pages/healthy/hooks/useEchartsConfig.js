import { computed, ref } from "vue";

/**
 * ECharts配置hooks
 * 提供常用的图表配置生成方法
 */
export default function useEchartsConfig() {
	/**
	 * 基础配置模板
	 */
	const baseConfig = {
		tooltip: { trigger: "axis", axisPointer: { type: "line" } },
		grid: { left: 0, right: 10, bottom: 15, top: 40, containLabel: true },
		xAxis: {
			type: "category",
			boundaryGap: false,
			data: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
			axisLine: { lineStyle: { color: "#999999" } },
			axisLabel: { color: "#666666" },
		},
		yAxis: {
			type: "value",
			axisLine: { lineStyle: { color: "#999999" } },
			axisLabel: { color: "#666666" },
			splitLine: { lineStyle: { type: "dashed", color: "#e0e0e0" } },
		},
	};

	/**
	 * 十六进制颜色转rgba
	 * @param {string} hex 十六进制颜色值
	 * @param {number} alpha 透明度
	 * @returns {string} rgba颜色值
	 */
	const hexToRgba = (hex, alpha = 1) => {
		const r = parseInt(hex.slice(1, 3), 16);
		const g = parseInt(hex.slice(3, 5), 16);
		const b = parseInt(hex.slice(5, 7), 16);
		return `rgba(${r},${g},${b},${alpha})`;
	};

	/**
	 * 处理图表数据，将 null、undefined 或空值转换为 0
	 * @param {Array} data 原始数据数组
	 * @returns {Array} 处理后的数据数组
	 */
	const processChartData = (data = []) => {
		return data.map((item) => {
			if (item === null || item === undefined || item === "") {
				return 0;
			}
			return Number(item) || 0;
		});
	};

	/**
	 * 创建单线图表配置（血糖、尿酸、血氧等单指标）
	 * @param {Object} options 配置选项
	 * @param {Array} options.data 数据数组
	 * @param {Array} options.xAxisData X轴数据，默认为周一到周日
	 * @param {string} options.color 线条颜色，默认为 #23b899
	 * @param {Object} options.yAxis Y轴配置，可覆盖默认配置
	 * @param {string} options.name 系列名称
	 * @returns {Object} ECharts配置对象
	 */
	const createSingleLineConfig = (options = {}) => {
		const { data = [], xAxisData = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"], color = "#23b899", yAxis = {}, name = "数据" } = options;

		// 处理数据，将 null、undefined 或空值转换为 0
		const processedData = processChartData(data);

		return {
			color: [color],
			...baseConfig,
			xAxis: {
				...baseConfig.xAxis,
				data: xAxisData,
			},
			yAxis: {
				...baseConfig.yAxis,
				...yAxis,
			},
			series: [
				{
					name: name,
					type: "line",
					smooth: true,
					data: processedData,
					stack: "Total",
					areaStyle: {
						color: {
							type: "linear",
							x: 0,
							y: 0,
							x2: 0,
							y2: 1,
							colorStops: [
								{ offset: 0, color: hexToRgba(color, 0.2) },
								{ offset: 1, color: hexToRgba(color, 0) },
							],
						},
					},
				},
			],
		};
	};

	/**
	 * 创建血糖图表配置（向后兼容）
	 * @param {Object} options 配置选项
	 * @returns {Object} ECharts配置对象
	 */
	const createBloodGlucoseConfig = (options = {}) => {
		return createSingleLineConfig({
			...options,
			name: options.name || "血糖",
		});
	};

	/**
	 * 创建血压图表配置
	 * @param {Object} options 配置选项
	 * @param {Array} options.systolicData 收缩压数据
	 * @param {Array} options.diastolicData 舒张压数据
	 * @param {Array} options.xAxisData X轴数据，默认为周一到周日
	 * @param {Array} options.colors 线条颜色数组，默认为 ["#23b899", "#FF8237"]
	 * @param {Array} options.name 系列名称数组，默认为 ["收缩压", "舒张压"]
	 * @param {Object} options.yAxis Y轴配置，可覆盖默认配置
	 * @returns {Object} ECharts配置对象
	 */
	const createBloodPressureConfig = (options = {}) => {
		const { systolicData = [], diastolicData = [], xAxisData = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"], colors = ["#23b899", "#FF8237"], name = ["收缩压", "舒张压"], yAxis = {} } = options;

		// 处理收缩压和舒张压数据，将 null、undefined 或空值转换为 0
		const processedSystolicData = processChartData(systolicData);
		const processedDiastolicData = processChartData(diastolicData);

		return {
			color: colors,
			...baseConfig,
			xAxis: {
				...baseConfig.xAxis,
				data: xAxisData,
			},
			yAxis: {
				...baseConfig.yAxis,
				...yAxis,
			},
			series: [
				{
					name: name[0],
					type: "line",
					smooth: true,
					data: processedSystolicData,
					areaStyle: {
						color: {
							type: "linear",
							x: 0,
							y: 0,
							x2: 0,
							y2: 1,
							colorStops: [
								{ offset: 0, color: hexToRgba(colors[0], 0.3) },
								{ offset: 1, color: hexToRgba(colors[0], 0) },
							],
						},
					},
				},
				{
					name: name[1],
					type: "line",
					smooth: true,
					data: processedDiastolicData,
					areaStyle: {
						color: {
							type: "linear",
							x: 0,
							y: 0,
							x2: 0,
							y2: 1,
							colorStops: [
								{ offset: 0, color: hexToRgba(colors[1], 0.3) },
								{ offset: 1, color: hexToRgba(colors[1], 0) },
							],
						},
					},
				},
			],
		};
	};

	/**
	 * 创建响应式血压图表配置
	 * @param {Object} bloodPressureData 血压数据对象，包含 range 和 spare 属性
	 * @param {Object} options 其他配置选项
	 * @returns {ComputedRef} 响应式的ECharts配置
	 */
	const createReactiveBloodPressureConfig = (bloodPressureData, options = {}) => {
		return computed(() => {
			return createBloodPressureConfig({
				systolicData: bloodPressureData.value?.range || [],
				diastolicData: bloodPressureData.value?.spare || [],
				...options,
			});
		});
	};

	/**
	 * 通用配置合并方法
	 * @param {Object} baseConfig 基础配置
	 * @param {Object} customConfig 自定义配置
	 * @returns {Object} 合并后的配置
	 */
	const mergeConfig = (baseConfig, customConfig) => {
		return {
			...baseConfig,
			...customConfig,
			xAxis: { ...baseConfig.xAxis, ...customConfig.xAxis },
			yAxis: { ...baseConfig.yAxis, ...customConfig.yAxis },
			series: customConfig.series || baseConfig.series,
		};
	};

	return {
		baseConfig,
		createSingleLineConfig,
		createBloodGlucoseConfig,
		createBloodPressureConfig,
		createReactiveBloodPressureConfig,
		mergeConfig,
		hexToRgba,
		processChartData,
	};
}
