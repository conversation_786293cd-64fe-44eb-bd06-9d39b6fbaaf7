import { ref, computed } from 'vue'
import { GetHealthReportOverview, GetHealthReportRisk, GetHealthReportDiet } from '@/server/api'
import { useHealthMemberInfoStore } from '@/store'

/**
 * 健康报告数据管理hooks
 * 统一管理API调用和数据处理逻辑
 */
export default function useHealthReportsData() {
    const healthMemberInfoStore = useHealthMemberInfoStore()
    const memberInfo = computed(() => healthMemberInfoStore.getMemberInfo())

    // 响应式数据
    const healthReportData = ref({
        year: '',
        weekRange: '',
        weekNo: 1,
        score: 0,
        scoreRate: 0,
        abnormalCount: 0,
        diseaseCount: 0,
    })

    const goodList = ref([])
    const badList = ref([])
    const factors = ref({})
    const riskData = ref({})
    const dietData = ref([])
    const formworkData = ref({})

    // 加载状态
    const loading = ref({
        overview: false,
        risk: false,
        diet: false,
    })

    // 错误状态
    const errors = ref({
        overview: null,
        risk: null,
        diet: null,
    })

    /**
     * 构建API请求参数
     * @param {string} resultId 报告主表id
     * @returns {Object} 请求参数
     */
    const buildApiParams = (resultId) => ({
        userId: memberInfo.value.userId,
        resultId,
    })

    /**
     * 根据传入的对象和data返回匹配的值
     * @param {Object} obj 目标对象
     * @param {Object} data 数据源
     * @returns {Object} 匹配后的对象
     */
    const matchData = (obj, data) => {
        const result = { ...obj }
        for (let key in result) {
            if (data[key] !== undefined) {
                result[key] = data[key]
            }
        }
        return result
    }

    /**
     * 获取健康报告总览
     * @param {string} resultId 报告主表id
     */
    const getHealthReportOverview = async (resultId) => {
        loading.value.overview = true
        errors.value.overview = null

        try {
            const params = buildApiParams(resultId)
            const { data } = await GetHealthReportOverview(params)

            // 更新健康报告数据
            healthReportData.value = matchData(healthReportData.value, data)
            formworkData.value = data.formwork || {}
            goodList.value = data.goodList ? data.goodList.split(',') : []
            badList.value = data.badList ? data.badList.split(',') : []
            factors.value = data.factors || {}

            return data
        } catch (error) {
            console.error('获取健康报告失败:', error)
            errors.value.overview = error
            throw error
        } finally {
            loading.value.overview = false
        }
    }

    /**
     * 获取慢病风险评估
     * @param {string} resultId 报告主表id
     */
    const getHealthReportRisk = async (resultId) => {
        loading.value.risk = true
        errors.value.risk = null

        try {
            const params = buildApiParams(resultId)
            const { data } = await GetHealthReportRisk(params)
            riskData.value = data || {}
            console.log('慢性病风险评估结果:', data)
            return data
        } catch (error) {
            console.error('获取慢性病风险评估结果失败:', error)
            errors.value.risk = error
            throw error
        } finally {
            loading.value.risk = false
        }
    }

    /**
     * 获取目前膳食摄入
     * @param {string} resultId 报告主表id
     */
    const getHealthReportDiet = async (resultId) => {
        loading.value.diet = true
        errors.value.diet = null

        try {
            const params = buildApiParams(resultId)
            const { data } = await GetHealthReportDiet(params)
            dietData.value = data || []
            console.log('目前膳食摄入结果:', data)
            return data
        } catch (error) {
            console.error('获取目前膳食摄入结果失败:', error)
            errors.value.diet = error
            throw error
        } finally {
            loading.value.diet = false
        }
    }

    /**
     * 批量获取所有报告数据
     * @param {string} resultId 报告主表id
     */
    const getAllReportsData = async (resultId) => {
        try {
            await Promise.all([getHealthReportOverview(resultId), getHealthReportRisk(resultId), getHealthReportDiet(resultId)])
        } catch (error) {
            console.error('获取报告数据失败:', error)
        }
    }

    /**
     * 重置所有数据
     */
    const resetData = () => {
        healthReportData.value = {
            year: '',
            weekRange: '',
            weekNo: 1,
            score: 0,
            scoreRate: 0,
            abnormalCount: 0,
            diseaseCount: 0,
        }
        goodList.value = []
        badList.value = []
        factors.value = {}
        riskData.value = {}
        dietData.value = []
        formworkData.value = {}

        // 重置加载和错误状态
        Object.keys(loading.value).forEach((key) => {
            loading.value[key] = false
        })
        Object.keys(errors.value).forEach((key) => {
            errors.value[key] = null
        })
    }

    // 计算属性：是否有任何加载中的请求
    const isLoading = computed(() => {
        return Object.values(loading.value).some(Boolean)
    })

    // 计算属性：是否有任何错误
    const hasErrors = computed(() => {
        return Object.values(errors.value).some(Boolean)
    })

    return {
        // 数据
        healthReportData,
        goodList,
        badList,
        factors,
        riskData,
        dietData,
        formworkData,

        // 状态
        loading,
        errors,
        isLoading,
        hasErrors,

        // 方法
        getHealthReportOverview,
        getHealthReportRisk,
        getHealthReportDiet,
        getAllReportsData,
        resetData,
        buildApiParams,
        matchData,
    }
}
