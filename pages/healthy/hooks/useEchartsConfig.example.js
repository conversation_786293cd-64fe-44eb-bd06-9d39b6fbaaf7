/**
 * useEchartsConfig 使用示例
 * 展示如何使用ECharts配置hooks
 */

import { ref, reactive } from 'vue';
import useEchartsConfig from './useEchartsConfig.js';

// 在Vue组件中使用示例
export function exampleUsage() {
	const {
		createBloodGlucoseConfig,
		createBloodPressureConfig,
		createReactiveBloodPressureConfig,
		mergeConfig,
		hexToRgba
	} = useEchartsConfig();

	// 示例1：创建血糖图表配置
	const bloodGlucoseOption = createBloodGlucoseConfig({
		data: [12, 23, 23, 64, 78, 89, 65],
		color: "#23b899",
		yAxis: {
			min: 0,
			max: 150,
			interval: 30
		}
	});

	// 示例2：创建血压图表配置
	const bloodPressureOption = createBloodPressureConfig({
		systolicData: [120, 125, 130, 128, 135, 140, 138],
		diastolicData: [80, 82, 85, 83, 88, 90, 87],
		colors: ["#23b899", "#FF8237"]
	});

	// 示例3：使用响应式血压配置
	const bloodPressureData = ref({
		range: [120, 125, 130, 128, 135, 140, 138], // 收缩压
		spare: [80, 82, 85, 83, 88, 90, 87]         // 舒张压
	});

	const reactiveBloodPressureOption = createReactiveBloodPressureConfig(bloodPressureData, {
		yAxis: {
			min: 60,
			max: 160,
			interval: 20
		}
	});

	// 示例4：自定义配置合并
	const customOption = mergeConfig(bloodGlucoseOption, {
		title: {
			text: '血糖趋势图',
			left: 'center'
		},
		legend: {
			show: true,
			bottom: 0
		}
	});

	// 示例5：颜色转换
	const rgbaColor = hexToRgba("#23b899", 0.5); // "rgba(35,184,153,0.5)"

	return {
		bloodGlucoseOption,
		bloodPressureOption,
		reactiveBloodPressureOption,
		customOption,
		rgbaColor
	};
}

// 在组件中的完整使用示例
export const componentExample = `
<template>
	<view>
		<!-- 血糖图表 -->
		<BaseECharts :options="bloodGlucoseOption" />
		
		<!-- 血压图表 -->
		<BaseECharts :options="bloodPressureOption" />
		
		<!-- 响应式血压图表 -->
		<BaseECharts :options="reactiveOption" />
	</view>
</template>

<script setup>
import { ref } from 'vue';
import BaseECharts from '@/pages/echarts/BaseEcharts.vue';
import useEchartsConfig from '@/common/useEchartsConfig.js';

const {
	createBloodGlucoseConfig,
	createBloodPressureConfig,
	createReactiveBloodPressureConfig
} = useEchartsConfig();

// 血糖配置
const bloodGlucoseOption = createBloodGlucoseConfig({
	data: [12, 23, 23, 64, 78, 89, 65]
});

// 血压配置
const bloodPressureOption = createBloodPressureConfig({
	systolicData: [120, 125, 130, 128, 135, 140, 138],
	diastolicData: [80, 82, 85, 83, 88, 90, 87]
});

// 响应式血压数据
const bloodPressureData = ref({
	range: [120, 125, 130, 128, 135, 140, 138],
	spare: [80, 82, 85, 83, 88, 90, 87]
});

const reactiveOption = createReactiveBloodPressureConfig(bloodPressureData);
</script>
`;
