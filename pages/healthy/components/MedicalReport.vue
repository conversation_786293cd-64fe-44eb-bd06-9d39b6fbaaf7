<!-- 通用医疗报告组件 -->
<script setup>
import { computed } from "vue";

const props = defineProps({
	// 医疗数据
	medicalData: {
		type: Object,
		required: true,
		default: () => ({
			range: [],
			spare: [], // 血压舒张压数据
			standard: [],
			unit: "",
			str: "",
			show: false,
		}),
	},
	// 报告类型，用于特殊处理
	reportType: {
		type: String,
		default: "default", // 'bloodGlucose', 'bloodPressure', 'uric', 'bloodFat'
	},
	// 统计标签
	statisticsLabels: {
		type: Array,
		default: () => ["平均值", "最高值", "最低值"],
	},
});

// 统计数据列表
const statisticsList = computed(() => {
	if (!props.medicalData?.show) return [];

	return props.statisticsLabels.map((label, index) => ({
		label,
		value: props.medicalData?.standard?.[index] || "--",
	}));
});

// 分析建议文本
const analysisText = computed(() => {
	return props.medicalData?.str || "暂无分析建议";
});

// 单位文本
const unitText = computed(() => {
	return props.medicalData?.unit || "";
});
</script>

<template>
	<view class="w-full h-full">
		<!-- 图表区域 -->
		<view class="w-full">
			<!-- 微信小程序中不支持子组件使用分包的组件和作用域插槽 -->
			<!-- <base-echarts ref="echartsRef" :options="option" /> -->
			<slot name="echarts"></slot>
		</view>

		<!-- 分析与建议 -->
		<view class="w-full bg-#F7F8FA b-rd-10 p-26 flex flex-col justify-between">
			<view class="font-size-28 font-bold text-#1a1a1a">分析与建议</view>
			<view class="font-size-28 text-#333333">{{ analysisText }}</view>
			<view class="font-size-24 text-#66737F my-10" v-if="unitText">单位：{{ unitText }}</view>

			<!-- 统计数据 -->
			<view class="flex justify-around" v-if="statisticsList.length > 0">
				<view class="flex flex-col items-center" v-for="item in statisticsList" :key="item.label">
					<text class="font-size-36 font-bold text-#1a1a1a">{{ item.value }}</text>
					<text class="font-size-26 text-#66737F">{{ item.label }}</text>
				</view>
			</view>
		</view>
	</view>
</template>
