<!-- 家庭圈-添加家庭成员 -->
<script setup>
import { ref, reactive } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { toast } from "@/uni_modules/uv-ui-tools/libs/function/index.js";
import { mobile, code, nickName } from "@/utils";
import { SendAddMemberCaptcha } from "@/server/api";

const formRef = ref();
const formData = {
	phone: "",
	smsCode: "",
	nickName: "",
};
const rules = {
	phone: mobile(),
	smsCode: code(),
	nickName: nickName(),
};
const model = reactive({
	...formData,
});
const tips = ref("");
const uCodeRef = ref();

function codeChange(text) {
	tips.value = text;
}

function getCode() {
	// 验证手机号是否合法
	formRef.value.validateField("phone", async (errorsRes) => {
		if (errorsRes && errorsRes.length) return;
		// 验证验证码按钮是否可点击
		if (uCodeRef.value.canGetCode) {
			// 模拟向后端请求验证码
			uni.showLoading({
				title: "正在获取验证码",
			});
			const { data } = await SendAddMemberCaptcha({ form: model.phone });
			model.smsCode = data;
			uni.hideLoading();
			// 这里此提示会被start()方法中的提示覆盖
			toast("验证码已发送");
			// 通知验证码组件内部开始倒计时
			uCodeRef.value.start();
		}
	});
}
// 校验
function submit() {
	return formRef.value
		.validate()
		.then((res) => {
			return res;
		})
		.catch((errors) => {
			throw errors;
		});
}
// 重置
function reset() {
	uCodeRef.value.reset();
	formRef.value.clearValidate();
	formRef.value.resetFields();
}

// 页面加载时执行
onLoad(() => {
	// TODO: 页面初始化逻辑
});

defineExpose({
	submit,
	reset,
	model,
});
</script>

<template>
	<view class="w-full">
		<uv-form label-position="left" :model="model" :rules="rules" ref="formRef">
			<uv-form-item prop="phone">
				<uv-input v-model="model.phone" border="none" placeholder="请输入手机号"> </uv-input>
			</uv-form-item>
			<uv-form-item prop="smsCode">
				<uv-input v-model="model.smsCode" placeholder="请输入验证码" border="none" :maxlength="4" type="number">
					<!-- vue3模式下必须使用v-slot:suffix -->
					<template v-slot:suffix>
						<uv-code ref="uCodeRef" @change="codeChange" seconds="60" changeText="X秒重新获取"></uv-code>
						<text @click="getCode" :text="tips" color="#0072FE">{{ tips }}</text>
					</template>
				</uv-input>
			</uv-form-item>
			<uv-form-item prop="nickName">
				<uv-input v-model="model.nickName" border="none" placeholder="请输入亲属称呼 如：爸、妈"> </uv-input>
			</uv-form-item>
		</uv-form>
	</view>
</template>

<style lang="scss" scoped>
:deep {
	.uv-input__content {
		background-color: #f0f3f9;
		height: 90rpx;
		border-radius: 10rpx;
		color: #222222;
		padding: 0 30rpx;
	}

	.uv-form-item__body__right__message {
		margin-left: 0 !important;
	}
}
</style>
