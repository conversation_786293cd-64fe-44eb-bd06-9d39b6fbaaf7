<!-- 健康报告-建议饮食 -->
<script setup>
import { ref } from "vue";

// 食物状态与颜色映射
const statusColorMap = {
	正常: "#222222",
	不足: "#44A4FD",
	过多: "#F5544A",
};

// 建议饮食数据
const dietList = ref([
	{
		name: "谷类",
		status: "过多",
		recommend: "350克/天",
	},
	{
		name: "豆类",
		status: "不足",
		recommend: "40克/天",
	},
	{
		name: "水果",
		status: "不足",
		recommend: "400克/天",
	},
	{
		name: "肉类",
		status: "不足",
		recommend: "75克/天",
	},
	{
		name: "蔬菜",
		status: "正常",
		recommend: "450克/天",
	},
	{
		name: "蛋类",
		status: "正常",
		recommend: "50克/天",
	},
]);
</script>

<template>
	<view>
		<view v-for="(item, idx) in dietList" :key="item.id" class="flex items-center my-30">
			<!-- 左侧灰色占位方块 -->
			<view class="w-100 h-100 bg-#F5F5F5 b-rd-10 mr-24 flex-shrink-0"></view>
			<!-- 右侧信息 -->
			<view class="flex-1 flex flex-col justify-between">
				<view class="flex items-center text-#222222">
					<!-- 食物名称 -->
					<text class="font-size-30 font-bold mr-30">
						{{ item.name }}
					</text>
					<!-- 当前摄入量状态，动态颜色 -->
					<text class="font-size-26">
						目前摄入量：<text :style="{ color: statusColorMap[item.status] }">{{ item.status }}</text>
					</text>
				</view>
				<!-- 推荐摄入量 -->
				<view class="font-size-26"> 推荐摄入量：{{ item.recommend }} </view>
			</view>
		</view>
	</view>
</template>

<style lang="scss" scoped>
/* 页面样式由unocss主导，若需补充全局样式可在此添加 */
</style>
