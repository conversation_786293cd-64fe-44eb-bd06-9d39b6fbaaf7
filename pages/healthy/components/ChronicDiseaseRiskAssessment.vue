<!-- 健康报告-慢性病风险评估 -->
<script setup>
import { ref, watch, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { useHealthMemberInfoStore } from "@/store";

const healthMemberInfoStore = useHealthMemberInfoStore();
const memberInfo = computed(() => healthMemberInfoStore.getMemberInfo());

const props = defineProps({
	riskData: {
		type: Object,
		default: () => ({}),
	},
});

// 基础风险列表定义 - 名称=name, 键名=key, 等级=levels, 是否已患=hasDisease
const riskListConfig = computed(() => {
	const defaultConfig = [
		{ name: "5年内患糖尿病", key: "diabRelaTier", levels: 5 },
		{ name: "10年内患缺血性心血管疾病", key: "civLevel", levels: 3 },
		{ name: "代谢综合征", key: "msLevel", levels: 3, hasDisease: true },
		{ name: "4年内高血压发生风险", key: "hbpLevel", levels: 4 },
		{ name: "肥胖症", key: "obesityLevel", levels: 4 },
		{ name: "尼古丁依赖成瘾", key: "nicotineLevel", levels: 3 },
		{ name: "血脂异常", key: "bloodFatLevel", levels: 3, hasDisease: true },
		{ name: "10年内主要质疏松性骨折", key: "fractureLevel", levels: 3 },
		{ name: "冠心病", key: "chdLevel", levels: 3 },
		{ name: "脂肪肝", key: "flLevel", levels: 3 },
		{ name: "痛风", key: "goutLevel", levels: 4 },
		{ name: "偏头痛", key: "dhhhchLevel", levels: 4, hasDisease: true },
		{ name: "脑卒中", key: "strokeLevel", levels: 4 },
		{ name: "慢性阻塞性肺病", key: "copdLevel", levels: 5 },
		{ name: "睡眠质量", key: "sleepLevel", levels: 3 },
	];
	// 根据性别动态调整风险列表
	const womenConfig = [{ name: "乳腺增生(女性)", key: "hyperplasiaLevel", levels: 3 }];
	return memberInfo.value.sex === 1 ? defaultConfig : [...defaultConfig, ...womenConfig];
});

// 统一的风险等级标签映射 - 基于1-5级风险
const riskLevelLabels = {
	1: { label: "低风险", color: "#07D798" },
	2: { label: "中风险", color: "#FEA801" },
	3: { label: "高风险", color: "#F5544A" },
	4: { label: "极高风险", color: "#F5544A" },
	5: { label: "极高风险", color: "#F5544A" },
	9: { label: "已患", color: "#F5544A" }, // 特殊值，表示已患病
};

// 特殊标签映射 - 用于覆盖默认标签
const specialLabelMap = {
	diabRelaTier: {
		1: "极低风险",
		2: "低风险",
		3: "中风险",
		4: "高风险",
		5: "极高风险",
	},
	nicotineLevel: {
		1: "轻度依赖",
		2: "中度依赖",
		3: "重度依赖",
	},
	msLevel: {
		3: "已患",
	},
	bloodFatLevel: {
		3: "已患",
	},
	sleepLevel: {
		1: "差（立即改善）",
		2: "一般（继续改善）",
		3: "良好（努力保持）",
	},
};

// 特殊颜色映射（睡眠等级描述与其它相反） - 用于覆盖默认颜色
const specialColorMap = {
	sleepLevel: {
		1: "#F5544A", // 差
		2: "#FEA801", // 一般
		3: "#07D798", // 良好
	},
	diabRelaTier: {
		1: "#07D798", // 极低风险
		2: "#07D798", // 低风险
		3: "#FEA801", // 中风险
		4: "#F5544A", // 高风险
		5: "#F5544A", // 极高风险
	},
};

const grayColor = "#E5E5E5";

// 获取风险等级标签
function getRiskLabel(key, level) {
	// 先检查是否有特殊标签
	if (specialLabelMap[key]?.[level]) {
		return specialLabelMap[key][level];
	}
	// 否则使用通用标签
	return riskLevelLabels[level]?.label || "低风险";
}

// 获取风险等级颜色
function getRiskColor(key, level) {
	// 检查是否有特殊颜色映射
	if (specialColorMap[key]?.[level]) {
		return specialColorMap[key][level];
	}

	// 检查是否是特殊的"已患"状态
	const config = riskListConfig.value.find((item) => item.key === key);
	if (config?.hasDisease && level === config.levels) {
		return riskLevelLabels[9].color;
	}

	// 否则使用通用颜色
	return riskLevelLabels[level]?.color || riskLevelLabels[1].color;
}

// 判断是否为"已患"状态
function isDisease(key, level) {
	const config = riskListConfig.value.find((item) => item.key === key);
	return config?.hasDisease && level === config.levels;
}

// 获取活跃段数
function getActiveSegments(item) {
	// 睡眠质量特殊处理：等级越高越好
	if (item.key === "sleepLevel") {
		return item.level;
	}

	// 其他风险：等级越低越好
	return item.level;
}

// 计算处理后的风险列表
const riskList = computed(() => {
	return riskListConfig.value.map((item) => {
		const key = item.key;
		const level = props.riskData?.[key] || 1;
		const label = getRiskLabel(key, level);
		const color = getRiskColor(key, level);
		const disease = isDisease(key, level);
		const activeSegments = getActiveSegments({ key, level });

		return {
			name: item.name,
			key: key,
			level: level,
			label: label,
			color: color,
			segments: item.levels,
			active: activeSegments,
			isDisease: disease,
		};
	});
});
</script>

<template>
	<view>
		<view v-for="(item, idx) in riskList" :key="idx">
			<view class="flex items-center justify-between mb-8">
				<view class="font-size-28 text-#121212 flex-1-1-auto overflow-hidden text-ellipsis whitespace-nowrap">{{ idx + 1 }}、{{ item.name }}</view>
				<view class="font-size-26" :style="{ color: item.color }">
					{{ item.label }}
				</view>
			</view>
			<view class="flex w-full gap-4 mt-20 mb-40">
				<!-- 已患疾病显示单个红色格子 -->
				<template v-if="item.isDisease">
					<view class="h-10 b-rd-4 flex-1" :style="{ background: item.color }"></view>
				</template>
				<!-- 正常风险等级显示多个格子 -->
				<template v-else>
					<view
						v-for="n in item.segments"
						:key="n"
						class="h-10 b-rd-4 flex-1"
						:style="{
							background: n <= item.active ? item.color : grayColor,
						}"></view>
				</template>
			</view>
		</view>
	</view>
</template>

<style lang="scss" scoped></style>
