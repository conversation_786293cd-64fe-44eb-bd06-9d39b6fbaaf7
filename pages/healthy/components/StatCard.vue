<!-- 日常监测-卡片 -->
<script setup>
import { formatDate } from "@/utils";

defineProps({
	icon: { type: String, required: true },
	title: { type: String, required: true },
	value: { type: String, required: true },
	unit: { type: String, default: "" },
	sub: { type: String, default: "" },
});

// 智能格式化时间，只对时间格式的字符串进行格式化
function formatTime(timeStr) {
	if (!timeStr || timeStr === "--") {
		return timeStr;
	}

	// 检查是否为时间格式（包含日期格式的字符串）
	// 时间格式通常包含 "-" 和数字，且符合日期格式
	const timePattern = /^\d{4}-\d{2}-\d{2}(\s\d{2}:\d{2}:\d{2})?$/;

	if (timePattern.test(timeStr)) {
		// 如果是时间格式，则格式化为年月日
		return formatDate(timeStr, "YYYY-MM-DD");
	}

	// 如果不是时间格式，直接返回原字符串
	return timeStr;
}
</script>
<template>
	<view class="stat-card flex flex-col items-center bg-#f4fffd border-2 border-solid border-#E2FFFB b-rd-16 px-0 py-0 w-214 h-236 box-border">
		<view class="flex flex-row items-center w-full px-20rpx pt-20rpx">
			<image class="w-32rpx h-32rpx mr-8rpx" :src="icon" mode="aspectFit" />
			<view class="font-size-24 text-#00b698">{{ title }}</view>
		</view>
		<view class="flex-1 flex flex-col justify-center items-center w-full px-20rpx">
			<view class="font-size-42 font-bold text-#1a1a1a mt-8rpx">{{ value }}</view>
			<view class="font-size-24 text-#66737f mt-2rpx">{{ unit }}</view>
			<view class="font-size-24 text-#66737f mt-16rpx text-center">{{ formatTime(sub) }}</view>
		</view>
	</view>
</template>
