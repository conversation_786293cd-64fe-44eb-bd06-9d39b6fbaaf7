<!-- 健康报告-危险因素汇总 -->
<script setup>
import { ref, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";

import { healthy } from "@/common/images";

const props = defineProps({
	// 继续保持
	goodList: {
		type: Array,
		default: () => ["体重正常", "腰围正常", "正常血糖"],
	},
	// 努力改善
	badList: {
		type: Array,
		default: () => ["血压正常高值", "血脂异常", "身体活动量不足", "饮酒过量", "蔬菜水果摄入量不足", "吸烟", "血尿酸异常"],
	},
});

const list = computed(() => [
	{
		title: "继续保持",
		icon: healthy.maintaining,
		items: props.goodList,
		bgColor: "#E8FAF3",
		color: "#00B698",
	},
	{
		title: "努力改善",
		icon: healthy.tryToImprove,
		items: props.badList,
		bgColor: "#FFECEB",
		color: "#FC3F33",
	},
]);

// 页面加载时执行
onLoad(() => {
	// TODO: 页面初始化逻辑
});
</script>

<template>
	<view class="flex flex-col" v-for="item in list" :key="item.id">
		<view class="flex items-center">
			<image :src="item.icon" class="w-38 h-38 m-r-15" />
			<text class="font-size-26 text-#121212 font-bold">{{ item.title }}</text>
		</view>
		<view class="my-36">
			<text class="font-size-24 b-rd-6 m-r-12 my-8 p-[10rpx_14rpx] inline-block" :style="{ backgroundColor: item.bgColor, color: item.color }" v-for="x in item.items" :key="x.id">{{ x }}</text>
		</view>
	</view>
</template>

<style lang="scss" scoped>
/* 页面样式 */
</style>
