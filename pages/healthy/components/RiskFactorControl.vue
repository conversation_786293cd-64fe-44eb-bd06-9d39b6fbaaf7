<!-- 健康报告-危险因素控制 -->
<script setup>
import { ref, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { formatHtmlContent } from "@/utils";
import { healthy } from "@/common/images";

const props = defineProps({
	factors: {
		type: Object,
		default: () => ({}),
	},
});

// 身体活动水平映射
const actStatusMap = {
	1: "充分",
	2: "中等",
	3: "不足",
};

// 血脂状态映射
const bloodFatStatusMap = {
	1: "低危人群",
	2: "高危人群",
	3: "血脂异常患者",
};

// 吸烟情况映射
const smokingStatusMap = {
	1: "是",
	2: "戒烟",
	3: "否",
};

// 字段映射关系配置
const fieldMappings = {
	actCard: actStatusMap,
	bloodFatCard: bloodFatStatusMap,
	smokingStatusCard: smokingStatusMap,
};

// 定义卡片配置映射
const cardConfig = {
	waistLineCard: {
		title: "腰围",
		icon: healthy.cholesterol2,
	},
	bodyWeightCard: {
		title: "体重",
		icon: healthy.cholesterol2,
	},
	fastingBgCard: {
		title: "空腹血糖",
		icon: healthy.cholesterol2,
	},
	hdlcCard: {
		title: "高密度脂蛋白胆固醇",
		icon: healthy.cholesterol2,
	},
	sbpdbpCard: {
		title: "血压",
		icon: healthy.cholesterol2,
	},
	bloodFatCard: {
		title: "血脂",
		icon: healthy.cholesterol2,
	},
	smokingStatusCard: {
		title: "吸烟状态",
		icon: healthy.cholesterol2,
	},
	alcoholDailyCard: {
		title: "饮酒量",
		icon: healthy.cholesterol2,
	},
	vegetablefruitCard: {
		title: "蔬果摄入",
		icon: healthy.cholesterol2,
	},
	actCard: {
		title: "身体活动水平",
		icon: healthy.cholesterol2,
	},
};

// 获取映射后的值
function getMappedValue(cardKey, value) {
	const mappingTable = fieldMappings[cardKey];
	if (mappingTable && mappingTable[value]) {
		return mappingTable[value];
	}
	return value;
}

// 获取所有需要显示的卡片
const cards = computed(() => {
	const result = [];

	// 遍历所有卡片类型
	Object.keys(cardConfig).forEach((cardKey) => {
		const cardData = props.factors?.[cardKey];
		const config = cardConfig[cardKey];

		// 检查卡片是否存在且需要显示
		if (cardData && cardData.show) {
			// 应用映射转换
			const current = getMappedValue(cardKey, cardData.actual);

			// 处理bloodFatCard特殊情况：没有scope时显示"正常"
			let target = cardData.scope;
			if (cardKey === "bloodFatCard" && (!target || target.trim() === "")) {
				target = "正常";
			}

			result.push({
				title: config.title,
				icon: config.icon,
				current: current,
				target: target,
				description: cardData.better,
			});
		}
	});

	return result;
});

// 页面加载时执行
onLoad(() => {
	// TODO: 页面初始化逻辑
});
</script>

<template>
	<view>
		<view v-for="card in cards" :key="card.id" class="mb-30">
			<!-- 危险因素卡片 -->
			<view class="bg-#FFF7EF b-rd-10 flex justify-between items-center p-[18rpx_20rpx]">
				<view class="flex items-center">
					<image :src="healthy.cholesterol2" class="w-38 h-38 mr-15" />
					<text class="font-size-24 font-bold">{{ card.title }}</text>
				</view>
				<view class="flex flex-col justify-around text-#121212 font-size-24">
					<view>
						当前状况：<text>{{ card.current }}</text>
					</view>
					<view>
						努力目标：<text class="text-#FE7E01">{{ card.target }}</text>
					</view>
				</view>
			</view>
			<!-- 措施 -->
			<view class="p-[18rpx_20rpx] m-y-20">
				<!-- 措施标题 -->
				<view class="flex">
					<uv-icon name="play-right-fill" color="#09ab8b"></uv-icon>
					<text class="text-#121212 font-size-28 ml-8">您的情况</text>
				</view>
				<!-- 措施内容 -->
				<!-- <text>您的血压为129/87mmHg，属于血压正常高值。高值血压是介于正常血压和高血压之间的一种状态，血压已相对偏高，比正常人更易发</text> -->
				<rich-text :nodes="formatHtmlContent(card.description)"></rich-text>
			</view>
		</view>
	</view>
</template>

<style lang="scss" scoped>
/* 页面样式 */
</style>
