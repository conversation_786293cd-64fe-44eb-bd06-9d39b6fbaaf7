<!-- 健康报告-饮食分析 -->
<script setup>
import { computed } from "vue";
import { formatHtmlContent } from "@/utils";

const props = defineProps({
	dietData: {
		type: Object,
		default: () => [],
	},
});

// 基础配置定义
const dietConfig = [
	{ category: "谷薯类及杂豆", key: "grain" },
	{ category: "蔬菜摄入", key: "vegetable" },
	{ category: "水果摄入", key: "fruit" },
	{ category: "畜禽肉类", key: "meat" },
	{ category: "鱼虾类", key: "fish" },
	{ category: "蛋类", key: "egg" },
	{ category: "奶类及奶制品", key: "milk" },
	{ category: "豆类及豆制品", key: "soybean" },
];

// 从数组中查找特定key的数据项
const findCategoryData = (key) => {
	const item = props.dietData.find((obj) => obj[key]);
	return item ? item[key] : {};
};

// 处理接口返回的数据，转换为表格可用的格式
const processedDietaryData = computed(() => {
	return dietConfig.map((item) => {
		const categoryData = findCategoryData(item.key);
		// 转换HTML标签
		const status = formatHtmlContent(categoryData.status);

		return {
			category: item.category,
			status: status,
			reference: categoryData.recommendedRange || "—",
		};
	});
});
</script>

<template>
	<!-- 3. 实现表格结构和表头 -->
	<view class="w-full">
		<view class="flex items-center w-full bg-#F5F6FA font-bold font-size-26 text-#323232">
			<view class="flex-1 text-left py-10px px-4px break-all">食物类别</view>
			<view class="flex-1 text-center py-10px px-4px break-all">状态</view>
			<view class="flex-1 text-center py-10px px-4px break-all">参考值(克/天)</view>
		</view>
		<!-- 4. 用v-for渲染表格内容 -->
		<view class="flex items-center w-full min-h-44px font-size-26 text-#323232" :style="{ background: idx % 2 === 1 ? '#F2F4F8' : '' }" v-for="(item, idx) in processedDietaryData" :key="idx">
			<view class="category flex-1 text-left py-10px px-4px break-all">{{ item.category }}</view>
			<view class="status flex-1 text-center py-10px px-4px break-all font-500 flex justify-center items-center">
				<template v-if="!item.status">
					<text class="text-#BDBDBD"> — </text>
				</template>
				<template v-else>
					<rich-text :nodes="item.status"></rich-text>
				</template>
			</view>
			<view class="reference flex-1 text-center py-10px px-4px break-all">{{ item.reference }}</view>
		</view>
	</view>
</template>
