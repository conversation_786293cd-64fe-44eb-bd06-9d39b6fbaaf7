<!-- 设备类型 -->
<script setup>
import { ref, onMounted, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { route } from "@/common/utils";
import { useHealthDeviceTypeStore } from "@/store";
import { GetProductsForAll } from "@/server/api";

const healthDeviceTypeStore = useHealthDeviceTypeStore(); // 获取当前选择的设备类型信息

const typeList = ref([]);

// 获取设备类型列表
async function getProductsForAll() {
	try {
		const { data } = await GetProductsForAll({ categoryId: 1 });
		typeList.value = data?.list || [];
	} catch (error) {
		console.error("获取设备类型列表失败:", error);
	}
}

const selectedId = ref(typeList.length > 0 ? typeList[0].id : null);

const currentDeviceTypeInfo = ref({}); // 当前选择的设备类型信息
// 选择设备类型
const selectDevice = (item) => {
	selectedId.value = item.id;
	currentDeviceTypeInfo.value = item;
	healthDeviceTypeStore.setDeviceTypeInfo(item); // 设置当前选择的设备类型信息
};

const modalRef = ref();
function handleConfirm() {
	route("/pages/healthy/pages/input_manual");
}
// 扫描设备二维码
const handleScanCode = () => {
	if (!selectedId.value) {
		uni.showToast({
			title: "请先选择设备类型",
			icon: "none",
		});
		return;
	}
	uni.scanCode({
		scanType: ["barCode", "qrCode"],
		success(res) {
			healthDeviceTypeStore.deviceTypeInfo.imei = res.result;
			route({ url: "pages/healthy/pages/equipment_info", type: "redirect" });
		},
		fail(error) {
			// 扫码失败时弹对话框（手动填写设备编号），点击后跳转新页面
			modalRef.value.open();
		},
	});
};

onLoad(() => {
	getProductsForAll();
});

onMounted(() => {
	// 获取设备类型列表之后，主动暂存一次数据
	healthDeviceTypeStore.setDeviceTypeInfo(typeList[0]);
});
</script>

<template>
	<app-layout>
		<view class="label-box">全部智能设备</view>
		<!-- 设备类型卡片 -->
		<view v-if="typeList.length > 0" class="flex flex-wrap">
			<view class="device-card" :class="{ 'device-card-selected': item.id === selectedId }" v-for="item in typeList" :key="item.id" @click="selectDevice(item)">
				<app-image :src="item.imageUrl" size="210" mode=""></app-image>
				<text class="mt-20 text-#141416 font-bold font-size-30">{{ item.name }}</text>
			</view>
		</view>

		<!-- 空数据提示 -->
		<view v-else class="flex justify-center w-full h-[60vh]">
			<uv-empty text="暂无数据"></uv-empty>
		</view>

		<!-- 占位 -->
		<view class="w-full h-120"></view>

		<view class="w-full fixed bottom-0 bg-white p-y-20">
			<button class="add-btn" :class="{ 'add-btn-disabled': !selectedId || typeList.length === 0 }" @click="handleScanCode">扫描设备二维码</button>
		</view>

		<my-uv-modal ref="modalRef" confirmColor="#38B597" lineColor="#F2F6FB" title="扫码失败" content="请手动输入设备编号" align="center" :showCancelButton="true" @confirm="handleConfirm"></my-uv-modal>
	</app-layout>
</template>

<style lang="scss">
page {
	background-color: $bg_color;
}
</style>
<style lang="scss" scoped>
.label-box {
	width: 100%;
	line-height: 73rpx;
	color: #828282;
	font-size: 24rpx;
	padding: 8rpx 0 8rpx 30rpx;
}

.device-card {
	width: 340rpx;
	height: 340rpx;
	border-radius: 20rpx;
	background-color: white;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin: 10rpx 16rpx;
	border: 3px solid transparent;
	transition: all 0.3s ease;
}

.device-card-selected {
	border: 3px solid #00b496;
}

.add-btn {
	width: 700rpx;
	height: 72rpx;
	background: linear-gradient(90deg, $main-green 0%, $main-green-dark 100%);
	color: #fff;
	font-size: 28rpx;
	border-radius: 36rpx;
	margin: 0 auto;
	display: block;
	box-shadow: 0 2rpx 8rpx 0 rgba(26, 188, 156, 0.08);
	text-align: center;
	letter-spacing: 2rpx;
	opacity: 0.95;
}

.add-btn-disabled {
	opacity: 0.5;
	background: linear-gradient(90deg, #999 0%, #666 100%);
}
</style>
