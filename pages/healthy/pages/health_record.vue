<!-- 健康-健康档案 -->
<script setup>
import { ref, reactive, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { timeFormat } from "@/uni_modules/uv-ui-tools/libs/function/index.js";
import { GetUserInfoByUserId, UpdateUserBasicInfo, CreateUserBasicInfo } from "@/server/api";
import { useHealthMemberInfoStore } from "@/store";
import { route } from "@/common/utils";
import { name, showToast } from "@/utils";

const healthMemberInfoStore = useHealthMemberInfoStore();
const memberInfo = healthMemberInfoStore.getMemberInfo(); // 成员信息
const isCreated = computed(() => memberInfo.isCreated); // 是否是创建者，否则无操作权限

const formRef = ref();

const dateSelectRef = ref();
const date = Number(new Date());
const dateValue = ref(date);

const maxDate = new Date().getTime();
// 点击出生日期
const handleBirthdateInputClick = () => {
	if (!isCreated.value) return;
	dateValue.value = new Date(formData.birth).getTime() || Number(new Date());
	dateSelectRef.value.open();
};

const handleDateConfirm = (e) => {
	formData.birth = timeFormat(e.value, "yyyy-mm-dd");
};
// 动态表单结构配置
const formSchema = computed(() => {
	return [
		{
			prop: "userName",
			label: "姓名",
			type: "input",
			required: true,
			props: {
				placeholder: "请填写您的姓名",
				disabled: !isCreated.value,
				disabledColor: "#ffffff",
			},
		},
		{
			prop: "sex",
			label: "性别",
			type: "radio",
			required: true,
			options: [
				{
					value: 1,
					label: "男",
				},
				{
					value: 2,
					label: "女",
				},
			],
			props: {
				disabled: !isCreated.value,
				disabledColor: "#ffffff",
			},
		},
		{
			prop: "birth",
			label: "出生日期",
			type: "input",
			required: true,
			showRight: true,
			props: {
				placeholder: "请选择",
				disabled: true,
				disabledColor: "#ffffff",
			},
			events: {
				click: handleBirthdateInputClick,
			},
		},
		{
			prop: "height",
			label: "身高",
			type: "input",
			inputType: "number",
			required: true,
			props: {
				placeholder: "请填写",
				type: "number",
				disabled: !isCreated.value,
				disabledColor: "#ffffff",
			},
			suffix: "cm",
		},
		{
			prop: "weight",
			label: "体重",
			type: "input",
			inputType: "number",
			required: true,
			props: {
				placeholder: "请填写",
				type: "number",
				disabled: !isCreated.value,
				disabledColor: "#ffffff",
			},
			suffix: "kg",
		},
		{
			prop: "waist",
			label: "腰围",
			type: "input",
			inputType: "number",
			required: true,
			props: {
				placeholder: "请填写",
				type: "number",
				disabled: !isCreated.value,
				disabledColor: "#ffffff",
			},
			suffix: "cm",
		},
		{
			prop: "circum",
			label: "臀围",
			type: "input",
			inputType: "number",
			required: true,
			props: {
				placeholder: "请填写",
				type: "number",
				disabled: !isCreated.value,
				disabledColor: "#ffffff",
			},
			suffix: "cm",
		},
	];
});

const radioList = [
	{
		label: "是",
		value: 1,
	},
	{
		label: "否",
		value: 2,
	},
];
// 疾病部分单独配置（也可和上面合并）
const diseaseSchema = [
	{
		prop: "ra",
		label: "类风湿性关节炎",
		type: "radio",
		options: radioList,
		props: {
			disabled: !isCreated.value,
		},
	},
	{
		prop: "op",
		label: "骨质疏松症",
		type: "radio",
		options: radioList,
		props: {
			disabled: !isCreated.value,
		},
	},
	{
		prop: "secondaryOp",
		label: "您是否患有骨质疏松相关疾病？",
		type: "radio",
		options: radioList,
		props: {
			disabled: !isCreated.value,
		},
	},
	{
		prop: "ach",
		label: "您是否服用过（强的松、塞米松）等药品？",
		type: "radio",
		options: radioList,
		props: {
			disabled: !isCreated.value,
		},
	},
];
// 您现在吸烟吗
const smokingSchema = [
	{
		prop: "smoke",
		label: "您现在吸烟吗？",
		type: "radio",
		options: [
			{
				label: "吸烟",
				value: 1,
			},
			{
				label: "戒烟",
				value: 2,
			},
			{
				label: "从不吸烟",
				value: 3,
			},
		],
		props: {
			disabled: !isCreated.value,
		},
	},
];

const form = {
	userName: "",
	sex: 1,
	birth: timeFormat(date, "yyyy-mm-dd"),
	height: "",
	weight: "",
	waist: "",
	circum: "",
	ra: 0,
	op: 0,
	secondaryOp: 0,
	ach: 0,
	smoke: 0,
};
const formData = reactive({
	...form,
});
// 姓名不能为空，也不能特殊字符

const rules = {
	userName: name(),
	sex: [
		{
			required: true,
			message: "请选择性别",
		},
	],
	birth: [
		{
			required: true,
			message: "请选择出生日期",
		},
	],
	height: [
		{
			required: true,
			message: "请输入身高",
		},
	],
	weight: [
		{
			required: true,
			message: "请输入体重",
		},
	],
	waist: [
		{
			required: true,
			message: "请输入腰围",
		},
	],
	circum: [
		{
			required: true,
			message: "请输入臀围",
		},
	],
};

const customStyle = {
	width: "158rpx",
	height: "70rpx",
	fontSize: "28rpx",
	border: "none",
};
const customStyleDef = {
	...customStyle,
	backgroundColor: "#F2F6F9",
	color: "#616161",
};
const customStyleActive = {
	...customStyle,
	backgroundColor: "#E8FAF3",
	color: "#00B698",
};

// 处理疾病选项点击
const handleDiseaseClick = (item, radio) => {
	formData[item.prop] = radio.value;
};

const isNewMember = ref(false); // 是否没有填写过健康档案的成员
// 获取用户基本信息
async function getUserInfoByUserId() {
	try {
		const res = await GetUserInfoByUserId({ userId: memberInfo.userId });
		// 当接口返回的数据里没有data时，表示没有填写过健康档案
		if (!res.data) {
			isNewMember.value = true;
			return;
		}
		isNewMember.value = false;
		Object.assign(formData, res.data);
		formData.birth = timeFormat(res.data.birth, "yyyy-mm-dd");
	} catch (error) {
		console.error("获取用户基本信息失败:", error);
	}
}

// 更新用户信息
async function updateUserBasicInfo() {
	try {
		await UpdateUserBasicInfo(formData);
		showToast({
			title: "更新成功",
			icon: "success",
			callback: () => {
				route({ type: "navigateBack" });
			},
		});
	} catch (error) {
		console.error("更新用户基本信息失败:", error);
	}
}

// 创建用户信息
async function createUserBasicInfo() {
	try {
		await CreateUserBasicInfo({ ...formData, userId: memberInfo.userId });
		showToast({
			title: "创建成功",
			icon: "success",
			callback: () => {
				route({ type: "navigateBack" });
			},
		});
	} catch (error) {
		console.error("创建用户基本信息失败:", error);
	}
}

async function saveBtn() {
	const valid = await formRef.value.validate();
	if (!valid) {
		uni.showToast({ title: "校验失败", icon: "none" });
		return;
	}
	isNewMember.value ? createUserBasicInfo() : updateUserBasicInfo();
}

// 页面加载时执行
onLoad(() => {
	getUserInfoByUserId();
});
</script>

<template>
	<app-layout class="max-w-750 overflow-x-hidden">
		<view class="label-box">以下为必选项</view>
		<uv-form :model="formData" ref="formRef" :rules="rules" label-width="220">
			<template v-for="item in formSchema" :key="item.prop">
				<uv-form-item :label="item.label" :prop="item.prop" borderBottom>
					<!-- 文本输入 -->
					<view v-if="item.type === 'input'" @click="item?.events?.click">
						<uv-input v-model="formData[item.prop]" v-bind="item.props" border="none" input-align="right">
							<template v-if="item.suffix" v-slot:suffix>
								<text>{{ item.suffix }}</text>
							</template>
						</uv-input>
					</view>
					<!-- 单选（性别/是或否） -->
					<uv-radio-group v-else-if="item.type === 'radio'" v-model="formData[item.prop]" activeColor="#00B496" v-bind="item.props" :customStyle="{ justifyContent: 'end' }">
						<uv-radio :customStyle="{ margin: '0 0 0 17rpx' }" v-for="(option, index) in item.options" :key="index" :label="option.label" :name="option.value"></uv-radio>
					</uv-radio-group>
					<!-- 箭头 -->
					<template v-if="item.showRight" v-slot:right>
						<uv-icon name="arrow-right"></uv-icon>
					</template>
				</uv-form-item>
			</template>

			<view class="label-box">您目前是否被医生诊断过患有下列疾病</view>
			<template v-for="item in diseaseSchema" :key="item.prop">
				<uv-form-item :label="item.label" :prop="item.prop" labelPosition="top" label-width="auto">
					<view class="flex gap-20 m-t-30">
						<uv-button v-for="radio in item.options" :key="item.id" :text="radio.label" shape="circle" :customStyle="radio.value === formData[item.prop] ? customStyleActive : customStyleDef" v-bind="item.props" @click="handleDiseaseClick(item, radio)"></uv-button>
					</view>
				</uv-form-item>
			</template>
			<view class="label-box"></view>
			<template v-for="item in smokingSchema" :key="item.prop">
				<uv-form-item :label="item.label" :prop="item.prop" labelPosition="top" label-width="auto">
					<view class="flex gap-20 m-t-30">
						<uv-button v-for="radio in item.options" :key="item.id" :text="radio.label" shape="circle" :customStyle="radio.value === formData[item.prop] ? customStyleActive : customStyleDef" v-bind="item.props" @click="handleDiseaseClick(item, radio)"></uv-button>
					</view>
				</uv-form-item>
			</template>
		</uv-form>
		<!-- 日期选择 -->
		<uv-datetime-picker ref="dateSelectRef" v-model="dateValue" mode="date" :minDate="0" :maxDate="maxDate" placeholder="请选择" @confirm="handleDateConfirm" />
		<!-- 占位 使用底部安全区后增加了68rpx占位 -->
		<view class="h-168"></view>
		<template v-if="isCreated">
			<view class="w-full fixed bottom-0 bg-white p-y-20">
				<button class="save-btn" @click="saveBtn">保存</button>
				<uv-safe-bottom></uv-safe-bottom>
			</view>
		</template>
	</app-layout>
</template>

<style lang="scss" scoped>
.label-box {
	width: 100%;
	line-height: 73rpx;
	color: #828282;
	background-color: #f1f3f7;
	font-size: 24rpx;
	padding: 8rpx 0 8rpx 30rpx;
}

.save-btn {
	width: 700rpx;
	height: 72rpx;
	background: linear-gradient(90deg, $main-green 0%, $main-green-dark 100%);
	color: #fff;
	font-size: 28rpx;
	border-radius: 36rpx;
	margin: 0 auto;
	display: block;
	box-shadow: 0 2rpx 8rpx 0 rgba(26, 188, 156, 0.08);
	text-align: center;
	letter-spacing: 2rpx;
	opacity: 0.95;
}

:deep {
	.uv-form-item {
		padding: 0 37rpx;
	}
	// 调整规则校验样式
	.uv-form-item__body__right__message {
		display: flex;
		justify-content: flex-end;
	}
}
</style>
