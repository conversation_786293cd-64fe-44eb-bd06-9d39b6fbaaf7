<!-- 健康-健康报告 -->
<script setup>
import { ref, computed, watch } from 'vue'
import { onLoad, onReady } from '@dcloudio/uni-app'

import { GetHealthReportWeekList } from '@/server/api'
import { useUserStore, useHealthMemberInfoStore } from '@/store'

import HealthReport from '../../tabBar/healthy/components/HealthReport.vue' // 健康周报
import MedicalReport from '../components/MedicalReport.vue' // 通用医疗报告
import SummaryOfRiskFactors from '../components/SummaryOfRiskFactors.vue' // 危险因素汇总
import RiskFactorControl from '../components/RiskFactorControl.vue' // 危险因素控制
import ChronicDiseaseRiskAssessment from '../components/ChronicDiseaseRiskAssessment.vue' // 慢性病风险评估
import DietaryAnalysis from '../components/DietaryAnalysis.vue' // 饮食分析
import baseEcharts from '@/pages/echarts/BaseEcharts.vue'
import useEchartsConfig from '../hooks/useEchartsConfig'
import useHealthReportsData from '../hooks/useHealthReportsData'
import References from '../components/References.vue'

// hooks
const { createSingleLineConfig, createBloodPressureConfig } = useEchartsConfig()
const { healthReportData, goodList, badList, factors, riskData, dietData, formworkData, getAllReportsData } = useHealthReportsData()

const userStore = useUserStore()
const userId = computed(() => userStore.userData.userId)

// 权限控制 - 只有创建者才能查看健康报告
const healthMemberInfoStore = useHealthMemberInfoStore()
const memberInfo = healthMemberInfoStore.getMemberInfo()
const isCreated = computed(() => memberInfo?.isCreated === true)

// 常量配置
const FORMWORK_STATUS = {
    1: '控制良好',
    2: '需要关注',
}

// 医疗报告配置
const medicalReportsConfig = [
    {
        key: 'fastingBg',
        title: '血糖报告',
        reportType: 'bloodGlucose',
        optionKey: 'bloodSugarOption',
        color: '#23b899',
        createConfig: (data) =>
            createSingleLineConfig({
                data: data?.range,
                name: '血糖',
                color: '#23b899',
            }),
    },
    {
        key: 'sbpdbp',
        title: '血压报告',
        reportType: 'bloodPressure',
        optionKey: 'bloodPressureOption',
        colors: ['#23b899', '#FF8237'],
        createConfig: (data) =>
            createBloodPressureConfig({
                systolicData: data?.range,
                diastolicData: data?.spare,
                name: ['收缩压', '舒张压'],
                colors: ['#23b899', '#FF8237'],
            }),
    },
    {
        key: 'uric',
        title: '尿酸报告',
        reportType: 'uric',
        optionKey: 'uricAcidOption',
        color: '#FF8237',
        createConfig: (data) =>
            createSingleLineConfig({
                data: data?.range,
                name: '尿酸',
                color: '#FF8237',
            }),
    },
    {
        key: 'bloodFat',
        title: '血脂报告',
        reportType: 'bloodFat',
        optionKey: 'bloodFatOption',
        color: '#FF5722',
        createConfig: (data) =>
            createSingleLineConfig({
                data: data?.range,
                name: '血脂',
                color: '#FF5722',
            }),
    },
]

// 周选择器相关
const pickerRef = ref()
const weekRange = ref('')
const selectedResultId = ref('') // 当前选中的报告ID
const weekList = ref([]) // 改为响应式数据，从接口获取
const columns = computed(() => [weekList.value]) // 改为计算属性

// 格式化周期显示文本
function formatWeekDisplay(item) {
    return `第${item.weekNo}周(${item.weekRange})`
}

// 图表配置选项
const chartOptions = ref({
    bloodSugarOption: [],
    bloodPressureOption: [],
    uricAcidOption: [],
    bloodFatOption: [],
})

// 周期选择器确认
function confirmPicker({ value }) {
    const selectedItem = value[0]
    if (!selectedItem) return

    // 使用格式化函数设置显示文本
    weekRange.value = formatWeekDisplay(selectedItem)
    selectedResultId.value = selectedItem.resultId
    loadAllReportsData()
}

// 更新图表配置
function updateChartOptions() {
    medicalReportsConfig.forEach((config) => {
        const data = formworkData.value[config.key]
        if (data?.show) {
            chartOptions.value[config.optionKey] = config.createConfig(data)
        }
    })
}

// 加载所有报告数据
async function loadAllReportsData() {
    if (!selectedResultId.value) {
        console.warn('未选择报告ID，无法加载数据')
        return
    }

    try {
        await getAllReportsData(selectedResultId.value)
        updateChartOptions()
    } catch (error) {
        console.error('加载报告数据失败:', error)
    }
}

// 计算属性：可见的医疗报告
const visibleMedicalReports = computed(() => {
    return medicalReportsConfig.filter((config) => formworkData.value[config.key]?.show)
})

// 获取状态框样式
function getStatusBoxStyle(status) {
    return {
        backgroundColor: status === 2 ? '#FF8237' : '#23b899',
    }
}

// 重置周报日期数据
function resetWeekData() {
    weekList.value = []
    weekRange.value = ''
    selectedResultId.value = ''
}

// 获取周报日期列表
async function getHealthReportWeekList() {
    if (!memberInfo.userId) {
        console.warn('用户ID不存在，无法获取周报日期列表')
        return
    }

    try {
        const { data } = await GetHealthReportWeekList({ userId: memberInfo.userId })

        if (!data || !Array.isArray(data) || data.length === 0) {
            resetWeekData()
            return
        }

        // 确保数据格式正确，使用格式化函数
        const formattedData = data.map((item) => ({
            ...item,
            label: formatWeekDisplay(item), // 使用格式化函数生成显示文本
        }))

        weekList.value = formattedData

        // 选择第一项作为默认值，使用格式化后的显示文本
        weekRange.value = weekList.value[0].label || '--'
        selectedResultId.value = formattedData[0].resultId || ''

        // 获取周报列表成功后，加载报告数据
        await loadAllReportsData()
    } catch (error) {
        resetWeekData()
        console.error('获取周报日期列表失败:', error)
    }
}

// 监听formworkData变化，更新图表配置
watch(
    () => formworkData.value,
    () => {
        updateChartOptions()
    },
    { deep: true },
)

onLoad(() => {
    getHealthReportWeekList()
})
</script>

<template>
    <app-layout>
        <!-- <ScrollTabs :tabs="tabList" contentHeight="calc(100vh - 100rpx)"> -->
        <uni-card class="menu_item">
            <template #title>
                <view class="flex justify-between items-center border-b-1 border-b-solid border-b-#ebeef5 p-20">
                    <text class="font-size-30 text-#3a3a3a">健康周报</text>
                    <view class="flex items-center" @click="pickerRef.open()">
                        <view class="font-size-28 text-#333333 m-r-16">
                            {{ weekRange }}
                        </view>
                        <uv-icon name="arrow-down" :size="16"></uv-icon>
                    </view>
                </view>
            </template>
            <HealthReport :isShowBtn="false" :healthReportData="healthReportData">
                <!-- <template #canvas>
					<canvas canvas-id="healthCircle" id="healthCircle" class="circle-canvas" width="100" height="100"></canvas>
				</template> -->
            </HealthReport>
        </uni-card>
        <!-- 医疗报告循环渲染 -->
        <template v-for="config in visibleMedicalReports" :key="config.key">
            <uni-card class="menu_item relative" :title="config.title">
                <!-- 状态 -->
                <view
                    class="absolute r-0 t-20 font-size-24 text-#fff p-[4rpx_14rpx] status-box"
                    :style="getStatusBoxStyle(formworkData[config.key]?.status)"
                >
                    {{ FORMWORK_STATUS[formworkData[config.key]?.status] || FORMWORK_STATUS[1] }}
                </view>
                <MedicalReport :medicalData="formworkData[config.key]" :reportType="config.reportType">
                    <template #echarts>
                        <base-echarts :options="chartOptions[config.optionKey]" />
                    </template>
                </MedicalReport>
            </uni-card>
        </template>
        <uni-card class="menu_item" title="危险因素汇总">
            <SummaryOfRiskFactors :goodList="goodList" :badList="badList" />
        </uni-card>
        <uni-card class="menu_item" title="危险因素控制及改善措施">
            <RiskFactorControl :factors="factors" />
        </uni-card>
        <uni-card class="menu_item" title="慢性病风险评估结果">
            <ChronicDiseaseRiskAssessment :riskData="riskData" />
        </uni-card>
        <uni-card class="menu_item" title="目前膳食摄入">
            <DietaryAnalysis :dietData="dietData" />
        </uni-card>
        <uni-card class="menu_item" title="信息参考">
            <References />
        </uni-card>
        <!-- <uni-card title="建议的饮食">
				<RecommendedDiet />
			</uni-card> -->
        <!-- 占位 -->
        <view class="w-full h-96"></view>
        <!-- </ScrollTabs> -->
        <!-- 提示 -->
        <view class="w-full h-96 bg-#D8DEE6 fixed b-0 l-0 text-center text-#5E6973 font-size-24 pt-20"
            >报告结果仅供参考，不构成医学建议，请咨询您的医生</view
        >
        <!-- 健康周报-报告-周期选择器 -->
        <uv-picker ref="pickerRef" :columns="columns" keyName="label" @confirm="confirmPicker"></uv-picker>
    </app-layout>
</template>

<style lang="scss" scoped>
.status-box {
    border-top-left-radius: 10rpx;
    border-bottom-left-radius: 10rpx;
}
</style>
