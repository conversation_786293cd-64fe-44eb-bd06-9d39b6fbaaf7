<!-- 全部设备-手动输入设备编号 -->
<script setup>
import { ref, reactive } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { route } from "@/common/utils";
import { useHealthDeviceTypeStore } from "@/store";

const healthDeviceTypeStore = useHealthDeviceTypeStore();

const snValue = ref("");

function nextBtn() {
	// 检查是否输入了设备编号
	if (!snValue.value) {
		uni.showToast({ title: "请输入设备编号", icon: "none" });
		return;
	}
	healthDeviceTypeStore.deviceTypeInfo.imei = snValue.value;
	route("pages/healthy/pages/equipment_info");
}

// 页面加载时执行
onLoad(() => {
	// TODO: 页面初始化逻辑
});
</script>

<template>
	<view class="p-[42rpx_60rpx]">
		<text class="color-#222222 font-size-28">手动输入设备编号（SN）</text>
		<uv-input placeholder="请输入" border="none" v-model="snValue" shape="circle"></uv-input>
		<button class="next-btn" @click="nextBtn">下一步</button>
	</view>
</template>

<style lang="scss" scoped>
.next-btn {
	width: 100%;
	height: 72rpx;
	background: linear-gradient(90deg, $main-green 0%, $main-green-dark 100%);
	color: #fff;
	font-size: 28rpx;
	border-radius: 36rpx;
	margin: 0 auto;
	display: block;
	box-shadow: 0 2rpx 8rpx 0 rgba(26, 188, 156, 0.08);
	text-align: center;
	letter-spacing: 2rpx;
	opacity: 0.95;
}

:deep {
	.uv-input__content {
		height: 100rpx;
		padding-left: 36rpx;
		margin: 34rpx auto 60rpx;
		border-radius: 50rpx;
		background-color: #f6f7f9;
	}
}
</style>
