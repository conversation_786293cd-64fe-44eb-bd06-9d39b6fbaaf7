<script setup>
import { ref, computed } from "vue";
import { usePublicStore, useHealthyNaireTypeStore, useHealthMemberInfoStore, useUserStore } from "@/store";
import { route } from "@/common/utils";
import { healthy } from "@/common/images";
import { totalBaseQuestionnaireList } from "@/common/questionnaire";

const publicStore = usePublicStore();
const userStore = useUserStore(); // 用户信息
const healthyNaireTypeStore = useHealthyNaireTypeStore(); // 问卷类型
const currentNaireType = healthyNaireTypeStore.getNaireType(); // 问卷类型
const navTitle = computed(() => (currentNaireType === 0 ? "基础版健康问卷" : "详尽版健康问卷"));

// 获取用户信息的性别，计算题目的数量
// 注意：这里只能基于性别条件计算，因为用户还未开始答题
const questionCount = computed(() => {
	return totalBaseQuestionnaireList.filter((item) => {
		if (item.condition) {
			// 只处理性别条件，答案条件在答题过程中动态处理
			if (item.condition.key === "sex") {
				return item.condition.value === userStore.userData.sex;
			}
			// 对于其他条件，暂时包含在内（实际数量可能会减少）
			return true;
		}
		return true;
	}).length;
});

const customStyle = {
	backgroundColor: "#23E3BC",
	borderRadius: "6rpx",
	padding: "25rpx 40rpx",
	fontSize: "34rpx",
	fontWeight: "bold",
	color: "#fff",
	width: "144rpx",
	height: "84rpx",
	marginLeft: "30rpx",
	border: "none",
};

function startTest() {
	route("/pages/healthy/pages/question_and_nswer");
}
</script>
<template>
	<app-layout class="max-w-750 overflow-x-hidden">
		<!-- 背景图片 -->
		<image :src="healthy.questionnaireNotesBg" class="absolute left-0 top-0 w-full z-0" style="height: 516px; object-fit: cover" mode="widthFix" />
		<!-- 自定义导航栏 -->
		<app-navBar back :fixed="true" bg-color="transparent">
			<template #content>
				<view class="w-full h-full flex justify-center items-center">{{ navTitle }}</view>
			</template>
		</app-navBar>
		<view class="w-full h-[90vh] relative" style="background: linear-gradient(180deg, #00c6a6 0%, #00cbc1 100%)" :style="{ marginTop: `${publicStore.menuButtonInfo.height + publicStore.menuButtonInfo.top + 20}px` }">
			<view>
				<!-- 问卷按钮 -->
				<!-- <uv-button text="问卷" :customStyle="customStyle"></uv-button> -->
				<!-- 问卷须知内容 -->
				<view class="z-10 mt-40 px-30">
					<text class="font-size-48 font-bold text-white">问卷须知</text>
					<view class="mt-20">
						<text class="font-size-32 font-bold text-white">这里是测评基础版健康风险评估！</text>
					</view>
					<view class="mt-20">
						<text class="font-size-28 text-white"> 本测评共有{{ questionCount }}题，你可以按照自己的第一反应做出选择，不需瞻前顾后，这样才能测出你的真实意愿。 </text>
					</view>
					<view class="mt-10">
						<text class="font-size-30 text-white"> 本测评不能作为专业的心理健康筛查工具。 </text>
					</view>
					<view class="mt-10">
						<text class="font-size-30 text-white"> 准备好了吗？我们马上开始噢！ </text>
					</view>
				</view>
				<!-- 底部按钮 -->
				<view class="fixed bottom-100 left-0 w-full flex justify-center z-20">
					<view class="w-556 h-106 bg-white b-rd-53 flex items-center justify-center font-size-34 text-[#00B496] shadow-lg" @click="startTest"> 开始测评 </view>
				</view>
			</view>
		</view>
	</app-layout>
</template>

<style lang="scss" scoped>
.pt-safe-top {
	padding-top: var(--window-top);
}
</style>
