<!-- 设备信息 -->
<script setup>
import { ref, watch, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { usePublicStore, useHealthDeviceTypeStore, useHealthMemberInfoStore } from "@/store";
import { CreateUserDevice, GetDeviceIsOnline } from "@/server/api";
import { route } from "@/common/utils";
import { showToast } from "@/utils";

const healthDeviceTypeStore = useHealthDeviceTypeStore(); // 获取当前选择的设备类型信息
const healthMemberInfoStore = useHealthMemberInfoStore(); // 获取当前选择的成员信息
const currentDeviceInfo = healthDeviceTypeStore.getDeviceTypeInfo();
const currentMemberInfo = healthMemberInfoStore.getMemberInfo();
const { userId, userName } = currentMemberInfo;
const { name, category, model, imei, videoUrl, id } = currentDeviceInfo;

const title = "设备信息";
const text = "确认添加";

// 是否是从人员设备列表进入
const isDeviceList = ref(false);

const formObj = {
	name,
	imei,
	userName,
	userId,
	model,
	category,
	onlineStatus: false,
};

const formData = ref({ ...formObj });

const cellGroups = computed(() => [
	{
		items: [
			{
				prop: "name",
				label: "设备名称",
				isLink: false,
			},
			{
				label: "所属家庭成员",
				prop: "userName",
			},
			{
				label: "设备类型",
				prop: "category",
			},
		],
	},
	{
		title: "设备信息",
		items: [
			{
				label: "设备型号",
				prop: "model",
			},
			{
				label: "在线状态",
				prop: "onlineStatus",
			},
		],
	},
	{
		title: "操作视频",
	},
]);

const statusMap = [
	{ value: true, label: "在线", color: "#1784FD" },
	{ value: false, label: "离线", color: "#888888" },
];
const getStatusMap = new Map(statusMap.map((item) => [item.value, { label: item.label, color: item.color }]));

// 获取单元格显示值
const getCellValue = (item) => {
	if (item.prop === "onlineStatus") {
		const status = getStatusMap.get(formData.value[item.prop]);
		return {
			text: status?.label || "未知",
			color: status?.color || "#888888",
			isStatus: true,
		};
	}
	return {
		text: formData.value[item.prop] || "",
		isStatus: false,
	};
};

const publicStore = usePublicStore();

// 新增设备
async function createUserDevice() {
	const params = {
		userId: formData.value.userId,
		deviceName: formData.value.name,
		imei: formData.value.imei,
		prodectName: formData.value.category,
		productId: id,
	};
	try {
		await CreateUserDevice(params);
		showToast({
			title: "添加成功",
			icon: "success",
			callback: () => {
				route({ url: "/pages/tabBar/healthy/healthy", type: "switchTab" });
			},
		});
	} catch (error) {
		console.log("createUserDevice error", error);
	}
}

// 获取设备在线状态
async function getDeviceIsOnline() {
	try {
		const { data } = await GetDeviceIsOnline({ imei });
		formData.value.onlineStatus = data;
	} catch (error) {
		console.error("获取设备在线状态失败：", error);
	}
}

watch(
	() => imei,
	(newVal) => {
		if (newVal) {
			getDeviceIsOnline();
		}
	},
	{ immediate: true }
);

onLoad((options) => {
	isDeviceList.value = options.isDeviceList;
});
</script>

<template>
	<app-layout class="max-w-750 overflow-x-hidden">
		<app-navBar back :fixed="true" bg-color="#fff">
			<template #content>
				<view class="w-full h-full flex justify-center items-center">
					{{ title }}
				</view>
			</template>
		</app-navBar>
		<view :style="{ marginTop: `${publicStore.menuButtonInfo.height + publicStore.menuButtonInfo.top}px` }" class="p-b-14">
			<uv-cell-group v-for="(group, groupIndex) in cellGroups" :key="groupIndex" :title="group.title" :border="true">
				<uv-cell v-for="(item, itemIndex) in group.items" :key="itemIndex" :icon="item.icon" :title="item.label" :value="null" :isLink="item.isLink">
					<template v-slot:value>
						<text v-if="getCellValue(item).isStatus" :style="{ color: getCellValue(item).color }">
							{{ getCellValue(item).text }}
						</text>
						<template v-else>
							{{ getCellValue(item).text }}
						</template>
					</template>
				</uv-cell>
			</uv-cell-group>
		</view>
		<!-- 产品视频 -->
		<template v-if="videoUrl">
			<!-- <view class="w-full px-40 mb-20 font-size-24 text-#828282">操作视频</view> -->
			<view class="mx-20">
				<video class="w-full" :src="videoUrl"></video>
			</view>
		</template>
		<view v-if="!isDeviceList" class="w-full fixed bottom-0 bg-white p-y-20">
			<button class="add-btn" @click="createUserDevice">{{ text }}</button>
		</view>
	</app-layout>
</template>

<style lang="scss" scoped>
:deep {
	.uv-cell-group {
		&__title {
			padding: 20rpx 37rpx !important;
			background-color: #f1f3f7 !important;
			&__text {
				font-size: 24rpx !important;
				color: #828282 !important;
			}
		}
	}
}
.add-btn {
	width: 700rpx;
	height: 72rpx;
	background: linear-gradient(90deg, $main-green 0%, $main-green-dark 100%);
	color: #fff;
	font-size: 28rpx;
	border-radius: 36rpx;
	margin: 0 auto;
	display: block;
	box-shadow: 0 2rpx 8rpx 0 rgba(26, 188, 156, 0.08);
	text-align: center;
	letter-spacing: 2rpx;
	opacity: 0.95;
}
</style>
