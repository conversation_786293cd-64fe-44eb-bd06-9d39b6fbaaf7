<!-- 健康-健康问卷 -->
<script setup>
import { computed, ref } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { useHealthMemberInfoStore, useHealthyNaireTypeStore } from '@/store'
import { GetUserQuestionnairePage, DeleteUserQuestionnaire } from '@/server/api'
import { healthy } from '@/common/images'

const healthMemberInfoStore = useHealthMemberInfoStore()
const memberInfo = healthMemberInfoStore.getMemberInfo() // 成员信息
const healthyNaireTypeStore = useHealthyNaireTypeStore()
const isCreated = computed(() => memberInfo.isCreated) // 是否是创建者，否则无操作权限

// 问卷卡片数据
const questionnaireTypes = [
    {
        key: 'basic',
        type: 0,
        title: '基础版',
        desc: '3分钟健康问卷',
        img: healthy.basicVersion,
        btnText: '参与填写',
        route: '/pages/healthy/pages/questionnaire_notes',
        disabled: false,
    },
    {
        key: 'exhaustive',
        type: 1,
        title: '详尽版',
        desc: '20分钟健康问卷',
        img: healthy.exhaustiveEdition,
        btnText: '参与填写',
        route: '/pages/healthy/pages/questionnaire_notes',
        disabled: true,
    },
]

// 我的问卷静态数据
const myQuestionnaires = ref([])

// 获取问卷类型
function getNaireType(type) {
    return type ? '详尽版健康问卷' : '基础版健康问卷'
}

// 参与填写
function handleFill(naireType) {
    if (!isCreated.value || naireType === 1) {
        uni.showToast({ title: '敬请期待', icon: 'none' })
        return
    }
    const item = questionnaireTypes.find((q) => q.type === naireType)
    if (item) {
        healthyNaireTypeStore.setNaireType(naireType)
        uni.navigateTo({ url: item.route })
    }
}

const modalRef = ref()
const currentId = ref('')
// 删除问卷
function handleDelete(id) {
    currentId.value = id
    modalRef.value.open()
}

// 确认删除
async function handleConfirm() {
    try {
        await DeleteUserQuestionnaire({ id: currentId.value })
        uni.showToast({ title: '删除成功', icon: 'success' })
        getUserQuestionnairePage()
    } catch (error) {
        console.error('删除问卷失败:', error)
    }
}
const pagingRef = ref()

// 获取用户所有健康问卷
async function getUserQuestionnairePage() {
    if (!memberInfo.userId) return
    try {
        const { data } = await GetUserQuestionnairePage({ userId: memberInfo.userId, pageNo: 1, pageSize: 10 })
        // myQuestionnaires.value = data.list;
        if (pagingRef.value) pagingRef.value.complete(data.list)
    } catch (error) {
        console.error('获取用户所有健康问卷失败:', error)
    }
}

function toDetail(id) {
    uni.navigateTo({ url: `/pages/healthy/pages/questionnaire_details?questionnaireId=${id}` })
}

async function queryList(pageNo, pageSize) {
    try {
        const { data } = await GetUserQuestionnairePage({ userId: memberInfo.userId, pageNo, pageSize })
        console.log('问卷数据---', data)

        if (pagingRef.value) pagingRef.value.complete(data.list)
    } catch (error) {
        if (pagingRef.value) pagingRef.value.complete(false)
    }
}

onShow(() => {
    getUserQuestionnairePage()
})
</script>

<template>
    <app-layout>
        <z-paging ref="pagingRef" v-model="myQuestionnaires" refresher-theme-style="black" use-refresher-status-bar-placeholder @query="queryList">
            <!-- 背景图 -->
            <image :src="healthy.questionnaireBg" class="absolute left-0 top-0 w-full z-0" style="height: 381px; object-fit: cover" mode="widthFix" />
            <app-navBar back :fixed="true" bg-color="transparent">
                <template #content>
                    <view class="w-full h-full flex justify-center items-center"> 健康问卷 </view>
                </template>
            </app-navBar>
            <view class="relative w-full">
                <!-- 问卷卡片区 -->
                <view class="flex flex-row justify-between px-20 mt-30 z-10 relative">
                    <view
                        v-for="item in questionnaireTypes"
                        :key="item.key"
                        class="flex-1 mx-8 relative flex flex-col items-center py-24"
                        @click="handleFill(item.type)"
                    >
                        <!-- 图片作为背景 -->
                        <image :src="item.img" class="w-345 h-431 block" mode="aspectFill" />
                        <!-- 覆盖内容 -->
                        <view class="absolute left-0 top-0 w-full h-full flex flex-col justify-between items-start px-70 py-80">
                            <!-- 标题和描述在图片上部 -->
                            <view class="flex flex-col pt-24">
                                <text
                                    class="font-size-36 font-bold"
                                    :style="{ color: item.disabled ? '#ccc' : '#fff' }"
                                    style="text-shadow: 0 2px 8px rgba(0, 0, 0, 0.18)"
                                    >{{ item.title }}</text
                                >
                                <text class="font-size-24 color-#fff mt-8 opacity-70" style="text-shadow: 0 2px 8px rgba(0, 0, 0, 0.18)">{{
                                    item.desc
                                }}</text>
                            </view>
                            <!-- 参与填写按钮在图片下部 -->
                            <view class="flex justify-center pb-24">
                                <view class="flex items-center font-size-26" :style="{ color: item.disabled ? '#ccc' : '#fff' }">
                                    <text>{{ item.btnText }}</text>
                                    <uv-icon name="arrow-right" :color="item.disabled ? '#ccc' : '#fff'"></uv-icon>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <!-- 我的问卷区 -->
                <view class="mt-40 px-20 z-10 relative">
                    <view class="flex justify-between mb-10">
                        <view class="flex flex-col items-start">
                            <text class="font-size-30 font-bold">我的问卷</text>
                            <view class="flex justify-center w-full mt-20">
                                <view class="w-38 h-5 bg-#00B496 rounded-2.5"></view>
                            </view>
                        </view>
                        <text class="font-size-26 color-#00B496 ml-8">注意：问卷需要28天更新</text>
                    </view>

                    <uv-list>
                        <uv-list-item
                            v-for="item in myQuestionnaires"
                            :key="item.id"
                            :title="getNaireType(item.naireType)"
                            :note="item.createTime"
                            :clickable="true"
                            @click="toDetail(item.id)"
                        >
                            <template #header>
                                <image :src="healthy.questionnaire" class="w-86 h-106 mr-50" mode="aspectFit" />
                            </template>
                            <template v-if="isCreated" #footer>
                                <!-- 在icon外层增加标签，防止无法使用stop修饰符 -->
                                <view @click.stop="handleDelete(item.id)">
                                    <uv-icon name="trash" :size="20" color="#999999"></uv-icon>
                                </view>
                            </template>
                        </uv-list-item>
                    </uv-list>
                </view>
            </view>
            <!-- 删除提示弹窗 -->
            <my-uv-modal ref="modalRef" content="确定删除该问卷吗？" align="center" :showCancelButton="true" @confirm="handleConfirm"></my-uv-modal>
        </z-paging>
    </app-layout>
</template>

<style lang="scss" scoped>
:deep {
    .uv-list-item__container {
        padding: 0 !important;
        margin: 20rpx 0;
    }
}
</style>
