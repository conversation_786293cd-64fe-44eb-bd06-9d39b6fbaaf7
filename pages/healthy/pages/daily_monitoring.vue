<!-- 健康-日常监测 -->
<script setup>
import { ref, onMounted, nextTick, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { GetHealthReportDailyMonitor } from '@/server/api'
import { useHealthMemberInfoStore } from '@/store'
import { formatDate, addTime, subtractTime, getDatePart } from '@/utils'
import StatCard from '../components/StatCard.vue'
import baseEcharts from '@/pages/echarts/BaseEcharts.vue'
import { healthy, dailyMonitor } from '@/common/images'
import useEchartsConfig from '../hooks/useEchartsConfig'

const healthMemberInfoStore = useHealthMemberInfoStore()
const currentMemberInfo = computed(() => healthMemberInfoStore.getMemberInfo())

const { createSingleLineConfig, createBloodPressureConfig } = useEchartsConfig()

// 指标类型配置
const indicatorTypes = [
    {
        dataType: 'blood_pressure',
        name: '血压',
        unit: 'mmhg',
        isDoubleIndicator: true, // 是否为双指标（收缩压/舒张压）
        color: ['#23b899', '#FF8237'],
        tabName: '血压趋势',
    },
    {
        dataType: 'blood_glucose',
        name: '血糖',
        unit: 'mmol/L',
        isDoubleIndicator: false,
        color: '#23b899',
        tabName: '血糖趋势',
    },
    {
        dataType: 'uric_acid',
        name: '尿酸',
        unit: 'μmol/L',
        isDoubleIndicator: false,
        color: '#FF8237',
        tabName: '尿酸趋势',
    },
]

// 当前选择的指标类型
const currentIndicatorType = ref(indicatorTypes[0]) // 默认血压

// 处理页面参数，设置初始指标类型
function handlePageParams(options) {
    // 如果有传递 dataType 参数，设置对应的指标类型
    if (options && options.dataType) {
        const targetIndicator = indicatorTypes.find((item) => item.dataType === options.dataType)
        if (targetIndicator) {
            currentIndicatorType.value = targetIndicator
        }
    }
}

const currentDate = new Date()
const date = ref(formatDate(currentDate, 'YYYY-MM'))

// 设置日期范围限制
const currentYear = currentDate.getFullYear()
const currentMonth = currentDate.getMonth() + 1 // getMonth() 返回 0-11，需要 +1

// 最小日期：今年1月
const minDate = ref(new Date(currentYear, 0, 1).getTime())
// 最大日期：当前月
const maxDate = ref(currentDate.getTime())

const datetimePickerRef = ref(null)

// 统一日期格式化函数
function ensureDateFormat(dateValue) {
    try {
        // 如果已经是 YYYY-MM 格式，直接返回
        if (typeof dateValue === 'string' && /^\d{4}-\d{2}$/.test(dateValue)) {
            return dateValue
        }
        // 否则格式化为 YYYY-MM
        return formatDate(new Date(dateValue), 'YYYY-MM')
    } catch (error) {
        console.error('日期格式化失败:', error)
        return formatDate(new Date(), 'YYYY-MM')
    }
}

// 显示日期
const displayDate = computed(() => {
    try {
        const formattedDate = ensureDateFormat(date.value)
        return formatDate(formattedDate, 'YYYY年MM月')
    } catch (error) {
        return formatDate(new Date(), 'YYYY年MM月')
    }
})

// 检查是否可以切换到上个月
const canGoPrevMonth = computed(() => {
    try {
        const currentDateStr = ensureDateFormat(date.value)
        const [year, month] = currentDateStr.split('-').map(Number)

        // 如果当前是今年1月，不能再往前
        if (year === currentYear && month <= 1) {
            return false
        }

        return true
    } catch (error) {
        return false
    }
})

// 检查是否可以切换到下个月
const canGoNextMonth = computed(() => {
    try {
        const currentDateStr = ensureDateFormat(date.value)
        const [year, month] = currentDateStr.split('-').map(Number)

        // 如果当前是今年当前月，不能再往后
        if (year === currentYear && month >= currentMonth) {
            return false
        }

        return true
    } catch (error) {
        return false
    }
})

// 切换月份
function changeMonth(offset) {
    try {
        // 使用统一的日期格式化函数
        const currentDateStr = ensureDateFormat(date.value)
        const currentDate = currentDateStr + '-01' // 添加日期以便处理

        let newDate
        if (offset > 0) {
            // 下个月
            newDate = addTime(currentDate, 1, 'month')
        } else {
            // 上个月
            newDate = subtractTime(currentDate, 1, 'month')
        }

        // 检查新日期是否在允许范围内
        const newDateObj = new Date(newDate)
        const newYear = newDateObj.getFullYear()
        const newMonth = newDateObj.getMonth() + 1

        // 限制范围：今年1月到当前月
        if (newYear < currentYear || newYear > currentYear) {
            // 不在今年范围内，不允许切换
            return
        }

        if (newYear === currentYear && newMonth > currentMonth) {
            // 超过当前月，不允许切换
            return
        }

        if (newYear === currentYear && newMonth < 1) {
            // 小于1月，不允许切换
            return
        }

        // 在允许范围内，更新日期
        date.value = formatDate(newDate, 'YYYY-MM')

        // 重新获取数据
        getHealthReportDailyMonitor()
    } catch (error) {
        console.error('切换月份失败:', error)
        // 如果出错，重置为当前月份
        date.value = formatDate(new Date(), 'YYYY-MM')
        getHealthReportDailyMonitor()
    }
}

// 日期选择器确认
function onDatePickerConfirm(e) {
    try {
        if (e && e.value) {
            // 使用统一的日期格式化函数
            date.value = ensureDateFormat(e.value)
            // 重新获取数据
            getHealthReportDailyMonitor()
        }
    } catch (error) {
        console.error('日期选择器确认失败:', error)
        // 如果出错，保持当前日期不变
    }
}

const barStyle = {
    background: '#198B6B',
    borderRadius: '3rpx',
}
const inactiveStyle = {
    fontWeight: 500,
    fontSize: '30rpx',
}
const activeStyle = {
    fontWeight: 'bold',
    fontSize: '32rpx',
}

const imgMap = {
    blood_pressure: dailyMonitor.monitorBloodPressure,
    blood_glucose: dailyMonitor.monitorBloodSugar,
    uric_acid: dailyMonitor.monitorUricAcid,
    blood_fat: dailyMonitor.monitorBloodFat,
}
const list = computed(() => {
    return [imgMap[currentIndicatorType.value.dataType]]
})

// 动态卡片配置
const cardConfigs = computed(() => [
    {
        icon: healthy.passRate,
        title: '合格率',
        valueKey: 'curMonthPassRate',
        subKey: 'lastMonthPassRate',
        subFormat: '上月同期{value}',
    },
    {
        icon: healthy.max,
        title: '最高值',
        unit: currentIndicatorType.value.unit,
        valueKey: 'maxValue',
        subKey: 'maxValueTime',
        subFormat: '{value}',
    },
    {
        icon: healthy.min,
        title: '最低值',
        unit: currentIndicatorType.value.unit,
        valueKey: 'minValue',
        subKey: 'minValueTime',
        subFormat: '{value}',
    },
])

const statCards = ref([])

// 默认初始化卡片数据
function initDefaultCards() {
    statCards.value = cardConfigs.value.map((config) => ({
        icon: config.icon,
        title: config.title,
        value: '--',
        unit: config.unit,
        sub: config.subFormat.replace('{value}', '--'),
    }))
}

// 根据API返回数据更新卡片
function updateStatCards(data) {
    statCards.value = cardConfigs.value.map((config) => {
        const value = data[config.valueKey] || '--'
        const subValue = data[config.subKey] || '--'
        return {
            icon: config.icon,
            title: config.title,
            value: value,
            unit: config.unit,
            sub: config.subFormat.replace('{value}', subValue),
        }
    })
}

const tabsRef = ref()
const current = ref(0)
const tabList = computed(() => [
    {
        name: currentIndicatorType.value.tabName,
        value: 1,
    },
    {
        name: `${currentIndicatorType.value.name}数据`,
        value: 2,
    },
])

function clickSwiperItem(item) {
    console.log(item)
}

async function tabsChange(index) {
    current.value = index
}

const seriesData = ref({})

// 图表数据
const bloodPressureData = ref([])

// 图表配置
const chartConfig = computed(() => {
    if (!seriesData.value.dates || seriesData.value.dates.length === 0) {
        return {}
    }

    if (currentIndicatorType.value.isDoubleIndicator) {
        // 血压双指标图表
        return createBloodPressureConfig({
            systolicData: seriesData.value.fronts || [],
            diastolicData: seriesData.value.hinders || [],
            xAxisData: seriesData.value.dates || [],
            colors: currentIndicatorType.value.color,
            name: ['收缩压', '舒张压'],
        })
    } else {
        // 单指标图表（血糖、尿酸等）
        // 对于单指标，使用fronts数组作为数据源
        return createSingleLineConfig({
            data: seriesData.value.fronts || [],
            xAxisData: seriesData.value.dates || [],
            color: currentIndicatorType.value.color,
            name: currentIndicatorType.value.name,
        })
    }
})

async function getHealthReportDailyMonitor() {
    const params = {
        userId: currentMemberInfo.value.userId,
        date: `${date.value}-01`,
        dataType: currentIndicatorType.value.dataType,
    }
    try {
        const { data } = await GetHealthReportDailyMonitor(params)

        // 检查数据是否为空或无效
        if (!data || Object.keys(data).length === 0) {
            console.log('接口返回空数据，使用默认数据')
            initDefaultCards()
            // 设置图表默认空数据
            seriesData.value = {
                fronts: [],
                hinders: [],
                dates: [],
            }
            bloodPressureData.value = []
            return
        }

        // 验证必要字段是否存在
        const { fronts = [], hinders = [], dates = [], datas = [] } = data

        // 更新卡片数据
        updateStatCards(data)

        // 处理日期格式 - 时间列表只展示月日，不展示年份
        const formattedDates = dates.map((item) => {
            if (typeof item === 'string' && item.includes('-')) {
                return item.split('-').slice(1).join('-')
            }
            return item
        })

        seriesData.value = {
            fronts,
            hinders,
            dates: formattedDates,
        }
        bloodPressureData.value = datas
    } catch (error) {
        console.error('获取日常监测结果失败:', error)
        // 发生错误时也要初始化默认数据
        initDefaultCards()
        seriesData.value = {
            fronts: [],
            hinders: [],
            dates: [],
        }
        bloodPressureData.value = []
        // 可以考虑添加用户提示
        uni.showToast({
            title: '数据加载失败',
            icon: 'none',
            duration: 2000,
        })
    }
}

// 页面加载时处理参数
onLoad((options) => {
    handlePageParams(options)
})

onMounted(() => {
    initDefaultCards()
    getHealthReportDailyMonitor()
})
</script>

<template>
    <app-layout>
        <!-- banner -->
        <view class="px-20">
            <view class="mt-20">
                <uv-swiper
                    :list="list"
                    keyName="image"
                    indicatorMode="dot"
                    height="260rpx"
                    radius="20rpx"
                    @click="clickSwiperItem"
                    indicator
                    circular
                ></uv-swiper>
            </view>
        </view>
        <view class="w-full leading-73rpx text-#828282 bg-#f1f3f7 text-24rpx py-8rpx pl-30rpx my-40rpx">{{ currentIndicatorType.name }}记录</view>
        <!-- 日期 -->
        <view class="flex justify-center items-center">
            <uv-icon name="arrow-left" size="16" :color="canGoPrevMonth ? '#000' : '#ccc'" @click="canGoPrevMonth && changeMonth(-1)"></uv-icon>
            <text class="font-size-36 font-bold color-#1a1a1a m-x-25" @click="() => datetimePickerRef.open()">{{ displayDate }}</text>
            <uv-icon name="arrow-right" size="16" :color="canGoNextMonth ? '#000' : '#ccc'" @click="canGoNextMonth && changeMonth(1)"></uv-icon>
        </view>
        <!-- 卡片 -->
        <view class="flex justify-evenly items-center m-t-50">
            <stat-card
                v-for="item in statCards"
                :key="item.id"
                :icon="item.icon"
                :title="item.title"
                :value="item.value"
                :unit="item.unit"
                :sub="item.sub"
            ></stat-card>
        </view>
        <!-- 图表 -->
        <view class="m-t-70 p-x-20">
            <z-tabs
                :bar-style="barStyle"
                :inactive-style="inactiveStyle"
                :active-style="activeStyle"
                ref="tabsRef"
                :list="tabList"
                :current="current"
                bottom-space="18rpx"
                active-color="#292929"
                inactive-color="#292929"
                tab-width="100"
                bar-height="5rpx"
                @change="tabsChange"
            >
                <!-- <template #right>
					<uv-button shape="circle" size="small" plain :icon="healthy.input" :customStyle="{ padding: '0 20rpx' }" text="手动录入"></uv-button>
				</template> -->
            </z-tabs>
            <template v-if="current === 0">
                <base-echarts :options="chartConfig" />
            </template>
            <template v-if="current === 1">
                <view v-for="item in bloodPressureData" :key="item.id" class="p-20 my-40 steps-border">
                    <view class="flex mb-38">
                        <uv-icon name="calendar"></uv-icon>
                        <text class="font-size-30 font-bold color-#1a1a1a ml-25">{{ item.date }}</text>
                    </view>
                    <uv-steps :current="item.value.length - 1" direction="column" dot activeColor="#198B6B">
                        <uv-steps-item v-for="x in item.value" :key="x.id">
                            <template #title>
                                <view class="flex w-full">
                                    <text class="w-100 text-left font-size-28 color-#6D6D6C">
                                        {{ x.time.substring(11, 16) }}
                                    </text>
                                    <text class="w-150 text-left font-size-28 mx-30" :style="{ color: x.desc !== '正常' ? '#F93C35' : '#6D6D6C' }">{{
                                        x.desc
                                    }}</text>
                                    <text class="w-200 text-left font-size-28 color-#6D6D6C">
                                        {{ `${x.value}${currentIndicatorType.unit}` }}
                                    </text>
                                </view>
                            </template>
                        </uv-steps-item>
                    </uv-steps>
                </view>
            </template>
        </view>
        <uv-datetime-picker
            ref="datetimePickerRef"
            v-model="date"
            :minDate="minDate"
            :maxDate="maxDate"
            mode="year-month"
            @confirm="onDatePickerConfirm"
        ></uv-datetime-picker>
    </app-layout>
</template>
<style lang="scss" scoped>
.steps-border {
    border-bottom: 2rpx solid #edeef0;
}
</style>
