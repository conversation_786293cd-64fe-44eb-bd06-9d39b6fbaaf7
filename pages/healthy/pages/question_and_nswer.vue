<!-- 健康-问卷答题 -->
<script setup>
import { ref, onUnmounted, computed, watch } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { route } from "@/common/utils";
import { totalBaseQuestionnaireList } from "@/common/questionnaire";
import { useHealthMemberInfoStore, useHealthyNaireTypeStore } from "@/store";
import { CreateUserQuestionnaire } from "@/server/api";
import { healthy } from "@/common/images";
import { throttle } from "@/utils";

const healthMemberInfoStore = useHealthMemberInfoStore();
const memberInfo = healthMemberInfoStore.getMemberInfo(); // 成员信息
const healthyNaireTypeStore = useHealthyNaireTypeStore(); // 问卷类型

const currentNaireType = healthyNaireTypeStore.getNaireType(); // 问卷类型

// 根据问卷类型显示对应背景图片
const nswerBgImg = computed(() => {
	return currentNaireType === 0 ? healthy.nswerBg : healthy.nswer1Bg;
});
console.log("wenjuan---", memberInfo);

// 题目数据，每个对象包含题干和选项
const questions = computed(() => {
	// 根据当前人的性别和用户答案处理数据
	return totalBaseQuestionnaireList.filter((item) => {
		if (item.condition) {
			// 处理性别条件
			if (item.condition.key === "sex") {
				return item.condition.value === memberInfo.sex;
			}
			// 处理基于用户答案的条件
			else {
				const userAnswer = answers.value[item.condition.key];
				return userAnswer === item.condition.value;
			}
		}
		return true;
	});
});

// 修改 answers 的数据结构为对象形式
const answers = ref({});

// 初始化input类型题目的默认值
function initializeDefaultValues() {
	questions.value.forEach((question) => {
		if (question.type === "input" && question.defaultValue !== undefined) {
			if (answers.value[question.key] === undefined) {
				answers.value[question.key] = question.defaultValue;
			}
		}
	});
}

// 监听questions变化，初始化默认值
watch(
	questions,
	() => {
		initializeDefaultValues();
	},
	{ immediate: true }
);

// 修改选择选项函数
function selectOption(idx) {
	if (timer.value === 0 || isPopupOpen()) return;

	const currentQuestion = questions.value[current.value];
	const selectedOption = currentQuestion.options[idx];

	if (currentQuestion.type === "multiselect") {
		// 多选逻辑
		if (!answers.value[currentQuestion.key]) {
			answers.value[currentQuestion.key] = [];
		}

		const currentAnswers = answers.value[currentQuestion.key];
		const optionValue = selectedOption.value;

		// 如果选择了"无"选项（空字符串），清空其他选项
		if (optionValue === "") {
			answers.value[currentQuestion.key] = [optionValue];
		} else {
			// 如果当前已选择"无"，先清空
			const noneIndex = currentAnswers.findIndex((val) => val === "");
			if (noneIndex !== -1) {
				currentAnswers.splice(noneIndex, 1);
			}

			// 切换选项状态
			const existingIndex = currentAnswers.indexOf(optionValue);
			if (existingIndex !== -1) {
				currentAnswers.splice(existingIndex, 1);
			} else {
				currentAnswers.push(optionValue);
			}
		}
	} else {
		// 单选逻辑
		answers.value[currentQuestion.key] = selectedOption.value;
	}

	// 检查当前题目索引是否超出范围，如果超出则调整到最后一题
	if (current.value >= questions.value.length) {
		current.value = questions.value.length - 1;
	}
}

// 添加计算属性来判断选项是否被选中
const isOptionSelected = (questionKey, optionValue, questionType) => {
	const answer = answers.value[questionKey];

	if (questionType === "multiselect") {
		return Array.isArray(answer) && answer.includes(optionValue);
	}

	return answer === optionValue;
};

// 简化结果收集逻辑
const questionnaireResults = computed(() => {
	const results = {};

	// 定义家族疾病史题目的key值
	const familyDiseaseKeys = ["diab", "hbp", "stroke", "copd", "gout", "fracture"];

	// 处理答案格式
	Object.keys(answers.value).forEach((key) => {
		const answer = answers.value[key];
		const question = questions.value.find((q) => q.key === key);

		if (question && question.type === "multiselect" && Array.isArray(answer)) {
			// 家族疾病史特殊处理：将多选数组转换为独立字段
			if (familyDiseaseKeys.includes(key)) {
				// 检查是否选择了"无"选项
				const hasNoneOption = answer.includes("");

				if (!hasNoneOption) {
					// 没有选择"无"，遍历选中的选项，每个选项作为独立字段设为1
					answer.forEach((selectedValue) => {
						if (selectedValue && selectedValue !== "") {
							results[selectedValue] = 1;
						}
					});
				}
				// 如果选择了"无"，则不传任何相关字段（符合API文档要求）
				// 不保存原始的多选key
			} else {
				// 其他多选题保持原有逻辑：转换为逗号分隔的字符串
				results[key] = answer.join(",");
			}
		} else {
			results[key] = answer;
		}
	});

	// 运动问卷特殊处理：根据sport_type选择，只保留对应的运动字段
	if (results.sport_type) {
		const sportType = results.sport_type;

		// 定义运动类型对应的字段映射
		const sportFieldsMap = {
			heavy: ["h_activity_freq", "h_act_tim_length"],
			medium: ["m_activity_freq", "m_act_tim_length"],
			light: ["walking_day", "walking_minutes"],
		};

		// 获取所有运动相关字段
		const allSportFields = ["h_activity_freq", "h_act_tim_length", "m_activity_freq", "m_act_tim_length", "walking_day", "walking_minutes"];

		// 获取当前选择的运动类型对应的字段
		const keepFields = sportFieldsMap[sportType] || [];

		// 移除不需要的运动字段
		allSportFields.forEach((field) => {
			if (!keepFields.includes(field)) {
				delete results[field];
			}
		});

		// 移除sport_type字段本身
		delete results.sport_type;

		// 移除旧的运动字段（如果存在）
		delete results.sport_days;
		delete results.sport_time;
	}

	return results;
});

// 当前题目索引（0基）
const current = ref(0);
// 是否正在切题中，防止重复点击
const animating = ref(false);
// 题目内容显示状态，用于CSS过渡效果
const questionVisible = ref(true);
// 答题倒计时（秒）
const timer = ref(179);
// 弹窗显示状态
const popupVisible = ref(false);
let interval = null;

/**
 * 判断弹窗是否显示
 */
function isPopupOpen() {
	return popupVisible.value;
}

/**
 * 添加必填项验证函数
 */
function validateCurrentQuestion() {
	const currentQuestion = questions.value[current.value];
	if (!currentQuestion.required) return true;

	const answer = answers.value[currentQuestion.key];

	// 对于input类型，检查是否有有效的数值
	if (currentQuestion.type === "input") {
		return answer !== undefined && answer !== null && answer !== "";
	}

	// 对于multiselect类型，检查是否至少选择了一项
	if (currentQuestion.type === "multiselect") {
		return Array.isArray(answer) && answer.length > 0;
	}

	// 对于select类型，检查是否已选择
	return answer !== undefined;
}

const questionnaireId = ref(""); // 创建的问卷id
// 创建问卷
async function createUserQuestionnaire() {
	const params = {
		userId: memberInfo.userId,
		naireType: currentNaireType, // 获取问卷类型
		naireContent: JSON.stringify(questionnaireResults.value),
	};
	try {
		const { data } = await CreateUserQuestionnaire(params);
		questionnaireId.value = data;
		popupVisible.value = true;
	} catch (error) {
		console.error("创建问卷失败：", error);
	}
}
/**
 * 提交问卷，打开结束弹窗
 */
function openEndModal() {
	// 清除定时器
	if (interval) {
		clearInterval(interval);
		interval = null;
	}
	createUserQuestionnaire();
}

/**
 * 切换到下一题的核心逻辑
 */
function switchToNextQuestion() {
	if (timer.value === 0 || isPopupOpen()) return;

	// 添加必填项验证
	if (!validateCurrentQuestion()) {
		uni.showToast({
			title: "请选择",
			icon: "none",
		});
		return;
	}

	if (current.value < questions.value.length - 1 && !animating.value) {
		animating.value = true;
		// 先淡出当前题目
		questionVisible.value = false;

		setTimeout(() => {
			// 切换题目
			current.value++;
			// 淡入新题目
			questionVisible.value = true;
			animating.value = false;
		}, 300); // 过渡时间
	} else if (current.value === questions.value.length - 1) {
		// 最后一题，弹出结束弹窗
		openEndModal();
	}
}

/**
 * 下一题逻辑 - 使用节流防止重复点击
 */
const nextQuestion = throttle(switchToNextQuestion, 300);

/**
 * 切换到上一题的核心逻辑
 */
function switchToPrevQuestion() {
	if (timer.value === 0 || isPopupOpen()) return;
	if (current.value > 0 && !animating.value) {
		animating.value = true;
		// 先淡出当前题目
		questionVisible.value = false;

		setTimeout(() => {
			// 切换题目
			current.value--;
			// 淡入新题目
			questionVisible.value = true;
			animating.value = false;
		}, 300); // 过渡时间
	}
}

/**
 * 上一题逻辑 - 使用节流防止重复点击
 */
const prevQuestion = throttle(switchToPrevQuestion, 300);

/**
 * 查看结果，跳转到结果页
 */
function goToResult() {
	// TODO: 跳转到结果页，传递answers.value等参数
	uni.redirectTo({ url: `/pages/healthy/pages/questionnaire_details?questionnaireId=${questionnaireId.value}` });
}

/**
 * 返回首页
 */
function goToHome() {
	uni.switchTab({ url: "/pages/tabBar/healthy/healthy" });
}

// 添加数值输入处理函数
function handleNumberInput(value) {
	const currentQuestion = questions.value[current.value];

	// 根据数据类型处理值
	if (currentQuestion.dataType === "integer") {
		answers.value[currentQuestion.key] = parseInt(value) || 0;
	} else {
		answers.value[currentQuestion.key] = parseFloat(value) || 0;
	}
}

// 倒计时逻辑，时间到自动弹窗
// onLoad(() => {
// 	interval = setInterval(() => {
// 		if (timer.value > 0) {
// 			timer.value--;
// 			if (timer.value === 0) {
// 				popupVisible.value = true;
// 			}
// 		}
// 	}, 1000);
// });

// 组件卸载时清理定时器
onUnmounted(() => {
	if (interval) clearInterval(interval);
});
</script>

<template>
	<app-layout class="max-w-750 overflow-x-hidden">
		<!-- 背景图片 -->
		<image :src="nswerBgImg" class="absolute w-[685rpx] h-[460rpx] left-[65rpx] top-[40rpx] z-1" mode="aspectFit" />
		<!-- 提示文字 -->
		<view class="absolute top-360 left-60 z-1 font-size-26 text-white">这里是测评{{ currentNaireType === 0 ? "基础版" : "详尽版" }}健康风险评估！</view>
		<!-- 自定义导航栏 -->
		<app-navBar back :fixed="true" bg-color="transparent" class="z-20 fixed t-0 l-0"> </app-navBar>
		<view class="w-full min-h-screen relative" style="background: linear-gradient(180deg, #00c6a6 0%, #00cbc1 100%)">
			<!-- 答题区域 -->
			<view class="relative z-10 flex flex-col items-center pt-[440rpx] pb-[120rpx]">
				<view class="w-11/12 bg-white shadow-lg p-[50rpx] b-rd-40">
					<!-- 问题进度与倒计时 -->
					<view class="flex items-center justify-between mb-[20rpx]">
						<view class="flex items-end">
							<text class="font-bold" style="font-size: 30rpx; color: #00b698">问题{{ current + 1 }}</text>
							<text style="font-size: 24rpx; color: #6d6d6c">/{{ questions.length }}</text>
						</view>
						<!-- <text class="font-bold" style="font-size: 28rpx; color: #ff7a00">{{ ((timer / 60) | 0).toString().padStart(2, "0") }}:{{ (timer % 60).toString().padStart(2, "0") }}</text> -->
					</view>
					<!-- 进度条 -->
					<view class="w-full h-[6rpx] flex items-center mb-[20rpx]">
						<view class="progress-bar-bg">
							<view class="progress-bar-fg" :style="{ width: ((current + 1) / questions.length) * 100 + '%' }"></view>
						</view>
					</view>
					<!-- 题目内容区 -->
					<div class="question-content-wrap">
						<div :key="current" class="question-content" :class="{ 'question-visible': questionVisible, 'question-hidden': !questionVisible }">
							<!-- 题目 -->
							<view class="mb-[32rpx]">
								<text class="font-bold" style="font-size: 34rpx; color: #222">{{ questions[current].question }}</text>
								<!-- 多选题提示 -->
								<view v-if="questions[current].type === 'multiselect'" class="multiselect-hint">
									<text>可选择多项</text>
								</view>
							</view>
							<!-- 选项 -->
							<view class="flex flex-col gap-[18rpx] mt-[160rpx]">
								<!-- 选择题选项 -->
								<template v-if="questions[current].type === 'select' || questions[current].type === 'multiselect'">
									<view v-for="(opt, idx) in questions[current].options" :key="idx" class="option-item" :class="isOptionSelected(questions[current].key, opt.value, questions[current].type) ? 'option-selected' : 'option-default'" @click="selectOption(idx)">
										<text :class="isOptionSelected(questions[current].key, opt.value, questions[current].type) ? 'option-text-selected' : 'option-text-default'">
											{{ String.fromCharCode(65 + idx) + "." + opt.label }}
										</text>
									</view>
								</template>

								<!-- 数值输入框 -->
								<template v-else-if="questions[current].type === 'input'">
									<view class="flex items-center justify-center">
										<uv-number-box v-model="answers[questions[current].key]" :min="questions[current].scope[0]" :max="questions[current].scope[1]" :step="questions[current].step || 1" :integer="questions[current].dataType === 'integer'" :inputWidth="80" :buttonSize="40" bgColor="#F8F9FA" color="#333333" buttonColor="#00B496" @change="handleNumberInput"></uv-number-box>
										<text v-if="questions[current].unit" class="ml-3 text-gray-600 font-size-28">
											{{ questions[current].unit }}
										</text>
									</view>
								</template>
							</view>
						</div>
					</div>
				</view>
			</view>
			<!-- 占位 -->
			<view class="h-100"></view>
			<!-- 底部按钮区 -->
			<view class="fixed bottom-0 left-0 w-full flex justify-center z-20 bg-white p-t-14 p-b-40">
				<template v-if="current === 0">
					<!-- 下一题按钮：第一题时只显示下一题 -->
					<view class="w-325 h-84 bg-#00B496 b-rd-42 flex items-center justify-center font-size-30 font-bold text-white shadow-lg" @click="nextQuestion" :class="isPopupOpen() ? 'opacity-50' : ''"> 下一题 </view>
				</template>
				<template v-else-if="current === questions.length - 1">
					<!-- 上一题/提交问卷：最后一题时显示 -->
					<view class="w-325 h-84 bg-white b-rd-42 flex items-center justify-center font-size-30 font-bold text-#00B496 m-r-20 prev-btn" @click="prevQuestion" :class="isPopupOpen() ? 'opacity-50' : ''"> 上一题 </view>
					<view class="w-325 h-84 bg-#00B496 b-rd-42 flex items-center justify-center font-size-30 font-bold text-white shadow-lg" @click="openEndModal" :class="isPopupOpen() ? 'opacity-50' : ''"> 提交问卷 </view>
				</template>
				<template v-else>
					<!-- 上一题/下一题：中间题时显示 -->
					<view class="w-325 h-84 bg-white b-rd-42 flex items-center justify-center font-size-30 font-bold text-#00C6A6 m-r-20 prev-btn" @click="prevQuestion" :class="isPopupOpen() ? 'opacity-50' : ''"> 上一题 </view>
					<view class="w-325 h-84 bg-#00B496 b-rd-42 flex items-center justify-center font-size-30 font-bold text-white shadow-lg" @click="nextQuestion" :class="isPopupOpen() ? 'opacity-50' : ''"> 下一题 </view>
				</template>
			</view>
			<!-- 结束弹窗：时间到或提交时共用 -->
			<view v-if="popupVisible" class="fixed inset-0 z-50 flex items-center justify-center">
				<!-- 遮罩层 -->
				<view class="absolute inset-0 bg-black opacity-40"></view>
				<!-- 弹框内容 -->
				<view class="relative w-[584rpx] h-[470rpx] b-rd-30 flex flex-col items-center justify-center py-[30rpx]" style="background: linear-gradient(180deg, #00c7a8 0%, #00cbc1 100%)">
					<!-- 悬浮图片 -->
					<image :src="healthy.endOfAnswer" class="absolute -top-[160rpx] left-1/2 w-[516rpx] h-[417rpx] transform -translate-x-1/2" mode="widthFix" />
					<view class="flex flex-col items-center w-full z-1">
						<text class="font-bold mb-[20rpx] color-white font-size-48 m-t-40" style="text-shadow: 0 2px 8px rgba(0, 0, 0, 0.18)">本次作答结束啦</text>
						<view class="w-[80%] auto mt-[40rpx]">
							<uv-button shape="circle" text="查看" :customTextStyle="{ color: '#00C6A6', fontSize: '30rpx', fontWeight: 'bold' }" :customStyle="{ border: 'none' }" @click="goToResult"></uv-button>
							<uv-button shape="circle" :plain="true" text="返回首页" :customTextStyle="{ color: '#fff', fontSize: '30rpx', fontWeight: 'bold' }" :customStyle="{ backgroundColor: 'transparent', marginTop: '30rpx' }" @click="goToHome"></uv-button>
						</view>
					</view>
				</view>
			</view>
		</view>
	</app-layout>
</template>

<style lang="scss" scoped>
/* 页面样式 */
.question-content-wrap {
	min-height: 220rpx;
}
.question-content {
	transition: opacity 0.3s ease-in-out;
	width: 100%;
}

/* 题目显示状态 */
.question-visible {
	opacity: 1;
}
.question-hidden {
	opacity: 0;
}
.progress-bar-bg {
	width: 100%;
	height: 6rpx;
	background: #f0f0f0;
	border-radius: 8rpx;
	overflow: hidden;
	position: relative;
	transition: background 0.3s;
}
.progress-bar-fg {
	height: 6rpx;
	background: #00b698;
	border-radius: 8rpx;
	transition: width 0.4s cubic-bezier(0.55, 0, 0.1, 1);
}
.option-item {
	border-radius: 56rpx;
	margin-bottom: 40rpx;
	padding: 30rpx 52rpx;
	font-size: 32rpx;
	font-weight: bold;
	display: flex;
	align-items: center;
	min-height: 64rpx;
	cursor: pointer;
	transition: all 0.2s, transform 0.15s cubic-bezier(0.55, 0, 0.1, 1);
	border: 2rpx solid transparent;
	background: #f5f5f5;
	color: #6d6d6c;
}
.option-default {
	background: #f5f5f5;
	color: #6d6d6c;
	border: 2rpx solid transparent;
}
.option-selected {
	background: #e5faf7;
	color: #00b698;
	border: 2rpx solid #76e6d4;
	animation: option-scale 0.18s cubic-bezier(0.55, 0, 0.1, 1);
}
@keyframes option-scale {
	0% {
		transform: scale(1);
	}
	60% {
		transform: scale(0.96);
	}
	100% {
		transform: scale(1);
	}
}
.option-text-default {
	color: #6d6d6c;
	font-size: 32rpx;
}
.option-text-selected {
	color: #00b698;
	font-size: 32rpx;
}
.prev-btn {
	border: 2rpx solid #00b698;
}

/* 步进器样式优化 */
:deep(.uv-number-box) {
	.uv-number-box__button {
		background-color: #00b698 !important;
		border-color: #00b698 !important;
		border-radius: 8rpx !important;
		width: 40rpx !important;
		height: 40rpx !important;

		&:hover {
			background-color: #009688 !important;
		}

		&.uv-number-box__button--disabled {
			background-color: #e9ecef !important;
			border-color: #e9ecef !important;
		}
	}

	.uv-number-box__input {
		background-color: #f8f9fa !important;
		border: 2rpx solid #e9ecef !important;
		border-radius: 8rpx !important;
		font-size: 32rpx !important;
		font-weight: 500 !important;
		text-align: center !important;
		color: #333333 !important;

		&:focus {
			border-color: #00b698 !important;
			background-color: #ffffff !important;
		}
	}
}

/* 多选题目样式 */
.multiselect-hint {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 20rpx;
	text-align: center;
}
</style>
