<!-- 全部设备 -->
<script setup>
import { ref, reactive } from "vue";
import { onShow } from "@dcloudio/uni-app";
import { route } from "@/common/utils";
import { GetUserAllDevice, GetFamilyAllMembers, DeleteUserDevice } from "@/server/api";
import { useUserStore, useHealthMemberInfoStore, useHealthDeviceTypeStore } from "@/store";

const healthMemberInfoStore = useHealthMemberInfoStore();
const healthDeviceTypeStore = useHealthDeviceTypeStore();

const tabsRef = ref();
const current = 0;
const tabList = ref([]);

const barStyle = {
	background: "#198B6B",
	borderRadius: "3rpx",
};
const inactiveStyle = {
	fontWeight: 500,
	fontSize: "30rpx",
};
const activeStyle = {
	fontWeight: "bold",
	fontSize: "32rpx",
};

// 当前是否是创建者，否则无操作权限
const isCreated = ref(false);

// 获取家庭成员
const userStore = useUserStore();
const userId = userStore.userData.userId;
async function getFamilyAllMembers() {
	try {
		const { data } = await GetFamilyAllMembers({ userId });
		tabList.value = data?.map((item) => ({ name: item.nickName || item.userName, value: item.userId, isCreated: item.isCreated || false }));
		// 如果data[0]是创建者的话，则isCreated为true
		isCreated.value = data?.[0]?.isCreated;
		getUserAllDevice(data?.[0].userId);
	} catch (error) {
		console.error("获取家庭成员失败:", error);
	}
}

// tabs触发事件
function tabsChange(e) {
	const item = tabList.value[e];
	isCreated.value = item.isCreated;
	const data = {
		userId: item.value,
		userName: item.name,
	};
	healthMemberInfoStore.setMemberInfo(data); // 切换tab时，暂存人员信息
	getUserAllDevice(item.value);
}

const deviceList = ref([]);

function addBtn() {
	route("pages/healthy/pages/equipment_type");
}

// 获取用户所有设备
async function getUserAllDevice(userId) {
	try {
		const { data } = await GetUserAllDevice({ userId });
		deviceList.value = data.list || [];
	} catch (error) {
		deviceList.value = [];
		console.error("获取用户所有设备失败:", error);
	}
}

// 解绑设备
async function deleteUserDevice({ id, userId }) {
	try {
		await DeleteUserDevice({ id });
		uni.showToast({ title: "解绑成功", icon: "success" });
		getUserAllDevice(userId);
	} catch (error) {
		console.log("DeleteUserDevice error", error);
	}
}

const modalRef = ref();
const unbindDeviceInfo = ref({});
function handleUnbind(item) {
	unbindDeviceInfo.value = item;
	modalRef.value.open();
}
function handleConfirm() {
	const { id, userId } = unbindDeviceInfo.value;
	deleteUserDevice({ id, userId });
}
function toPath(item) {
	healthDeviceTypeStore.setDeviceTypeInfo(item);
	route({ url: "/pages/healthy/pages/equipment_info", params: { isDeviceList: true } }); // 临时增加字段区分是否设备列表跳转
}

onShow(() => {
	getFamilyAllMembers();
});
</script>

<template>
	<app-layout class="max-w-750 overflow-x-hidden">
		<view class="bg-#f1f3f7">
			<z-tabs :bar-style="barStyle" :inactive-style="inactiveStyle" :active-style="activeStyle" ref="tabsRef" :list="tabList" :current="current" bottom-space="18rpx" active-color="#292929" inactive-color="#292929" bar-height="5rpx" @change="tabsChange"></z-tabs>
			<view class="label-box">已绑定设备</view>
			<template v-if="!deviceList.length">
				<uv-empty></uv-empty>
			</template>
			<template v-else>
				<!-- 设备卡片 -->
				<view class="w-[690rpx] bg-white h-[256rpx] b-rd-20 m-auto p-[36rpx_34rpx] mb-24" v-for="item in deviceList" :key="item.id" @click="toPath(item)">
					<view class="flex justify-between">
						<!-- 左侧设备信息 -->
						<view class="flex flex-col justify-between">
							<view class="flex flex-col">
								<text class="color-#1A1A1A font-size-32 font-bold">
									{{ item.deviceName }}
								</text>
								<uv-text color="#77838F" size="12" :lines="1" :text="`设备号：${item.imei}`" margin="12rpx 0 0 0"></uv-text>
							</view>
							<template v-if="isCreated">
								<view class="w-98 h-60 text-center line-height-54 color-#828D9C font-size-28 b-rd-30 border-2 border-solid border-[#D8DEE6] mt-20" @click.stop="handleUnbind(item)"> 解绑 </view>
							</template>
						</view>
						<!-- 设备图片 -->
						<template v-if="item.imageUrl">
							<app-image :src="item.imageUrl" size="200" mode=""></app-image>
						</template>
					</view>
				</view>
			</template>
			<!-- 占位 -->
			<view class="h-168"></view>
			<template v-if="isCreated">
				<view class="w-full fixed bottom-0 bg-white p-y-20">
					<button class="add-btn" @click="addBtn">添加设备</button>
					<uv-safe-bottom></uv-safe-bottom>
				</view>
			</template>
		</view>
		<my-uv-modal ref="modalRef" content="确定解绑该设备吗？" align="center" :showCancelButton="true" @confirm="handleConfirm"></my-uv-modal>
	</app-layout>
</template>

<style lang="scss">
page {
	background-color: $bg_color;
}
</style>
<style lang="scss" scoped>
.label-box {
	width: 100%;
	line-height: 73rpx;
	color: #828282;
	font-size: 24rpx;
	padding: 8rpx 0 8rpx 30rpx;
	// margin-top: 80rpx;
}

.add-btn {
	width: 700rpx;
	height: 72rpx;
	background: linear-gradient(90deg, $main-green 0%, $main-green-dark 100%);
	color: #fff;
	font-size: 28rpx;
	border-radius: 36rpx;
	margin: 0 auto;
	display: block;
	box-shadow: 0 2rpx 8rpx 0 rgba(26, 188, 156, 0.08);
	text-align: center;
	letter-spacing: 2rpx;
	opacity: 0.95;
}
</style>
