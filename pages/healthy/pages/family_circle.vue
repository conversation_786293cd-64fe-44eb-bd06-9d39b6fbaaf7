<!-- 家庭圈 -->
<script setup>
import { ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { useUserStore } from "@/store";
import { UpdateFamilyMemberRealtion, AddFamilyMember, GetFamilyAllMembers, DeleteFamilyMemberRealtion } from "@/server/api";

import AddMember from "../components/AddMember.vue";
import { healthy } from "@/common/images";

const userStore = useUserStore();
const userId = userStore.userData.userId;

const pagingRef = ref(null);
async function onRefresh() {
	getFamilyAllMembers();
	if (pagingRef.value) pagingRef.value.complete();
}

const familyName = ref("");
const memberList = ref([]); // 成员列表
const createdMemberInfo = ref({}); // 创建者信息
async function getFamilyAllMembers() {
	try {
		const { data } = await GetFamilyAllMembers({ userId });
		// 在res.data中根据isCreated找到当前账号信息
		if (!data || !data.length) return;
		createdMemberInfo.value = data.find((item) => item.isCreated);
		familyName.value = createdMemberInfo.value.familyName;
		memberList.value = data;
	} catch (error) {
		console.error("获取家庭成员失败:", error);
	}
}

const customStyle = {
	width: "260rpx",
	height: "88rpx",
	borderRadius: "44rpx", //圆角
	border: "none",
	fontSize: "30rpx",
};
const customStyle1 = {
	...customStyle,
	background: "#F2F3F6",
	color: "#828D9C",
};
const customStyle2 = {
	...customStyle,
	background: "#00B496",
	color: "#fff",
};

const modalRef = ref();
const modalFormData = ref({
	familyName: "", // 弹窗内家庭名称
});
function openModal() {
	modalRef.value.open();
	modalFormData.value.familyName = familyName.value;
}

const formRef = ref();
const rules = {
	familyName: [
		{ required: true, message: "请输入家庭名称", trigger: "blur" },
		{ pattern: /^\S{1,10}$/, message: "格式错误", trigger: "blur" },
	],
};
// 修改家庭名称
async function handleEditFamilyNameConfirm() {
	try {
		await formRef.value.validate();

		const { id, familyId, userId } = createdMemberInfo.value;
		const params = { id, familyId, userId, familyName: modalFormData.value.familyName };

		await UpdateFamilyMemberRealtion(params);
		uni.showToast({ title: "修改成功", icon: "success" });
		getFamilyAllMembers();
		modalRef.value.close();
	} catch (err) {
		console.log("校验或请求失败", err);
	} finally {
		modalRef.value.closeLoading();
	}
}
// 取消弹窗时重置表单
function handleEditFamilyNameCancel() {
	formRef.value.clearValidate();
	formRef.value.resetFields();
}

// 添加家庭成员
const addMemberModalRef = ref();
const addMemberRef = ref();
function addMemberBtn() {
	addMemberModalRef.value.open();
}
async function handleAddMemberConfirm() {
	try {
		const res = await addMemberRef.value.submit();
		const newMemberInfo = addMemberRef.value.model;
		const { familyId, familyName } = createdMemberInfo.value;
		const params = {
			...newMemberInfo,
			familyId,
			familyName,
		};
		createFamilyMemberRealtion(params);
	} catch (err) {
		// 校验失败
		console.log("校验失败", err);
	} finally {
		addMemberModalRef.value.closeLoading();
	}
}

function handleCancelMember() {
	addMemberModalRef.value.close();
}

// 添加家庭成员
async function createFamilyMemberRealtion(params) {
	try {
		const res = await AddFamilyMember(params);
		uni.showToast({ title: "添加成功", icon: "success" });
		addMemberModalRef.value.close();
		getFamilyAllMembers();
	} catch (error) {
		console.error("添加家庭成员失败:", error);
	}
}

function handleConfirmMember() {
	handleAddMemberConfirm();
}

// 左滑操作
const options = [
	// {
	// 	text: "编辑",
	// 	style: {
	// 		backgroundColor: "#FFA336",
	// 	},
	// },
	{
		text: "移出家庭",
		style: {
			backgroundColor: "#F94B4A",
		},
	},
];
function handleActionOptions(isCreated) {
	return isCreated ? [options[0]] : options;
}

async function deleteFamilyMemberRealtion(id) {
	try {
		await DeleteFamilyMemberRealtion({ id });
		uni.showToast({ title: "移出家庭成功", icon: "success" });
		getFamilyAllMembers();
	} catch (error) {
		console.error("移出家庭失败:", error);
	}
}
function handleActionBtn({ name, index }) {
	deleteFamilyMemberRealtion(name);

	// if (index === 0) {
	// 	uni.showToast({ title: "功能开发中...", icon: "none" });
	// } else {
	// 	deleteFamilyMemberRealtion(id);
	// }
}

// 页面加载时执行
onLoad(() => {
	getFamilyAllMembers();
});
</script>

<template>
	<app-layout class="max-w-750 overflow-x-hidden">
		<z-paging ref="pagingRef" refresher-only refresher-theme-style="black" use-refresher-status-bar-placeholder @onRefresh="onRefresh">
			<app-navBar back :fixed="true" bg-color="#f1f3f7">
				<template #content>
					<view class="w-full h-full flex justify-center items-center"> 家庭圈 </view>
				</template>
			</app-navBar>
			<view class="bg-#f1f3f7 w-full h-full">
				<!-- 我的家庭 -->
				<view class="bg-#fff b-rd-20 flex justify-between items-center p-[9rpx_16rpx] m-x-30 m-b-30">
					<view class="flex items-center">
						<app-image :src="healthy.myFamily" size="134"></app-image>
						<text class="m-l-17 font-size-32 color-#333">{{ familyName }}</text>
					</view>
					<view class="flex b-rd-8 p-[8rpx_18rpx] border-1 border-solid border-#D8DEE6" @click="openModal">
						<uv-icon name="edit-pen" color="#828D9C"></uv-icon>
						<text class="font-size-26 text-#828D9C m-l-9">修改</text>
					</view>
				</view>
				<!-- 家庭成员label -->
				<view class="font-size-26 color-#616161 m-l-48">家庭成员</view>
				<uv-swipe-action>
					<uv-swipe-action-item class="b-rd-20 m-30" v-for="item in memberList" :key="item.id" :name="item.id" :options="handleActionOptions(item.isCreated)" :disabled="item.isCreated === 1" @click="handleActionBtn">
						<view class="flex justify-between items-center p-[35rpx_30rpx]">
							<view class="flex">
								<app-image :src="healthy.avatarM1" size="90"></app-image>
								<view class="flex flex-col justify-between ml-30">
									<view class="">
										<text class="m-r-18 font-size-32 color-#1a1a1a">{{ item.nickName || item.userName }}</text>
										<text class="b-rd-6 color-#fff bg-gradient-to-r from-[#1978FF] to-[#1990FF] p-[6rpx_11rpx] font-size-24" v-if="item.isCreated">创建者</text>
									</view>
									<!-- <view class="color-#77838F font-size-26" v-if="item.deviceNum"> 设备：{{ item.deviceNum }} </view> -->
								</view>
							</view>
							<!-- <uv-icon name="more-dot-fill" color="#9d9e9f"></uv-icon> -->
						</view>
					</uv-swipe-action-item>
				</uv-swipe-action>
			</view>
			<!-- 占位 使用底部安全区后增加了68rpx占位 -->
			<view class="h-168"></view>
			<template #bottom>
				<view class="w-full py-10">
					<button class="add-btn" @click="addMemberBtn">添加家庭成员</button>
				</view>
				<uv-safe-bottom></uv-safe-bottom>
			</template>
			<!-- 修改家庭名称 【关闭默认的缩放效果，否则打开弹窗的时候会看到】 -->
			<uv-modal ref="modalRef" title="修改家庭名称" asyncClose show-cancel-button confirm-color="#00B496" :zoom="false" @confirm="handleEditFamilyNameConfirm" @close="handleEditFamilyNameCancel">
				<uv-form :model="modalFormData" :rules="rules" ref="formRef">
					<uv-form-item prop="familyName">
						<uv-input placeholder="请输入内容" v-model="modalFormData.familyName" border="none" input-align="center"></uv-input>
					</uv-form-item>
				</uv-form>
			</uv-modal>
			<!-- <uv-popup ref="modalRef"></uv-popup> -->
			<!-- 添加家庭成员 -->
			<uv-modal ref="addMemberModalRef" title="新成员手机号" show-cancel-button confirm-color="#00B496" asyncClose :zoom="false" @close="() => addMemberRef.reset()">
				<AddMember ref="addMemberRef" class="w-full" />
				<!-- 自定义按钮 -->
				<template #confirmButton>
					<view class="flex justify-evenly m-b-30">
						<uv-button color="#828D9C" shape="circle" :customStyle="customStyle1" text="取消" @click="handleCancelMember"></uv-button>
						<uv-button color="#fff" shape="circle" :customStyle="customStyle2" text="确认" @click="handleConfirmMember"></uv-button>
					</view>
				</template>
			</uv-modal>
		</z-paging>
	</app-layout>
</template>

<style lang="scss">
page {
	background-color: $bg_color;
}
</style>
<style lang="scss" scoped>
$main-green: #02c9a8;
$main-green-dark: #00b496;
$main-gradient: linear-gradient(90deg, $main-green 0%, $main-green-dark 100%);
$white: #fff;

.add-btn {
	width: 700rpx;
	height: 72rpx;
	background: $main-gradient;
	color: $white;
	font-size: 28rpx;
	border-radius: 36rpx;
	margin: 0 auto;
	display: block;
	box-shadow: 0 2rpx 8rpx 0 rgba(26, 188, 156, 0.08);
	text-align: center;
	letter-spacing: 2rpx;
	opacity: 0.95;
}

:deep {
	// 弹窗圆角
	.uv-popup__content {
		border-radius: 20rpx !important;
	}
	// 添加成员表单样式
	.uv-input__content {
		background-color: #f0f3f9;
		height: 90rpx;
		border-radius: 10rpx;
		color: #222222;
		padding: 0 30rpx;
	}
	.uv-swipe-action-item {
		margin: 30rpx;
		border-radius: 20rpx;
	}
	.uv-form-item__body__right__message {
		margin-left: 0 !important;
	}
	// 修改家庭名称样式
	.uv-modal__content {
		flex-direction: column !important;
	}
}
</style>
