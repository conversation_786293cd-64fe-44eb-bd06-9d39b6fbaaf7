<!-- 调查问卷-问卷详情 -->
<script setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { GetUserQuestionnaire, DeleteUserQuestionnaire } from '@/server/api'
import { totalBaseQuestionnaireList } from '@/common/questionnaire'
import { useRoute } from 'vue-router'
import { useHealthMemberInfoStore } from '@/store'
import { healthy } from '@/common/images'

// #ifdef H5
const navTop = 140
// #endif
// #ifndef H5
const navTop = 100
// #endif

const healthMemberInfoStore = useHealthMemberInfoStore()
const memberInfo = healthMemberInfoStore.getMemberInfo() // 成员信息
const isCreated = computed(() => memberInfo.isCreated) // 是否是创建者，否则无操作权限

// 路由传参 - 问卷id
// #ifdef H5
let { questionnaireId } = useRoute().query
// #endif
// #ifndef H5
let questionnaireId = ''
// #endif
onLoad((option) => {
    questionnaireId = option.questionnaireId
})

// 是否是基础版
const isBasic = ref(true)
const title = computed(() => (isBasic.value ? '基础版健康问卷' : '详尽版健康问卷'))

// 返回首页
function goToHome() {
    uni.switchTab({ url: '/pages/tabBar/healthy/healthy' })
}

const modalRef = ref()
// 删除问卷
function handleDelete() {
    modalRef.value.open()
}

// 确认删除
async function handleConfirm() {
    try {
        await DeleteUserQuestionnaire({ id: questionnaireId })
        uni.switchTab({ url: '/pages/tabBar/healthy/healthy' })
        uni.showToast({ title: '删除成功', icon: 'success' })
    } catch (error) {
        console.error('删除问卷失败:', error)
    }
}

// 调查内容
const questionnaireResults = ref({})
// 处理后的问卷列表
const processedQuestionnaireList = ref([])

// 匹配问卷答案与问题
function matchQuestionnaireAnswers(questions, answers) {
    return questions.map((item) => {
        const result = { ...item }
        // 修改判断条件，正确处理值为0的答案
        if (answers[item.key] !== undefined && answers[item.key] !== null) {
            // 如果是多选题，处理逗号分隔的字符串答案
            if (item.type === 'multiselect' && item.options) {
                const answerString = answers[item.key]
                // 支持数组格式（兼容旧数据）和逗号分隔字符串格式
                const answerArray = Array.isArray(answerString) ? answerString : answerString.split(',')

                const selectedLabels = answerArray
                    .map((value) => {
                        // 处理字符串值，保留空字符串用于匹配"无"选项
                        const processedValue = typeof value === 'string' ? value.trim() : value
                        // 对于空字符串，需要精确匹配value为空字符串的选项
                        const option = item.options.find((opt) => {
                            if (value === '' || processedValue === '') {
                                return opt.value === ''
                            }
                            return opt.value === processedValue
                        })
                        return option ? option.label : null
                    })
                    .filter((label) => label !== null) // 过滤掉null值

                result.answer = selectedLabels.length > 0 ? selectedLabels.join('、') : '未回答'
            }
            // 如果是单选题，找到对应的选项标签
            else if (item.type === 'select' && item.options) {
                const option = item.options.find((opt) => opt.value === answers[item.key])
                if (option) {
                    result.answer = option.label
                }
            } else {
                // 如果是输入题，直接显示答案
                result.answer = answers[item.key] + (item.unit || '')
            }
        }
        // 运动类型选择问题的特殊处理：如果没有直接答案但有推断的sport_type，显示对应选项
        else if (item.key === 'sport_type' && answers.sport_type && item.options) {
            const option = item.options.find((opt) => opt.value === answers.sport_type)
            if (option) {
                result.answer = option.label
            }
        }
        return result
    })
}

// 根据当前人的性别和问卷答案筛选题目
const filteredQuestionnaireList = computed(() => {
    return totalBaseQuestionnaireList.filter((item) => {
        if (item.condition) {
            // 处理性别条件
            if (item.condition.key === 'sex') {
                return item.condition.value === memberInfo.sex
            }
            // 处理基于用户答案的条件
            else {
                const userAnswer = questionnaireResults.value[item.condition.key]
                return userAnswer === item.condition.value
            }
        }
        return true
    })
})

// 问卷完成时间
const createTime = ref('')

// 运动类型推断函数
function inferSportType(answers) {
    // 根据存在的运动字段推断用户选择的运动类型
    if (answers.h_activity_freq !== undefined || answers.h_act_tim_length !== undefined) {
        return 'heavy'
    } else if (answers.m_activity_freq !== undefined || answers.m_act_tim_length !== undefined) {
        return 'medium'
    } else if (answers.walking_day !== undefined || answers.walking_minutes !== undefined) {
        return 'light'
    }
    return null
}

// 家族疾病史数据重构函数
function reconstructFamilyDiseaseData(answers) {
    // 定义家族疾病史字段映射关系
    const familyDiseaseMapping = {
        diab: ['diab_2', 'diab_f', 'diab_m', 'diab_bro', 'diab_sis'],
        hbp: ['hbp', 'hbp_f', 'hbp_m', 'hbp_bro', 'hbp_sis'],
        stroke: ['stroke', 'stroke_f', 'stroke_m', 'stroke_bro', 'stroke_sis'],
        copd: ['copd', 'chronic_f', 'chronic_m', 'chronic_bro', 'chronic_sis'],
        gout: ['gout', 'gout_f', 'gout_m', 'gout_bro', 'gout_sis'],
        fracture: ['fracture', 'pf_f', 'pf_m', 'pf_bro', 'pf_sis'],
    }

    // 重构家族疾病史数据
    Object.keys(familyDiseaseMapping).forEach((diseaseKey) => {
        const fields = familyDiseaseMapping[diseaseKey]
        const selectedFields = []

        // 检查每个字段是否存在且值为1
        fields.forEach((field) => {
            if (answers[field] === 1) {
                selectedFields.push(field)
            }
        })

        // 如果有选中的字段，重构为多选数组格式
        if (selectedFields.length > 0) {
            answers[diseaseKey] = selectedFields.join(',')
        } else {
            // 如果没有选中任何字段，表示选择了"无"，设置为空字符串
            answers[diseaseKey] = ''
        }
    })

    return answers
}

// 获取问卷调查内容
async function getUserQuestionnaire() {
    try {
        const { data } = await GetUserQuestionnaire({ id: questionnaireId })
        createTime.value = data.createTime
        questionnaireResults.value = JSON.parse(data.naireContent)
        isBasic.value = data.naireType === 0

        // 运动问卷特殊处理：推断并重建sport_type字段
        const inferredSportType = inferSportType(questionnaireResults.value)
        if (inferredSportType) {
            questionnaireResults.value.sport_type = inferredSportType
        }

        // 家族疾病史特殊处理：重构独立字段为多选格式
        questionnaireResults.value = reconstructFamilyDiseaseData(questionnaireResults.value)

        // 处理问卷答案
        processedQuestionnaireList.value = matchQuestionnaireAnswers(filteredQuestionnaireList.value, questionnaireResults.value)
    } catch (error) {
        console.error('获取问卷调查内容失败:', error)
    }
}
// 根据questionnaireResults和questionnaireList匹配答案

onMounted(() => {
    getUserQuestionnaire()
})
</script>

<template>
    <app-layout class="max-w-750 overflow-x-hidden">
        <!-- 背景图片 -->
        <image :src="healthy.questionnaireBg" class="absolute w-full h-381 left-0 top-0" mode="aspectFit" />
        <!-- 自定义导航栏 -->
        <app-navBar back :fixed="true" bg-color="transparent" class="z-1"> </app-navBar>
        <view class="flex h-106 absolute top-200 left-40">
            <image :src="healthy.questionnaire" class="w-86 h-106 mr-50" mode="aspectFit" />
            <view class="flex flex-col justify-between">
                <text class="color-black font-bold font-size-44">{{ title }}</text>
                <text class="color-#4E5A66 font-size-26">问卷完成时间：{{ createTime }}</text>
            </view>
        </view>
        <!-- 答题记录 -->
        <view :style="{ marginTop: navTop + 'px' }" class="px-40">
            <scroll-view scroll-y class="h-[calc(100vh-500rpx)]">
                <uv-steps dot direction="column" activeColor="#09AB8B" :current="processedQuestionnaireList.length - 1">
                    <uv-steps-item v-for="(item, index) in processedQuestionnaireList" :key="item.id">
                        <template #title>
                            <text class="color-#1A1A1A font-size-30 font-bold">{{ index + 1 }}. {{ item.question }}</text>
                        </template>
                        <template #desc>
                            <text class="color-#00B698 font-size-28 m-[35rpx_0_64rpx_25rpx]">{{ item.answer || '未回答' }}</text>
                        </template>
                    </uv-steps-item>
                </uv-steps>
            </scroll-view>
        </view>
        <view class="fixed bottom-0 left-0 w-full flex justify-center p-t-14 p-b-40 bg-white">
            <template v-if="isCreated">
                <view
                    class="w-325 h-84 bg-white b-rd-42 flex items-center justify-center font-size-30 font-bold text-#323232 m-r-20 border-1 border-solid border-#E7E7E7"
                    @click="handleDelete"
                >
                    删除问卷
                </view>
            </template>
            <view
                class="w-325 h-84 bg-#00B496 b-rd-42 flex items-center justify-center font-size-30 font-bold text-white shadow-lg"
                @click="goToHome"
            >
                返回首页
            </view>
        </view>
        <!-- 删除提示弹窗 -->
        <my-uv-modal ref="modalRef" content="确定删除该问卷吗？" align="center" :showCancelButton="true" @confirm="handleConfirm"></my-uv-modal>
    </app-layout>
</template>

<style lang="scss" scoped></style>
