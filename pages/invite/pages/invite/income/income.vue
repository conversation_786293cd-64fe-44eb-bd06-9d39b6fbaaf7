<script setup>
import { onLoad } from "@dcloudio/uni-app";
import { reactive, ref, computed } from "vue";
import { route } from "@/common/utils";

// import { getinviteIncome } from "@/server/api";

const data = reactive({
	page: 1,
	dataList: [],
	prevdate: "",
	total: 0,
	totalmoney: 0,
});

const pagePaging = ref(null);
const actualTotal = computed(() => {
	return data.dataList.filter((elem) => elem.id != "").length;
});

onLoad(() => {
	getData();
});

function onRefresh() {
	data.page = 1;
	getData();
}
// 触底加载
function scrolltolower() {
	if (actualTotal.value >= data.total) {
		return;
	}
	data.page += 1;
	getDataList();
}

function getData() {
	return;

	uni.showLoading({
		title: "加载中...",
	});
	getinviteIncome({
		page: data.page,
		limit: 10,
		filter: JSON.stringify({
			relation: "referraluser",
		}),
		op: JSON.stringify({
			relation: "referraluser",
		}),
		prevdate: data.prevdate,
	})
		.then((res) => {
			console.log(res);
			if (res.apiStatus) {
				data.total = res.total;
				data.totalmoney = res.totalmoney || 0;
				if (data.page == 1) {
					data.dataList = res.rows;
				} else {
					data.dataList = [...data.dataList, ...res.rows];
				}
				if (res.total > 0) {
					data.prevdate = data.dataList[data.dataList.length - 1].date;
				}
			}
			uni.hideLoading();
			pagePaging.value.complete([]);
		})
		.catch((err) => {
			console.log(err);
			uni.hideLoading();
			pagePaging.value.complete([]);
		});
}
</script>

<template>
	<app-layout>
		<z-paging ref="pagePaging" refresher-only @onRefresh="onRefresh" @scrolltolower="scrolltolower">
			<view class="income">
				<view class="flex justify-between items-center h-114 px-30 border-b-solid border-b-#EFEFEF">
					<view class="flex justify-start items-center">
						<view class="w-6 h-24 bg-#7E46F5 border-rd-2 mr-14"></view>
						<view class="text-30 text-[#000000] font-bold">邀请总收入:{{ data.totalmoney }}元</view>
					</view>
					<!-- 	<view class="w-176 h-68 border-rd-34 bg-#F2F2F2 flex justify-center items-center">
						<text class="text-28 text-[#101010]">2024年</text>
						<image class="w-20 h-20 ml-10" src="@/pages/tabBar/user/static/triangle.png" mode=""></image>
					</view> -->
				</view>
				<view class="" v-for="(item, index) in data.dataList" :key="index">
					<view v-if="item.id == ''" class="w-full h-90 bg-#EFEFEF flex justify-start items-center pl-30 text-32 text-[#101010]">
						{{ item.date }}
					</view>
					<view v-else class="flex justify-start items-center py-20 px-30">
						<app-image class="w-96 h-96 border-rd-48 mr-20 overflow-hidden" :src="item.referraluser?.avatar" mode=""></app-image>
						<view class="flex-1">
							<view class="flex justify-between items-center">
								<text class="text-32 text-[#131314] font-bold">{{ item.referraluser?.nickname }}</text>
								<text class="text-30 text-[#491AEB] font-bold">{{ item.money }}</text>
							</view>
							<view class="mt-10">
								<text class="text-26 text-[#36424E] mr-12">{{ item.memo }}</text>
							</view>
						</view>
					</view>
				</view>
				<view v-if="data.dataList.length == 0" class="mt-200">
					<uv-empty mode="list" text="没有数据哦~"></uv-empty>
				</view>
			</view>
		</z-paging>
	</app-layout>
</template>

<style lang="scss" scoped>
.income {
}
</style>
