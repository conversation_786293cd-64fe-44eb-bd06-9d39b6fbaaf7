<script setup>
import { reactive, ref } from "vue";

import { onLoad, onPageScroll } from "@dcloudio/uni-app";

import { getDistributeConfig } from "@/server/api";
import { common } from "@/common/images";

const content = ref("");

onLoad(() => {
	uni.setNavigationBarColor({
		frontColor: "#ffffff",
	});
});

function loadData() {
	getDistributeConfig().then((res) => {
		if (res.apiStatus && res.data) {
			content.value = res.data.playMethods;
		}
	});
}

function goBack() {
	uni.navigateBack({
		delta: 1,
	});
}

onLoad(() => {
	loadData();
});
</script>
<template>
	<app-layout>
		<view class="rules p-0 flex flex-col">
			<app-navBar class="" :bar-bg="'#0F0D07'" :back="true" :back-icon="common.iconLeftBai">
				<template v-slot:content>
					<view class="w-full flex justify-between items-center h-88 bg-#0F0D07">
						<view class="flex-1 text-34 text-[#fff] font-bold text-center">邀请规则</view>
					</view>
				</template>
			</app-navBar>
			<view class="pt-20 h-full flex-1 flex flex-col">
				<view class="flex-1 bg-#fff p-0 relative pt-100 p-50" style="border-radius: 30rpx 30rpx 0rpx 0rpx">
					<view class="text-26 text-[#51535E]">
						<uv-parse class="mt-10" :content="content"></uv-parse>
					</view>
				</view>
			</view>
		</view>
	</app-layout>
</template>
<style>
body {
	background: #0f0d07;
}
</style>
<style lang="scss" scoped>
.rules {
	min-height: 100vh;
	// .rule_content {
	// 	background-image: url("@/pages/invite/static/rule_content.png");
	// 	background-repeat: no-repeat;
	// 	background-size: cover;
	// }
}
</style>
