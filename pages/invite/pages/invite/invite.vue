<script setup>
import { onLoad } from "@dcloudio/uni-app";

import { reactive, ref } from "vue";

import { useUserStore } from "@/store";

import { canENV, route } from "@/common/utils";

import { getinviteCode, getinviteLog, getWXChat } from "@/server/api";

import dayjs from "dayjs";
import { common } from "@/common/images";

const userStore = useUserStore();

const pageData = reactive({
	shareList: [
		{
			label: "微信",
			icon: "@/pages/invite/static/icon_weixin_btn.png",
			key: "WXSceneSession",
			show: false,
		},
		{
			label: "朋友圈",
			icon: "@/pages/invite/static/icon_pengyouquan_btn.png",
			key: "WXSceneTimeline",
			show: false,
		},
		{
			label: "QQ",
			icon: "",
			key: "qq",
			show: false,
		},
		{
			label: "微博",
			icon: "",
			key: "sinaweibo",
			show: false,
		},
		{
			label: "下载邀请图",
			icon: "@/pages/invite/static/icon_xiaozai_btn.png",
			key: "xz",
			show: true,
		},
	],

	topTotal: {
		totalnumber: 0, //邀请好友总人数
		validnumber: 0, //有效邀请好友
		totalmoney: 0, // 邀请好友总收入
	},
	referral_code: "", // 邀请码
	rankingList: [],
	qrcodeSrc: "",

	shareUrl: "",

	sharePosterUrl: "",

	miniProgram: null,
});

const pagePaging = ref(null);
const sharePopup = ref(null);
const painterCodeImg = ref(null);
const qrcode = ref(null);

onLoad((options) => {
	init();
});

function onRefresh() {
	loadRankingList();
}

function initData() {
	initCode();
	loadRankingList();
}

async function initCode() {
	try {
		const res = await getinviteCode();

		if (res.apiStatus) {
			pageData.referral_code = res.data;
		}
	} catch (error) {
		//TODO handle the exception
	}

	try {
		let envVersion = "";

		canENV(
			() => {
				envVersion = "develop";
			},
			() => {
				// envVersion = 'trial';
				envVersion = 'release';
			}
		);

		const codeRes = await getWXChat({
			path: `pages/index/index?referral_code=${pageData.referral_code}&login=1`,
			page: `pages/index/index`,
			scene: `?referral_code=${pageData.referral_code}&login=1`,
			width: 430,
			autoColor: true,
			isHyaline: true,
			checkPath: false,
			envVersion,
		});

		if (codeRes.apiStatus) {
			pageData.shareUrl = codeRes.data;
			pageData.qrcodeSrc = codeRes.data;
		}
	} catch (error) {
		//TODO handle the exception
	}

	let maType = 0;
	canENV(
		() => {
			maType = 1;
		},
		() => {
			// maType = 2;
			maType = 0;
		}
	);

	pageData.miniProgram = {
		id: 'gh_d331c8817c01',
		path: `pages/index/index?referral_code=${pageData.referral_code}&login=1`,
		type: maType
	};

	if (pageData.miniProgram) {
		// #ifdef MP-WEIXIN
		// pageData.shareList[0].show = true;
		// #endif
	}

	uni.hideLoading();
}

function loadRankingList() {
	getinviteLog({
		current: 1,
		size: 50,
	})
		.then((res) => {
			if (res.apiStatus) {
				pagePaging.value.complete(res.data.records);
				pageData.rankingList = res.data.records;
			} else {
				pagePaging.value.complete(false);
			}
		})
		.catch((err) => {
			pagePaging.value.complete(false);
		});
}

function init() {
	uni.showLoading({
		title: "加载中...",
	});

	// #ifdef APP
	uni.getProvider({
		service: "share",
		success: (res) => {
			if (~res.provider.indexOf("weixin")) {
				pageData.shareList[0].show = true;
				pageData.shareList[1].show = true;
			}
			// if (~res.provider.indexOf('qq')) {
			// 	pageData.shareList[2].show = true;
			// }
			// if (~res.provider.indexOf('sinaweibo')) {
			// 	pageData.shareList[3].show = true;
			// }
		},
	});
	// #endif
	// #ifdef MP-WEIXIN
	// pageData.shareList[0].show = true;
	// #endif

	initData();
}

function openSharePopup() {
	sharePopup.value.open();
}

function closeSharePopup() {
	sharePopup.value.close();
}

function qrcodeEnd(e) {
	if (e.success) {
		qrcode.value.toTempFilePath({
			success: (res) => {
				// pageData.qrcodeSrc = res.tempFilePath;
			},
		});
	}
}

// 分享
function share(e) {
	// console.log(e, painterCodeImg.value.canvasToTempFilePathSync);

	closeSharePopup();

	painterCodeImg.value.canvasToTempFilePathSync({
		fileType: "jpg",
		// 如果返回的是base64是无法使用 saveImageToPhotosAlbum，需要设置 pathType为url
		pathType: "url",
		quality: 1,
		success: (res) => {
			// res.tempFilePath可下载也可直接分享
			pageData.sharePosterUrl = res.tempFilePath;
			if (e == "xz") {
				// #ifndef H5
				// 下载
				uni.saveImageToPhotosAlbum({
					filePath: res.tempFilePath,
					success: () => {
						uni.showToast({
							title: "保存成功",
							icon: "success",
						});
					},
				});
				// #endif
				// #ifdef H5
				uni.previewImage({
					urls: [res.tempFilePath],
				});
				// #endif
			} else if (e == "qq") {
				uni.share({
					provider: "qq",
					type: 2,
					imageUrl: res.tempFilePath,
					success: (res) => {
						console.log(res);
						uni.showToast({
							title: "分享成功",
							icon: "success",
						});
					},
					fail: (err) => {
						console.log(err);
					},
				});
			} else if (e == "sinaweibo") {
				uni.share({
					provider: "sinaweibo",
					type: 0,
					title: "我发现了一个宝藏应用，快来一起看看吧~",
					summary: "我发现了一个宝藏应用，快来一起看看吧~",
					// href: pageData.shareUrl,
					imageUrl: res.tempFilePath,
					success: (res) => {
						console.log(res);
						uni.showToast({
							title: "分享成功",
							icon: "success",
						});
					},
					fail: (err) => {
						console.log(err);
					},
				});
			} else if (e == "WXSceneSession") {
				// #ifdef APP
				uni.share({
					provider: "weixin",
					scene: "WXSceneSession", // WXSceneSession WXSceneTimeline
					type: pageData.miniProgram ? 5 : 2,
					imageUrl: res.tempFilePath,
					miniProgram: pageData.miniProgram,
					success: (res) => {
						// console.log(res);
					},
					fail: (err) => {
						// console.log(err);
					},
				});
				// #endif
			} else if (e == "WXSceneTimeline") {
				// #ifdef APP
				uni.share({
					provider: "weixin",
					scene: "WXSceneTimeline", // WXSceneSession WXSceneTimeline
					type: pageData.miniProgram ? 5 : 2,
					imageUrl: res.tempFilePath,
					miniProgram: pageData.miniProgram,
					success: (res) => {
						// console.log(res);
					},
					fail: (err) => {
						// console.log(err);
					},
				});
				// #endif
			}
		},
		fail: (err) => {
			console.log(err);
		},
	});
}

// function onShareAppMessage(e) {

// 	const promise = new Promise(resolve =>
// 		painterCodeImg.value.canvasToTempFilePathSync({
// 			fileType: 'jpg',
// 			pathType: 'url',
// 			quality: 1,
// 			success: (res) => {
// 				pageData.sharePosterUrl = res.tempFilePath;
// 				resolve({
// 					title: '和家无忧',
// 					path: pageData.miniProgram ? `/${pageData.miniProgram.path}` : '',
// 					imageUrl: res.tempFilePath
// 				})
// 			}
// 		})
// 	})

// 	return {
// 		title: '和家无忧',
// 		path: pageData.miniProgram ? `/${pageData.miniProgram.path}` : '',
// 		promise

// 	}
// }

// function onShareTimeline() {
// 	return {
// 		title: '和家无忧',
// 		query: `referral_code=${pageData.referral_code}`,
// 		imageUrl: pageData.sharePosterUrl
// 	}
// }

function copy() {
	uni.setClipboardData({
		data: pageData.referral_code,
		success: function () {
			uni.showToast({
				title: "复制成功",
				icon: "success",
			});
		},
		fail: function () {
			uni.showToast({
				title: "复制失败",
				icon: "none",
			});
		},
	});
}
</script>

<template>
	<app-layout>
		<z-paging ref="pagePaging" bg-color="linear-gradient(0deg, #FFD7B7 0%, #FFD7B7 100%)" refresher-only @onRefresh="onRefresh">
			<template #top>
				<app-navBar bg-color="rgba(255,255,255,0)" back rightPlaceholderSize="70rpx" :backIcon="common.iconLeftBai" :fixed="true" :fixed-placeholder="false">
					<template #content>
						<view class="w-full h-full flex justify-between items-center px-0">
							<view class="flex-1 text-center">
								<text class="text-32 text-#fff font-bold"></text>
							</view>
						</view>
					</template>
				</app-navBar>
			</template>
			<view class="invite">
				<view class="w-full relative">
					<app-image src="@/pages/invite/static/bg_invite_header.png" width="100%" mode="widthFix"></app-image>

					<view @click="route('/pages/invite/pages/invite/rules/rules')" class="absolute top-279 right-0 w-47 h-126 flex justify-center items-center bg-#FFEFCB text-24 text-#FF413F" style="writing-mode: vertical-lr; border-radius: 12rpx 0rpx 0rpx 12rpx"> 邀请规则 </view>

					<view class="w-full flex flex-center mt-10">
						<app-image src="@/pages/invite/static/bg_invite_btn.png" width="604" height="162" @click="openSharePopup()"></app-image>
					</view>
				</view>
				<view class="px-20 pb-30">
					<view class="invitation_code w-full h-670 relative">
						<app-image src="@/pages/invite/static/bg_invite_yaoqingma.png" width="710" height="670" mode="" class="absolute top-0 left-0"></app-image>

						<view class="w-full h-full relative">
							<view class="w-full flex flex-center pt-12">
								<view class="text-30 text-[#DB4C00] font-bold">邀请码</view>
							</view>

							<view class="flex flex-center mt-80">
								<view class="w-full text-center text-60 text-[#000] line-height-60 font-bold">
									{{ pageData.referral_code }}
								</view>
							</view>

							<view class="w-full mt-25 flex flex-center">
								<view @click="copy()" class="w-310 h-46 border-rd-23 bg-#FEEFDD flex justify-center items-center">
									<view class="text-24 text-[#EE6C35] mr-10">复制邀请码</view>
									<app-image src="@/pages/invite/static/icon_invite_btn.png" size="20" mode=""></app-image>
								</view>
							</view>

							<view class="w-full flex flex-center mt-60">
								<view class="w-300 h-300">
									<!-- <uv-qrcode @complete="qrcodeEnd" ref="qrcode" size="300rpx" :value="pageData.shareUrl"></uv-qrcode> -->
									<image :src="pageData.shareUrl" mode="widthFix" class="w-300"></image>
								</view>
							</view>
						</view>
					</view>

					<view class="w-full mt-27 w-full flex felx-center h-667 relative">
						<app-image src="@/pages/invite/static/bg_invite_paihangbang.png" width="100%" height="667" mode="" class="absolute top-0 left-0"></app-image>
						<view class="relative w-full h-full flex flex-col">
							<view class="w-full flex flex-center pt-10">
								<view class="text-30 text-[#DB4C00] font-bold text-center">邀请好友记录</view>
							</view>

							<view class="w-full h-0 flex-1 mt-55 px-34 pb-30 felx flex-col">
								<view class="w-full h-full border-rd-20 bg-#fff px-30 pb-25 flex-1 flex flex-col">
									<view class="w-full h-80 flex items-center border-b-solid border-b-#FAE7D2 border-1">
										<view class="text-24 text-[#4B4B54] pl-16 w-40%">邀请用户</view>
										<view class="text-24 text-[#4B4B54] pl-16 w-30%">获得好礼</view>
										<view class="text-24 text-[#4B4B54] pl-16 w-30z%">邀请时间</view>
									</view>
									<view class="w-full flex-1 h-0 felx flex-col pt-12">
										<view class="w-full h-full flex-1 overflow-y-auto">
											<template v-for="(item, index) in pageData.rankingList" :key="index">
												<view class="w-full py-13 flex items-center">
													<view class="pl-16 w-40%">
														<view class="w-full flex items-center">
															<app-image :src="item.userHeadPortrait" size="46" rd="50%" mr="12" mode=""></app-image>
															<view class="text-26 font-500 uv-line-1">{{ item.userNickname }}</view>
														</view>
													</view>
													<view class="pl-16 w-30%">
														<view class="w-full flex items-center">
															<template v-if="item.reward === 'Integral'">
																<view class="text-24 text-#777777 font-500 uv-line-1">{{ item.rewardValue }}积分</view>
															</template>
															<template v-if="item.reward === 'red'">
																<view class="text-24 text-#777777 font-500 uv-line-1">￥{{ item.rewardValue }}红包</view>
															</template>
														</view>
													</view>
													<view class="pl-16 w-30%">
														<view class="w-full flex items-center">
															<view class="text-24 text-#777777 font-500 uv-line-1">{{ dayjs(item.createTime).format("YYYY/MM/DD") }}</view>
														</view>
													</view>
												</view>
											</template>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- <view class="w-full">
						<view class="mt-49 relative">
							<view
								class="invited_by w-354 h-74 absolute left-178 top--22 flex justify-center items-center text-30 text-[#DB4C00] font-bold">
								我邀请的
							</view>
							<view
								class="w-710 h-200 pt-30 bg-#FEF7EA border-solid border-2 border-#FFDBA5 border-rd-20 flex justify-between items-center">
								<view class="w-354 pl-66" @click="route('pages/invite/pages/invite/number/number')">
									<view class="text-26 text-[#4B4B54] flex justify-start items-center">
										有效邀请的好友
										<image class="w-22 h-22 ml-5" :src="common.iconRightHui" mode=""></image>
									</view>
									<view class="text-50 text-[#222222] mt-20">
										{{pageData.topTotal.validnumber}}
										<text class="text-30">人</text>
									</view>
								</view>
								<view class="w-2 h-80 bg-#F1DCBC"></view>
								<view class="w-354 pl-66" @click="route('pages/invite/pages/invite/income/income')">
									<view class="text-26 text-[#4B4B54] flex justify-start items-center">
										邀请好友总收入
										<image class="w-22 h-22 ml-5" :src="common.iconRightHui" mode=""></image>
									</view>
									<view class="text-50 text-[#222222] mt-20">
										{{pageData.topTotal.totalmoney}}
										<text class="text-30">元</text>
									</view>
								</view>
							</view>
						</view>
					</view> -->
				</view>

				<!-- <image :src="pageData.sharePosterUrl" class="w-full" mode="widthFix"></image> -->

				<uv-popup ref="sharePopup" mode="bottom" :round="10" :safeAreaInsetBottom="true">
					<view>
						<view class="w-full h-106 border-b-solid border-b-#EDEEF0 border-1 flex justify-between items-center px-30">
							<app-image :src="common.iconCloseHui" size="30" class="op-0" mode=""></app-image>
							<view class="text-30 text-[#000] font-bold">邀请分享</view>
							<app-image @click="closeSharePopup()" :src="common.iconCloseHui" size="30" mode=""></app-image>
						</view>
						<view class="pt-20 pb-40 grid grid-cols-4">
							<template v-for="(item, index) in pageData.shareList" :key="index">
								<template v-if="item.key === 'WXSceneSession'">
									<!-- #ifdef MP-WEIXIN -->
									<template v-if="item.show">
										<button class="uv-reset-button flex flex-col justify-center items-center mt-30" open-type="share">
											<template v-if="item.icon">
												<app-image :src="item.icon" size="88"></app-image>
											</template>
											<template v-else>
												<view class="w-88 h-88"></view>
											</template>
											<view class="text-22 text-#7A7A8A mt-15">
												{{ item.label }}
											</view>
										</button>
									</template>
									<!-- #endif -->
									<!-- #ifdef APP -->
									<template v-if="item.show">
										<view class="flex flex-col justify-center items-center mt-30" @click="share(item.key)">
											<template v-if="item.icon">
												<app-image :src="item.icon" size="88"></app-image>
											</template>
											<template v-else>
												<view class="w-88 h-88"></view>
											</template>
											<view class="text-22 text-#7A7A8A mt-15">
												{{ item.label }}
											</view>
										</view>
									</template>
									<!-- #endif -->
								</template>
								<template v-else>
									<template v-if="item.show">
										<view class="flex flex-col justify-center items-center mt-30" @click="share(item.key)">
											<template v-if="item.icon">
												<app-image :src="item.icon" size="88"></app-image>
											</template>
											<template v-else>
												<view class="w-88 h-88"></view>
											</template>
											<view class="text-22 text-#7A7A8A mt-15">
												{{ item.label }}
											</view>
										</view>
									</template>
								</template>
							</template>
						</view>
						<view class="w-full h-14 bg-#F2F4F5"></view>
						<view @click="closeSharePopup()" class="w-full h-140 pt-34 text-30 text-[#4B5057] text-center font-bold">取消</view>
					</view>
				</uv-popup>

				<!-- 海报 -->
				<l-painter ref="painterCodeImg" custom-style="position: fixed;left: 200%;">
					<l-painter-view css="width: 750rpx; height: 1624rpx;">
						<l-painter-image src="/pages/invite/static/bg_invite_placard.png" css="width: 750rpx; height: 1624rpx; position: absolute;" />
						<l-painter-view css="width: 750rpx;text-align: center; position: absolute; top: 1020rpx; left: 0;">
							<l-painter-text :text="pageData.referral_code" css="font-size: 72rpx;color: #000;font-weight: bold;" />
						</l-painter-view>
						<!-- 邀请码，可更换为网络图片 -->
						<l-painter-image :src="pageData.qrcodeSrc" css="width: 300rpx; height: 300rpx; position: absolute;bottom: 110rpx;left: 225rpx;" />
					</l-painter-view>
				</l-painter>
			</view>
		</z-paging>
	</app-layout>
</template>

<style>
body,
page {
	background: linear-gradient(0deg, #ffd7b7 0%, #ffd7b7 100%);
}
</style>
<style scoped lang="scss">
.invite {
	.invited_by {
		// background-image: url("@/pages/invite/static/invited_by.png");
		background-size: cover;
		background-repeat: no-repeat;
	}

	.text_ellipsis {
		width: 100%;
		display: -webkit-box;
		/* 兼容旧版浏览器 */
		-webkit-line-clamp: 1;
		/* 显示两行 */
		-webkit-box-orient: vertical;
		/* 子元素在垂直方向排列 */
		overflow: hidden;
		text-overflow: ellipsis;
	}
}
</style>
