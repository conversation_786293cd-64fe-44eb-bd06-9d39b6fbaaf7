!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).echarts={})}(this,function(t){"use strict";var _=function(t,e){return(_=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])}))(t,e)};function u(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}_(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var x=function(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1},b=new function(){this.browser=new x,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow="undefined"!=typeof window};"object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?(b.wxa=!0,b.touchEventsSupported=!0):"undefined"==typeof document&&"undefined"!=typeof self?b.worker=!0:!b.hasGlobalWindow||"Deno"in window?(b.node=!0,b.svgSupported=!0):(J=navigator.userAgent,re=(Ht=b).browser,rt=J.match(/Firefox\/([\d.]+)/),U=J.match(/MSIE\s([\d.]+)/)||J.match(/Trident\/.+?rv:(([\d.]+))/),Q=J.match(/Edge?\/([\d.]+)/),J=/micromessenger/i.test(J),rt&&(re.firefox=!0,re.version=rt[1]),U&&(re.ie=!0,re.version=U[1]),Q&&(re.edge=!0,re.version=Q[1],re.newEdge=18<+Q[1].split(".")[0]),J&&(re.weChat=!0),Ht.svgSupported="undefined"!=typeof SVGRect,Ht.touchEventsSupported="ontouchstart"in window&&!re.ie&&!re.edge,Ht.pointerEventsSupported="onpointerdown"in window&&(re.edge||re.ie&&11<=+re.version),Ht.domSupported="undefined"!=typeof document,rt=document.documentElement.style,Ht.transform3dSupported=(re.ie&&"transition"in rt||re.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in rt)&&!("OTransition"in rt),Ht.transformSupported=Ht.transform3dSupported||re.ie&&9<=+re.version);var j="12px sans-serif";var w,S,T=function(t){var e={};if("undefined"!=typeof JSON)for(var n=0;n<t.length;n++){var i=String.fromCharCode(n+32),r=(t.charCodeAt(n)-20)/100;e[i]=r}return e}("007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N"),G={createCanvas:function(){return"undefined"!=typeof document&&document.createElement("canvas")},measureText:function(t,e){if(w||(n=G.createCanvas(),w=n&&n.getContext("2d")),w)return S!==e&&(S=w.font=e||j),w.measureText(t);t=t||"",e=e||j;var n=/((?:\d+)?\.?\d*)px/.exec(e),i=n&&+n[1]||12,r=0;if(0<=e.indexOf("mono"))r=i*t.length;else for(var o=0;o<t.length;o++){var a=T[t[o]];r+=null==a?i:a*i}return{width:r}},loadImage:function(t,e,n){var i=new Image;return i.onload=e,i.onerror=n,i.src=t,i}};function C(t){for(var e in G)t[e]&&(G[e]=t[e])}var A=lt(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(t,e){return t["[object "+e+"]"]=!0,t},{}),P=lt(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(t,e){return t["[object "+e+"Array]"]=!0,t},{}),W=Object.prototype.toString,U=Array.prototype,q=U.forEach,Z=U.filter,K=U.slice,$=U.map,Q=function(){}.constructor,J=Q?Q.prototype:null,tt="__proto__",et=2311;function nt(){return et++}function it(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];"undefined"!=typeof console&&console.error.apply(console,t)}function y(t){if(null==t||"object"!=typeof t)return t;var e=t,n=W.call(t);if("[object Array]"===n){if(!Dt(t))for(var e=[],i=0,r=t.length;i<r;i++)e[i]=y(t[i])}else if(P[n]){if(!Dt(t)){var o=t.constructor;if(o.from)e=o.from(t);else{e=new o(t.length);for(i=0,r=t.length;i<r;i++)e[i]=t[i]}}}else if(!A[n]&&!Dt(t)&&!yt(t))for(var a in e={},t)t.hasOwnProperty(a)&&a!==tt&&(e[a]=y(t[a]));return e}function d(t,e,n){if(!R(e)||!R(t))return n?y(e):t;for(var i in e){var r,o;e.hasOwnProperty(i)&&i!==tt&&(r=t[i],!R(o=e[i])||!R(r)||F(o)||F(r)||yt(o)||yt(r)||ft(o)||ft(r)||Dt(o)||Dt(r)?!n&&i in t||(t[i]=y(e[i])):d(r,o,n))}return t}function L(t,e){if(Object.assign)Object.assign(t,e);else for(var n in e)e.hasOwnProperty(n)&&n!==tt&&(t[n]=e[n]);return t}function z(t,e,n){for(var i=ht(e),r=0,o=i.length;r<o;r++){var a=i[r];(n?null!=e[a]:null==t[a])&&(t[a]=e[a])}return t}var rt=G.createCanvas;function I(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n}return-1}function ot(t,e){var n,i=t.prototype;function r(){}for(n in r.prototype=e.prototype,t.prototype=new r,i)i.hasOwnProperty(n)&&(t.prototype[n]=i[n]);(t.prototype.constructor=t).superClass=e}function at(t,e,n){if(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(e),r=0;r<i.length;r++){var o=i[r];"constructor"!==o&&(n?null!=e[o]:null==t[o])&&(t[o]=e[o])}else z(t,e,n)}function st(t){return!!t&&"string"!=typeof t&&"number"==typeof t.length}function O(t,e,n){if(t&&e)if(t.forEach&&t.forEach===q)t.forEach(e,n);else if(t.length===+t.length)for(var i=0,r=t.length;i<r;i++)e.call(n,t[i],i,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(n,t[o],o,t)}function B(t,e,n){if(!t)return[];if(!e)return St(t);if(t.map&&t.map===$)return t.map(e,n);for(var i=[],r=0,o=t.length;r<o;r++)i.push(e.call(n,t[r],r,t));return i}function lt(t,e,n,i){if(t&&e){for(var r=0,o=t.length;r<o;r++)n=e.call(i,n,t[r],r,t);return n}}function ut(t,e,n){if(!t)return[];if(!e)return St(t);if(t.filter&&t.filter===Z)return t.filter(e,n);for(var i=[],r=0,o=t.length;r<o;r++)e.call(n,t[r],r,t)&&i.push(t[r]);return i}function ht(t){if(!t)return[];if(Object.keys)return Object.keys(t);var e,n=[];for(e in t)t.hasOwnProperty(e)&&n.push(e);return n}var ct=J&&k(J.bind)?J.call.bind(J.bind):function(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return function(){return t.apply(e,n.concat(K.call(arguments)))}};function pt(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return function(){return t.apply(this,e.concat(K.call(arguments)))}}function F(t){return Array.isArray?Array.isArray(t):"[object Array]"===W.call(t)}function k(t){return"function"==typeof t}function V(t){return"string"==typeof t}function dt(t){return"[object String]"===W.call(t)}function H(t){return"number"==typeof t}function R(t){var e=typeof t;return"function"==e||!!t&&"object"==e}function ft(t){return!!A[W.call(t)]}function gt(t){return!!P[W.call(t)]}function yt(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function mt(t){return null!=t.colorStops}function vt(t){return null!=t.image}function _t(t){return"[object RegExp]"===W.call(t)}function xt(t){return t!=t}function wt(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n=0,i=t.length;n<i;n++)if(null!=t[n])return t[n]}function N(t,e){return null!=t?t:e}function bt(t,e,n){return null!=t?t:null!=e?e:n}function St(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return K.apply(t,e)}function Mt(t){var e;return"number"==typeof t?[t,t,t,t]:2===(e=t.length)?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function Tt(t,e){if(!t)throw new Error(e)}function Ct(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var It="__ec_primitive__";function kt(t){t[It]=!0}function Dt(t){return t[It]}Pt.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},Pt.prototype.has=function(t){return this.data.hasOwnProperty(t)},Pt.prototype.get=function(t){return this.data[t]},Pt.prototype.set=function(t,e){return this.data[t]=e,this},Pt.prototype.keys=function(){return ht(this.data)},Pt.prototype.forEach=function(t){var e,n=this.data;for(e in n)n.hasOwnProperty(e)&&t(n[e],e)};var At=Pt;function Pt(){this.data={}}var Lt="function"==typeof Map;Rt.prototype.hasKey=function(t){return this.data.has(t)},Rt.prototype.get=function(t){return this.data.get(t)},Rt.prototype.set=function(t,e){return this.data.set(t,e),e},Rt.prototype.each=function(n,i){this.data.forEach(function(t,e){n.call(i,t,e)})},Rt.prototype.keys=function(){var t=this.data.keys();return Lt?Array.from(t):t},Rt.prototype.removeKey=function(t){this.data.delete(t)};var Ot=Rt;function Rt(t){var n=F(t),i=(this.data=new(Lt?Map:At),this);function e(t,e){n?i.set(t,e):i.set(e,t)}t instanceof Rt?t.each(e):t&&O(t,e)}function E(t){return new Ot(t)}function Nt(t,e){for(var n=new t.constructor(t.length+e.length),i=0;i<t.length;i++)n[i]=t[i];for(var r=t.length,i=0;i<e.length;i++)n[i+r]=e[i];return n}function Et(t,e){var n,t=Object.create?Object.create(t):((n=function(){}).prototype=t,new n);return e&&L(t,e),t}function zt(t){t=t.style;t.webkitUserSelect="none",t.userSelect="none",t.webkitTapHighlightColor="rgba(0,0,0,0)",t["-webkit-touch-callout"]="none"}function Bt(t,e){return t.hasOwnProperty(e)}function Ft(){}var Vt=180/Math.PI,Ht=Object.freeze({__proto__:null,HashMap:Ot,RADIAN_TO_DEGREE:Vt,assert:Tt,bind:ct,clone:y,concatArray:Nt,createCanvas:rt,createHashMap:E,createObject:Et,curry:pt,defaults:z,disableUserSelect:zt,each:O,eqNaN:xt,extend:L,filter:ut,find:function(t,e,n){if(t&&e)for(var i=0,r=t.length;i<r;i++)if(e.call(n,t[i],i,t))return t[i]},guid:nt,hasOwn:Bt,indexOf:I,inherits:ot,isArray:F,isArrayLike:st,isBuiltInObject:ft,isDom:yt,isFunction:k,isGradientObject:mt,isImagePatternObject:vt,isNumber:H,isObject:R,isPrimitive:Dt,isRegExp:_t,isString:V,isStringSafe:dt,isTypedArray:gt,keys:ht,logError:it,map:B,merge:d,mergeAll:function(t,e){for(var n=t[0],i=1,r=t.length;i<r;i++)n=d(n,t[i],e);return n},mixin:at,noop:Ft,normalizeCssArray:Mt,reduce:lt,retrieve:wt,retrieve2:N,retrieve3:bt,setAsPrimitive:kt,slice:St,trim:Ct});function Gt(t,e){return[t=null==t?0:t,e=null==e?0:e]}function Wt(t){return[t[0],t[1]]}function Ut(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t}function Xt(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}function Yt(t){return Math.sqrt(qt(t))}function qt(t){return t[0]*t[0]+t[1]*t[1]}function Zt(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t}function jt(t,e){var n=Yt(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t}function Kt(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var $t=Kt;function Qt(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var Jt=Qt;function te(t,e,n,i){return t[0]=e[0]+i*(n[0]-e[0]),t[1]=e[1]+i*(n[1]-e[1]),t}function ee(t,e,n){var i=e[0],e=e[1];return t[0]=n[0]*i+n[2]*e+n[4],t[1]=n[1]*i+n[3]*e+n[5],t}function ne(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t}function ie(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}var re=Object.freeze({__proto__:null,add:Ut,applyTransform:ee,clone:Wt,copy:function(t,e){return t[0]=e[0],t[1]=e[1],t},create:Gt,dist:$t,distSquare:Jt,distance:Kt,distanceSquare:Qt,div:function(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},len:Yt,lenSquare:qt,length:Yt,lengthSquare:qt,lerp:te,max:ie,min:ne,mul:function(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t},negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},normalize:jt,scale:Zt,scaleAndAdd:function(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t},set:function(t,e,n){return t[0]=e,t[1]=n,t},sub:Xt}),oe=function(t,e){this.target=t,this.topTarget=e&&e.topTarget},ae=(se.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&((this._draggingTarget=e).dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new oe(e,t),"dragstart",t.event))},se.prototype._drag=function(t){var e,n,i,r,o=this._draggingTarget;o&&(e=t.offsetX,n=t.offsetY,i=e-this._x,r=n-this._y,this._x=e,this._y=n,o.drift(i,r,t),this.handler.dispatchToElement(new oe(o,t),"drag",t.event),i=this.handler.findHover(e,n,o).target,r=this._dropTarget,o!==(this._dropTarget=i))&&(r&&i!==r&&this.handler.dispatchToElement(new oe(r,t),"dragleave",t.event),i)&&i!==r&&this.handler.dispatchToElement(new oe(i,t),"dragenter",t.event)},se.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new oe(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new oe(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},se);function se(t){(this.handler=t).on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}ue.prototype.on=function(t,e,n,i){this._$handlers||(this._$handlers={});var r=this._$handlers;if("function"==typeof e&&(i=n,n=e,e=null),n&&t){var o=this._$eventProcessor;null!=e&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),r[t]||(r[t]=[]);for(var a=0;a<r[t].length;a++)if(r[t][a].h===n)return this;o={h:n,query:e,ctx:i||this,callAtLast:n.zrEventfulCallAtLast},e=r[t].length-1,i=r[t][e];i&&i.callAtLast?r[t].splice(e,0,o):r[t].push(o)}return this},ue.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},ue.prototype.off=function(t,e){var n=this._$handlers;if(n)if(t)if(e){if(n[t]){for(var i=[],r=0,o=n[t].length;r<o;r++)n[t][r].h!==e&&i.push(n[t][r]);n[t]=i}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];else this._$handlers={};return this},ue.prototype.trigger=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(this._$handlers){var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=i.length,s=0;s<a;s++){var l=i[s];if(!r||!r.filter||null==l.query||r.filter(t,l.query))switch(o){case 0:l.h.call(l.ctx);break;case 1:l.h.call(l.ctx,e[0]);break;case 2:l.h.call(l.ctx,e[0],e[1]);break;default:l.h.apply(l.ctx,e)}}r&&r.afterTrigger&&r.afterTrigger(t)}return this},ue.prototype.triggerWithContext=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(this._$handlers){var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=e[o-1],s=i.length,l=0;l<s;l++){var u=i[l];if(!r||!r.filter||null==u.query||r.filter(t,u.query))switch(o){case 0:u.h.call(a);break;case 1:u.h.call(a,e[0]);break;case 2:u.h.call(a,e[0],e[1]);break;default:u.h.apply(a,e.slice(1,o-1))}}r&&r.afterTrigger&&r.afterTrigger(t)}return this};var le=ue;function ue(t){t&&(this._$eventProcessor=t)}var he=Math.log(2);function ce(t,e,n,i,r,o){var a,s=i+"-"+r,l=t.length;if(o.hasOwnProperty(s))return o[s];if(1===e)return a=Math.round(Math.log((1<<l)-1&~r)/he),t[n][a];for(var u=i|1<<n,h=n+1;i&1<<h;)h++;for(var c=0,p=0,d=0;p<l;p++){var f=1<<p;f&r||(c+=(d%2?-1:1)*t[n][p]*ce(t,e-1,h,u,r|f,o),d++)}return o[s]=c}function pe(t,e){var n=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},r=ce(n,8,0,0,0,i);if(0!==r){for(var o=[],a=0;a<8;a++)for(var s=0;s<8;s++)null==o[s]&&(o[s]=0),o[s]+=((a+s)%2?-1:1)*ce(n,7,0===a?1:0,1<<a,1<<s,i)/r*e[a];return function(t,e,n){var i=e*o[6]+n*o[7]+1;t[0]=(e*o[0]+n*o[1]+o[2])/i,t[1]=(e*o[3]+n*o[4]+o[5])/i}}}var de="___zrEVENTSAVED",fe=[];function ge(t,e,n,i,r){if(e.getBoundingClientRect&&b.domSupported&&!ye(e)){var o=e[de]||(e[de]={}),e=function(t,e,n){for(var i=n?"invTrans":"trans",r=e[i],o=e.srcCoords,a=[],s=[],l=!0,u=0;u<4;u++){var h=t[u].getBoundingClientRect(),c=2*u,p=h.left,h=h.top;a.push(p,h),l=l&&o&&p===o[c]&&h===o[1+c],s.push(t[u].offsetLeft,t[u].offsetTop)}return l&&r?r:(e.srcCoords=a,e[i]=n?pe(s,a):pe(a,s))}(function(t,e){var n=e.markers;if(!n){n=e.markers=[];for(var i=["left","right"],r=["top","bottom"],o=0;o<4;o++){var a=document.createElement("div"),s=a.style,l=o%2,u=(o>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[l]+":0",r[u]+":0",i[1-l]+":auto",r[1-u]+":auto",""].join("!important;"),t.appendChild(a),n.push(a)}}return n}(e,o),o,r);if(e)return e(t,n,i),!0}return!1}function ye(t){return"CANVAS"===t.nodeName.toUpperCase()}var me=/([&<>"'])/g,ve={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function _e(t){return null==t?"":(t+"").replace(me,function(t,e){return ve[e]})}var xe=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,we=[],be=b.browser.firefox&&+b.browser.version.split(".")[0]<39;function Se(t,e,n,i){return n=n||{},i?Me(t,e,n):be&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):Me(t,e,n),n}function Me(t,e,n){if(b.domSupported&&t.getBoundingClientRect){var i,r=e.clientX,e=e.clientY;if(ye(t))return i=t.getBoundingClientRect(),n.zrX=r-i.left,void(n.zrY=e-i.top);if(ge(we,t,r,e))return n.zrX=we[0],void(n.zrY=we[1])}n.zrX=n.zrY=0}function Te(t){return t||window.event}function Ce(t,e,n){var i;return null==(e=Te(e)).zrX&&((i=e.type)&&0<=i.indexOf("touch")?(i=("touchend"!==i?e.targetTouches:e.changedTouches)[0])&&Se(t,i,e,n):(Se(t,e,e,n),i=function(t){var e=t.wheelDelta;if(e)return e;var n=t.deltaX,t=t.deltaY;return null!=n&&null!=t?3*(0!==t?Math.abs(t):Math.abs(n))*(0<t||!(t<0)&&0<n?-1:1):e}(e),e.zrDelta=i?i/120:-(e.detail||0)/3),t=e.button,null==e.which&&void 0!==t&&xe.test(e.type))&&(e.which=1&t?1:2&t?3:4&t?2:0),e}var Ie=function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0},ke=(De.prototype.recognize=function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},De.prototype.clear=function(){return this._track.length=0,this},De.prototype._doTrack=function(t,e,n){var i=t.touches;if(i){for(var r={points:[],touches:[],target:e,event:t},o=0,a=i.length;o<a;o++){var s=i[o],l=Se(n,s,{});r.points.push([l.zrX,l.zrY]),r.touches.push(s)}this._track.push(r)}},De.prototype._recognize=function(t){for(var e in Pe)if(Pe.hasOwnProperty(e)){e=Pe[e](this._track,t);if(e)return e}},De);function De(){this._track=[]}function Ae(t){var e=t[1][0]-t[0][0],t=t[1][1]-t[0][1];return Math.sqrt(e*e+t*t)}var Pe={pinch:function(t,e){var n,i=t.length;if(i)return n=(t[i-1]||{}).points,(i=(t[i-2]||{}).points||n)&&1<i.length&&n&&1<n.length?(i=Ae(n)/Ae(i),isFinite(i)||(i=1),e.pinchScale=i,i=[(n[0][0]+n[1][0])/2,(n[0][1]+n[1][1])/2],e.pinchX=i[0],e.pinchY=i[1],{type:"pinch",target:t[0].target,event:e}):void 0}};function Le(){return[1,0,0,1,0,0]}function Oe(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function Re(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function Ne(t,e,n){var i=e[0]*n[0]+e[2]*n[1],r=e[1]*n[0]+e[3]*n[1],o=e[0]*n[2]+e[2]*n[3],a=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],n=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=i,t[1]=r,t[2]=o,t[3]=a,t[4]=s,t[5]=n,t}function Ee(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t}function ze(t,e,n,i){void 0===i&&(i=[0,0]);var r=e[0],o=e[2],a=e[4],s=e[1],l=e[3],e=e[5],u=Math.sin(n),n=Math.cos(n);return t[0]=r*n+s*u,t[1]=-r*u+s*n,t[2]=o*n+l*u,t[3]=-o*u+n*l,t[4]=n*(a-i[0])+u*(e-i[1])+i[0],t[5]=n*(e-i[1])-u*(a-i[0])+i[1],t}function Be(t,e,n){var i=n[0],n=n[1];return t[0]=e[0]*i,t[1]=e[1]*n,t[2]=e[2]*i,t[3]=e[3]*n,t[4]=e[4]*i,t[5]=e[5]*n,t}function Fe(t,e){var n=e[0],i=e[2],r=e[4],o=e[1],a=e[3],e=e[5],s=n*a-o*i;return s?(t[0]=a*(s=1/s),t[1]=-o*s,t[2]=-i*s,t[3]=n*s,t[4]=(i*e-a*r)*s,t[5]=(o*r-n*e)*s,t):null}var Ve=Object.freeze({__proto__:null,clone:function(t){var e=Le();return Re(e,t),e},copy:Re,create:Le,identity:Oe,invert:Fe,mul:Ne,rotate:ze,scale:Be,translate:Ee}),M=(e.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},e.prototype.clone=function(){return new e(this.x,this.y)},e.prototype.set=function(t,e){return this.x=t,this.y=e,this},e.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},e.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},e.prototype.scale=function(t){this.x*=t,this.y*=t},e.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},e.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},e.prototype.dot=function(t){return this.x*t.x+this.y*t.y},e.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},e.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},e.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},e.prototype.distance=function(t){var e=this.x-t.x,t=this.y-t.y;return Math.sqrt(e*e+t*t)},e.prototype.distanceSquare=function(t){var e=this.x-t.x,t=this.y-t.y;return e*e+t*t},e.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},e.prototype.transform=function(t){var e,n;if(t)return e=this.x,n=this.y,this.x=t[0]*e+t[2]*n+t[4],this.y=t[1]*e+t[3]*n+t[5],this},e.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},e.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},e.set=function(t,e,n){t.x=e,t.y=n},e.copy=function(t,e){t.x=e.x,t.y=e.y},e.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},e.lenSquare=function(t){return t.x*t.x+t.y*t.y},e.dot=function(t,e){return t.x*e.x+t.y*e.y},e.add=function(t,e,n){t.x=e.x+n.x,t.y=e.y+n.y},e.sub=function(t,e,n){t.x=e.x-n.x,t.y=e.y-n.y},e.scale=function(t,e,n){t.x=e.x*n,t.y=e.y*n},e.scaleAndAdd=function(t,e,n,i){t.x=e.x+n.x*i,t.y=e.y+n.y*i},e.lerp=function(t,e,n,i){var r=1-i;t.x=r*e.x+i*n.x,t.y=r*e.y+i*n.y},e);function e(t,e){this.x=t||0,this.y=e||0}var He=Math.min,Ge=Math.max,We=new M,Ue=new M,Xe=new M,Ye=new M,qe=new M,Ze=new M,X=(je.prototype.union=function(t){var e=He(t.x,this.x),n=He(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=Ge(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=Ge(t.y+t.height,this.y+this.height)-n:this.height=t.height,this.x=e,this.y=n},je.prototype.applyTransform=function(t){je.applyTransform(this,this,t)},je.prototype.calculateTransform=function(t){var e=t.width/this.width,n=t.height/this.height,i=Le();return Ee(i,i,[-this.x,-this.y]),Be(i,i,[e,n]),Ee(i,i,[t.x,t.y]),i},je.prototype.intersect=function(t,e){if(!t)return!1;t instanceof je||(t=je.create(t));var n,i,r,o,a,s,l,u,h=this,c=h.x,p=h.x+h.width,d=h.y,h=h.y+h.height,f=t.x,g=t.x+t.width,y=t.y,t=t.y+t.height,m=!(p<f||g<c||h<y||t<d);return e&&(n=1/0,i=0,r=Math.abs(p-f),o=Math.abs(g-c),a=Math.abs(h-y),s=Math.abs(t-d),l=Math.min(r,o),u=Math.min(a,s),p<f||g<c?i<l&&(i=l,r<o?M.set(Ze,-r,0):M.set(Ze,o,0)):l<n&&(n=l,r<o?M.set(qe,r,0):M.set(qe,-o,0)),h<y||t<d?i<u&&(i=u,a<s?M.set(Ze,0,-a):M.set(Ze,0,s)):l<n&&(n=l,a<s?M.set(qe,0,a):M.set(qe,0,-s))),e&&M.copy(e,m?qe:Ze),m},je.prototype.contain=function(t,e){var n=this;return t>=n.x&&t<=n.x+n.width&&e>=n.y&&e<=n.y+n.height},je.prototype.clone=function(){return new je(this.x,this.y,this.width,this.height)},je.prototype.copy=function(t){je.copy(this,t)},je.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},je.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},je.prototype.isZero=function(){return 0===this.width||0===this.height},je.create=function(t){return new je(t.x,t.y,t.width,t.height)},je.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},je.applyTransform=function(t,e,n){var i,r,o,a;n?n[1]<1e-5&&-1e-5<n[1]&&n[2]<1e-5&&-1e-5<n[2]?(i=n[0],r=n[3],o=n[4],a=n[5],t.x=e.x*i+o,t.y=e.y*r+a,t.width=e.width*i,t.height=e.height*r,t.width<0&&(t.x+=t.width,t.width=-t.width),t.height<0&&(t.y+=t.height,t.height=-t.height)):(We.x=Xe.x=e.x,We.y=Ye.y=e.y,Ue.x=Ye.x=e.x+e.width,Ue.y=Xe.y=e.y+e.height,We.transform(n),Ye.transform(n),Ue.transform(n),Xe.transform(n),t.x=He(We.x,Ue.x,Xe.x,Ye.x),t.y=He(We.y,Ue.y,Xe.y,Ye.y),o=Ge(We.x,Ue.x,Xe.x,Ye.x),a=Ge(We.y,Ue.y,Xe.y,Ye.y),t.width=o-t.x,t.height=a-t.y):t!==e&&je.copy(t,e)},je);function je(t,e,n,i){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=n,this.height=i}var Ke="silent";function $e(){Ie(this.event)}u(tn,Qe=le),tn.prototype.dispose=function(){},tn.prototype.setCursor=function(){};var Qe,Je=tn;function tn(){var t=null!==Qe&&Qe.apply(this,arguments)||this;return t.handler=null,t}var en,nn=function(t,e){this.x=t,this.y=e},rn=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],on=new X(0,0,0,0),an=(u(sn,en=le),sn.prototype.setHandlerProxy=function(e){this.proxy&&this.proxy.dispose(),e&&(O(rn,function(t){e.on&&e.on(t,this[t],this)},this),e.handler=this),this.proxy=e},sn.prototype.mousemove=function(t){var e=t.zrX,n=t.zrY,i=un(this,e,n),r=this._hovered,o=r.target,i=(o&&!o.__zr&&(o=(r=this.findHover(r.x,r.y)).target),this._hovered=i?new nn(e,n):this.findHover(e,n)),e=i.target,n=this.proxy;n.setCursor&&n.setCursor(e?e.cursor:"default"),o&&e!==o&&this.dispatchToElement(r,"mouseout",t),this.dispatchToElement(i,"mousemove",t),e&&e!==o&&this.dispatchToElement(i,"mouseover",t)},sn.prototype.mouseout=function(t){var e=t.zrEventControl;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&this.trigger("globalout",{type:"globalout",event:t})},sn.prototype.resize=function(){this._hovered=new nn(0,0)},sn.prototype.dispatch=function(t,e){t=this[t];t&&t.call(this,e)},sn.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},sn.prototype.setCursorStyle=function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},sn.prototype.dispatchToElement=function(t,e,n){var i=(t=t||{}).target;if(!i||!i.silent){for(var r="on"+e,o={type:e,event:n,target:(t=t).target,topTarget:t.topTarget,cancelBubble:!1,offsetX:n.zrX,offsetY:n.zrY,gestureEvent:n.gestureEvent,pinchX:n.pinchX,pinchY:n.pinchY,pinchScale:n.pinchScale,wheelDelta:n.zrDelta,zrByTouch:n.zrByTouch,which:n.which,stop:$e};i&&(i[r]&&(o.cancelBubble=!!i[r].call(i,o)),i.trigger(e,o),i=i.__hostTarget||i.parent,!o.cancelBubble););o.cancelBubble||(this.trigger(e,o),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(t){"function"==typeof t[r]&&t[r].call(t,o),t.trigger&&t.trigger(e,o)}))}},sn.prototype.findHover=function(t,e,n){var i=this.storage.getDisplayList(),r=new nn(t,e);if(ln(i,r,t,e,n),this._pointerSize&&!r.target){for(var o=[],a=this._pointerSize,s=a/2,l=new X(t-s,e-s,a,a),u=i.length-1;0<=u;u--){var h=i[u];h===n||h.ignore||h.ignoreCoarsePointer||h.parent&&h.parent.ignoreCoarsePointer||(on.copy(h.getBoundingRect()),h.transform&&on.applyTransform(h.transform),on.intersect(l)&&o.push(h))}if(o.length)for(var c=Math.PI/12,p=2*Math.PI,d=0;d<s;d+=4)for(var f=0;f<p;f+=c)if(ln(o,r,t+d*Math.cos(f),e+d*Math.sin(f),n),r.target)return r}return r},sn.prototype.processGesture=function(t,e){this._gestureMgr||(this._gestureMgr=new ke);var n=this._gestureMgr,i=("start"===e&&n.clear(),n.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom));"end"===e&&n.clear(),i&&(e=i.type,t.gestureEvent=e,(n=new nn).target=i.target,this.dispatchToElement(n,e,i.event))},sn);function sn(t,e,n,i,r){var o=en.call(this)||this;return o._hovered=new nn(0,0),o.storage=t,o.painter=e,o.painterRoot=i,o._pointerSize=r,n=n||new Je,o.proxy=null,o.setHandlerProxy(n),o._draggingMgr=new ae(o),o}function ln(t,e,n,i,r){for(var o=t.length-1;0<=o;o--){var a=t[o],s=void 0;if(a!==r&&!a.ignore&&(s=function(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){for(var i=t,r=void 0,o=!1;i;){if(!(o=i.ignoreClip?!0:o)){var a=i.getClipPath();if(a&&!a.contain(e,n))return!1}i.silent&&(r=!0);a=i.__hostTarget,i=a||i.parent}return!r||Ke}return!1}(a,n,i))&&(e.topTarget||(e.topTarget=a),s!==Ke)){e.target=a;break}}}function un(t,e,n){t=t.painter;return e<0||e>t.getWidth()||n<0||n>t.getHeight()}O(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(a){an.prototype[a]=function(t){var e,n,i=t.zrX,r=t.zrY,o=un(this,i,r);if("mouseup"===a&&o||(n=(e=this.findHover(i,r)).target),"mousedown"===a)this._downEl=n,this._downPoint=[t.zrX,t.zrY],this._upEl=n;else if("mouseup"===a)this._upEl=n;else if("click"===a){if(this._downEl!==this._upEl||!this._downPoint||4<$t(this._downPoint,[t.zrX,t.zrY]))return;this._downPoint=null}this.dispatchToElement(e,a,t)}});var hn=32,cn=7;function pn(t,e,n,i){var r=e+1;if(r===n)return 1;if(i(t[r++],t[e])<0){for(;r<n&&i(t[r],t[r-1])<0;)r++;var o=t,a=e,s=r;for(s--;a<s;){var l=o[a];o[a++]=o[s],o[s--]=l}}else for(;r<n&&0<=i(t[r],t[r-1]);)r++;return r-e}function dn(t,e,n,i,r){for(i===e&&i++;i<n;i++){for(var o,a=t[i],s=e,l=i;s<l;)r(a,t[o=s+l>>>1])<0?l=o:s=1+o;var u=i-s;switch(u){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;0<u;)t[s+u]=t[s+u-1],u--}t[s]=a}}function fn(t,e,n,i,r,o){var a=0,s=0,l=1;if(0<o(t,e[n+r])){for(s=i-r;l<s&&0<o(t,e[n+r+l]);)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s),a+=r,l+=r}else{for(s=r+1;l<s&&o(t,e[n+r-l])<=0;)(l=1+((a=l)<<1))<=0&&(l=s);i=a,a=r-(l=s<l?s:l),l=r-i}for(a++;a<l;){var u=a+(l-a>>>1);0<o(t,e[n+u])?a=u+1:l=u}return l}function gn(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])<0){for(s=r+1;l<s&&o(t,e[n+r-l])<0;)(l=1+((a=l)<<1))<=0&&(l=s);var u=a,a=r-(l=s<l?s:l),l=r-u}else{for(s=i-r;l<s&&0<=o(t,e[n+r+l]);)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s),a+=r,l+=r}for(a++;a<l;){var h=a+(l-a>>>1);o(t,e[n+h])<0?l=h:a=h+1}return l}function yn(A,P){var L,O,R=cn,N=0,E=[];function e(t){var e=L[t],n=O[t],i=L[t+1],r=O[t+1],t=(O[t]=n+r,t===N-3&&(L[t+1]=L[t+2],O[t+1]=O[t+2]),N--,gn(A[i],A,e,n,0,P));if(e+=t,0!=(n-=t)&&0!==(r=fn(A[e+n-1],A,i,r,r-1,P)))if(n<=r){var o=e,a=n,t=i,s=r,l=0;for(l=0;l<a;l++)E[l]=A[o+l];var u=0,h=t,c=o;if(A[c++]=A[h++],0==--s)for(l=0;l<a;l++)A[c+l]=E[u+l];else if(1===a){for(l=0;l<s;l++)A[c+l]=A[h+l];A[c+s]=E[u]}else{for(var p,d,f,g=R;;){d=p=0,f=!1;do{if(P(A[h],E[u])<0){if(A[c++]=A[h++],d++,(p=0)==--s){f=!0;break}}else if(A[c++]=E[u++],p++,d=0,1==--a){f=!0;break}}while((p|d)<g);if(f)break;do{if(0!==(p=gn(A[h],E,u,a,0,P))){for(l=0;l<p;l++)A[c+l]=E[u+l];if(c+=p,u+=p,(a-=p)<=1){f=!0;break}}if(A[c++]=A[h++],0==--s){f=!0;break}if(0!==(d=fn(E[u],A,h,s,0,P))){for(l=0;l<d;l++)A[c+l]=A[h+l];if(c+=d,h+=d,0===(s-=d)){f=!0;break}}if(A[c++]=E[u++],1==--a){f=!0;break}}while(g--,cn<=p||cn<=d);if(f)break;g<0&&(g=0),g+=2}if((R=g)<1&&(R=1),1===a){for(l=0;l<s;l++)A[c+l]=A[h+l];A[c+s]=E[u]}else{if(0===a)throw new Error;for(l=0;l<a;l++)A[c+l]=E[u+l]}}}else{var y=e,m=n,v=i,_=r,x=0;for(x=0;x<_;x++)E[x]=A[v+x];var w=y+m-1,b=_-1,S=v+_-1,M=0,T=0;if(A[S--]=A[w--],0==--m)for(M=S-(_-1),x=0;x<_;x++)A[M+x]=E[x];else if(1===_){for(T=(S-=m)+1,M=(w-=m)+1,x=m-1;0<=x;x--)A[T+x]=A[M+x];A[S]=E[b]}else{for(var C=R;;){var I=0,k=0,D=!1;do{if(P(E[b],A[w])<0){if(A[S--]=A[w--],I++,(k=0)==--m){D=!0;break}}else if(A[S--]=E[b--],k++,I=0,1==--_){D=!0;break}}while((I|k)<C);if(D)break;do{if(0!==(I=m-gn(E[b],A,y,m,m-1,P))){for(m-=I,T=(S-=I)+1,M=(w-=I)+1,x=I-1;0<=x;x--)A[T+x]=A[M+x];if(0===m){D=!0;break}}if(A[S--]=E[b--],1==--_){D=!0;break}if(0!==(k=_-fn(A[w],E,0,_,_-1,P))){for(_-=k,T=(S-=k)+1,M=(b-=k)+1,x=0;x<k;x++)A[T+x]=E[M+x];if(_<=1){D=!0;break}}if(A[S--]=A[w--],0==--m){D=!0;break}}while(C--,cn<=I||cn<=k);if(D)break;C<0&&(C=0),C+=2}if((R=C)<1&&(R=1),1===_){for(T=(S-=m)+1,M=(w-=m)+1,x=m-1;0<=x;x--)A[T+x]=A[M+x];A[S]=E[b]}else{if(0===_)throw new Error;for(M=S-(_-1),x=0;x<_;x++)A[M+x]=E[x]}}}}return L=[],O=[],{mergeRuns:function(){for(;1<N;){var t=N-2;if(1<=t&&O[t-1]<=O[t]+O[t+1]||2<=t&&O[t-2]<=O[t]+O[t-1])O[t-1]<O[t+1]&&t--;else if(O[t]>O[t+1])break;e(t)}},forceMergeRuns:function(){for(;1<N;){var t=N-2;0<t&&O[t-1]<O[t+1]&&t--,e(t)}},pushRun:function(t,e){L[N]=t,O[N]=e,N+=1}}}function mn(t,e,n,i){var r=(i=i||t.length)-(n=n||0);if(!(r<2)){var o=0;if(r<hn)dn(t,n,i,n+(o=pn(t,n,i,e)),e);else{var a,s=yn(t,e),l=function(t){for(var e=0;hn<=t;)e|=1&t,t>>=1;return t+e}(r);do{}while((o=pn(t,n,i,e))<l&&(dn(t,n,n+(a=l<(a=r)?l:r),n+o,e),o=a),s.pushRun(n,o),s.mergeRuns(),n+=o,0!==(r-=o));s.forceMergeRuns()}}}var vn=1,_n=4,xn=!1;function wn(){xn||(xn=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function bn(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}Mn.prototype.traverse=function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},Mn.prototype.getDisplayList=function(t,e){e=e||!1;var n=this._displayList;return!t&&n.length||this.updateDisplayList(e),n},Mn.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,i=0,r=e.length;i<r;i++)this._updateAndAddDisplayable(e[i],null,t);n.length=this._displayListLen,mn(n,bn)},Mn.prototype._updateAndAddDisplayable=function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.update(),t.afterUpdate();var i=t.getClipPath();if(t.ignoreClip)e=null;else if(i){e=e?e.slice():[];for(var r=i,o=t;r;)r.parent=o,r.updateTransform(),e.push(r),r=(o=r).getClipPath()}if(t.childrenRef){for(var a=t.childrenRef(),s=0;s<a.length;s++){var l=a[s];t.__dirty&&(l.__dirty|=vn),this._updateAndAddDisplayable(l,e,n)}t.__dirty=0}else{i=t;e&&e.length?i.__clipPaths=e:i.__clipPaths&&0<i.__clipPaths.length&&(i.__clipPaths=[]),isNaN(i.z)&&(wn(),i.z=0),isNaN(i.z2)&&(wn(),i.z2=0),isNaN(i.zlevel)&&(wn(),i.zlevel=0),this._displayList[this._displayListLen++]=i}i=t.getDecalElement&&t.getDecalElement(),i=(i&&this._updateAndAddDisplayable(i,e,n),t.getTextGuideLine()),i=(i&&this._updateAndAddDisplayable(i,e,n),t.getTextContent());i&&this._updateAndAddDisplayable(i,e,n)}},Mn.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},Mn.prototype.delRoot=function(t){if(t instanceof Array)for(var e=0,n=t.length;e<n;e++)this.delRoot(t[e]);else{var i=I(this._roots,t);0<=i&&this._roots.splice(i,1)}},Mn.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},Mn.prototype.getRoots=function(){return this._roots},Mn.prototype.dispose=function(){this._displayList=null,this._roots=null};var Sn=Mn;function Mn(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=bn}var Tn=b.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){return setTimeout(t,16)},Cn={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),-(n*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)))},elasticOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?n*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*-.5:n*Math.pow(2,-10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*.5+1)},backIn:function(t){return t*t*(2.70158*t-1.70158)},backOut:function(t){return--t*t*(2.70158*t+1.70158)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((1+e)*t-e)*.5:.5*((t-=2)*t*((1+e)*t+e)+2)},bounceIn:function(t){return 1-Cn.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*Cn.bounceIn(2*t):.5*Cn.bounceOut(2*t-1)+.5}},In=Math.pow,kn=Math.sqrt,Dn=1e-8,An=kn(3),Pn=1/3,Ln=Gt(),On=Gt(),Rn=Gt();function Nn(t){return-Dn<t&&t<Dn}function En(t){return Dn<t||t<-Dn}function zn(t,e,n,i,r){var o=1-r;return o*o*(o*t+3*r*e)+r*r*(r*i+3*o*n)}function Bn(t,e,n,i,r){var o=1-r;return 3*(((e-t)*o+2*(n-e)*r)*o+(i-n)*r*r)}function Fn(t,e,n,i,r,o){var a,s,i=i+3*(e-n)-t,n=3*(n-2*e+t),e=3*(e-t),t=t-r,r=n*n-3*i*e,l=n*e-9*i*t,t=e*e-3*n*t,u=0;return Nn(r)&&Nn(l)?Nn(n)?o[0]=0:0<=(a=-e/n)&&a<=1&&(o[u++]=a):Nn(e=l*l-4*r*t)?(s=-(t=l/r)/2,0<=(a=-n/i+t)&&a<=1&&(o[u++]=a),0<=s&&s<=1&&(o[u++]=s)):0<e?(e=r*n+1.5*i*(-l-(t=kn(e))),0<=(a=(-n-((t=(t=r*n+1.5*i*(-l+t))<0?-In(-t,Pn):In(t,Pn))+(e=e<0?-In(-e,Pn):In(e,Pn))))/(3*i))&&a<=1&&(o[u++]=a)):(t=(2*r*n-3*i*l)/(2*kn(r*r*r)),e=Math.acos(t)/3,a=(-n-2*(l=kn(r))*(t=Math.cos(e)))/(3*i),s=(-n+l*(t+An*Math.sin(e)))/(3*i),r=(-n+l*(t-An*Math.sin(e)))/(3*i),0<=a&&a<=1&&(o[u++]=a),0<=s&&s<=1&&(o[u++]=s),0<=r&&r<=1&&(o[u++]=r)),u}function Vn(t,e,n,i,r){var o,a=6*n-12*e+6*t,i=9*e+3*i-3*t-9*n,n=3*e-3*t,e=0;return Nn(i)?En(a)&&0<=(o=-n/a)&&o<=1&&(r[e++]=o):Nn(t=a*a-4*i*n)?r[0]=-a/(2*i):0<t&&(t=(-a-(n=kn(t)))/(2*i),0<=(o=(-a+n)/(2*i))&&o<=1&&(r[e++]=o),0<=t)&&t<=1&&(r[e++]=t),e}function Hn(t,e,n,i,r,o){var a=(e-t)*r+t,e=(n-e)*r+e,n=(i-n)*r+n,s=(e-a)*r+a,e=(n-e)*r+e,r=(e-s)*r+s;o[0]=t,o[1]=a,o[2]=s,o[3]=r,o[4]=r,o[5]=e,o[6]=n,o[7]=i}function Gn(t,e,n,i,r,o,a,s,l,u,h){var c,p,d,f,g=.005,y=1/0;Ln[0]=l,Ln[1]=u;for(var m=0;m<1;m+=.05)On[0]=zn(t,n,r,a,m),On[1]=zn(e,i,o,s,m),(d=Jt(Ln,On))<y&&(c=m,y=d);for(var y=1/0,v=0;v<32&&!(g<1e-4);v++)p=c+g,On[0]=zn(t,n,r,a,f=c-g),On[1]=zn(e,i,o,s,f),d=Jt(On,Ln),0<=f&&d<y?(c=f,y=d):(Rn[0]=zn(t,n,r,a,p),Rn[1]=zn(e,i,o,s,p),f=Jt(Rn,Ln),p<=1&&f<y?(c=p,y=f):g*=.5);return h&&(h[0]=zn(t,n,r,a,c),h[1]=zn(e,i,o,s,c)),kn(y)}function Wn(t,e,n,i){var r=1-i;return r*(r*t+2*i*e)+i*i*n}function Un(t,e,n,i){return 2*((1-i)*(e-t)+i*(n-e))}function Xn(t,e,n){n=t+n-2*e;return 0==n?.5:(t-e)/n}function Yn(t,e,n,i,r){var o=(e-t)*i+t,e=(n-e)*i+e,i=(e-o)*i+o;r[0]=t,r[1]=o,r[2]=i,r[3]=i,r[4]=e,r[5]=n}function qn(t,e,n,i,r,o,a,s,l){var u,h=.005,c=1/0;Ln[0]=a,Ln[1]=s;for(var p=0;p<1;p+=.05)On[0]=Wn(t,n,r,p),On[1]=Wn(e,i,o,p),(y=Jt(Ln,On))<c&&(u=p,c=y);for(var c=1/0,d=0;d<32&&!(h<1e-4);d++){var f=u-h,g=u+h,y=(On[0]=Wn(t,n,r,f),On[1]=Wn(e,i,o,f),Jt(On,Ln));0<=f&&y<c?(u=f,c=y):(Rn[0]=Wn(t,n,r,g),Rn[1]=Wn(e,i,o,g),f=Jt(Rn,Ln),g<=1&&f<c?(u=g,c=f):h*=.5)}return l&&(l[0]=Wn(t,n,r,u),l[1]=Wn(e,i,o,u)),kn(c)}var Zn=/cubic-bezier\(([0-9,\.e ]+)\)/;function jn(t){t=t&&Zn.exec(t);if(t){var e,t=t[1].split(","),n=+Ct(t[0]),i=+Ct(t[1]),r=+Ct(t[2]),o=+Ct(t[3]);if(!isNaN(n+i+r+o))return e=[],function(t){return t<=0?0:1<=t?1:Fn(0,n,r,1,t,e)&&zn(0,i,o,1,e[0])}}}$n.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),!this._paused){var n=this._life,i=t-this._startTime-this._pausedTime,r=i/n,o=(r<0&&(r=0),r=Math.min(r,1),this.easingFunc),o=o?o(r):r;if(this.onframe(o),1===r){if(!this.loop)return!0;this._startTime=t-i%n,this._pausedTime=0,this.onrestart()}return!1}this._pausedTime+=e},$n.prototype.pause=function(){this._paused=!0},$n.prototype.resume=function(){this._paused=!1},$n.prototype.setEasing=function(t){this.easing=t,this.easingFunc=k(t)?t:Cn[t]||jn(t)};var Kn=$n;function $n(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||Ft,this.ondestroy=t.ondestroy||Ft,this.onrestart=t.onrestart||Ft,t.easing&&this.setEasing(t.easing)}var Qn=function(t){this.value=t},Jn=(ti.prototype.insert=function(t){t=new Qn(t);return this.insertEntry(t),t},ti.prototype.insertEntry=function(t){this.head?((this.tail.next=t).prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},ti.prototype.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},ti.prototype.len=function(){return this._len},ti.prototype.clear=function(){this.head=this.tail=null,this._len=0},ti);function ti(){this._len=0}ni.prototype.put=function(t,e){var n,i,r=this._list,o=this._map,a=null;return null==o[t]&&(i=r.len(),n=this._lastRemovedEntry,i>=this._maxSize&&0<i&&(i=r.head,r.remove(i),delete o[i.key],a=i.value,this._lastRemovedEntry=i),n?n.value=e:n=new Qn(e),n.key=t,r.insertEntry(n),o[t]=n),a},ni.prototype.get=function(t){var t=this._map[t],e=this._list;if(null!=t)return t!==e.tail&&(e.remove(t),e.insertEntry(t)),t.value},ni.prototype.clear=function(){this._list.clear(),this._map={}},ni.prototype.len=function(){return this._list.len()};var ei=ni;function ni(t){this._list=new Jn,this._maxSize=10,this._map={},this._maxSize=t}var ii={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function ri(t){return(t=Math.round(t))<0?0:255<t?255:t}function oi(t){return t<0?0:1<t?1:t}function ai(t){return t.length&&"%"===t.charAt(t.length-1)?ri(parseFloat(t)/100*255):ri(parseInt(t,10))}function si(t){return t.length&&"%"===t.charAt(t.length-1)?oi(parseFloat(t)/100):oi(parseFloat(t))}function li(t,e,n){return n<0?n+=1:1<n&&--n,6*n<1?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function ui(t,e,n){return t+(e-t)*n}function hi(t,e,n,i,r){return t[0]=e,t[1]=n,t[2]=i,t[3]=r,t}function ci(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var pi=new ei(20),di=null;function fi(t,e){di&&ci(di,e),di=pi.put(t,di||e.slice())}function gi(t,e){if(t){e=e||[];var n=pi.get(t);if(n)return ci(e,n);n=(t+="").replace(/ /g,"").toLowerCase();if(n in ii)return ci(e,ii[n]),fi(t,e),e;var i=n.length;if("#"===n.charAt(0))return 4===i||5===i?0<=(r=parseInt(n.slice(1,4),16))&&r<=4095?(hi(e,(3840&r)>>4|(3840&r)>>8,240&r|(240&r)>>4,15&r|(15&r)<<4,5===i?parseInt(n.slice(4),16)/15:1),fi(t,e),e):void hi(e,0,0,0,1):7===i||9===i?0<=(r=parseInt(n.slice(1,7),16))&&r<=16777215?(hi(e,(16711680&r)>>16,(65280&r)>>8,255&r,9===i?parseInt(n.slice(7),16)/255:1),fi(t,e),e):void hi(e,0,0,0,1):void 0;var r=n.indexOf("("),o=n.indexOf(")");if(-1!==r&&o+1===i){var i=n.substr(0,r),a=n.substr(r+1,o-(r+1)).split(","),s=1;switch(i){case"rgba":if(4!==a.length)return 3===a.length?hi(e,+a[0],+a[1],+a[2],1):hi(e,0,0,0,1);s=si(a.pop());case"rgb":return 3<=a.length?(hi(e,ai(a[0]),ai(a[1]),ai(a[2]),3===a.length?s:si(a[3])),fi(t,e),e):void hi(e,0,0,0,1);case"hsla":return 4!==a.length?void hi(e,0,0,0,1):(a[3]=si(a[3]),yi(a,e),fi(t,e),e);case"hsl":return 3!==a.length?void hi(e,0,0,0,1):(yi(a,e),fi(t,e),e);default:return}}hi(e,0,0,0,1)}}function yi(t,e){var n=(parseFloat(t[0])%360+360)%360/360,i=si(t[1]),r=si(t[2]),i=r<=.5?r*(i+1):r+i-r*i,r=2*r-i;return hi(e=e||[],ri(255*li(r,i,n+1/3)),ri(255*li(r,i,n)),ri(255*li(r,i,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function mi(t,e){var n=gi(t);if(n){for(var i=0;i<3;i++)n[i]=e<0?n[i]*(1-e)|0:(255-n[i])*e+n[i]|0,255<n[i]?n[i]=255:n[i]<0&&(n[i]=0);return wi(n,4===n.length?"rgba":"rgb")}}function vi(t,e,n){var i,r,o;if(e&&e.length&&0<=t&&t<=1)return n=n||[],t=t*(e.length-1),i=Math.floor(t),o=Math.ceil(t),r=e[i],e=e[o],n[0]=ri(ui(r[0],e[0],o=t-i)),n[1]=ri(ui(r[1],e[1],o)),n[2]=ri(ui(r[2],e[2],o)),n[3]=oi(ui(r[3],e[3],o)),n}var _i=vi;function xi(t,e,n){var i,r,o,a;if(e&&e.length&&0<=t&&t<=1)return t=t*(e.length-1),i=Math.floor(t),r=Math.ceil(t),a=gi(e[i]),e=gi(e[r]),a=wi([ri(ui(a[0],e[0],o=t-i)),ri(ui(a[1],e[1],o)),ri(ui(a[2],e[2],o)),oi(ui(a[3],e[3],o))],"rgba"),n?{color:a,leftIndex:i,rightIndex:r,value:t}:a}var n=xi;function wi(t,e){var n;if(t&&t.length)return n=t[0]+","+t[1]+","+t[2],"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(n+=","+t[3]),e+"("+n+")"}function bi(t,e){t=gi(t);return t?(.299*t[0]+.587*t[1]+.114*t[2])*t[3]/255+(1-t[3])*e:0}var Si=new ei(100);function Mi(t){var e;return V(t)?((e=Si.get(t))||(e=mi(t,-.1),Si.put(t,e)),e):mt(t)?((e=L({},t)).colorStops=B(t.colorStops,function(t){return{offset:t.offset,color:mi(t.color,-.1)}}),e):t}_i=Object.freeze({__proto__:null,fastLerp:vi,fastMapToColor:_i,lerp:xi,lift:mi,liftColor:Mi,lum:bi,mapToColor:n,modifyAlpha:function(t,e){if((t=gi(t))&&null!=e)return t[3]=oi(e),wi(t,"rgba")},modifyHSL:function(t,e,n,i){var r=gi(t);if(t)return r=function(t){var e,n,i,r,o,a,s,l,u,h;if(t)return h=t[0]/255,e=t[1]/255,n=t[2]/255,s=Math.min(h,e,n),r=((i=Math.max(h,e,n))+s)/2,0==(u=i-s)?a=o=0:(a=r<.5?u/(i+s):u/(2-i-s),s=((i-h)/6+u/2)/u,l=((i-e)/6+u/2)/u,u=((i-n)/6+u/2)/u,h===i?o=u-l:e===i?o=1/3+s-u:n===i&&(o=2/3+l-s),o<0&&(o+=1),1<o&&--o),h=[360*o,a,r],null!=t[3]&&h.push(t[3]),h}(r),null!=e&&(r[0]=(t=e,(t=Math.round(t))<0?0:360<t?360:t)),null!=n&&(r[1]=si(n)),null!=i&&(r[2]=si(i)),wi(yi(r),"rgba")},parse:gi,random:function(){return wi([Math.round(255*Math.random()),Math.round(255*Math.random()),Math.round(255*Math.random())],"rgb")},stringify:wi,toHex:function(t){if(t=gi(t))return((1<<24)+(t[0]<<16)+(t[1]<<8)+ +t[2]).toString(16).slice(1)}});b.hasGlobalWindow&&k(window.btoa);var Ti=Array.prototype.slice;function Ci(t,e,n){return(e-t)*n+t}function Ii(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=Ci(e[o],n[o],i);return t}function ki(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=e[o]+n[o]*i;return t}function Di(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=e[a][s]+n[a][s]*i}return t}function Ai(t){if(st(t)){var e=t.length;if(st(t[0])){for(var n=[],i=0;i<e;i++)n.push(Ti.call(t[i]));return n}return Ti.call(t)}return t}function Pi(t){return t[0]=Math.floor(t[0])||0,t[1]=Math.floor(t[1])||0,t[2]=Math.floor(t[2])||0,t[3]=null==t[3]?1:t[3],"rgba("+t.join(",")+")"}function Li(t){return 4===t||5===t}function Oi(t){return 1===t||2===t}var Ri=[0,0,0,0],Ni=(Ei.prototype.isFinished=function(){return this._finished},Ei.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},Ei.prototype.needsAnimate=function(){return 1<=this.keyframes.length},Ei.prototype.getAdditiveTrack=function(){return this._additiveTrack},Ei.prototype.addKeyframe=function(t,e,n){this._needsSort=!0;var i,r=this.keyframes,o=r.length,a=!1,s=6,l=e,u=(st(e)?(1==(s=i=st((i=e)&&i[0])?2:1)&&!H(e[0])||2==i&&!H(e[0][0]))&&(a=!0):H(e)&&!xt(e)?s=0:V(e)?isNaN(+e)?(i=gi(e))&&(l=i,s=3):s=0:mt(e)&&((u=L({},l)).colorStops=B(e.colorStops,function(t){return{offset:t.offset,color:gi(t.color)}}),"linear"===e.type?s=4:"radial"===e.type&&(s=5),l=u),0===o?this.valType=s:s===this.valType&&6!==s||(a=!0),this.discrete=this.discrete||a,{time:t,value:l,rawValue:e,percent:0});return n&&(u.easing=n,u.easingFunc=k(n)?n:Cn[n]||jn(n)),r.push(u),u},Ei.prototype.prepare=function(t,e){for(var n=this.keyframes,i=(this._needsSort&&n.sort(function(t,e){return t.time-e.time}),this.valType),r=n.length,o=n[r-1],a=this.discrete,s=Oi(i),l=Li(i),u=0;u<r;u++){var h=n[u],c=h.value,p=o.value;if(h.percent=h.time/t,!a)if(s&&u!==r-1){x=_=v=m=y=g=f=d=h=void 0;var d=p,f=i,g=h=c,y=d;if(g.push&&y.push){var h=g.length,m=y.length;if(h!==m)if(m<h)g.length=m;else for(var v=h;v<m;v++)g.push(1===f?y[v]:Ti.call(y[v]));for(var _=g[0]&&g[0].length,v=0;v<g.length;v++)if(1===f)isNaN(g[v])&&(g[v]=y[v]);else for(var x=0;x<_;x++)isNaN(g[v][x])&&(g[v][x]=y[v][x])}}else if(l){T=M=S=b=w=h=d=void 0;for(var d=c.colorStops,h=p.colorStops,w=d.length,b=h.length,S=b<w?h:d,h=Math.min(w,b),M=S[h-1]||{color:[0,0,0,0],offset:0},T=h;T<Math.max(w,b);T++)S.push({offset:M.offset,color:M.color.slice()})}}if(!a&&5!==i&&e&&this.needsAnimate()&&e.needsAnimate()&&i===e.valType&&!e._finished){this._additiveTrack=e;for(var C=n[0].value,u=0;u<r;u++)0===i?n[u].additiveValue=n[u].value-C:3===i?n[u].additiveValue=ki([],n[u].value,C,-1):Oi(i)&&(n[u].additiveValue=(1===i?ki:Di)([],n[u].value,C,-1))}},Ei.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var n,i,r,o,a=null!=this._additiveTrack,s=a?"additiveValue":"value",l=this.valType,u=this.keyframes,h=u.length,c=this.propName,p=3===l,d=this._lastFr,f=Math.min;if(1===h)n=i=u[0];else{if(e<0)g=0;else if(e<this._lastFrP){for(var g=f(d+1,h-1);0<=g&&!(u[g].percent<=e);g--);g=f(g,h-2)}else{for(g=d;g<h&&!(u[g].percent>e);g++);g=f(g-1,h-2)}i=u[g+1],n=u[g]}n&&i&&(this._lastFr=g,this._lastFrP=e,d=i.percent-n.percent,r=0==d?1:f((e-n.percent)/d,1),i.easingFunc&&(r=i.easingFunc(r)),f=a?this._additiveValue:p?Ri:t[c],(Oi(l)||p)&&(f=f||(this._additiveValue=[])),this.discrete?t[c]=(r<1?n:i).rawValue:Oi(l)?(1===l?Ii:function(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=Ci(e[a][s],n[a][s],i)}})(f,n[s],i[s],r):Li(l)?(d=n[s],o=i[s],t[c]={type:(l=4===l)?"linear":"radial",x:Ci(d.x,o.x,r),y:Ci(d.y,o.y,r),colorStops:B(d.colorStops,function(t,e){e=o.colorStops[e];return{offset:Ci(t.offset,e.offset,r),color:Pi(Ii([],t.color,e.color,r))}}),global:o.global},l?(t[c].x2=Ci(d.x2,o.x2,r),t[c].y2=Ci(d.y2,o.y2,r)):t[c].r=Ci(d.r,o.r,r)):p?(Ii(f,n[s],i[s],r),a||(t[c]=Pi(f))):(l=Ci(n[s],i[s],r),a?this._additiveValue=l:t[c]=l),a)&&this._addToTarget(t)}},Ei.prototype._addToTarget=function(t){var e=this.valType,n=this.propName,i=this._additiveValue;0===e?t[n]=t[n]+i:3===e?(gi(t[n],Ri),ki(Ri,Ri,i,1),t[n]=Pi(Ri)):1===e?ki(t[n],t[n],i,1):2===e&&Di(t[n],t[n],i,1)},Ei);function Ei(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}i.prototype.getMaxTime=function(){return this._maxTime},i.prototype.getDelay=function(){return this._delay},i.prototype.getLoop=function(){return this._loop},i.prototype.getTarget=function(){return this._target},i.prototype.changeTarget=function(t){this._target=t},i.prototype.when=function(t,e,n){return this.whenWithKeys(t,e,ht(e),n)},i.prototype.whenWithKeys=function(t,e,n,i){for(var r=this._tracks,o=0;o<n.length;o++){var a=n[o];if(!(l=r[a])){var s,l=r[a]=new Ni(a),u=void 0,h=this._getAdditiveTrack(a);if(h?(u=(s=(s=h.keyframes)[s.length-1])&&s.value,3===h.valType&&(u=u&&Pi(u))):u=this._target[a],null==u)continue;0<t&&l.addKeyframe(0,Ai(u),i),this._trackKeys.push(a)}l.addKeyframe(t,Ai(e[a]),i)}return this._maxTime=Math.max(this._maxTime,t),this},i.prototype.pause=function(){this._clip.pause(),this._paused=!0},i.prototype.resume=function(){this._clip.resume(),this._paused=!1},i.prototype.isPaused=function(){return!!this._paused},i.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},i.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,n=0;n<e;n++)t[n].call(this)},i.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var n=0;n<e.length;n++)e[n].call(this)},i.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,n=0;n<e.length;n++)t[e[n]].setFinished()},i.prototype._getAdditiveTrack=function(t){var e,n=this._additiveAnimators;if(n)for(var i=0;i<n.length;i++){var r=n[i].getTrack(t);r&&(e=r)}return e},i.prototype.start=function(t){if(!(0<this._started)){this._started=1;for(var e,o=this,a=[],n=this._maxTime||0,i=0;i<this._trackKeys.length;i++){var r=this._trackKeys[i],s=this._tracks[r],r=this._getAdditiveTrack(r),l=s.keyframes,u=l.length;s.prepare(n,r),s.needsAnimate()&&(!this._allowDiscrete&&s.discrete?((r=l[u-1])&&(o._target[s.propName]=r.rawValue),s.setFinished()):a.push(s))}return a.length||this._force?(e=new Kn({life:n,loop:this._loop,delay:this._delay||0,onframe:function(t){o._started=2;var e=o._additiveAnimators;if(e){for(var n=!1,i=0;i<e.length;i++)if(e[i]._clip){n=!0;break}n||(o._additiveAnimators=null)}for(i=0;i<a.length;i++)a[i].step(o._target,t);var r=o._onframeCbs;if(r)for(i=0;i<r.length;i++)r[i](o._target,t)},ondestroy:function(){o._doneCallback()}}),this._clip=e,this.animation&&this.animation.addClip(e),t&&e.setEasing(t)):this._doneCallback(),this}},i.prototype.stop=function(t){var e;this._clip&&(e=this._clip,t&&e.onframe(1),this._abortedCallback())},i.prototype.delay=function(t){return this._delay=t,this},i.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},i.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},i.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},i.prototype.getClip=function(){return this._clip},i.prototype.getTrack=function(t){return this._tracks[t]},i.prototype.getTracks=function(){var e=this;return B(this._trackKeys,function(t){return e._tracks[t]})},i.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var n=this._tracks,i=this._trackKeys,r=0;r<t.length;r++){var o=n[t[r]];o&&!o.isFinished()&&(e?o.step(this._target,1):1===this._started&&o.step(this._target,0),o.setFinished())}for(var a=!0,r=0;r<i.length;r++)if(!n[i[r]].isFinished()){a=!1;break}return a&&this._abortedCallback(),a},i.prototype.saveTo=function(t,e,n){if(t){e=e||this._trackKeys;for(var i=0;i<e.length;i++){var r=e[i],o=this._tracks[r];o&&!o.isFinished()&&(o=(o=o.keyframes)[n?0:o.length-1])&&(t[r]=Ai(o.rawValue))}}},i.prototype.__changeFinalValue=function(t,e){e=e||ht(t);for(var n=0;n<e.length;n++){var i,r=e[n],o=this._tracks[r];o&&1<(i=o.keyframes).length&&(i=i.pop(),o.addKeyframe(i.time,t[r]),o.prepare(this._maxTime,o.getAdditiveTrack()))}};var zi=i;function i(t,e,n,i){this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,(this._loop=e)&&i?it("Can' use additive animation on looped animation."):(this._additiveAnimators=i,this._allowDiscrete=n)}function Bi(){return(new Date).getTime()}u(Hi,Fi=le),Hi.prototype.addClip=function(t){t.animation&&this.removeClip(t),this._head?((this._tail.next=t).prev=this._tail,t.next=null,this._tail=t):this._head=this._tail=t,t.animation=this},Hi.prototype.addAnimator=function(t){t.animation=this;t=t.getClip();t&&this.addClip(t)},Hi.prototype.removeClip=function(t){var e,n;t.animation&&(e=t.prev,n=t.next,e?e.next=n:this._head=n,n?n.prev=e:this._tail=e,t.next=t.prev=t.animation=null)},Hi.prototype.removeAnimator=function(t){var e=t.getClip();e&&this.removeClip(e),t.animation=null},Hi.prototype.update=function(t){for(var e=Bi()-this._pausedTime,n=e-this._time,i=this._head;i;)var r=i.next,i=(i.step(e,n)&&(i.ondestroy(),this.removeClip(i)),r);this._time=e,t||(this.trigger("frame",n),this.stage.update&&this.stage.update())},Hi.prototype._startLoop=function(){var e=this;this._running=!0,Tn(function t(){e._running&&(Tn(t),e._paused||e.update())})},Hi.prototype.start=function(){this._running||(this._time=Bi(),this._pausedTime=0,this._startLoop())},Hi.prototype.stop=function(){this._running=!1},Hi.prototype.pause=function(){this._paused||(this._pauseStart=Bi(),this._paused=!0)},Hi.prototype.resume=function(){this._paused&&(this._pausedTime+=Bi()-this._pauseStart,this._paused=!1)},Hi.prototype.clear=function(){for(var t=this._head;t;){var e=t.next;t.prev=t.next=t.animation=null,t=e}this._head=this._tail=null},Hi.prototype.isFinished=function(){return null==this._head},Hi.prototype.animate=function(t,e){e=e||{},this.start();t=new zi(t,e.loop);return this.addAnimator(t),t};var Fi,Vi=Hi;function Hi(t){var e=Fi.call(this)||this;return e._running=!1,e._time=0,e._pausedTime=0,e._pauseStart=0,e._paused=!1,e.stage=(t=t||{}).stage||{},e}var Gi,Wi=b.domSupported,Ui=(Gi={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},{mouse:n=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],touch:["touchstart","touchend","touchmove"],pointer:B(n,function(t){var e=t.replace("mouse","pointer");return Gi.hasOwnProperty(e)?e:t})}),Xi=["mousemove","mouseup"],Yi=["pointermove","pointerup"],qi=!1;function Zi(t){t=t.pointerType;return"pen"===t||"touch"===t}function ji(t){t&&(t.zrByTouch=!0)}function Ki(t,e){for(var n=e,i=!1;n&&9!==n.nodeType&&!(i=n.domBelongToZr||n!==e&&n===t.painterRoot);)n=n.parentNode;return i}var $i=function(t,e){this.stopPropagation=Ft,this.stopImmediatePropagation=Ft,this.preventDefault=Ft,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY},Qi={mousedown:function(t){t=Ce(this.dom,t),this.__mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=Ce(this.dom,t);var e=this.__mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||this.__togglePointerCapture(!0),this.trigger("mousemove",t)},mouseup:function(t){t=Ce(this.dom,t),this.__togglePointerCapture(!1),this.trigger("mouseup",t)},mouseout:function(t){Ki(this,(t=Ce(this.dom,t)).toElement||t.relatedTarget)||(this.__pointerCapturing&&(t.zrEventControl="no_globalout"),this.trigger("mouseout",t))},wheel:function(t){qi=!0,t=Ce(this.dom,t),this.trigger("mousewheel",t)},mousewheel:function(t){qi||(t=Ce(this.dom,t),this.trigger("mousewheel",t))},touchstart:function(t){ji(t=Ce(this.dom,t)),this.__lastTouchMoment=new Date,this.handler.processGesture(t,"start"),Qi.mousemove.call(this,t),Qi.mousedown.call(this,t)},touchmove:function(t){ji(t=Ce(this.dom,t)),this.handler.processGesture(t,"change"),Qi.mousemove.call(this,t)},touchend:function(t){ji(t=Ce(this.dom,t)),this.handler.processGesture(t,"end"),Qi.mouseup.call(this,t),+new Date-+this.__lastTouchMoment<300&&Qi.click.call(this,t)},pointerdown:function(t){Qi.mousedown.call(this,t)},pointermove:function(t){Zi(t)||Qi.mousemove.call(this,t)},pointerup:function(t){Qi.mouseup.call(this,t)},pointerout:function(t){Zi(t)||Qi.mouseout.call(this,t)}},Ji=(O(["click","dblclick","contextmenu"],function(e){Qi[e]=function(t){t=Ce(this.dom,t),this.trigger(e,t)}}),{pointermove:function(t){Zi(t)||Ji.mousemove.call(this,t)},pointerup:function(t){Ji.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}});function tr(i,r){var o=r.domHandlers;b.pointerEventsSupported?O(Ui.pointer,function(e){nr(r,e,function(t){o[e].call(i,t)})}):(b.touchEventsSupported&&O(Ui.touch,function(n){nr(r,n,function(t){var e;o[n].call(i,t),(e=r).touching=!0,null!=e.touchTimer&&(clearTimeout(e.touchTimer),e.touchTimer=null),e.touchTimer=setTimeout(function(){e.touching=!1,e.touchTimer=null},700)})}),O(Ui.mouse,function(e){nr(r,e,function(t){t=Te(t),r.touching||o[e].call(i,t)})}))}function er(i,r){function t(n){nr(r,n,function(t){var e;t=Te(t),Ki(i,t.target)||(e=t,t=Ce(i.dom,new $i(i,e),!0),r.domHandlers[n].call(i,t))},{capture:!0})}b.pointerEventsSupported?O(Yi,t):b.touchEventsSupported||O(Xi,t)}function nr(t,e,n,i){t.mounted[e]=n,t.listenerOpts[e]=i,t.domTarget.addEventListener(e,n,i)}function ir(t){var e,n,i,r,o,a=t.mounted;for(e in a)a.hasOwnProperty(e)&&(n=t.domTarget,r=a[i=e],o=t.listenerOpts[e],n.removeEventListener(i,r,o));t.mounted={}}var rr,or=function(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e},ar=(u(sr,rr=le),sr.prototype.dispose=function(){ir(this._localHandlerScope),Wi&&ir(this._globalHandlerScope)},sr.prototype.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},sr.prototype.__togglePointerCapture=function(t){var e;this.__mayPointerCapture=null,Wi&&+this.__pointerCapturing^+t&&(this.__pointerCapturing=t,e=this._globalHandlerScope,t?er(this,e):ir(e))},sr);function sr(t,e){var n=rr.call(this)||this;return n.__pointerCapturing=!1,n.dom=t,n.painterRoot=e,n._localHandlerScope=new or(t,Qi),Wi&&(n._globalHandlerScope=new or(document,Ji)),tr(n,n._localHandlerScope),n}var n=1,lr=n=b.hasGlobalWindow?Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1):n,ur="#333",hr="#ccc",cr=Oe;function pr(t){return 5e-5<t||t<-5e-5}var dr=[],fr=[],gr=Le(),yr=Math.abs,mr=(vr.prototype.getLocalTransform=function(t){return vr.getLocalTransform(this,t)},vr.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},vr.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},vr.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},vr.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},vr.prototype.needLocalTransform=function(){return pr(this.rotation)||pr(this.x)||pr(this.y)||pr(this.scaleX-1)||pr(this.scaleY-1)||pr(this.skewX)||pr(this.skewY)},vr.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),n=this.transform;e||t?(n=n||Le(),e?this.getLocalTransform(n):cr(n),t&&(e?Ne(n,t,n):Re(n,t)),this.transform=n,this._resolveGlobalScaleRatio(n)):n&&(cr(n),this.invTransform=null)},vr.prototype._resolveGlobalScaleRatio=function(t){var e,n,i=this.globalScaleRatio;null!=i&&1!==i&&(this.getGlobalScale(dr),n=((dr[1]-(n=dr[1]<0?-1:1))*i+n)/dr[1]||0,t[0]*=i=((dr[0]-(e=dr[0]<0?-1:1))*i+e)/dr[0]||0,t[1]*=i,t[2]*=n,t[3]*=n),this.invTransform=this.invTransform||Le(),Fe(this.invTransform,t)},vr.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},vr.prototype.setLocalTransform=function(t){var e,n,i,r;t&&(r=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],e=Math.atan2(t[1],t[0]),n=Math.PI/2+e-Math.atan2(t[3],t[2]),i=Math.sqrt(i)*Math.cos(n),r=Math.sqrt(r),this.skewX=n,this.skewY=0,this.rotation=-e,this.x=+t[4],this.y=+t[5],this.scaleX=r,this.scaleY=i,this.originX=0,this.originY=0)},vr.prototype.decomposeTransform=function(){var t,e,n;this.transform&&(e=this.parent,t=this.transform,e&&e.transform&&(e.invTransform=e.invTransform||Le(),Ne(fr,e.invTransform,t),t=fr),e=this.originX,n=this.originY,(e||n)&&(gr[4]=e,gr[5]=n,Ne(fr,t,gr),fr[4]-=e,fr[5]-=n,t=fr),this.setLocalTransform(t))},vr.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1])):(t[0]=1,t[1]=1),t},vr.prototype.transformCoordToLocal=function(t,e){t=[t,e],e=this.invTransform;return e&&ee(t,t,e),t},vr.prototype.transformCoordToGlobal=function(t,e){t=[t,e],e=this.transform;return e&&ee(t,t,e),t},vr.prototype.getLineScale=function(){var t=this.transform;return t&&1e-10<yr(t[0]-1)&&1e-10<yr(t[3]-1)?Math.sqrt(yr(t[0]*t[3]-t[2]*t[1])):1},vr.prototype.copyTransform=function(t){for(var e=this,n=t,i=0;i<_r.length;i++){var r=_r[i];e[r]=n[r]}},vr.getLocalTransform=function(t,e){e=e||[];var n=t.originX||0,i=t.originY||0,r=t.scaleX,o=t.scaleY,a=t.anchorX,s=t.anchorY,l=t.rotation||0,u=t.x,h=t.y,c=t.skewX?Math.tan(t.skewX):0,t=t.skewY?Math.tan(-t.skewY):0;return n||i||a||s?(e[4]=-(a=n+a)*r-c*(s=i+s)*o,e[5]=-s*o-t*a*r):e[4]=e[5]=0,e[0]=r,e[3]=o,e[1]=t*r,e[2]=c*o,l&&ze(e,e,l),e[4]+=n+u,e[5]+=i+h,e},vr.initDefaultProps=((n=vr.prototype).scaleX=n.scaleY=n.globalScaleRatio=1,void(n.x=n.y=n.originX=n.originY=n.skewX=n.skewY=n.rotation=n.anchorX=n.anchorY=0)),vr);function vr(){}var _r=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];var xr={};function wr(t,e){var n=xr[e=e||j],i=(n=n||(xr[e]=new ei(500))).get(t);return null==i&&(i=G.measureText(t,e).width,n.put(t,i)),i}function br(t,e,n,i){t=wr(t,e),e=Cr(e),n=Mr(0,t,n),i=Tr(0,e,i);return new X(n,i,t,e)}function Sr(t,e,n,i){var r=((t||"")+"").split("\n");if(1===r.length)return br(r[0],e,n,i);for(var o=new X(0,0,0,0),a=0;a<r.length;a++){var s=br(r[a],e,n,i);0===a?o.copy(s):o.union(s)}return o}function Mr(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function Tr(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function Cr(t){return wr("国",t)}function Ir(t,e){return"string"==typeof t?0<=t.lastIndexOf("%")?parseFloat(t)/100*e:parseFloat(t):t}function kr(t,e,n){var i=e.position||"inside",r=null!=e.distance?e.distance:5,o=n.height,a=n.width,s=o/2,l=n.x,u=n.y,h="left",c="top";if(i instanceof Array)l+=Ir(i[0],n.width),u+=Ir(i[1],n.height),c=h=null;else switch(i){case"left":l-=r,u+=s,h="right",c="middle";break;case"right":l+=r+a,u+=s,c="middle";break;case"top":l+=a/2,u-=r,h="center",c="bottom";break;case"bottom":l+=a/2,u+=o+r,h="center";break;case"inside":l+=a/2,u+=s,h="center",c="middle";break;case"insideLeft":l+=r,u+=s,c="middle";break;case"insideRight":l+=a-r,u+=s,h="right",c="middle";break;case"insideTop":l+=a/2,u+=r,h="center";break;case"insideBottom":l+=a/2,u+=o-r,h="center",c="bottom";break;case"insideTopLeft":l+=r,u+=r;break;case"insideTopRight":l+=a-r,u+=r,h="right";break;case"insideBottomLeft":l+=r,u+=o-r,c="bottom";break;case"insideBottomRight":l+=a-r,u+=o-r,h="right",c="bottom"}return(t=t||{}).x=l,t.y=u,t.align=h,t.verticalAlign=c,t}var Dr,Ar="__zr_normal__",Pr=_r.concat(["ignore"]),Lr=lt(_r,function(t,e){return t[e]=!0,t},{ignore:!1}),Or={},Rr=new X(0,0,0,0),n=(r.prototype._init=function(t){this.attr(t)},r.prototype.drift=function(t,e,n){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;(i=i||(this.transform=[1,0,0,1,0,0]))[4]+=t,i[5]+=e,this.decomposeTransform(),this.markRedraw()},r.prototype.beforeUpdate=function(){},r.prototype.afterUpdate=function(){},r.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},r.prototype.updateInnerText=function(t){var e,n,i,r,o,a,s,l,u,h,c=this._textContent;!c||c.ignore&&!t||(this.textConfig||(this.textConfig={}),l=(t=this.textConfig).local,i=n=void 0,r=!1,(e=c.innerTransformable).parent=l?this:null,h=!1,e.copyTransform(c),null!=t.position&&(u=Rr,t.layoutRect?u.copy(t.layoutRect):u.copy(this.getBoundingRect()),l||u.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(Or,t,u):kr(Or,t,u),e.x=Or.x,e.y=Or.y,n=Or.align,i=Or.verticalAlign,o=t.origin)&&null!=t.rotation&&(s=a=void 0,s="center"===o?(a=.5*u.width,.5*u.height):(a=Ir(o[0],u.width),Ir(o[1],u.height)),h=!0,e.originX=-e.x+a+(l?0:u.x),e.originY=-e.y+s+(l?0:u.y)),null!=t.rotation&&(e.rotation=t.rotation),(o=t.offset)&&(e.x+=o[0],e.y+=o[1],h||(e.originX=-o[0],e.originY=-o[1])),a=null==t.inside?"string"==typeof t.position&&0<=t.position.indexOf("inside"):t.inside,s=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),h=u=l=void 0,a&&this.canBeInsideText()?(l=t.insideFill,u=t.insideStroke,null!=l&&"auto"!==l||(l=this.getInsideTextFill()),null!=u&&"auto"!==u||(u=this.getInsideTextStroke(l),h=!0)):(l=t.outsideFill,u=t.outsideStroke,null!=l&&"auto"!==l||(l=this.getOutsideFill()),null!=u&&"auto"!==u||(u=this.getOutsideStroke(l),h=!0)),(l=l||"#000")===s.fill&&u===s.stroke&&h===s.autoStroke&&n===s.align&&i===s.verticalAlign||(r=!0,s.fill=l,s.stroke=u,s.autoStroke=h,s.align=n,s.verticalAlign=i,c.setDefaultTextStyle(s)),c.__dirty|=vn,r&&c.dirtyStyle(!0))},r.prototype.canBeInsideText=function(){return!0},r.prototype.getInsideTextFill=function(){return"#fff"},r.prototype.getInsideTextStroke=function(t){return"#000"},r.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?hr:ur},r.prototype.getOutsideStroke=function(t){for(var e=this.__zr&&this.__zr.getBackgroundColor(),n="string"==typeof e&&gi(e),i=(n=n||[255,255,255,1])[3],r=this.__zr.isDarkMode(),o=0;o<3;o++)n[o]=n[o]*i+(r?0:255)*(1-i);return n[3]=1,wi(n,"rgba")},r.prototype.traverse=function(t,e){},r.prototype.attrKV=function(t,e){"textConfig"===t?this.setTextConfig(e):"textContent"===t?this.setTextContent(e):"clipPath"===t?this.setClipPath(e):"extra"===t?(this.extra=this.extra||{},L(this.extra,e)):this[t]=e},r.prototype.hide=function(){this.ignore=!0,this.markRedraw()},r.prototype.show=function(){this.ignore=!1,this.markRedraw()},r.prototype.attr=function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(R(t))for(var n=ht(t),i=0;i<n.length;i++){var r=n[i];this.attrKV(r,t[r])}return this.markRedraw(),this},r.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,n=0;n<this.animators.length;n++){var i=this.animators[n],r=i.__fromStateTransition;i.getLoop()||r&&r!==Ar||(r=(r=i.targetName)?e[r]:e,i.saveTo(r))}},r.prototype._innerSaveToNormal=function(t){var e=(e=this._normalState)||(this._normalState={});t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,Pr)},r.prototype._savePrimaryToNormal=function(t,e,n){for(var i=0;i<n.length;i++){var r=n[i];null==t[r]||r in e||(e[r]=this[r])}},r.prototype.hasState=function(){return 0<this.currentStates.length},r.prototype.getState=function(t){return this.states[t]},r.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},r.prototype.clearStates=function(t){this.useState(Ar,!1,t)},r.prototype.useState=function(t,e,n,i){var r=t===Ar,o=this.hasState();if(o||!r){var a,o=this.currentStates,s=this.stateTransition;if(!(0<=I(o,t))||!e&&1!==o.length){if((a=(a=this.stateProxy&&!r?this.stateProxy(t):a)||this.states&&this.states[t])||r)return r||this.saveCurrentToNormalState(a),(o=!!(a&&a.hoverLayer||i))&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,a,this._normalState,e,!n&&!this.__inHover&&s&&0<s.duration,s),i=this._textContent,s=this._textGuide,i&&i.useState(t,e,n,o),s&&s.useState(t,e,n,o),r?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!o&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~vn),a;it("State "+t+" not exists.")}}},r.prototype.useStates=function(t,e,n){if(t.length){var i=[],r=this.currentStates,o=t.length,a=o===r.length;if(a)for(var s=0;s<o;s++)if(t[s]!==r[s]){a=!1;break}if(!a){for(s=0;s<o;s++){var l=t[s],u=void 0;(u=(u=this.stateProxy?this.stateProxy(l,t):u)||this.states[l])&&i.push(u)}var h=i[o-1],h=!!(h&&h.hoverLayer||n),n=(h&&this._toggleHoverLayerFlag(!0),this._mergeStates(i)),c=this.stateTransition,n=(this.saveCurrentToNormalState(n),this._applyStateObj(t.join(","),n,this._normalState,!1,!e&&!this.__inHover&&c&&0<c.duration,c),this._textContent),c=this._textGuide;n&&n.useStates(t,e,h),c&&c.useStates(t,e,h),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!h&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~vn)}}else this.clearStates()},r.prototype.isSilent=function(){for(var t=this.silent,e=this.parent;!t&&e;){if(e.silent){t=!0;break}e=e.parent}return t},r.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},r.prototype.removeState=function(t){var e,t=I(this.currentStates,t);0<=t&&((e=this.currentStates.slice()).splice(t,1),this.useStates(e))},r.prototype.replaceState=function(t,e,n){var i=this.currentStates.slice(),t=I(i,t),r=0<=I(i,e);0<=t?r?i.splice(t,1):i[t]=e:n&&!r&&i.push(e),this.useStates(i)},r.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},r.prototype._mergeStates=function(t){for(var e,n={},i=0;i<t.length;i++){var r=t[i];L(n,r),r.textConfig&&L(e=e||{},r.textConfig)}return e&&(n.textConfig=e),n},r.prototype._applyStateObj=function(t,e,n,i,r,o){for(var a=!(e&&i),s=(e&&e.textConfig?(this.textConfig=L({},(i?this:n).textConfig),L(this.textConfig,e.textConfig)):a&&n.textConfig&&(this.textConfig=n.textConfig),{}),l=!1,u=0;u<Pr.length;u++){var h=Pr[u],c=r&&Lr[h];e&&null!=e[h]?c?(l=!0,s[h]=e[h]):this[h]=e[h]:a&&null!=n[h]&&(c?(l=!0,s[h]=n[h]):this[h]=n[h])}if(!r)for(u=0;u<this.animators.length;u++){var p=this.animators[u],d=p.targetName;p.getLoop()||p.__changeFinalValue(d?(e||n)[d]:e||n)}l&&this._transitionState(t,s,o)},r.prototype._attachComponent=function(t){var e;t.__zr&&!t.__hostTarget||t!==this&&((e=this.__zr)&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this)},r.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},r.prototype.getClipPath=function(){return this._clipPath},r.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},r.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},r.prototype.getTextContent=function(){return this._textContent},r.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new mr,this._attachComponent(t),this._textContent=t,this.markRedraw())},r.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),L(this.textConfig,t),this.markRedraw()},r.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},r.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},r.prototype.getTextGuideLine=function(){return this._textGuide},r.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},r.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},r.prototype.markRedraw=function(){this.__dirty|=vn;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},r.prototype.dirty=function(){this.markRedraw()},r.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,n=this._textGuide;e&&(e.__inHover=t),n&&(n.__inHover=t)},r.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},r.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},r.prototype.animate=function(t,e,n){var i=t?this[t]:this,i=new zi(i,e,n);return t&&(i.targetName=t),this.addAnimator(i,t),i},r.prototype.addAnimator=function(n,t){var e=this.__zr,i=this;n.during(function(){i.updateDuringAnimation(t)}).done(function(){var t=i.animators,e=I(t,n);0<=e&&t.splice(e,1)}),this.animators.push(n),e&&e.animation.addAnimator(n),e&&e.wakeUp()},r.prototype.updateDuringAnimation=function(t){this.markRedraw()},r.prototype.stopAnimation=function(t,e){for(var n=this.animators,i=n.length,r=[],o=0;o<i;o++){var a=n[o];t&&t!==a.scope?r.push(a):a.stop(e)}return this.animators=r,this},r.prototype.animateTo=function(t,e,n){Er(this,t,e,n)},r.prototype.animateFrom=function(t,e,n){Er(this,t,e,n,!0)},r.prototype._transitionState=function(t,e,n,i){for(var r=Er(this,e,n,i),o=0;o<r.length;o++)r[o].__fromStateTransition=t},r.prototype.getBoundingRect=function(){return null},r.prototype.getPaintRect=function(){return null},r.initDefaultProps=((Dr=r.prototype).type="element",Dr.name="",Dr.ignore=Dr.silent=Dr.isGroup=Dr.draggable=Dr.dragging=Dr.ignoreClip=Dr.__inHover=!1,Dr.__dirty=vn,void(Object.defineProperty&&(Nr("position","_legacyPos","x","y"),Nr("scale","_legacyScale","scaleX","scaleY"),Nr("origin","_legacyOrigin","originX","originY")))),r);function r(t){this.id=et++,this.animators=[],this.currentStates=[],this.states={},this._init(t)}function Nr(t,e,n,i){function r(e,t){Object.defineProperty(t,0,{get:function(){return e[n]},set:function(t){e[n]=t}}),Object.defineProperty(t,1,{get:function(){return e[i]},set:function(t){e[i]=t}})}Object.defineProperty(Dr,t,{get:function(){var t;return this[e]||(t=this[e]=[],r(this,t)),this[e]},set:function(t){this[n]=t[0],this[i]=t[1],this[e]=t,r(this,t)}})}function Er(t,e,n,i,r){function o(){u=!0,--l<=0&&(u?h&&h():c&&c())}function a(){--l<=0&&(u?h&&h():c&&c())}var s=[],l=(!function t(e,n,i,r,o,a,s,l){var u=ht(r);var h=o.duration;var c=o.delay;var p=o.additive;var d=o.setToFinal;var f=!R(a);var g=e.animators;var y=[];for(var m=0;m<u.length;m++){var v=u[m],_=r[v];null!=_&&null!=i[v]&&(f||a[v])?!R(_)||st(_)||mt(_)?y.push(v):n?l||(i[v]=_,e.updateDuringAnimation(n)):t(e,v,i[v],_,o,a&&a[v],s,l):l||(i[v]=_,e.updateDuringAnimation(n),y.push(v))}var x=y.length;if(!p&&x)for(var w,b=0;b<g.length;b++)(S=g[b]).targetName===n&&S.stopTracks(y)&&(w=I(g,S),g.splice(w,1));o.force||(y=ut(y,function(t){return!Fr(r[t],i[t])}),x=y.length);if(0<x||o.force&&!s.length){var S,M=void 0,T=void 0,C=void 0;if(l){T={},d&&(M={});for(b=0;b<x;b++){v=y[b];T[v]=i[v],d?M[v]=r[v]:i[v]=r[v]}}else if(d){C={};for(b=0;b<x;b++){v=y[b];C[v]=Ai(i[v]),Br(i,r,v)}}(S=new zi(i,!1,!1,p?ut(g,function(t){return t.targetName===n}):null)).targetName=n,o.scope&&(S.scope=o.scope),d&&M&&S.whenWithKeys(0,M,y),C&&S.whenWithKeys(0,C,y),S.whenWithKeys(null==h?500:h,l?T:r,y).delay(c||0),e.addAnimator(S,n),s.push(S)}}(t,"",t,e,n=n||{},i,s,r),s.length),u=!1,h=n.done,c=n.aborted;l||h&&h(),0<s.length&&n.during&&s[0].during(function(t,e){n.during(e)});for(var p=0;p<s.length;p++){var d=s[p];d.done(o),d.aborted(a),n.force&&d.duration(n.duration),d.start(n.easing)}return s}function zr(t,e,n){for(var i=0;i<n;i++)t[i]=e[i]}function Br(t,e,n){if(st(e[n]))if(st(t[n])||(t[n]=[]),gt(e[n])){var i=e[n].length;t[n].length!==i&&(t[n]=new e[n].constructor(i),zr(t[n],e[n],i))}else{var r=e[n],o=t[n],a=r.length;if(st(r[0]))for(var s=r[0].length,l=0;l<a;l++)o[l]?zr(o[l],r[l],s):o[l]=Array.prototype.slice.call(r[l]);else zr(o,r,a);o.length=r.length}else t[n]=e[n]}function Fr(t,e){return t===e||st(t)&&st(e)&&function(t,e){var n=t.length;if(n!==e.length)return!1;for(var i=0;i<n;i++)if(t[i]!==e[i])return!1;return!0}(t,e)}at(n,le),at(n,mr);u(Gr,Vr=n),Gr.prototype.childrenRef=function(){return this._children},Gr.prototype.children=function(){return this._children.slice()},Gr.prototype.childAt=function(t){return this._children[t]},Gr.prototype.childOfName=function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},Gr.prototype.childCount=function(){return this._children.length},Gr.prototype.add=function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},Gr.prototype.addBefore=function(t,e){var n;return t&&t!==this&&t.parent!==this&&e&&e.parent===this&&0<=(e=(n=this._children).indexOf(e))&&(n.splice(e,0,t),this._doAdd(t)),this},Gr.prototype.replace=function(t,e){t=I(this._children,t);return 0<=t&&this.replaceAt(e,t),this},Gr.prototype.replaceAt=function(t,e){var n=this._children,i=n[e];return t&&t!==this&&t.parent!==this&&t!==i&&(n[e]=t,i.parent=null,(n=this.__zr)&&i.removeSelfFromZr(n),this._doAdd(t)),this},Gr.prototype._doAdd=function(t){t.parent&&t.parent.remove(t);var e=(t.parent=this).__zr;e&&e!==t.__zr&&t.addSelfToZr(e),e&&e.refresh()},Gr.prototype.remove=function(t){var e=this.__zr,n=this._children,i=I(n,t);return i<0||(n.splice(i,1),t.parent=null,e&&t.removeSelfFromZr(e),e&&e.refresh()),this},Gr.prototype.removeAll=function(){for(var t=this._children,e=this.__zr,n=0;n<t.length;n++){var i=t[n];e&&i.removeSelfFromZr(e),i.parent=null}return t.length=0,this},Gr.prototype.eachChild=function(t,e){for(var n=this._children,i=0;i<n.length;i++){var r=n[i];t.call(e,r,i)}return this},Gr.prototype.traverse=function(t,e){for(var n=0;n<this._children.length;n++){var i=this._children[n],r=t.call(e,i);i.isGroup&&!r&&i.traverse(t,e)}return this},Gr.prototype.addSelfToZr=function(t){Vr.prototype.addSelfToZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].addSelfToZr(t)},Gr.prototype.removeSelfFromZr=function(t){Vr.prototype.removeSelfFromZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].removeSelfFromZr(t)},Gr.prototype.getBoundingRect=function(t){for(var e=new X(0,0,0,0),n=t||this._children,i=[],r=null,o=0;o<n.length;o++){var a,s=n[o];s.ignore||s.invisible||(a=s.getBoundingRect(),(s=s.getLocalTransform(i))?(X.applyTransform(e,a,s),(r=r||e.clone()).union(e)):(r=r||a.clone()).union(a))}return r||e};var Vr,Hr=Gr;function Gr(t){var e=Vr.call(this)||this;return e.isGroup=!0,e._children=[],e.attr(t),e}Hr.prototype.type="group";var Wr={},Ur={};qr.prototype.add=function(t){!this._disposed&&t&&(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},qr.prototype.remove=function(t){!this._disposed&&t&&(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},qr.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},qr.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=function(t){if(t){if("string"==typeof t)return bi(t,1)<.4;if(t.colorStops){for(var e=t.colorStops,n=0,i=e.length,r=0;r<i;r++)n+=bi(e[r].color,1);return(n/=i)<.4}}return!1}(t))},qr.prototype.getBackgroundColor=function(){return this._backgroundColor},qr.prototype.setDarkMode=function(t){this._darkMode=t},qr.prototype.isDarkMode=function(){return this._darkMode},qr.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},qr.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},qr.prototype.flush=function(){this._disposed||this._flush(!1)},qr.prototype._flush=function(t){var e,n=Bi(),t=(this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately()),Bi());e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:t-n})):0<this._sleepAfterStill&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill)&&this.animation.stop()},qr.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},qr.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},qr.prototype.refreshHover=function(){this._needsRefreshHover=!0},qr.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&"canvas"===this.painter.getType()&&this.painter.refreshHover())},qr.prototype.resize=function(t){this._disposed||(this.painter.resize((t=t||{}).width,t.height),this.handler.resize())},qr.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},qr.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},qr.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},qr.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},qr.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},qr.prototype.on=function(t,e,n){return this._disposed||this.handler.on(t,e,n),this},qr.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},qr.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},qr.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof Hr&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},qr.prototype.dispose=function(){var t;this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,t=this.id,delete Ur[t])};var Xr,Yr=qr;function qr(t,e,n){var i,r=this,o=(this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,n=n||{},this.dom=e,this.id=t,new Sn),a=n.renderer||"canvas",a=(Wr[a]||(a=ht(Wr)[0]),n.useDirtyRect=null!=n.useDirtyRect&&n.useDirtyRect,new Wr[a](e,o,n,t)),e=n.ssr||a.ssrOnly,t=(this.storage=o,this.painter=a,b.node||b.worker||e?null:new ar(a.getViewportRoot(),a.root)),s=n.useCoarsePointer;(null==s||"auto"===s?b.touchEventsSupported:!!s)&&(i=N(n.pointerSize,44)),this.handler=new an(o,a,t,a.root,i),this.animation=new Vi({stage:{update:e?null:function(){return r._flush(!0)}}}),e||this.animation.start()}function Zr(t,e){t=new Yr(et++,t,e);return Ur[t.id]=t}function jr(t,e){Wr[t]=e}function Kr(t){Xr=t}var $r=Object.freeze({__proto__:null,dispose:function(t){t.dispose()},disposeAll:function(){for(var t in Ur)Ur.hasOwnProperty(t)&&Ur[t].dispose();Ur={}},getElementSSRData:function(t){if("function"==typeof Xr)return Xr(t)},getInstance:function(t){return Ur[t]},init:Zr,registerPainter:jr,registerSSRDataGetter:Kr,version:"5.6.1"}),Qr=20;function Jr(t,e,n,i){var r=e[0],e=e[1],o=n[0],n=n[1],a=e-r,s=n-o;if(0==a)return 0==s?o:(o+n)/2;if(i)if(0<a){if(t<=r)return o;if(e<=t)return n}else{if(r<=t)return o;if(t<=e)return n}else{if(t===r)return o;if(t===e)return n}return(t-r)/a*s+o}function to(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return V(t)?t.replace(/^\s+|\s+$/g,"").match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?NaN:+t}function eo(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),Qr),t=(+t).toFixed(e),n?t:+t}function no(t){if(t=+t,isNaN(t))return 0;if(1e-14<t)for(var e=1,n=0;n<15;n++,e*=10)if(Math.round(t*e)/e===t)return n;return io(t)}function io(t){var t=t.toString().toLowerCase(),e=t.indexOf("e"),n=0<e?+t.slice(e+1):0,e=0<e?e:t.length,t=t.indexOf(".");return Math.max(0,(t<0?0:e-1-t)-n)}function ro(t,e){var n=Math.log,i=Math.LN10,t=Math.floor(n(t[1]-t[0])/i),n=Math.round(n(Math.abs(e[1]-e[0]))/i),e=Math.min(Math.max(-t+n,0),20);return isFinite(e)?e:20}function oo(t){var e=2*Math.PI;return(t%e+e)%e}function ao(t){return-1e-4<t&&t<1e-4}var so=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function lo(t){var e,n;return t instanceof Date?t:V(t)?(e=so.exec(t))?e[8]?(n=+e[4]||0,"Z"!==e[8].toUpperCase()&&(n-=+e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,n,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0))):new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0):new Date(NaN):null==t?new Date(NaN):new Date(Math.round(t))}function uo(t){return Math.pow(10,ho(t))}function ho(t){var e;return 0===t?0:(e=Math.floor(Math.log(t)/Math.LN10),10<=t/Math.pow(10,e)&&e++,e)}function co(t,e){var n=ho(t),i=Math.pow(10,n),r=t/i,e=e?r<1.5?1:r<2.5?2:r<4?3:r<7?5:10:r<1?1:r<2?2:r<3?3:r<5?5:10;return t=e*i,-20<=n?+t.toFixed(n<0?-n:0):t}function po(t){var e=parseFloat(t);return e==t&&(0!==e||!V(t)||t.indexOf("x")<=0)?e:NaN}function fo(t){return!isNaN(po(t))}function go(){return Math.round(9*Math.random())}function yo(t,e){return null==t?e:null==e?t:t*e/function t(e,n){return 0===n?e:t(n,e%n)}(t,e)}function f(t){throw new Error(t)}function mo(t,e,n){return(e-t)*n+t}var vo="series\0";function _o(t){return t instanceof Array?t:null==t?[]:[t]}function xo(t,e,n){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var i=0,r=n.length;i<r;i++){var o=n[i];!t.emphasis[e].hasOwnProperty(o)&&t[e].hasOwnProperty(o)&&(t.emphasis[e][o]=t[e][o])}}}var wo=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function bo(t){return!R(t)||F(t)||t instanceof Date?t:t.value}function So(t,n,e){var o,a,s,l,r,u,i,h,c,p,d="normalMerge"===e,f="replaceMerge"===e,g="replaceAll"===e,y=(t=t||[],n=(n||[]).slice(),E()),e=(O(n,function(t,e){R(t)||(n[e]=null)}),function(t,e,n){var i=[];if("replaceAll"!==n)for(var r=0;r<t.length;r++){var o=t[r];o&&null!=o.id&&e.set(o.id,r),i.push({existing:"replaceMerge"===n||ko(o)?null:o,newOption:null,keyInfo:null,brandNew:null})}return i}(t,y,e));return(d||f)&&(o=e,a=t,s=y,O(l=n,function(t,e){var n,i,r;t&&null!=t.id&&(n=To(t.id),null!=(i=s.get(n)))&&(Tt(!(r=o[i]).newOption,'Duplicated option on id "'+n+'".'),r.newOption=t,r.existing=a[i],l[e]=null)})),d&&(r=e,O(u=n,function(t,e){if(t&&null!=t.name)for(var n=0;n<r.length;n++){var i=r[n].existing;if(!r[n].newOption&&i&&(null==i.id||null==t.id)&&!ko(t)&&!ko(i)&&Mo("name",i,t))return r[n].newOption=t,void(u[e]=null)}})),d||f?(h=e,c=f,O(n,function(t){if(t){for(var e,n=0;(e=h[n])&&(e.newOption||ko(e.existing)||e.existing&&null!=t.id&&!Mo("id",t,e.existing));)n++;e?(e.newOption=t,e.brandNew=c):h.push({newOption:t,brandNew:c,existing:null,keyInfo:null}),n++}})):g&&(i=e,O(n,function(t){i.push({newOption:t,brandNew:!0,existing:null,keyInfo:null})})),t=e,p=E(),O(t,function(t){var e=t.existing;e&&p.set(e.id,t)}),O(t,function(t){var e=t.newOption;Tt(!e||null==e.id||!p.get(e.id)||p.get(e.id)===t,"id duplicates: "+(e&&e.id)),e&&null!=e.id&&p.set(e.id,t),t.keyInfo||(t.keyInfo={})}),O(t,function(t,e){var n=t.existing,i=t.newOption,r=t.keyInfo;if(R(i)){if(r.name=null!=i.name?To(i.name):n?n.name:vo+e,n)r.id=To(n.id);else if(null!=i.id)r.id=To(i.id);else for(var o=0;r.id="\0"+r.name+"\0"+o++,p.get(r.id););p.set(r.id,t)}}),e}function Mo(t,e,n){e=Co(e[t],null),n=Co(n[t],null);return null!=e&&null!=n&&e===n}function To(t){return Co(t,"")}function Co(t,e){return null==t?e:V(t)?t:H(t)||dt(t)?t+"":e}function Io(t){t=t.name;return!(!t||!t.indexOf(vo))}function ko(t){return t&&null!=t.id&&0===To(t.id).indexOf("\0_ec_\0")}function Do(t,r,o){O(t,function(t){var e,n,i=t.newOption;R(i)&&(t.keyInfo.mainType=r,t.keyInfo.subType=(e=r,i=i,t=t.existing,n=o,i.type||(t?t.subType:n.determineSubType(e,i))))})}function Ao(e,t){return null!=t.dataIndexInside?t.dataIndexInside:null!=t.dataIndex?F(t.dataIndex)?B(t.dataIndex,function(t){return e.indexOfRawIndex(t)}):e.indexOfRawIndex(t.dataIndex):null!=t.name?F(t.name)?B(t.name,function(t){return e.indexOfName(t)}):e.indexOfName(t.name):void 0}function Po(){var e="__ec_inner_"+Lo++;return function(t){return t[e]||(t[e]={})}}var Lo=go();function Oo(n,t,i){var t=Ro(t,i),e=t.mainTypeSpecified,r=t.queryOptionMap,o=t.others,a=i?i.defaultMainType:null;return!e&&a&&r.set(a,{}),r.each(function(t,e){t=Eo(n,e,t,{useDefault:a===e,enableAll:!i||null==i.enableAll||i.enableAll,enableNone:!i||null==i.enableNone||i.enableNone});o[e+"Models"]=t.models,o[e+"Model"]=t.models[0]}),o}function Ro(t,i){var e=V(t)?((e={})[t+"Index"]=0,e):t,r=E(),o={},a=!1;return O(e,function(t,e){var n;"dataIndex"===e||"dataIndexInside"===e?o[e]=t:(n=(e=e.match(/^(\w+)(Index|Id|Name)$/)||[])[1],e=(e[2]||"").toLowerCase(),!n||!e||i&&i.includeMainTypes&&I(i.includeMainTypes,n)<0||(a=a||!!n,(r.get(n)||r.set(n,{}))[e]=t))}),{mainTypeSpecified:a,queryOptionMap:r,others:o}}var No={useDefault:!0,enableAll:!1,enableNone:!1};function Eo(t,e,n,i){i=i||No;var r=n.index,o=n.id,n=n.name,a={models:null,specified:null!=r||null!=o||null!=n};return a.specified?"none"===r||!1===r?(Tt(i.enableNone,'`"none"` or `false` is not a valid value on index option.'),a.models=[]):("all"===r&&(Tt(i.enableAll,'`"all"` is not a valid value on index option.'),r=o=n=null),a.models=t.queryComponents({mainType:e,index:r,id:o,name:n})):(r=void 0,a.models=i.useDefault&&(r=t.getComponent(e))?[r]:[]),a}function zo(t,e,n){t.setAttribute?t.setAttribute(e,n):t[e]=n}function Bo(t,e,n,i,r){var o=null==e||"auto"===e;if(null==i)return i;if(H(i))return eo(p=mo(n||0,i,r),o?Math.max(no(n||0),no(i)):e);if(V(i))return r<1?n:i;for(var a=[],s=n,l=i,u=Math.max(s?s.length:0,l.length),h=0;h<u;++h){var c,p,d=t.getDimensionInfo(h);d&&"ordinal"===d.type?a[h]=(r<1&&s?s:l)[h]:(p=mo(d=s&&s[h]?s[h]:0,c=l[h],r),a[h]=eo(p,o?Math.max(no(d),no(c)):e))}return a}var Fo=".",Vo="___EC__COMPONENT__CONTAINER___",Ho="___EC__EXTENDED_CLASS___";function Go(t){var e={main:"",sub:""};return t&&(t=t.split(Fo),e.main=t[0]||"",e.sub=t[1]||""),e}function Wo(t){(t.$constructor=t).extend=function(t){var e,n,i,r=this;function o(){return n.apply(this,arguments)||this}return k(i=r)&&/^class\s/.test(Function.prototype.toString.call(i))?(u(o,n=r),e=o):ot(e=function(){(t.$constructor||r).apply(this,arguments)},this),L(e.prototype,t),e[Ho]=!0,e.extend=this.extend,e.superCall=Yo,e.superApply=qo,e.superClass=r,e}}function Uo(t,e){t.extend=e.extend}var Xo=Math.round(10*Math.random());function Yo(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return this.superClass.prototype[e].apply(t,n)}function qo(t,e,n){return this.superClass.prototype[e].apply(t,n)}function Zo(t){var r={};t.registerClass=function(t){var e,n=t.type||t.prototype.type;return n&&(Tt(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(e=n),'componentType "'+e+'" illegal'),(e=Go(t.prototype.type=n)).sub?e.sub!==Vo&&(function(t){var e=r[t.main];e&&e[Vo]||(e=r[t.main]={___EC__COMPONENT__CONTAINER___:!0});return e}(e)[e.sub]=t):r[e.main]=t),t},t.getClass=function(t,e,n){var i=r[t];if(i&&i[Vo]&&(i=e?i[e]:null),n&&!i)throw new Error(e?"Component "+t+"."+(e||"")+" is used but not imported.":t+".type should be specified.");return i},t.getClassesByMainType=function(t){var t=Go(t),n=[],t=r[t.main];return t&&t[Vo]?O(t,function(t,e){e!==Vo&&n.push(t)}):n.push(t),n},t.hasClass=function(t){t=Go(t);return!!r[t.main]},t.getAllClassMainTypes=function(){var n=[];return O(r,function(t,e){n.push(e)}),n},t.hasSubTypes=function(t){t=Go(t),t=r[t.main];return t&&t[Vo]}}function jo(a,s){for(var t=0;t<a.length;t++)a[t][1]||(a[t][1]=a[t][0]);return s=s||!1,function(t,e,n){for(var i={},r=0;r<a.length;r++){var o=a[r][1];e&&0<=I(e,o)||n&&I(n,o)<0||null!=(o=t.getShallow(o,s))&&(i[a[r][0]]=o)}return i}}var Ko=jo([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),$o=(Qo.prototype.getAreaStyle=function(t,e){return Ko(this,t,e)},Qo);function Qo(){}var Jo=new ei(50);function ta(t,e,n,i,r){return t?"string"==typeof t?(e&&e.__zrImageSrc===t||!n||(n={hostEl:n,cb:i,cbPayload:r},(i=Jo.get(t))?na(e=i.image)||i.pending.push(n):((e=G.loadImage(t,ea,ea)).__zrImageSrc=t,Jo.put(t,e.__cachedImgObj={image:e,pending:[n]}))),e):t:e}function ea(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],i=n.cb;i&&i(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}function na(t){return t&&t.width&&t.height}var ia=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function ra(t,e,n,i,r,o){if(n){for(var a=(e+"").split("\n"),s=(o=oa(n,i,r,o),!1),l={},u=0,h=a.length;u<h;u++)aa(l,a[u],o),a[u]=l.textLine,s=s||l.isTruncated;t.text=a.join("\n"),t.isTruncated=s}else t.text="",t.isTruncated=!1}function oa(t,e,n,i){for(var r=L({},i=i||{}),o=(r.font=e,n=N(n,"..."),r.maxIterations=N(i.maxIterations,2),r.minChar=N(i.minChar,0)),a=(r.cnCharWidth=wr("国",e),r.ascCharWidth=wr("a",e)),s=(r.placeholder=N(i.placeholder,""),t=Math.max(0,t-1)),l=0;l<o&&a<=s;l++)s-=a;i=wr(n,e);return s<i&&(n="",i=0),s=t-i,r.ellipsis=n,r.ellipsisWidth=i,r.contentWidth=s,r.containerWidth=t,r}function aa(t,e,n){var i=n.containerWidth,r=n.font,o=n.contentWidth;if(i)if((l=wr(e,r))<=i)t.textLine=e,t.isTruncated=!1;else{for(var a=0;;a++){if(l<=o||a>=n.maxIterations){e+=n.ellipsis;break}var s=0===a?function(t,e,n,i){for(var r=0,o=0,a=t.length;o<a&&r<e;o++){var s=t.charCodeAt(o);r+=0<=s&&s<=127?n:i}return o}(e,o,n.ascCharWidth,n.cnCharWidth):0<l?Math.floor(e.length*o/l):0,l=wr(e=e.substr(0,s),r)}""===e&&(e=n.placeholder),t.textLine=e,t.isTruncated=!0}else t.textLine="",t.isTruncated=!1}var sa=function(){},la=function(t){this.tokens=[],t&&(this.tokens=t)},ua=function(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[],this.isTruncated=!1};function ha(t,e){var n=new ua;if(null!=t&&(t+=""),t){for(var i,r=e.width,o=e.height,a=e.overflow,s="break"!==a&&"breakAll"!==a||null==r?null:{width:r,accumWidth:0,breakAll:"breakAll"===a},l=ia.lastIndex=0;null!=(i=ia.exec(t));){var u=i.index;l<u&&ca(n,t.substring(l,u),e,s),ca(n,i[2],e,s,i[1]),l=ia.lastIndex}l<t.length&&ca(n,t.substring(l,t.length),e,s);var h,c=[],p=0,d=0,f=e.padding,g="truncate"===a,y="truncate"===e.lineOverflow,m={};t:for(var v=0;v<n.lines.length;v++){for(var _=n.lines[v],x=0,w=0,b=0;b<_.tokens.length;b++){var S=(A=_.tokens[b]).styleName&&e.rich[A.styleName]||{},M=A.textPadding=S.padding,T=M?M[1]+M[3]:0,C=A.font=S.font||e.font,I=(A.contentHeight=Cr(C),N(S.height,A.contentHeight));if(A.innerHeight=I,M&&(I+=M[0]+M[2]),A.height=I,A.lineHeight=bt(S.lineHeight,e.lineHeight,I),A.align=S&&S.align||e.align,A.verticalAlign=S&&S.verticalAlign||"middle",y&&null!=o&&p+A.lineHeight>o){M=n.lines.length;0<b?(_.tokens=_.tokens.slice(0,b),L(_,w,x),n.lines=n.lines.slice(0,v+1)):n.lines=n.lines.slice(0,v),n.isTruncated=n.isTruncated||n.lines.length<M;break t}var k,M=S.width,D=null==M||"auto"===M;"string"==typeof M&&"%"===M.charAt(M.length-1)?(A.percentWidth=M,c.push(A),A.contentWidth=wr(A.text,C)):(D&&(M=(M=S.backgroundColor)&&M.image)&&(k=void 0,na(M="string"==typeof(h=M)?(k=Jo.get(h))&&k.image:h))&&(A.width=Math.max(A.width,M.width*I/M.height)),null!=(k=g&&null!=r?r-w:null)&&k<A.width?!D||k<T?(A.text="",A.width=A.contentWidth=0):(ra(m,A.text,k-T,C,e.ellipsis,{minChar:e.truncateMinChar}),A.text=m.text,n.isTruncated=n.isTruncated||m.isTruncated,A.width=A.contentWidth=wr(A.text,C)):A.contentWidth=wr(A.text,C)),A.width+=T,w+=A.width,S&&(x=Math.max(x,A.lineHeight))}L(_,w,x)}n.outerWidth=n.width=N(r,d),n.outerHeight=n.height=N(o,p),n.contentHeight=p,n.contentWidth=d,f&&(n.outerWidth+=f[1]+f[3],n.outerHeight+=f[0]+f[2]);for(v=0;v<c.length;v++){var A,P=(A=c[v]).percentWidth;A.width=parseInt(P,10)/100*n.width}}return n;function L(t,e,n){t.width=e,t.lineHeight=n,p+=n,d=Math.max(d,e)}}function ca(t,e,n,i,r){var o,a,s=""===e,l=r&&n.rich[r]||{},u=t.lines,h=l.font||n.font,c=!1;i?(n=(t=l.padding)?t[1]+t[3]:0,null!=l.width&&"auto"!==l.width?(t=Ir(l.width,i.width)+n,0<u.length&&t+i.accumWidth>i.width&&(o=e.split("\n"),c=!0),i.accumWidth=t):(t=da(e,h,i.width,i.breakAll,i.accumWidth),i.accumWidth=t.accumWidth+n,a=t.linesWidths,o=t.lines)):o=e.split("\n");for(var p=0;p<o.length;p++){var d,f,g=o[p],y=new sa;y.styleName=r,y.text=g,y.isLineHolder=!g&&!s,"number"==typeof l.width?y.width=l.width:y.width=a?a[p]:wr(g,h),p||c?u.push(new la([y])):1===(f=(d=(u[u.length-1]||(u[0]=new la)).tokens).length)&&d[0].isLineHolder?d[0]=y:!g&&f&&!s||d.push(y)}}var pa=lt(",&?/;] ".split(""),function(t,e){return t[e]=!0,t},{});function da(t,e,n,i,r){for(var o,a=[],s=[],l="",u="",h=0,c=0,p=0;p<t.length;p++){var d,f,g=t.charAt(p);"\n"===g?(u&&(l+=u,c+=h),a.push(l),s.push(c),u=l="",c=h=0):(d=wr(g,e),f=!(i||(f=void 0,!(32<=(f=(f=o=g).charCodeAt(0))&&f<=591||880<=f&&f<=4351||4608<=f&&f<=5119||7680<=f&&f<=8303))||!!pa[o]),(a.length?n<c+d:n<r+c+d)?c?(l||u)&&(c=f?(l||(l=u,u="",c=h=0),a.push(l),s.push(c-h),u+=g,l="",h+=d):(u&&(l+=u,u="",h=0),a.push(l),s.push(c),l=g,d)):f?(a.push(u),s.push(h),u=g,h=d):(a.push(g),s.push(d)):(c+=d,f?(u+=g,h+=d):(u&&(l+=u,u="",h=0),l+=g)))}return a.length||l||(l=t,u="",h=0),u&&(l+=u),l&&(a.push(l),s.push(c)),1===a.length&&(c+=r),{accumWidth:c,lines:a,linesWidths:s}}var fa,ga="__zr_style_"+Math.round(10*Math.random()),ya={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},ma={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}},va=(ya[ga]=!0,["z","z2","invisible"]),_a=["invisible"],n=(u(o,fa=n),o.prototype._init=function(t){for(var e=ht(t),n=0;n<e.length;n++){var i=e[n];"style"===i?this.useStyle(t[i]):fa.prototype.attrKV.call(this,i,t[i])}this.style||this.useStyle({})},o.prototype.beforeBrush=function(){},o.prototype.afterBrush=function(){},o.prototype.innerBeforeBrush=function(){},o.prototype.innerAfterBrush=function(){},o.prototype.shouldBePainted=function(t,e,n,i){var r=this.transform;if(this.ignore||this.invisible||0===this.style.opacity||this.culling&&function(t,e,n){xa.copy(t.getBoundingRect()),t.transform&&xa.applyTransform(t.transform);return wa.width=e,wa.height=n,!xa.intersect(wa)}(this,t,e)||r&&!r[0]&&!r[3])return!1;if(n&&this.__clipPaths)for(var o=0;o<this.__clipPaths.length;++o)if(this.__clipPaths[o].isZeroArea())return!1;if(i&&this.parent)for(var a=this.parent;a;){if(a.ignore)return!1;a=a.parent}return!0},o.prototype.contain=function(t,e){return this.rectContain(t,e)},o.prototype.traverse=function(t,e){t.call(e,this)},o.prototype.rectContain=function(t,e){t=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(t[0],t[1])},o.prototype.getPaintRect=function(){var t,e,n,i,r,o=this._paintRect;return this._paintRect&&!this.__dirty||(r=this.transform,t=this.getBoundingRect(),e=(i=this.style).shadowBlur||0,n=i.shadowOffsetX||0,i=i.shadowOffsetY||0,o=this._paintRect||(this._paintRect=new X(0,0,0,0)),r?X.applyTransform(o,t,r):o.copy(t),(e||n||i)&&(o.width+=2*e+Math.abs(n),o.height+=2*e+Math.abs(i),o.x=Math.min(o.x,o.x+n-e),o.y=Math.min(o.y,o.y+i-e)),r=this.dirtyRectTolerance,o.isZero())||(o.x=Math.floor(o.x-r),o.y=Math.floor(o.y-r),o.width=Math.ceil(o.width+1+2*r),o.height=Math.ceil(o.height+1+2*r)),o},o.prototype.setPrevPaintRect=function(t){t?(this._prevPaintRect=this._prevPaintRect||new X(0,0,0,0),this._prevPaintRect.copy(t)):this._prevPaintRect=null},o.prototype.getPrevPaintRect=function(){return this._prevPaintRect},o.prototype.animateStyle=function(t){return this.animate("style",t)},o.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():this.markRedraw()},o.prototype.attrKV=function(t,e){"style"!==t?fa.prototype.attrKV.call(this,t,e):this.style?this.setStyle(e):this.useStyle(e)},o.prototype.setStyle=function(t,e){return"string"==typeof t?this.style[t]=e:L(this.style,t),this.dirtyStyle(),this},o.prototype.dirtyStyle=function(t){t||this.markRedraw(),this.__dirty|=2,this._rect&&(this._rect=null)},o.prototype.dirty=function(){this.dirtyStyle()},o.prototype.styleChanged=function(){return!!(2&this.__dirty)},o.prototype.styleUpdated=function(){this.__dirty&=-3},o.prototype.createStyle=function(t){return Et(ya,t)},o.prototype.useStyle=function(t){t[ga]||(t=this.createStyle(t)),this.__inHover?this.__hoverStyle=t:this.style=t,this.dirtyStyle()},o.prototype.isStyleObject=function(t){return t[ga]},o.prototype._innerSaveToNormal=function(t){fa.prototype._innerSaveToNormal.call(this,t);var e=this._normalState;t.style&&!e.style&&(e.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(t,e,va)},o.prototype._applyStateObj=function(t,e,n,i,r,o){fa.prototype._applyStateObj.call(this,t,e,n,i,r,o);var a,s=!(e&&i);if(e&&e.style?r?i?a=e.style:(a=this._mergeStyle(this.createStyle(),n.style),this._mergeStyle(a,e.style)):(a=this._mergeStyle(this.createStyle(),(i?this:n).style),this._mergeStyle(a,e.style)):s&&(a=n.style),a)if(r){var l=this.style;if(this.style=this.createStyle(s?{}:l),s)for(var u=ht(l),h=0;h<u.length;h++)(p=u[h])in a&&(a[p]=a[p],this.style[p]=l[p]);for(var c=ht(a),h=0;h<c.length;h++){var p=c[h];this.style[p]=this.style[p]}this._transitionState(t,{style:a},o,this.getAnimationStyleProps())}else this.useStyle(a);for(var d=this.__inHover?_a:va,h=0;h<d.length;h++){p=d[h];e&&null!=e[p]?this[p]=e[p]:s&&null!=n[p]&&(this[p]=n[p])}},o.prototype._mergeStates=function(t){for(var e,n=fa.prototype._mergeStates.call(this,t),i=0;i<t.length;i++){var r=t[i];r.style&&this._mergeStyle(e=e||{},r.style)}return e&&(n.style=e),n},o.prototype._mergeStyle=function(t,e){return L(t,e),t},o.prototype.getAnimationStyleProps=function(){return ma},o.initDefaultProps=((n=o.prototype).type="displayable",n.invisible=!1,n.z=0,n.z2=0,n.zlevel=0,n.culling=!1,n.cursor="pointer",n.rectHover=!1,n.incremental=!1,n._rect=null,n.dirtyRectTolerance=0,void(n.__dirty=2|vn)),o);function o(t){return fa.call(this,t)||this}var xa=new X(0,0,0,0),wa=new X(0,0,0,0);var ba=Math.min,Sa=Math.max,Ma=Math.sin,Ta=Math.cos,Ca=2*Math.PI,Ia=Gt(),ka=Gt(),Da=Gt();function Aa(t,e,n,i,r,o){r[0]=ba(t,n),r[1]=ba(e,i),o[0]=Sa(t,n),o[1]=Sa(e,i)}var Pa=[],La=[];var Y={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},Oa=[],Ra=[],Na=[],Ea=[],za=[],Ba=[],Fa=Math.min,Va=Math.max,Ha=Math.cos,Ga=Math.sin,Wa=Math.abs,Ua=Math.PI,Xa=2*Ua,Ya="undefined"!=typeof Float32Array,qa=[];function Za(t){return Math.round(t/Ua*1e8)/1e8%2*Ua}a.prototype.increaseVersion=function(){this._version++},a.prototype.getVersion=function(){return this._version},a.prototype.setScale=function(t,e,n){0<(n=n||0)&&(this._ux=Wa(n/lr/t)||0,this._uy=Wa(n/lr/e)||0)},a.prototype.setDPR=function(t){this.dpr=t},a.prototype.setContext=function(t){this._ctx=t},a.prototype.getContext=function(){return this._ctx},a.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},a.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},a.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(Y.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},a.prototype.lineTo=function(t,e){var n=Wa(t-this._xi),i=Wa(e-this._yi),r=n>this._ux||i>this._uy;return this.addData(Y.L,t,e),this._ctx&&r&&this._ctx.lineTo(t,e),r?(this._xi=t,this._yi=e,this._pendingPtDist=0):(r=n*n+i*i)>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=r),this},a.prototype.bezierCurveTo=function(t,e,n,i,r,o){return this._drawPendingPt(),this.addData(Y.C,t,e,n,i,r,o),this._ctx&&this._ctx.bezierCurveTo(t,e,n,i,r,o),this._xi=r,this._yi=o,this},a.prototype.quadraticCurveTo=function(t,e,n,i){return this._drawPendingPt(),this.addData(Y.Q,t,e,n,i),this._ctx&&this._ctx.quadraticCurveTo(t,e,n,i),this._xi=n,this._yi=i,this},a.prototype.arc=function(t,e,n,i,r,o){this._drawPendingPt(),qa[0]=i,qa[1]=r,s=o,(l=Za((a=qa)[0]))<0&&(l+=Xa),h=l-a[0],u=a[1],u+=h,!s&&Xa<=u-l?u=l+Xa:s&&Xa<=l-u?u=l-Xa:!s&&u<l?u=l+(Xa-Za(l-u)):s&&l<u&&(u=l-(Xa-Za(u-l))),a[0]=l,a[1]=u;var a,s,l,u,h=(r=qa[1])-(i=qa[0]);return this.addData(Y.A,t,e,n,n,i,h,0,o?0:1),this._ctx&&this._ctx.arc(t,e,n,i,r,o),this._xi=Ha(r)*n+t,this._yi=Ga(r)*n+e,this},a.prototype.arcTo=function(t,e,n,i,r){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,n,i,r),this},a.prototype.rect=function(t,e,n,i){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,n,i),this.addData(Y.R,t,e,n,i),this},a.prototype.closePath=function(){this._drawPendingPt(),this.addData(Y.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&t.closePath(),this._xi=e,this._yi=n,this},a.prototype.fill=function(t){t&&t.fill(),this.toStatic()},a.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},a.prototype.len=function(){return this._len},a.prototype.setData=function(t){var e=t.length;this.data&&this.data.length===e||!Ya||(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e},a.prototype.appendPath=function(t){for(var e=(t=t instanceof Array?t:[t]).length,n=0,i=this._len,r=0;r<e;r++)n+=t[r].len();Ya&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n));for(r=0;r<e;r++)for(var o=t[r].data,a=0;a<o.length;a++)this.data[i++]=o[a];this._len=i},a.prototype.addData=function(t,e,n,i,r,o,a,s,l){if(this._saveData){var u=this.data;this._len+arguments.length>u.length&&(this._expandData(),u=this.data);for(var h=0;h<arguments.length;h++)u[this._len++]=arguments[h]}},a.prototype._drawPendingPt=function(){0<this._pendingPtDist&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},a.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},a.prototype.toStatic=function(){var t;this._saveData&&(this._drawPendingPt(),(t=this.data)instanceof Array)&&(t.length=this._len,Ya)&&11<this._len&&(this.data=new Float32Array(t))},a.prototype.getBoundingRect=function(){Na[0]=Na[1]=za[0]=za[1]=Number.MAX_VALUE,Ea[0]=Ea[1]=Ba[0]=Ba[1]=-Number.MAX_VALUE;for(var t,e=this.data,n=0,i=0,r=0,o=0,a=0;a<this._len;){var E=e[a++],z=1===a;switch(z&&(r=n=e[a],o=i=e[a+1]),E){case Y.M:n=r=e[a++],i=o=e[a++],za[0]=r,za[1]=o,Ba[0]=r,Ba[1]=o;break;case Y.L:Aa(n,i,e[a],e[a+1],za,Ba),n=e[a++],i=e[a++];break;case Y.C:G=H=m=y=V=g=f=d=p=c=F=B=h=u=l=s=void 0;var s=n,l=i,u=e[a++],h=e[a++],B=e[a++],F=e[a++],c=e[a],p=e[a+1],d=za,f=Ba,g=Vn,V=zn,y=g(s,u,B,c,Pa);d[0]=1/0,d[1]=1/0,f[0]=-1/0,f[1]=-1/0;for(var m=0;m<y;m++){var H=V(s,u,B,c,Pa[m]);d[0]=ba(H,d[0]),f[0]=Sa(H,f[0])}for(y=g(l,h,F,p,La),m=0;m<y;m++){var G=V(l,h,F,p,La[m]);d[1]=ba(G,d[1]),f[1]=Sa(G,f[1])}d[0]=ba(s,d[0]),f[0]=Sa(s,f[0]),d[0]=ba(c,d[0]),f[0]=Sa(c,f[0]),d[1]=ba(l,d[1]),f[1]=Sa(l,f[1]),d[1]=ba(p,d[1]),f[1]=Sa(p,f[1]),n=e[a++],i=e[a++];break;case Y.Q:g=n,L=i,M=e[a++],x=e[a++],S=e[a],v=e[a+1],b=za,T=Ba,t=w=t=_=void 0,_=Wn,t=Sa(ba((w=Xn)(g,M,S),1),0),w=Sa(ba(w(L,x,v),1),0),M=_(g,M,S,t),t=_(L,x,v,w),b[0]=ba(g,S,M),b[1]=ba(L,v,t),T[0]=Sa(g,S,M),T[1]=Sa(L,v,t),n=e[a++],i=e[a++];break;case Y.A:var v,_=e[a++],x=e[a++],w=e[a++],b=e[a++],S=e[a++],M=e[a++]+S,T=(a+=1,!e[a++]),C=(z&&(r=Ha(S)*w+_,o=Ga(S)*b+x),N=v=U=W=R=O=L=P=A=D=k=I=C=void 0,_),I=x,k=w,D=b,A=S,P=M,L=T,O=za,R=Ba,W=ne,U=ie;if((v=Math.abs(A-P))%Ca<1e-4&&1e-4<v)O[0]=C-k,O[1]=I-D,R[0]=C+k,R[1]=I+D;else{Ia[0]=Ta(A)*k+C,Ia[1]=Ma(A)*D+I,ka[0]=Ta(P)*k+C,ka[1]=Ma(P)*D+I,W(O,Ia,ka),U(R,Ia,ka),(A%=Ca)<0&&(A+=Ca),(P%=Ca)<0&&(P+=Ca),P<A&&!L?P+=Ca:A<P&&L&&(A+=Ca),L&&(v=P,P=A,A=v);for(var N=0;N<P;N+=Math.PI/2)A<N&&(Da[0]=Ta(N)*k+C,Da[1]=Ma(N)*D+I,W(O,Da,O),U(R,Da,R))}n=Ha(M)*w+_,i=Ga(M)*b+x;break;case Y.R:Aa(r=n=e[a++],o=i=e[a++],r+e[a++],o+e[a++],za,Ba);break;case Y.Z:n=r,i=o}ne(Na,Na,za),ie(Ea,Ea,Ba)}return 0===a&&(Na[0]=Na[1]=Ea[0]=Ea[1]=0),new X(Na[0],Na[1],Ea[0]-Na[0],Ea[1]-Na[1])},a.prototype._calculateLength=function(){for(var t=this.data,e=this._len,n=this._ux,i=this._uy,r=0,o=0,a=0,s=0,l=(this._pathSegLen||(this._pathSegLen=[]),this._pathSegLen),u=0,h=0,c=0;c<e;){var p=t[c++],d=1===c,f=(d&&(a=r=t[c],s=o=t[c+1]),-1);switch(p){case Y.M:r=a=t[c++],o=s=t[c++];break;case Y.L:var g=t[c++],y=(_=t[c++])-o;(Wa(I=g-r)>n||Wa(y)>i||c===e-1)&&(f=Math.sqrt(I*I+y*y),r=g,o=_);break;case Y.C:var m=t[c++],v=t[c++],g=t[c++],_=t[c++],x=t[c++],w=t[c++],f=function(t,e,n,i,r,o,a,s,l){for(var u=t,h=e,c=0,p=1/l,d=1;d<=l;d++){var f=d*p,g=zn(t,n,r,a,f),f=zn(e,i,o,s,f),y=g-u,m=f-h;c+=Math.sqrt(y*y+m*m),u=g,h=f}return c}(r,o,m,v,g,_,x,w,10),r=x,o=w;break;case Y.Q:f=function(t,e,n,i,r,o,a){for(var s=t,l=e,u=0,h=1/a,c=1;c<=a;c++){var p=c*h,d=Wn(t,n,r,p),p=Wn(e,i,o,p),f=d-s,g=p-l;u+=Math.sqrt(f*f+g*g),s=d,l=p}return u}(r,o,m=t[c++],v=t[c++],g=t[c++],_=t[c++],10),r=g,o=_;break;case Y.A:var x=t[c++],w=t[c++],b=t[c++],S=t[c++],M=t[c++],T=t[c++],C=T+M;c+=1,d&&(a=Ha(M)*b+x,s=Ga(M)*S+w),f=Va(b,S)*Fa(Xa,Math.abs(T)),r=Ha(C)*b+x,o=Ga(C)*S+w;break;case Y.R:a=r=t[c++],s=o=t[c++];f=2*t[c++]+2*t[c++];break;case Y.Z:var I=a-r,y=s-o;f=Math.sqrt(I*I+y*y),r=a,o=s}0<=f&&(u+=l[h++]=f)}return this._pathLen=u},a.prototype.rebuildPath=function(t,e){var n,i,r,o,a,s,l,u,h=this.data,E=this._ux,z=this._uy,B=this._len,c=e<1,p=0,d=0,f=0;if(!c||(this._pathSegLen||this._calculateLength(),a=this._pathSegLen,s=e*this._pathLen))t:for(var g=0;g<B;){var y=h[g++],F=1===g;switch(F&&(n=r=h[g],i=o=h[g+1]),y!==Y.L&&0<f&&(t.lineTo(l,u),f=0),y){case Y.M:n=r=h[g++],i=o=h[g++],t.moveTo(r,o);break;case Y.L:var m=h[g++],v=h[g++],_=Wa(m-r),x=Wa(v-o);if(E<_||z<x){if(c){if(s<p+(N=a[d++])){var w=(s-p)/N;t.lineTo(r*(1-w)+m*w,o*(1-w)+v*w);break t}p+=N}t.lineTo(m,v),r=m,o=v,f=0}else{_=_*_+x*x;f<_&&(l=m,u=v,f=_)}break;case Y.C:var b=h[g++],S=h[g++],M=h[g++],T=h[g++],x=h[g++],_=h[g++];if(c){if(s<p+(N=a[d++])){Hn(r,b,M,x,w=(s-p)/N,Oa),Hn(o,S,T,_,w,Ra),t.bezierCurveTo(Oa[1],Ra[1],Oa[2],Ra[2],Oa[3],Ra[3]);break t}p+=N}t.bezierCurveTo(b,S,M,T,x,_),r=x,o=_;break;case Y.Q:b=h[g++],S=h[g++],M=h[g++],T=h[g++];if(c){if(s<p+(N=a[d++])){Yn(r,b,M,w=(s-p)/N,Oa),Yn(o,S,T,w,Ra),t.quadraticCurveTo(Oa[1],Ra[1],Oa[2],Ra[2]);break t}p+=N}t.quadraticCurveTo(b,S,M,T),r=M,o=T;break;case Y.A:var C=h[g++],I=h[g++],k=h[g++],D=h[g++],A=h[g++],P=h[g++],L=h[g++],V=!h[g++],H=D<k?k:D,O=.001<Wa(k-D),R=A+P,G=!1;if(c&&(s<p+(N=a[d++])&&(R=A+P*(s-p)/N,G=!0),p+=N),O&&t.ellipse?t.ellipse(C,I,k,D,L,A,R,V):t.arc(C,I,H,A,R,V),G)break t;F&&(n=Ha(A)*k+C,i=Ga(A)*D+I),r=Ha(R)*k+C,o=Ga(R)*D+I;break;case Y.R:n=r=h[g],i=o=h[g+1],m=h[g++],v=h[g++];var N,P=h[g++],O=h[g++];if(c){if(s<p+(N=a[d++])){L=s-p;t.moveTo(m,v),t.lineTo(m+Fa(L,P),v),0<(L-=P)&&t.lineTo(m+P,v+Fa(L,O)),0<(L-=O)&&t.lineTo(m+Va(P-L,0),v+O),0<(L-=P)&&t.lineTo(m,v+Va(O-L,0));break t}p+=N}t.rect(m,v,P,O);break;case Y.Z:if(c){if(s<p+(N=a[d++])){w=(s-p)/N;t.lineTo(r*(1-w)+n*w,o*(1-w)+i*w);break t}p+=N}t.closePath(),r=n,o=i}}},a.prototype.clone=function(){var t=new a,e=this.data;return t.data=e.slice?e.slice():Array.prototype.slice.call(e),t._len=this._len,t},a.CMD=Y,a.initDefaultProps=((hu=a.prototype)._saveData=!0,hu._ux=0,hu._uy=0,hu._pendingPtDist=0,void(hu._version=0));var ja=a;function a(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}function Ka(t,e,n,i,r,o,a){var s;if(0!==r)return s=0,!(e+(r=r)<a&&i+r<a||a<e-r&&a<i-r||t+r<o&&n+r<o||o<t-r&&o<n-r)&&(t===n?Math.abs(o-t)<=r/2:(o=(s=(e-i)/(t-n))*o-a+(t*i-n*e)/(t-n))*o/(s*s+1)<=r/2*r/2)}var $a=2*Math.PI;function Qa(t){return(t%=$a)<0&&(t+=$a),t}var Ja=2*Math.PI;function ts(t,e,n,i,r,o){return e<o&&i<o||o<e&&o<i||i===e?0:(n=(o=(o-e)/(i-e))*(n-t)+t)===r?1/0:r<n?1!=o&&0!=o?i<e?1:-1:i<e?.5:-.5:0}var es=ja.CMD,ns=2*Math.PI,is=1e-4;var rs=[-1,-1,-1],os=[-1,-1];function as(t,e,n,i,r,o,a,s,l,u){if(e<u&&i<u&&o<u&&s<u||u<e&&u<i&&u<o&&u<s)return 0;var h=Fn(e,i,o,s,u,rs);if(0===h)return 0;for(var c,p=0,d=-1,f=void 0,g=void 0,y=0;y<h;y++){var m=rs[y],v=0===m||1===m?.5:1;zn(t,n,r,a,m)<l||(d<0&&(d=Vn(e,i,o,s,os),os[1]<os[0]&&1<d&&(c=void 0,c=os[0],os[0]=os[1],os[1]=c),f=zn(e,i,o,s,os[0]),1<d)&&(g=zn(e,i,o,s,os[1])),2===d?m<os[0]?p+=f<e?v:-v:m<os[1]?p+=g<f?v:-v:p+=s<g?v:-v:m<os[0]?p+=f<e?v:-v:p+=s<f?v:-v)}return p}function ss(t,e,n,i,r,o,a,s){if(e<s&&i<s&&o<s||s<e&&s<i&&s<o)return 0;c=rs,h=(l=e)-2*(u=i)+(h=o),u=2*(u-l),l-=s=s,s=0,Nn(h)?En(u)&&0<=(p=-l/u)&&p<=1&&(c[s++]=p):Nn(l=u*u-4*h*l)?0<=(p=-u/(2*h))&&p<=1&&(c[s++]=p):0<l&&(d=(-u-(l=kn(l)))/(2*h),0<=(p=(-u+l)/(2*h))&&p<=1&&(c[s++]=p),0<=d)&&d<=1&&(c[s++]=d);var l,u,h,c,p,d,f=s;if(0===f)return 0;var g=Xn(e,i,o);if(0<=g&&g<=1){for(var y=0,m=Wn(e,i,o,g),v=0;v<f;v++){var _=0===rs[v]||1===rs[v]?.5:1;Wn(t,n,r,rs[v])<a||(rs[v]<g?y+=m<e?_:-_:y+=o<m?_:-_)}return y}return _=0===rs[0]||1===rs[0]?.5:1,Wn(t,n,r,rs[0])<a?0:o<e?_:-_}function ls(t,e,n,i,r){for(var o,a=t.data,s=t.len(),l=0,u=0,h=0,c=0,p=0,d=0;d<s;){var f=a[d++],g=1===d;switch(f===es.M&&1<d&&(n||(l+=ts(u,h,c,p,i,r))),g&&(c=u=a[d],p=h=a[d+1]),f){case es.M:u=c=a[d++],h=p=a[d++];break;case es.L:if(n){if(Ka(u,h,a[d],a[d+1],e,i,r))return!0}else l+=ts(u,h,a[d],a[d+1],i,r)||0;u=a[d++],h=a[d++];break;case es.C:if(n){if(function(t,e,n,i,r,o,a,s,l,u,h){if(0!==l)return!(e+(l=l)<h&&i+l<h&&o+l<h&&s+l<h||h<e-l&&h<i-l&&h<o-l&&h<s-l||t+l<u&&n+l<u&&r+l<u&&a+l<u||u<t-l&&u<n-l&&u<r-l&&u<a-l)&&Gn(t,e,n,i,r,o,a,s,u,h,null)<=l/2}(u,h,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],e,i,r))return!0}else l+=as(u,h,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],i,r)||0;u=a[d++],h=a[d++];break;case es.Q:if(n){if(function(t,e,n,i,r,o,a,s,l){if(0!==a)return!(e+(a=a)<l&&i+a<l&&o+a<l||l<e-a&&l<i-a&&l<o-a||t+a<s&&n+a<s&&r+a<s||s<t-a&&s<n-a&&s<r-a)&&qn(t,e,n,i,r,o,s,l,null)<=a/2}(u,h,a[d++],a[d++],a[d],a[d+1],e,i,r))return!0}else l+=ss(u,h,a[d++],a[d++],a[d],a[d+1],i,r)||0;u=a[d++],h=a[d++];break;case es.A:var y=a[d++],m=a[d++],v=a[d++],_=a[d++],x=a[d++],w=a[d++],b=(d+=1,!!(1-a[d++])),S=Math.cos(x)*v+y,M=Math.sin(x)*_+m,T=(g?(c=S,p=M):l+=ts(u,h,S,M,i,r),(i-y)*_/v+y);if(n){if(function(t,e,n,i,r,o,a,s,l){if(0!==a)return a=a,s-=t,l-=e,!(n<(t=Math.sqrt(s*s+l*l))-a||t+a<n)&&(Math.abs(i-r)%Ja<1e-4||((r=o?(e=i,i=Qa(r),Qa(e)):(i=Qa(i),Qa(r)))<i&&(r+=Ja),(t=Math.atan2(l,s))<0&&(t+=Ja),i<=t&&t<=r)||i<=t+Ja&&t+Ja<=r)}(y,m,_,x,x+w,b,e,T,r))return!0}else l+=function(t,e,n,i,r,o,a,s){if(n<(s-=e)||s<-n)return 0;var e=Math.sqrt(n*n-s*s);if(rs[0]=-e,rs[1]=e,(n=Math.abs(i-r))<1e-4)return 0;if(ns-1e-4<=n)return r=ns,h=o?1:-1,a>=rs[i=0]+t&&a<=rs[1]+t?h:0;r<i&&(e=i,i=r,r=e),i<0&&(i+=ns,r+=ns);for(var l=0,u=0;u<2;u++){var h,c=rs[u];a<c+t&&(h=o?1:-1,i<=(c=(c=Math.atan2(s,c))<0?ns+c:c)&&c<=r||i<=c+ns&&c+ns<=r)&&(l+=h=c>Math.PI/2&&c<1.5*Math.PI?-h:h)}return l}(y,m,_,x,x+w,b,T,r);u=Math.cos(x+w)*v+y,h=Math.sin(x+w)*_+m;break;case es.R:c=u=a[d++],p=h=a[d++];if(S=c+a[d++],M=p+a[d++],n){if(Ka(c,p,S,p,e,i,r)||Ka(S,p,S,M,e,i,r)||Ka(S,M,c,M,e,i,r)||Ka(c,M,c,p,e,i,r))return!0}else l=(l+=ts(S,p,S,M,i,r))+ts(c,M,c,p,i,r);break;case es.Z:if(n){if(Ka(u,h,c,p,e,i,r))return!0}else l+=ts(u,h,c,p,i,r);u=c,h=p}}return n||(t=h,o=p,Math.abs(t-o)<is)||(l+=ts(u,h,c,p,i,r)||0),0!==l}var us,hs=z({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},ya),cs={style:z({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},ma.style)},ps=_r.concat(["invisible","culling","z","z2","zlevel","parent"]),ds=(u(s,us=n),s.prototype.update=function(){var e=this,t=(us.prototype.update.call(this),this.style);if(t.decal){var n,i=this._decalEl=this._decalEl||new s,r=(i.buildPath===s.prototype.buildPath&&(i.buildPath=function(t){e.buildPath(t,e.shape)}),i.silent=!0,i.style);for(n in t)r[n]!==t[n]&&(r[n]=t[n]);r.fill=t.fill?t.decal:null,r.decal=null,r.shadowColor=null,t.strokeFirst&&(r.stroke=null);for(var o=0;o<ps.length;++o)i[ps[o]]=this[ps[o]];i.__dirty|=vn}else this._decalEl&&(this._decalEl=null)},s.prototype.getDecalElement=function(){return this._decalEl},s.prototype._init=function(t){var e=ht(t),n=(this.shape=this.getDefaultShape(),this.getDefaultStyle());n&&this.useStyle(n);for(var i=0;i<e.length;i++){var r=e[i],o=t[r];"style"===r?this.style?L(this.style,o):this.useStyle(o):"shape"===r?L(this.shape,o):us.prototype.attrKV.call(this,r,o)}this.style||this.useStyle({})},s.prototype.getDefaultStyle=function(){return null},s.prototype.getDefaultShape=function(){return{}},s.prototype.canBeInsideText=function(){return this.hasFill()},s.prototype.getInsideTextFill=function(){var t,e=this.style.fill;if("none"!==e){if(V(e))return.5<(t=bi(e,0))?ur:.2<t?"#eee":hr;if(e)return hr}return ur},s.prototype.getInsideTextStroke=function(t){var e=this.style.fill;if(V(e)){var n=this.__zr;if(!(!n||!n.isDarkMode())==bi(t,0)<.4)return e}},s.prototype.buildPath=function(t,e,n){},s.prototype.pathUpdated=function(){this.__dirty&=~_n},s.prototype.getUpdatedPathProxy=function(t){return this.path||this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,t),this.path},s.prototype.createPathProxy=function(){this.path=new ja(!1)},s.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return!(null==e||"none"===e||!(0<t.lineWidth))},s.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},s.prototype.getBoundingRect=function(){var t,e,n=this._rect,i=this.style,r=!n;return r&&(t=!1,this.path||(t=!0,this.createPathProxy()),e=this.path,(t||this.__dirty&_n)&&(e.beginPath(),this.buildPath(e,this.shape,!1),this.pathUpdated()),n=e.getBoundingRect()),this._rect=n,this.hasStroke()&&this.path&&0<this.path.len()?(t=this._rectStroke||(this._rectStroke=n.clone()),(this.__dirty||r)&&(t.copy(n),e=i.strokeNoScale?this.getLineScale():1,r=i.lineWidth,this.hasFill()||(i=this.strokeContainThreshold,r=Math.max(r,null==i?4:i)),1e-10<e)&&(t.width+=r/e,t.height+=r/e,t.x-=r/e/2,t.y-=r/e/2),t):n},s.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),r=this.style;if(t=n[0],e=n[1],i.contain(t,e)){n=this.path;if(this.hasStroke()){i=r.lineWidth,r=r.strokeNoScale?this.getLineScale():1;if(1e-10<r&&(this.hasFill()||(i=Math.max(i,this.strokeContainThreshold)),ls(n,i/r,!0,t,e)))return!0}if(this.hasFill())return ls(n,0,!1,t,e)}return!1},s.prototype.dirtyShape=function(){this.__dirty|=_n,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},s.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},s.prototype.animateShape=function(t){return this.animate("shape",t)},s.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():"shape"===t?this.dirtyShape():this.markRedraw()},s.prototype.attrKV=function(t,e){"shape"===t?this.setShape(e):us.prototype.attrKV.call(this,t,e)},s.prototype.setShape=function(t,e){var n=(n=this.shape)||(this.shape={});return"string"==typeof t?n[t]=e:L(n,t),this.dirtyShape(),this},s.prototype.shapeChanged=function(){return!!(this.__dirty&_n)},s.prototype.createStyle=function(t){return Et(hs,t)},s.prototype._innerSaveToNormal=function(t){us.prototype._innerSaveToNormal.call(this,t);var e=this._normalState;t.shape&&!e.shape&&(e.shape=L({},this.shape))},s.prototype._applyStateObj=function(t,e,n,i,r,o){us.prototype._applyStateObj.call(this,t,e,n,i,r,o);var a,s=!(e&&i);if(e&&e.shape?r?i?a=e.shape:(a=L({},n.shape),L(a,e.shape)):(a=L({},(i?this:n).shape),L(a,e.shape)):s&&(a=n.shape),a)if(r){this.shape=L({},this.shape);for(var l={},u=ht(a),h=0;h<u.length;h++){var c=u[h];"object"==typeof a[c]?this.shape[c]=a[c]:l[c]=a[c]}this._transitionState(t,{shape:l},o)}else this.shape=a,this.dirtyShape()},s.prototype._mergeStates=function(t){for(var e,n=us.prototype._mergeStates.call(this,t),i=0;i<t.length;i++){var r=t[i];r.shape&&this._mergeStyle(e=e||{},r.shape)}return e&&(n.shape=e),n},s.prototype.getAnimationStyleProps=function(){return cs},s.prototype.isZeroArea=function(){return!1},s.extend=function(n){u(r,i=s),r.prototype.getDefaultStyle=function(){return y(n.style)},r.prototype.getDefaultShape=function(){return y(n.shape)};var i,t,e=r;function r(t){var e=i.call(this,t)||this;return n.init&&n.init.call(e,t),e}for(t in n)"function"==typeof n[t]&&(e.prototype[t]=n[t]);return e},s.initDefaultProps=((hu=s.prototype).type="path",hu.strokeContainThreshold=5,hu.segmentIgnoreThreshold=0,hu.subPixelOptimize=!1,hu.autoBatch=!1,void(hu.__dirty=2|vn|_n)),s);function s(t){return us.call(this,t)||this}var fs,gs=z({strokeFirst:!0,font:j,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},hs),ys=(u(ms,fs=n),ms.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return null!=e&&"none"!==e&&0<t.lineWidth},ms.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},ms.prototype.createStyle=function(t){return Et(gs,t)},ms.prototype.setBoundingRect=function(t){this._rect=t},ms.prototype.getBoundingRect=function(){var t,e=this.style;return this._rect||(null!=(t=e.text)?t+="":t="",(t=Sr(t,e.font,e.textAlign,e.textBaseline)).x+=e.x||0,t.y+=e.y||0,this.hasStroke()&&(e=e.lineWidth,t.x-=e/2,t.y-=e/2,t.width+=e,t.height+=e),this._rect=t),this._rect},ms.initDefaultProps=void(ms.prototype.dirtyRectTolerance=10),ms);function ms(){return null!==fs&&fs.apply(this,arguments)||this}ys.prototype.type="tspan";var vs=z({x:0,y:0},ya),_s={style:z({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},ma.style)};u(bs,xs=n),bs.prototype.createStyle=function(t){return Et(vs,t)},bs.prototype._getSize=function(t){var e,n=this.style,i=n[t];return null!=i?i:(i=(i=n.image)&&"string"!=typeof i&&i.width&&i.height?n.image:this.__image)?null==(e=n[n="width"===t?"height":"width"])?i[t]:i[t]/i[n]*e:0},bs.prototype.getWidth=function(){return this._getSize("width")},bs.prototype.getHeight=function(){return this._getSize("height")},bs.prototype.getAnimationStyleProps=function(){return _s},bs.prototype.getBoundingRect=function(){var t=this.style;return this._rect||(this._rect=new X(t.x||0,t.y||0,this.getWidth(),this.getHeight())),this._rect};var xs,ws=bs;function bs(){return null!==xs&&xs.apply(this,arguments)||this}ws.prototype.type="image";var Ss=Math.round;function Ms(t,e,n){var i,r,o;if(e)return i=e.x1,r=e.x2,o=e.y1,e=e.y2,t.x1=i,t.x2=r,t.y1=o,t.y2=e,(n=n&&n.lineWidth)&&(Ss(2*i)===Ss(2*r)&&(t.x1=t.x2=Cs(i,n,!0)),Ss(2*o)===Ss(2*e))&&(t.y1=t.y2=Cs(o,n,!0)),t}function Ts(t,e,n){var i,r,o;if(e)return i=e.x,r=e.y,o=e.width,e=e.height,t.x=i,t.y=r,t.width=o,t.height=e,(n=n&&n.lineWidth)&&(t.x=Cs(i,n,!0),t.y=Cs(r,n,!0),t.width=Math.max(Cs(i+o,n,!1)-t.x,0===o?0:1),t.height=Math.max(Cs(r+e,n,!1)-t.y,0===e?0:1)),t}function Cs(t,e,n){var i;return e?((i=Ss(2*t))+Ss(e))%2==0?i/2:(i+(n?1:-1))/2:t}var Is,ks=function(){this.x=0,this.y=0,this.width=0,this.height=0},Ds={},As=(u(Ps,Is=ds),Ps.prototype.getDefaultShape=function(){return new ks},Ps.prototype.buildPath=function(t,e){var n,i,r,o,a,s,l,u,h,c,p,d,f,g;this.subPixelOptimize?(n=(a=Ts(Ds,e,this.style)).x,i=a.y,r=a.width,o=a.height,a.r=e.r,e=a):(n=e.x,i=e.y,r=e.width,o=e.height),e.r?(a=t,p=(e=e).x,d=e.y,f=e.width,g=e.height,e=e.r,f<0&&(p+=f,f=-f),g<0&&(d+=g,g=-g),"number"==typeof e?s=l=u=h=e:e instanceof Array?1===e.length?s=l=u=h=e[0]:2===e.length?(s=u=e[0],l=h=e[1]):3===e.length?(s=e[0],l=h=e[1],u=e[2]):(s=e[0],l=e[1],u=e[2],h=e[3]):s=l=u=h=0,f<s+l&&(s*=f/(c=s+l),l*=f/c),f<u+h&&(u*=f/(c=u+h),h*=f/c),g<l+u&&(l*=g/(c=l+u),u*=g/c),g<s+h&&(s*=g/(c=s+h),h*=g/c),a.moveTo(p+s,d),a.lineTo(p+f-l,d),0!==l&&a.arc(p+f-l,d+l,l,-Math.PI/2,0),a.lineTo(p+f,d+g-u),0!==u&&a.arc(p+f-u,d+g-u,u,0,Math.PI/2),a.lineTo(p+h,d+g),0!==h&&a.arc(p+h,d+g-h,h,Math.PI/2,Math.PI),a.lineTo(p,d+s),0!==s&&a.arc(p+s,d+s,s,Math.PI,1.5*Math.PI)):t.rect(n,i,r,o)},Ps.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},Ps);function Ps(t){return Is.call(this,t)||this}As.prototype.type="rect";var Ls,Os={fill:"#000"},Rs={style:z({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},ma.style)},Ns=(u(Es,Ls=n),Es.prototype.childrenRef=function(){return this._children},Es.prototype.update=function(){Ls.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var t=0;t<this._children.length;t++){var e=this._children[t];e.zlevel=this.zlevel,e.z=this.z,e.z2=this.z2,e.culling=this.culling,e.cursor=this.cursor,e.invisible=this.invisible}},Es.prototype.updateTransform=function(){var t=this.innerTransformable;t?(t.updateTransform(),t.transform&&(this.transform=t.transform)):Ls.prototype.updateTransform.call(this)},Es.prototype.getLocalTransform=function(t){var e=this.innerTransformable;return e?e.getLocalTransform(t):Ls.prototype.getLocalTransform.call(this,t)},Es.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),Ls.prototype.getComputedTransform.call(this)},Es.prototype._updateSubTexts=function(){var t;this._childCursor=0,Hs(t=this.style),O(t.rich,Hs),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},Es.prototype.addSelfToZr=function(t){Ls.prototype.addSelfToZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].__zr=t},Es.prototype.removeSelfFromZr=function(t){Ls.prototype.removeSelfFromZr.call(this,t);for(var e=0;e<this._children.length;e++)this._children[e].__zr=null},Es.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var t=new X(0,0,0,0),e=this._children,n=[],i=null,r=0;r<e.length;r++){var o=e[r],a=o.getBoundingRect(),o=o.getLocalTransform(n);o?(t.copy(a),t.applyTransform(o),(i=i||t.clone()).union(t)):(i=i||a.clone()).union(a)}this._rect=i||t}return this._rect},Es.prototype.setDefaultTextStyle=function(t){this._defaultStyle=t||Os},Es.prototype.setTextContent=function(t){},Es.prototype._mergeStyle=function(t,e){var n,i;return e&&(n=e.rich,i=t.rich||n&&{},L(t,e),n&&i?(this._mergeRich(i,n),t.rich=i):i&&(t.rich=i)),t},Es.prototype._mergeRich=function(t,e){for(var n=ht(e),i=0;i<n.length;i++){var r=n[i];t[r]=t[r]||{},L(t[r],e[r])}},Es.prototype.getAnimationStyleProps=function(){return Rs},Es.prototype._getOrCreateChild=function(t){var e=this._children[this._childCursor];return e&&e instanceof t||(e=new t),(this._children[this._childCursor++]=e).__zr=this.__zr,e.parent=this,e},Es.prototype._updatePlainTexts=function(){for(var t,e=this.style,n=e.font||j,i=e.padding,r=function(t,e){null!=t&&(t+="");var n,i=e.overflow,r=e.padding,o=e.font,a="truncate"===i,s=Cr(o),l=N(e.lineHeight,s),u=!!e.backgroundColor,h="truncate"===e.lineOverflow,c=!1,p=e.width,i=(n=null==p||"break"!==i&&"breakAll"!==i?t?t.split("\n"):[]:t?da(t,e.font,p,"breakAll"===i,0).lines:[]).length*l,d=N(e.height,i);if(d<i&&h&&(h=Math.floor(d/l),c=c||n.length>h,n=n.slice(0,h)),t&&a&&null!=p)for(var f=oa(p,o,e.ellipsis,{minChar:e.truncateMinChar,placeholder:e.placeholder}),g={},y=0;y<n.length;y++)aa(g,n[y],f),n[y]=g.textLine,c=c||g.isTruncated;for(var h=d,m=0,y=0;y<n.length;y++)m=Math.max(wr(n[y],o),m);return null==p&&(p=m),t=m,r&&(h+=r[0]+r[2],t+=r[1]+r[3],p+=r[1]+r[3]),{lines:n,height:d,outerWidth:t=u?p:t,outerHeight:h,lineHeight:l,calculatedLineHeight:s,contentWidth:m,contentHeight:i,width:p,isTruncated:c}}(Xs(e),e),o=Ys(e),a=!!e.backgroundColor,s=r.outerHeight,l=r.outerWidth,u=r.contentWidth,h=r.lines,c=r.lineHeight,p=this._defaultStyle,d=(this.isTruncated=!!r.isTruncated,e.x||0),f=e.y||0,g=e.align||p.align||"left",y=e.verticalAlign||p.verticalAlign||"top",m=d,v=Tr(f,r.contentHeight,y),_=((o||i)&&(t=Mr(d,l,g),f=Tr(f,s,y),o)&&this._renderBackground(e,e,t,f,l,s),v+=c/2,i&&(m=Us(d,g,i),"top"===y?v+=i[0]:"bottom"===y&&(v-=i[2])),0),o=!1,x=(Ws(("fill"in e?e:(o=!0,p)).fill)),w=(Gs("stroke"in e?e.stroke:a||p.autoStroke&&!o?null:(_=2,p.stroke))),b=0<e.textShadowBlur,S=null!=e.width&&("truncate"===e.overflow||"break"===e.overflow||"breakAll"===e.overflow),M=r.calculatedLineHeight,T=0;T<h.length;T++){var C=this._getOrCreateChild(ys),I=C.createStyle();C.useStyle(I),I.text=h[T],I.x=m,I.y=v,g&&(I.textAlign=g),I.textBaseline="middle",I.opacity=e.opacity,I.strokeFirst=!0,b&&(I.shadowBlur=e.textShadowBlur||0,I.shadowColor=e.textShadowColor||"transparent",I.shadowOffsetX=e.textShadowOffsetX||0,I.shadowOffsetY=e.textShadowOffsetY||0),I.stroke=w,I.fill=x,w&&(I.lineWidth=e.lineWidth||_,I.lineDash=e.lineDash,I.lineDashOffset=e.lineDashOffset||0),I.font=n,Vs(I,e),v+=c,S&&C.setBoundingRect(new X(Mr(I.x,u,I.textAlign),Tr(I.y,M,I.textBaseline),u,M))}},Es.prototype._updateRichTexts=function(){for(var t=this.style,e=ha(Xs(t),t),n=e.width,i=e.outerWidth,r=e.outerHeight,o=t.padding,a=t.x||0,s=t.y||0,l=this._defaultStyle,u=t.align||l.align,l=t.verticalAlign||l.verticalAlign,a=(this.isTruncated=!!e.isTruncated,Mr(a,i,u)),u=Tr(s,r,l),h=a,c=u,p=(o&&(h+=o[3],c+=o[0]),h+n),d=(Ys(t)&&this._renderBackground(t,t,a,u,i,r),!!t.backgroundColor),f=0;f<e.lines.length;f++){for(var g=e.lines[f],y=g.tokens,m=y.length,v=g.lineHeight,_=g.width,x=0,w=h,b=p,S=m-1,M=void 0;x<m&&(!(M=y[x]).align||"left"===M.align);)this._placeToken(M,t,v,c,w,"left",d),_-=M.width,w+=M.width,x++;for(;0<=S&&"right"===(M=y[S]).align;)this._placeToken(M,t,v,c,b,"right",d),_-=M.width,b-=M.width,S--;for(w+=(n-(w-h)-(p-b)-_)/2;x<=S;)M=y[x],this._placeToken(M,t,v,c,w+M.width/2,"center",d),w+=M.width,x++;c+=v}},Es.prototype._placeToken=function(t,e,n,i,r,o,a){var s=e.rich[t.styleName]||{},l=(s.text=t.text,t.verticalAlign),u=i+n/2;"top"===l?u=i+t.height/2:"bottom"===l&&(u=i+n-t.height/2);!t.isLineHolder&&Ys(s)&&this._renderBackground(s,e,"right"===o?r-t.width:"center"===o?r-t.width/2:r,u-t.height/2,t.width,t.height);var l=!!s.backgroundColor,i=t.textPadding,n=(i&&(r=Us(r,o,i),u-=t.height/2-i[0]-t.innerHeight/2),this._getOrCreateChild(ys)),i=n.createStyle(),h=(n.useStyle(i),this._defaultStyle),c=!1,p=0,d=Ws(("fill"in s?s:"fill"in e?e:(c=!0,h)).fill),l=Gs("stroke"in s?s.stroke:"stroke"in e?e.stroke:l||a||h.autoStroke&&!c?null:(p=2,h.stroke)),a=0<s.textShadowBlur||0<e.textShadowBlur,c=(i.text=t.text,i.x=r,i.y=u,a&&(i.shadowBlur=s.textShadowBlur||e.textShadowBlur||0,i.shadowColor=s.textShadowColor||e.textShadowColor||"transparent",i.shadowOffsetX=s.textShadowOffsetX||e.textShadowOffsetX||0,i.shadowOffsetY=s.textShadowOffsetY||e.textShadowOffsetY||0),i.textAlign=o,i.textBaseline="middle",i.font=t.font||j,i.opacity=bt(s.opacity,e.opacity,1),Vs(i,s),l&&(i.lineWidth=bt(s.lineWidth,e.lineWidth,p),i.lineDash=N(s.lineDash,e.lineDash),i.lineDashOffset=e.lineDashOffset||0,i.stroke=l),d&&(i.fill=d),t.contentWidth),h=t.contentHeight;n.setBoundingRect(new X(Mr(i.x,c,i.textAlign),Tr(i.y,h,i.textBaseline),c,h))},Es.prototype._renderBackground=function(t,e,n,i,r,o){var a,s,l,u,h=t.backgroundColor,c=t.borderWidth,p=t.borderColor,d=h&&h.image,f=h&&!d,g=t.borderRadius,y=this,g=((f||t.lineHeight||c&&p)&&((a=this._getOrCreateChild(As)).useStyle(a.createStyle()),a.style.fill=null,(l=a.shape).x=n,l.y=i,l.width=r,l.height=o,l.r=g,a.dirtyShape()),f?((u=a.style).fill=h||null,u.fillOpacity=N(t.fillOpacity,1)):d&&((s=this._getOrCreateChild(ws)).onload=function(){y.dirtyStyle()},(l=s.style).image=h.image,l.x=n,l.y=i,l.width=r,l.height=o),c&&p&&((u=a.style).lineWidth=c,u.stroke=p,u.strokeOpacity=N(t.strokeOpacity,1),u.lineDash=t.borderDash,u.lineDashOffset=t.borderDashOffset||0,a.strokeContainThreshold=0,a.hasFill())&&a.hasStroke()&&(u.strokeFirst=!0,u.lineWidth*=2),(a||s).style);g.shadowBlur=t.shadowBlur||0,g.shadowColor=t.shadowColor||"transparent",g.shadowOffsetX=t.shadowOffsetX||0,g.shadowOffsetY=t.shadowOffsetY||0,g.opacity=bt(t.opacity,e.opacity,1)},Es.makeFont=function(t){var e,n="";return(n=null!=(e=t).fontSize||e.fontFamily||e.fontWeight?[t.fontStyle,t.fontWeight,"string"!=typeof(e=t.fontSize)||-1===e.indexOf("px")&&-1===e.indexOf("rem")&&-1===e.indexOf("em")?isNaN(+e)?"12px":e+"px":e,t.fontFamily||"sans-serif"].join(" "):n)&&Ct(n)||t.textFont||t.font},Es);function Es(t){var e=Ls.call(this)||this;return e.type="text",e._children=[],e._defaultStyle=Os,e.attr(t),e}var zs={left:!0,right:1,center:1},Bs={top:1,bottom:1,middle:1},Fs=["fontStyle","fontWeight","fontSize","fontFamily"];function Vs(t,e){for(var n=0;n<Fs.length;n++){var i=Fs[n],r=e[i];null!=r&&(t[i]=r)}}function Hs(t){var e;t&&(t.font=Ns.makeFont(t),e=t.align,t.align=null==(e="middle"===e?"center":e)||zs[e]?e:"left",e=t.verticalAlign,t.verticalAlign=null==(e="center"===e?"middle":e)||Bs[e]?e:"top",t.padding)&&(t.padding=Mt(t.padding))}function Gs(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function Ws(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function Us(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}function Xs(t){t=t.text;return null!=t&&(t+=""),t}function Ys(t){return!!(t.backgroundColor||t.lineHeight||t.borderWidth&&t.borderColor)}var D=Po(),qs=1,Zs={},js=Po(),Ks=Po(),$s=0,Qs=1,Js=2,tl=["emphasis","blur","select"],el=["normal","emphasis","blur","select"],nl="highlight",il="downplay",rl="select",ol="unselect",al="toggleSelect";function sl(t){return null!=t&&"none"!==t}function ll(t,e,n){t.onHoverStateChange&&(t.hoverState||0)!==n&&t.onHoverStateChange(e),t.hoverState=n}function ul(t){ll(t,"emphasis",Js)}function hl(t){t.hoverState===Js&&ll(t,"normal",$s)}function cl(t){ll(t,"blur",Qs)}function pl(t){t.hoverState===Qs&&ll(t,"normal",$s)}function dl(t){t.selected=!0}function fl(t){t.selected=!1}function gl(t,e,n){e(t,n)}function yl(t,e,n){gl(t,e,n),t.isGroup&&t.traverse(function(t){gl(t,e,n)})}function ml(t,e){switch(e){case"emphasis":t.hoverState=Js;break;case"normal":t.hoverState=$s;break;case"blur":t.hoverState=Qs;break;case"select":t.selected=!0}}function vl(t,e,n){var i=0<=I(t.currentStates,e),r=t.style.opacity,t=i?null:function(t,e,n,i){for(var r=t.style,o={},a=0;a<e.length;a++){var s=e[a],l=r[s];o[s]=null==l?i&&i[s]:l}for(a=0;a<t.animators.length;a++){var u=t.animators[a];u.__fromStateTransition&&u.__fromStateTransition.indexOf(n)<0&&"style"===u.targetName&&u.saveTo(o,e)}return o}(t,["opacity"],e,{opacity:1}),e=(n=n||{}).style||{};return null==e.opacity&&(n=L({},n),e=L({opacity:i?r:.1*t.opacity},e),n.style=e),n}function _l(t,e){var n,i,r,o,a,s=this.states[t];if(this.style){if("emphasis"===t)return n=this,i=s,e=(e=e)&&0<=I(e,"select"),a=!1,n instanceof ds&&(r=js(n),o=e&&r.selectFill||r.normalFill,e=e&&r.selectStroke||r.normalStroke,sl(o)||sl(e))&&("inherit"===(r=(i=i||{}).style||{}).fill?(a=!0,i=L({},i),(r=L({},r)).fill=o):!sl(r.fill)&&sl(o)?(a=!0,i=L({},i),(r=L({},r)).fill=Mi(o)):!sl(r.stroke)&&sl(e)&&(a||(i=L({},i),r=L({},r)),r.stroke=Mi(e)),i.style=r),i&&null==i.z2&&(a||(i=L({},i)),o=n.z2EmphasisLift,i.z2=n.z2+(null!=o?o:10)),i;if("blur"===t)return vl(this,t,s);if("select"===t)return e=this,(r=s)&&null==r.z2&&(r=L({},r),a=e.z2SelectLift,r.z2=e.z2+(null!=a?a:9)),r}return s}function xl(t){t.stateProxy=_l;var e=t.getTextContent(),t=t.getTextGuideLine();e&&(e.stateProxy=_l),t&&(t.stateProxy=_l)}function wl(t,e){kl(t,e)||t.__highByOuter||yl(t,ul)}function bl(t,e){kl(t,e)||t.__highByOuter||yl(t,hl)}function Sl(t,e){t.__highByOuter|=1<<(e||0),yl(t,ul)}function Ml(t,e){(t.__highByOuter&=~(1<<(e||0)))||yl(t,hl)}function Tl(t){yl(t,pl)}function Cl(t){yl(t,dl)}function Il(t){yl(t,fl)}function kl(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function Dl(r){var e=r.getModel(),o=[],a=[];e.eachComponent(function(t,e){var n=Ks(e),t="series"===t,i=t?r.getViewOfSeriesModel(e):r.getViewOfComponentModel(e);t||a.push(i),n.isBlured&&(i.group.traverse(function(t){pl(t)}),t)&&o.push(e),n.isBlured=!1}),O(a,function(t){t&&t.toggleBlurSeries&&t.toggleBlurSeries(o,!1,e)})}function Al(t,o,a,s){var l,u,h,n=s.getModel();function c(t,e){for(var n=0;n<e.length;n++){var i=t.getItemGraphicEl(e[n]);i&&Tl(i)}}a=a||"coordinateSystem",null!=t&&o&&"none"!==o&&(l=n.getSeriesByIndex(t),(u=l.coordinateSystem)&&u.master&&(u=u.master),h=[],n.eachSeries(function(t){var e=l===t,n=t.coordinateSystem,n=(n=n&&n.master?n.master:n)&&u?n===u:e;if(!("series"===a&&!e||"coordinateSystem"===a&&!n||"series"===o&&e)){if(s.getViewOfSeriesModel(t).group.traverse(function(t){t.__highByOuter&&e&&"self"===o||cl(t)}),st(o))c(t.getData(),o);else if(R(o))for(var i=ht(o),r=0;r<i.length;r++)c(t.getData(i[r]),o[i[r]]);h.push(t),Ks(t).isBlured=!0}}),n.eachComponent(function(t,e){"series"!==t&&(t=s.getViewOfComponentModel(e))&&t.toggleBlurSeries&&t.toggleBlurSeries(h,!0,n)}))}function Pl(t,e,n){var i;null!=t&&null!=e&&(t=n.getModel().getComponent(t,e))&&(Ks(t).isBlured=!0,i=n.getViewOfComponentModel(t))&&i.focusBlurEnabled&&i.group.traverse(function(t){cl(t)})}function Ll(t,e,n,i){var r={focusSelf:!1,dispatchers:null};if(null==t||"series"===t||null==e||null==n)return r;t=i.getModel().getComponent(t,e);if(!t)return r;e=i.getViewOfComponentModel(t);if(!e||!e.findHighDownDispatchers)return r;for(var o,a=e.findHighDownDispatchers(n),s=0;s<a.length;s++)if("self"===D(a[s]).focus){o=!0;break}return{focusSelf:o,dispatchers:a}}function Ol(i){O(i.getAllData(),function(t){var e=t.data,n=t.type;e.eachItemGraphicEl(function(t,e){(i.isSelected(e,n)?Cl:Il)(t)})})}function Rl(t,e,n){Fl(t,!0),yl(t,xl);t=D(t),null!=e?(t.focus=e,t.blurScope=n):t.focus&&(t.focus=null)}function Nl(t,e,n,i){i?Fl(t,!1):Rl(t,e,n)}var El=["emphasis","blur","select"],zl={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function Bl(t,e,n,i){n=n||"itemStyle";for(var r=0;r<El.length;r++){var o=El[r],a=e.getModel([o,n]);t.ensureState(o).style=i?i(a):a[zl[n]]()}}function Fl(t,e){var e=!1===e,n=t;t.highDownSilentOnTouch&&(n.__highDownSilentOnTouch=t.highDownSilentOnTouch),e&&!n.__highDownDispatcher||(n.__highByOuter=n.__highByOuter||0,n.__highDownDispatcher=!e)}function Vl(t){return!(!t||!t.__highDownDispatcher)}function Hl(t){t=t.type;return t===rl||t===ol||t===al}function Gl(t){t=t.type;return t===nl||t===il}var Wl=ja.CMD,Ul=[[],[],[]],Xl=Math.sqrt,Yl=Math.atan2;var ql=Math.sqrt,Zl=Math.sin,jl=Math.cos,Kl=Math.PI;function $l(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function Ql(t,e){return(t[0]*e[0]+t[1]*e[1])/($l(t)*$l(e))}function Jl(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(Ql(t,e))}function tu(t,e,n,i,r,o,a,s,l,u,h){var l=l*(Kl/180),c=jl(l)*(t-n)/2+Zl(l)*(e-i)/2,p=-1*Zl(l)*(t-n)/2+jl(l)*(e-i)/2,d=c*c/(a*a)+p*p/(s*s),d=(1<d&&(a*=ql(d),s*=ql(d)),(r===o?-1:1)*ql((a*a*(s*s)-a*a*(p*p)-s*s*(c*c))/(a*a*(p*p)+s*s*(c*c)))||0),r=d*a*p/s,d=d*-s*c/a,t=(t+n)/2+jl(l)*r-Zl(l)*d,n=(e+i)/2+Zl(l)*r+jl(l)*d,e=Jl([1,0],[(c-r)/a,(p-d)/s]),i=[(c-r)/a,(p-d)/s],c=[(-1*c-r)/a,(-1*p-d)/s],r=Jl(i,c);Ql(i,c)<=-1&&(r=Kl),(r=1<=Ql(i,c)?0:r)<0&&(p=Math.round(r/Kl*1e6)/1e6,r=2*Kl+p%2*Kl),h.addData(u,t,n,a,s,e,r,l,o)}var eu=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,nu=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;u(ou,iu=ds),ou.prototype.applyTransform=function(t){};var iu,ru=ou;function ou(){return null!==iu&&iu.apply(this,arguments)||this}function au(t){return null!=t.setData}function su(t,e){var S=function(t){var e=new ja;if(t){var n,i=0,r=0,o=i,a=r,s=ja.CMD,l=t.match(eu);if(l){for(var u=0;u<l.length;u++){for(var h=l[u],c=h.charAt(0),p=void 0,d=h.match(nu)||[],f=d.length,g=0;g<f;g++)d[g]=parseFloat(d[g]);for(var y=0;y<f;){var m=void 0,v=void 0,_=void 0,x=void 0,w=void 0,b=void 0,S=void 0,M=i,T=r,C=void 0,I=void 0;switch(c){case"l":i+=d[y++],r+=d[y++],p=s.L,e.addData(p,i,r);break;case"L":i=d[y++],r=d[y++],p=s.L,e.addData(p,i,r);break;case"m":i+=d[y++],r+=d[y++],p=s.M,e.addData(p,i,r),o=i,a=r,c="l";break;case"M":i=d[y++],r=d[y++],p=s.M,e.addData(p,i,r),o=i,a=r,c="L";break;case"h":i+=d[y++],p=s.L,e.addData(p,i,r);break;case"H":i=d[y++],p=s.L,e.addData(p,i,r);break;case"v":r+=d[y++],p=s.L,e.addData(p,i,r);break;case"V":r=d[y++],p=s.L,e.addData(p,i,r);break;case"C":p=s.C,e.addData(p,d[y++],d[y++],d[y++],d[y++],d[y++],d[y++]),i=d[y-2],r=d[y-1];break;case"c":p=s.C,e.addData(p,d[y++]+i,d[y++]+r,d[y++]+i,d[y++]+r,d[y++]+i,d[y++]+r),i+=d[y-2],r+=d[y-1];break;case"S":m=i,v=r,C=e.len(),I=e.data,n===s.C&&(m+=i-I[C-4],v+=r-I[C-3]),p=s.C,M=d[y++],T=d[y++],i=d[y++],r=d[y++],e.addData(p,m,v,M,T,i,r);break;case"s":m=i,v=r,C=e.len(),I=e.data,n===s.C&&(m+=i-I[C-4],v+=r-I[C-3]),p=s.C,M=i+d[y++],T=r+d[y++],i+=d[y++],r+=d[y++],e.addData(p,m,v,M,T,i,r);break;case"Q":M=d[y++],T=d[y++],i=d[y++],r=d[y++],p=s.Q,e.addData(p,M,T,i,r);break;case"q":M=d[y++]+i,T=d[y++]+r,i+=d[y++],r+=d[y++],p=s.Q,e.addData(p,M,T,i,r);break;case"T":m=i,v=r,C=e.len(),I=e.data,n===s.Q&&(m+=i-I[C-4],v+=r-I[C-3]),i=d[y++],r=d[y++],p=s.Q,e.addData(p,m,v,i,r);break;case"t":m=i,v=r,C=e.len(),I=e.data,n===s.Q&&(m+=i-I[C-4],v+=r-I[C-3]),i+=d[y++],r+=d[y++],p=s.Q,e.addData(p,m,v,i,r);break;case"A":_=d[y++],x=d[y++],w=d[y++],b=d[y++],S=d[y++],tu(M=i,T=r,i=d[y++],r=d[y++],b,S,_,x,w,p=s.A,e);break;case"a":_=d[y++],x=d[y++],w=d[y++],b=d[y++],S=d[y++],tu(M=i,T=r,i+=d[y++],r+=d[y++],b,S,_,x,w,p=s.A,e)}}"z"!==c&&"Z"!==c||(p=s.Z,e.addData(p),i=o,r=a),n=p}e.toStatic()}}return e}(t),t=L({},e);return t.buildPath=function(t){var e;au(t)?(t.setData(S.data),(e=t.getContext())&&t.rebuildPath(e,1)):S.rebuildPath(e=t,1)},t.applyTransform=function(t){var e=S,n=t;if(n){for(var i,r,o,a,s=e.data,l=e.len(),u=Wl.M,h=Wl.C,c=Wl.L,p=Wl.R,d=Wl.A,f=Wl.Q,g=0,y=0;g<l;){switch(i=s[g++],y=g,r=0,i){case u:case c:r=1;break;case h:r=3;break;case f:r=2;break;case d:var m=n[4],v=n[5],_=Xl(n[0]*n[0]+n[1]*n[1]),x=Xl(n[2]*n[2]+n[3]*n[3]),w=Yl(-n[1]/x,n[0]/_);s[g]*=_,s[g++]+=m,s[g]*=x,s[g++]+=v,s[g++]*=_,s[g++]*=x,s[g++]+=w,s[g++]+=w,y=g+=2;break;case p:a[0]=s[g++],a[1]=s[g++],ee(a,a,n),s[y++]=a[0],s[y++]=a[1],a[0]+=s[g++],a[1]+=s[g++],ee(a,a,n),s[y++]=a[0],s[y++]=a[1]}for(o=0;o<r;o++){var b=Ul[o];b[0]=s[g++],b[1]=s[g++],ee(b,b,n),s[y++]=b[0],s[y++]=b[1]}}e.increaseVersion()}this.dirtyShape()},t}var lu,uu=function(){this.cx=0,this.cy=0,this.r=0},hu=(u(cu,lu=ds),cu.prototype.getDefaultShape=function(){return new uu},cu.prototype.buildPath=function(t,e){t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI)},cu);function cu(t){return lu.call(this,t)||this}hu.prototype.type="circle";var pu,du=function(){this.cx=0,this.cy=0,this.rx=0,this.ry=0},fu=(u(gu,pu=ds),gu.prototype.getDefaultShape=function(){return new du},gu.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=e.rx,e=e.ry,o=.5522848*r,a=.5522848*e;t.moveTo(n-r,i),t.bezierCurveTo(n-r,i-a,n-o,i-e,n,i-e),t.bezierCurveTo(n+o,i-e,n+r,i-a,n+r,i),t.bezierCurveTo(n+r,i+a,n+o,i+e,n,i+e),t.bezierCurveTo(n-o,i+e,n-r,i+a,n-r,i),t.closePath()},gu);function gu(t){return pu.call(this,t)||this}fu.prototype.type="ellipse";var yu=Math.PI,mu=2*yu,vu=Math.sin,_u=Math.cos,xu=Math.acos,wu=Math.atan2,bu=Math.abs,Su=Math.sqrt,Mu=Math.max,Tu=Math.min,Cu=1e-4;function Iu(t,e,n,i,r,o,a){var s=t-n,l=e-i,a=(a?o:-o)/Su(s*s+l*l),l=a*l,a=-a*s,s=t+l,t=e+a,e=n+l,n=i+a,i=(s+e)/2,u=(t+n)/2,h=e-s,c=n-t,p=h*h+c*c,o=r-o,s=s*n-e*t,n=(c<0?-1:1)*Su(Mu(0,o*o*p-s*s)),e=(s*c-h*n)/p,t=(-s*h-c*n)/p,d=(s*c+h*n)/p,s=(-s*h+c*n)/p,h=e-i,c=t-u,n=d-i,p=s-u;return n*n+p*p<h*h+c*c&&(e=d,t=s),{cx:e,cy:t,x0:-l,y0:-a,x1:e*(r/o-1),y1:t*(r/o-1)}}function ku(t,e){var n,i,r,o,a,s,l,u,h,c,p,d,f,g,y,m,v,_,x,w,b,S,M,T,C,I,k,D,A,P,L=Mu(e.r,0),O=Mu(e.r0||0,0),R=0<L;(R||0<O)&&(R||(L=O,O=0),L<O&&(R=L,L=O,O=R),R=e.startAngle,n=e.endAngle,isNaN(R)||isNaN(n)||(i=e.cx,r=e.cy,o=!!e.clockwise,m=bu(n-R),Cu<(a=mu<m&&m%mu)&&(m=a),Cu<L?mu-Cu<m?(t.moveTo(i+L*_u(R),r+L*vu(R)),t.arc(i,r,L,R,n,!o),Cu<O&&(t.moveTo(i+O*_u(n),r+O*vu(n)),t.arc(i,r,O,n,R,o))):(S=b=w=x=_=v=c=h=I=C=T=M=u=l=s=a=void 0,p=L*_u(R),d=L*vu(R),f=O*_u(n),g=O*vu(n),(y=Cu<m)&&((e=e.cornerRadius)&&(a=(e=function(t){if(F(t)){var e=t.length;if(!e)return t;e=1===e?[t[0],t[0],0,0]:2===e?[t[0],t[0],t[1],t[1]]:3===e?t.concat(t[2]):t}else e=[t,t,t,t];return e}(e))[0],s=e[1],l=e[2],u=e[3]),e=bu(L-O)/2,M=Tu(e,l),T=Tu(e,u),C=Tu(e,a),I=Tu(e,s),v=h=Mu(M,T),_=c=Mu(C,I),Cu<h||Cu<c)&&(x=L*_u(n),w=L*vu(n),b=O*_u(R),S=O*vu(R),m<yu)&&(e=function(t,e,n,i,r,o,a,s){var l=(s=s-o)*(n=n-t)-(a=a-r)*(i=i-e);if(!(l*l<Cu))return[t+(l=(a*(e-o)-s*(t-r))/l)*n,e+l*i]}(p,d,b,S,x,w,f,g))&&(M=p-e[0],T=d-e[1],C=x-e[0],I=w-e[1],m=1/vu(xu((M*C+T*I)/(Su(M*M+T*T)*Su(C*C+I*I)))/2),M=Su(e[0]*e[0]+e[1]*e[1]),v=Tu(h,(L-M)/(1+m)),_=Tu(c,(O-M)/(m-1))),y?Cu<v?(k=Tu(l,v),D=Tu(u,v),A=Iu(b,S,p,d,L,k,o),P=Iu(x,w,f,g,L,D,o),t.moveTo(i+A.cx+A.x0,r+A.cy+A.y0),v<h&&k===D?t.arc(i+A.cx,r+A.cy,v,wu(A.y0,A.x0),wu(P.y0,P.x0),!o):(0<k&&t.arc(i+A.cx,r+A.cy,k,wu(A.y0,A.x0),wu(A.y1,A.x1),!o),t.arc(i,r,L,wu(A.cy+A.y1,A.cx+A.x1),wu(P.cy+P.y1,P.cx+P.x1),!o),0<D&&t.arc(i+P.cx,r+P.cy,D,wu(P.y1,P.x1),wu(P.y0,P.x0),!o))):(t.moveTo(i+p,r+d),t.arc(i,r,L,R,n,!o)):t.moveTo(i+p,r+d),Cu<O&&y?Cu<_?(k=Tu(a,_),A=Iu(f,g,x,w,O,-(D=Tu(s,_)),o),P=Iu(p,d,b,S,O,-k,o),t.lineTo(i+A.cx+A.x0,r+A.cy+A.y0),_<c&&k===D?t.arc(i+A.cx,r+A.cy,_,wu(A.y0,A.x0),wu(P.y0,P.x0),!o):(0<D&&t.arc(i+A.cx,r+A.cy,D,wu(A.y0,A.x0),wu(A.y1,A.x1),!o),t.arc(i,r,O,wu(A.cy+A.y1,A.cx+A.x1),wu(P.cy+P.y1,P.cx+P.x1),o),0<k&&t.arc(i+P.cx,r+P.cy,k,wu(P.y1,P.x1),wu(P.y0,P.x0),!o))):(t.lineTo(i+f,r+g),t.arc(i,r,O,n,R,o)):t.lineTo(i+f,r+g)):t.moveTo(i,r),t.closePath()))}var Du,Au=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0,this.cornerRadius=0},Pu=(u(Lu,Du=ds),Lu.prototype.getDefaultShape=function(){return new Au},Lu.prototype.buildPath=function(t,e){ku(t,e)},Lu.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},Lu);function Lu(t){return Du.call(this,t)||this}Pu.prototype.type="sector";var Ou,Ru=function(){this.cx=0,this.cy=0,this.r=0,this.r0=0},Nu=(u(Eu,Ou=ds),Eu.prototype.getDefaultShape=function(){return new Ru},Eu.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=2*Math.PI;t.moveTo(n+e.r,i),t.arc(n,i,e.r,0,r,!1),t.moveTo(n+e.r0,i),t.arc(n,i,e.r0,0,r,!0)},Eu);function Eu(t){return Ou.call(this,t)||this}function zu(t,e,n){var i=e.smooth,r=e.points;if(r&&2<=r.length){if(i)for(var o=function(t,e,n,i){var r,o,a=[],s=[],l=[],u=[];if(i){for(var h=[1/0,1/0],c=[-1/0,-1/0],p=0,d=t.length;p<d;p++)ne(h,h,t[p]),ie(c,c,t[p]);ne(h,h,i[0]),ie(c,c,i[1])}for(p=0,d=t.length;p<d;p++){var f=t[p];if(n)r=t[p?p-1:d-1],o=t[(p+1)%d];else{if(0===p||p===d-1){a.push(Wt(t[p]));continue}r=t[p-1],o=t[p+1]}Xt(s,o,r),Zt(s,s,e);var g=Kt(f,r),y=Kt(f,o),m=g+y,m=(0!==m&&(g/=m,y/=m),Zt(l,s,-g),Zt(u,s,y),Ut([],f,l)),g=Ut([],f,u);i&&(ie(m,m,h),ne(m,m,c),ie(g,g,h),ne(g,g,c)),a.push(m),a.push(g)}return n&&a.push(a.shift()),a}(r,i,n,e.smoothConstraint),a=(t.moveTo(r[0][0],r[0][1]),r.length),s=0;s<(n?a:a-1);s++){var l=o[2*s],u=o[2*s+1],h=r[(s+1)%a];t.bezierCurveTo(l[0],l[1],u[0],u[1],h[0],h[1])}else{t.moveTo(r[0][0],r[0][1]);for(var s=1,c=r.length;s<c;s++)t.lineTo(r[s][0],r[s][1])}n&&t.closePath()}}Nu.prototype.type="ring";var Bu,Fu=function(){this.points=null,this.smooth=0,this.smoothConstraint=null},Vu=(u(Hu,Bu=ds),Hu.prototype.getDefaultShape=function(){return new Fu},Hu.prototype.buildPath=function(t,e){zu(t,e,!0)},Hu);function Hu(t){return Bu.call(this,t)||this}Vu.prototype.type="polygon";var Gu,Wu=function(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null},Uu=(u(Xu,Gu=ds),Xu.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},Xu.prototype.getDefaultShape=function(){return new Wu},Xu.prototype.buildPath=function(t,e){zu(t,e,!1)},Xu);function Xu(t){return Gu.call(this,t)||this}Uu.prototype.type="polyline";var Yu,qu={},Zu=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1},ju=(u(Ku,Yu=ds),Ku.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},Ku.prototype.getDefaultShape=function(){return new Zu},Ku.prototype.buildPath=function(t,e){o=(this.subPixelOptimize?(n=(o=Ms(qu,e,this.style)).x1,i=o.y1,r=o.x2,o):(n=e.x1,i=e.y1,r=e.x2,e)).y2;var n,i,r,o,e=e.percent;0!==e&&(t.moveTo(n,i),e<1&&(r=n*(1-e)+r*e,o=i*(1-e)+o*e),t.lineTo(r,o))},Ku.prototype.pointAt=function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]},Ku);function Ku(t){return Yu.call(this,t)||this}ju.prototype.type="line";var $u=[],Qu=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1};function Ju(t,e,n){var i=t.cpx2,r=t.cpy2;return null!=i||null!=r?[(n?Bn:zn)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?Bn:zn)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?Un:Wn)(t.x1,t.cpx1,t.x2,e),(n?Un:Wn)(t.y1,t.cpy1,t.y2,e)]}u(nh,th=ds),nh.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},nh.prototype.getDefaultShape=function(){return new Qu},nh.prototype.buildPath=function(t,e){var n=e.x1,i=e.y1,r=e.x2,o=e.y2,a=e.cpx1,s=e.cpy1,l=e.cpx2,u=e.cpy2,e=e.percent;0!==e&&(t.moveTo(n,i),null==l||null==u?(e<1&&(Yn(n,a,r,e,$u),a=$u[1],r=$u[2],Yn(i,s,o,e,$u),s=$u[1],o=$u[2]),t.quadraticCurveTo(a,s,r,o)):(e<1&&(Hn(n,a,l,r,e,$u),a=$u[1],l=$u[2],r=$u[3],Hn(i,s,u,o,e,$u),s=$u[1],u=$u[2],o=$u[3]),t.bezierCurveTo(a,s,l,u,r,o)))},nh.prototype.pointAt=function(t){return Ju(this.shape,t,!1)},nh.prototype.tangentAt=function(t){t=Ju(this.shape,t,!0);return jt(t,t)};var th,eh=nh;function nh(t){return th.call(this,t)||this}eh.prototype.type="bezier-curve";var ih,rh=function(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},oh=(u(ah,ih=ds),ah.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},ah.prototype.getDefaultShape=function(){return new rh},ah.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,e=e.clockwise,s=Math.cos(o),l=Math.sin(o);t.moveTo(s*r+n,l*r+i),t.arc(n,i,r,o,a,!e)},ah);function ah(t){return ih.call(this,t)||this}oh.prototype.type="arc";u(uh,sh=ds),uh.prototype._updatePathDirty=function(){for(var t=this.shape.paths,e=this.shapeChanged(),n=0;n<t.length;n++)e=e||t[n].shapeChanged();e&&this.dirtyShape()},uh.prototype.beforeBrush=function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1],t[n].segmentIgnoreThreshold)},uh.prototype.buildPath=function(t,e){for(var n=e.paths||[],i=0;i<n.length;i++)n[i].buildPath(t,n[i].shape,!0)},uh.prototype.afterBrush=function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].pathUpdated()},uh.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),ds.prototype.getBoundingRect.call(this)};var sh,lh=uh;function uh(){var t=null!==sh&&sh.apply(this,arguments)||this;return t.type="compound",t}ch.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})};var hh=ch;function ch(t){this.colorStops=t||[]}u(fh,ph=hh);var ph,dh=fh;function fh(t,e,n,i,r,o){r=ph.call(this,r)||this;return r.x=null==t?0:t,r.y=null==e?0:e,r.x2=null==n?1:n,r.y2=null==i?0:i,r.type="linear",r.global=o||!1,r}u(yh,gh=hh);var gh,hh=yh;function yh(t,e,n,i,r){i=gh.call(this,i)||this;return i.x=null==t?.5:t,i.y=null==e?.5:e,i.r=null==n?.5:n,i.type="radial",i.global=r||!1,i}var mh=[0,0],vh=[0,0],_h=new M,xh=new M,wh=(bh.prototype.fromBoundingRect=function(t,e){var n=this._corners,i=this._axes,r=t.x,o=t.y,a=r+t.width,t=o+t.height;if(n[0].set(r,o),n[1].set(a,o),n[2].set(a,t),n[3].set(r,t),e)for(var s=0;s<4;s++)n[s].transform(e);M.sub(i[0],n[1],n[0]),M.sub(i[1],n[3],n[0]),i[0].normalize(),i[1].normalize();for(s=0;s<2;s++)this._origin[s]=i[s].dot(n[0])},bh.prototype.intersect=function(t,e){var n=!0,i=!e;return _h.set(1/0,1/0),xh.set(0,0),!this._intersectCheckOneSide(this,t,_h,xh,i,1)&&(n=!1,i)||!this._intersectCheckOneSide(t,this,_h,xh,i,-1)&&(n=!1,i)||i||M.copy(e,n?_h:xh),n},bh.prototype._intersectCheckOneSide=function(t,e,n,i,r,o){for(var a=!0,s=0;s<2;s++){var l=this._axes[s];if(this._getProjMinMaxOnAxis(s,t._corners,mh),this._getProjMinMaxOnAxis(s,e._corners,vh),mh[1]<vh[0]||vh[1]<mh[0]){if(a=!1,r)return a;var u=Math.abs(vh[0]-mh[1]),h=Math.abs(mh[0]-vh[1]);Math.min(u,h)>i.len()&&(u<h?M.scale(i,l,-u*o):M.scale(i,l,h*o))}else n&&(u=Math.abs(vh[0]-mh[1]),h=Math.abs(mh[0]-vh[1]),Math.min(u,h)<n.len())&&(u<h?M.scale(n,l,u*o):M.scale(n,l,-h*o))}return a},bh.prototype._getProjMinMaxOnAxis=function(t,e,n){for(var i=this._axes[t],r=this._origin,o=e[0].dot(i)+r[t],a=o,s=o,l=1;l<e.length;l++)var u=e[l].dot(i)+r[t],a=Math.min(u,a),s=Math.max(u,s);n[0]=a,n[1]=s},bh);function bh(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var n=0;n<4;n++)this._corners[n]=new M;for(n=0;n<2;n++)this._axes[n]=new M;t&&this.fromBoundingRect(t,e)}var Sh,Mh=[],n=(u(Th,Sh=n),Th.prototype.traverse=function(t,e){t.call(e,this)},Th.prototype.useStyle=function(){this.style={}},Th.prototype.getCursor=function(){return this._cursor},Th.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},Th.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},Th.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},Th.prototype.addDisplayable=function(t,e){(e?this._temporaryDisplayables:this._displayables).push(t),this.markRedraw()},Th.prototype.addDisplayables=function(t,e){e=e||!1;for(var n=0;n<t.length;n++)this.addDisplayable(t[n],e)},Th.prototype.getDisplayables=function(){return this._displayables},Th.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},Th.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},Th.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++)(e=this._displayables[t]).parent=this,e.update(),e.parent=null;for(var e,t=0;t<this._temporaryDisplayables.length;t++)(e=this._temporaryDisplayables[t]).parent=this,e.update(),e.parent=null},Th.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new X(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var n=this._displayables[e],i=n.getBoundingRect().clone();n.needLocalTransform()&&i.applyTransform(n.getLocalTransform(Mh)),t.union(i)}this._rect=t}return this._rect},Th.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(n[0],n[1]))for(var i=0;i<this._displayables.length;i++)if(this._displayables[i].contain(t,e))return!0;return!1},Th);function Th(){var t=null!==Sh&&Sh.apply(this,arguments)||this;return t.notClear=!0,t.incremental=!0,t._displayables=[],t._temporaryDisplayables=[],t._cursor=0,t}var Ch=Po();function Ih(t,e,n,i,r,o,a){var s,l,u,h,c,p,d=!1,f=(k(r)?(a=o,o=r,r=null):R(r)&&(o=r.cb,a=r.during,d=r.isFrom,l=r.removeOpt,r=r.dataIndex),"leave"===t),g=(f||e.stopAnimation("leave"),p=t,s=r,l=f?l||{}:null,i=(g=i)&&i.getAnimationDelayParams?i.getAnimationDelayParams(e,r):null,g&&g.ecModel&&(u=(u=g.ecModel.getUpdatePayload())&&u.animation),p="update"===p,g&&g.isAnimationEnabled()?(c=h=r=void 0,c=l?(r=N(l.duration,200),h=N(l.easing,"cubicOut"),0):(r=g.getShallow(p?"animationDurationUpdate":"animationDuration"),h=g.getShallow(p?"animationEasingUpdate":"animationEasing"),g.getShallow(p?"animationDelayUpdate":"animationDelay")),k(c=u&&(null!=u.duration&&(r=u.duration),null!=u.easing&&(h=u.easing),null!=u.delay)?u.delay:c)&&(c=c(s,i)),{duration:(r=k(r)?r(s):r)||0,delay:c,easing:h}):null);g&&0<g.duration?(p={duration:g.duration,delay:g.delay||0,easing:g.easing,done:o,force:!!o||!!a,setToFinal:!f,scope:t,during:a},d?e.animateFrom(n,p):e.animateTo(n,p)):(e.stopAnimation(),d||e.attr(n),a&&a(1),o&&o())}function kh(t,e,n,i,r,o){Ih("update",t,e,n,i,r,o)}function Dh(t,e,n,i,r,o){Ih("enter",t,e,n,i,r,o)}function Ah(t){if(!t.__zr)return!0;for(var e=0;e<t.animators.length;e++)if("leave"===t.animators[e].scope)return!0;return!1}function Ph(t,e,n,i,r,o){Ah(t)||Ih("leave",t,e,n,i,r,o)}function Lh(t,e,n,i){t.removeTextContent(),t.removeTextGuideLine(),Ph(t,{style:{opacity:0}},e,n,i)}var Oh=Math.max,Rh=Math.min,Nh={};function Eh(t){return ds.extend(t)}var zh=function(t,e){var n,i=su(t,e);function r(t){t=n.call(this,t)||this;return t.applyTransform=i.applyTransform,t.buildPath=i.buildPath,t}return u(r,n=ru),r};function Bh(t,e){return zh(t,e)}function Fh(t,e){Nh[t]=e}function Vh(t){if(Nh.hasOwnProperty(t))return Nh[t]}function Hh(t,e,n,i){t=new ru(su(t,e));return n&&("center"===i&&(n=Wh(n,t.getBoundingRect())),Xh(t,n)),t}function Gh(t,e,n){var i=new ws({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){"center"===n&&(t={width:t.width,height:t.height},i.setStyle(Wh(e,t)))}});return i}function Wh(t,e){var e=e.width/e.height,n=t.height*e,e=n<=t.width?t.height:(n=t.width)/e;return{x:t.x+t.width/2-n/2,y:t.y+t.height/2-e/2,width:n,height:e}}function Uh(t,e){for(var n=[],i=t.length,r=0;r<i;r++){var o=t[r];n.push(o.getUpdatedPathProxy(!0))}return(e=new ds(e)).createPathProxy(),e.buildPath=function(t){var e;au(t)&&(t.appendPath(n),e=t.getContext())&&t.rebuildPath(e,1)},e}function Xh(t,e){t.applyTransform&&(e=t.getBoundingRect().calculateTransform(e),t.applyTransform(e))}function Yh(t,e){return Ms(t,t,{lineWidth:e}),t}var qh=Cs;function Zh(t,e){for(var n=Oe([]);t&&t!==e;)Ne(n,t.getLocalTransform(),n),t=t.parent;return n}function jh(t,e,n){return e&&!st(e)&&(e=mr.getLocalTransform(e)),ee([],t,e=n?Fe([],e):e)}function Kh(t){return!t.isGroup}function $h(t,e,i){var r,n;function o(t){var e={x:t.x,y:t.y,rotation:t.rotation};return null!=t.shape&&(e.shape=L({},t.shape)),e}t&&e&&(n={},t.traverse(function(t){Kh(t)&&t.anid&&(n[t.anid]=t)}),r=n,e.traverse(function(t){var e,n;Kh(t)&&t.anid&&(e=r[t.anid])&&(n=o(t),t.attr(o(e)),kh(t,n,i,D(t).dataIndex))}))}function Qh(t,n){return B(t,function(t){var e=t[0],e=Oh(e,n.x),t=(e=Rh(e,n.x+n.width),t[1]),t=Oh(t,n.y);return[e,Rh(t,n.y+n.height)]})}function Jh(t,e){var n=Oh(t.x,e.x),i=Rh(t.x+t.width,e.x+e.width),r=Oh(t.y,e.y),t=Rh(t.y+t.height,e.y+e.height);if(n<=i&&r<=t)return{x:n,y:r,width:i-n,height:t-r}}function tc(t,e,n){var e=L({rectHover:!0},e),i=e.style={strokeNoScale:!0};if(n=n||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(i.image=t.slice(8),z(i,n),new ws(e)):Hh(t.replace("path://",""),e,n,"center")}function ec(t,e,n,i,r,o,a,s){var l,n=n-t,i=i-e,a=a-r,s=s-o,u=a*i-n*s;return!((l=u)<=1e-6&&-1e-6<=l||(r=((l=t-r)*i-n*(t=e-o))/u)<0||1<r||(i=(l*s-a*t)/u)<0||1<i)}function nc(t){var e=t.itemTooltipOption,n=t.componentModel,i=t.itemName,e=V(e)?{formatter:e}:e,r=n.mainType,n=n.componentIndex,o={componentType:r,name:i,$vars:["name"]},a=(o[r+"Index"]=n,t.formatterParamsExtra),t=(a&&O(ht(a),function(t){Bt(o,t)||(o[t]=a[t],o.$vars.push(t))}),D(t.el));t.componentMainType=r,t.componentIndex=n,t.tooltipConfig={name:i,option:z({content:i,encodeHTMLContent:!0,formatterParams:o},e)}}function ic(t,e){var n;(n=t.isGroup?e(t):n)||t.traverse(e)}function rc(t,e){if(t)if(F(t))for(var n=0;n<t.length;n++)ic(t[n],e);else ic(t,e)}Fh("circle",hu),Fh("ellipse",fu),Fh("sector",Pu),Fh("ring",Nu),Fh("polygon",Vu),Fh("polyline",Uu),Fh("rect",As),Fh("line",ju),Fh("bezierCurve",eh),Fh("arc",oh);var oc=Object.freeze({__proto__:null,Arc:oh,BezierCurve:eh,BoundingRect:X,Circle:hu,CompoundPath:lh,Ellipse:fu,Group:Hr,Image:ws,IncrementalDisplayable:n,Line:ju,LinearGradient:dh,OrientedBoundingRect:wh,Path:ds,Point:M,Polygon:Vu,Polyline:Uu,RadialGradient:hh,Rect:As,Ring:Nu,Sector:Pu,Text:Ns,applyTransform:jh,clipPointsByRect:Qh,clipRectByRect:Jh,createIcon:tc,extendPath:Bh,extendShape:Eh,getShapeClass:Vh,getTransform:Zh,groupTransition:$h,initProps:Dh,isElementRemoved:Ah,lineLineIntersect:ec,linePolygonIntersect:function(t,e,n,i,r){for(var o=0,a=r[r.length-1];o<r.length;o++){var s=r[o];if(ec(t,e,n,i,s[0],s[1],a[0],a[1]))return!0;a=s}},makeImage:Gh,makePath:Hh,mergePath:Uh,registerShape:Fh,removeElement:Ph,removeElementWithFadeOut:function(t,e,n){function i(){t.parent&&t.parent.remove(t)}t.isGroup?t.traverse(function(t){t.isGroup||Lh(t,e,n,i)}):Lh(t,e,n,i)},resizePath:Xh,setTooltipConfig:nc,subPixelOptimize:qh,subPixelOptimizeLine:Yh,subPixelOptimizeRect:function(t){return Ts(t.shape,t.shape,t.style),t},transformDirection:function(t,e,n){var i=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),i=jh(["left"===t?-i:"right"===t?i:0,"top"===t?-r:"bottom"===t?r:0],e,n);return Math.abs(i[0])>Math.abs(i[1])?0<i[0]?"right":"left":0<i[1]?"bottom":"top"},traverseElements:rc,updateProps:kh}),ac={};function sc(t,e){for(var n=0;n<tl.length;n++){var i=tl[n],r=e[i],i=t.ensureState(i);i.style=i.style||{},i.style.text=r}var o=t.currentStates.slice();t.clearStates(!0),t.setStyle({text:e.normal}),t.useStates(o,!0)}function lc(t,e,n){for(var i,r=t.labelFetcher,o=t.labelDataIndex,a=t.labelDimIndex,s=e.normal,l={normal:i=null==(i=r?r.getFormattedLabel(o,"normal",null,a,s&&s.get("formatter"),null!=n?{interpolatedValue:n}:null):i)?k(t.defaultText)?t.defaultText(o,t,n):t.defaultText:i},u=0;u<tl.length;u++){var h=tl[u],c=e[h];l[h]=N(r?r.getFormattedLabel(o,h,null,a,c&&c.get("formatter")):null,i)}return l}function uc(t,e,n,i){n=n||ac;for(var r=t instanceof Ns,o=!1,a=0;a<el.length;a++)if((p=e[el[a]])&&p.getShallow("show")){o=!0;break}var s=r?t:t.getTextContent();if(o){r||(s||(s=new Ns,t.setTextContent(s)),t.stateProxy&&(s.stateProxy=t.stateProxy));var l=lc(n,e),u=e.normal,h=!!u.getShallow("show"),c=cc(u,i&&i.normal,n,!1,!r);c.text=l.normal,r||t.setTextConfig(pc(u,n,!1));for(a=0;a<tl.length;a++){var p,d,f,g=tl[a];(p=e[g])&&(d=s.ensureState(g),(f=!!N(p.getShallow("show"),h))!=h&&(d.ignore=!f),d.style=cc(p,i&&i[g],n,!0,!r),d.style.text=l[g],r||(t.ensureState(g).textConfig=pc(p,n,!0)))}s.silent=!!u.getShallow("silent"),null!=s.style.x&&(c.x=s.style.x),null!=s.style.y&&(c.y=s.style.y),s.ignore=!h,s.useStyle(c),s.dirty(),n.enableTextSetter&&(mc(s).setLabelText=function(t){t=lc(n,e,t);sc(s,t)})}else s&&(s.ignore=!0);t.dirty()}function hc(t,e){for(var n={normal:t.getModel(e=e||"label")},i=0;i<tl.length;i++){var r=tl[i];n[r]=t.getModel([r,e])}return n}function cc(t,e,n,i,r){var o,a={},s=a,l=t,u=n,h=i,c=r;u=u||ac;var p,t=l.ecModel,d=t&&t.option.textStyle,f=function(t){var e;for(;t&&t!==t.ecModel;){var n=(t.option||ac).rich;if(n){e=e||{};for(var i=ht(n),r=0;r<i.length;r++){var o=i[r];e[o]=1}}t=t.parentModel}return e}(l);if(f)for(var g in p={},f)f.hasOwnProperty(g)&&(o=l.getModel(["rich",g]),yc(p[g]={},o,d,u,h,c,!1,!0));return p&&(s.rich=p),(t=l.get("overflow"))&&(s.overflow=t),null!=(t=l.get("minMargin"))&&(s.margin=t),yc(s,l,d,u,h,c,!0,!1),e&&L(a,e),a}function pc(t,e,n){e=e||{};var i={},r=t.getShallow("rotate"),o=N(t.getShallow("distance"),n?null:5),a=t.getShallow("offset"),n=t.getShallow("position")||(n?null:"inside");return null!=(n="outside"===n?e.defaultOutsidePosition||"top":n)&&(i.position=n),null!=a&&(i.offset=a),null!=r&&(r*=Math.PI/180,i.rotation=r),null!=o&&(i.distance=o),i.outsideFill="inherit"===t.get("color")?e.inheritColor||null:"auto",i}var dc=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],fc=["align","lineHeight","width","height","tag","verticalAlign","ellipsis"],gc=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function yc(t,e,n,i,r,o,a,s){n=!r&&n||ac;var l=i&&i.inheritColor,u=e.getShallow("color"),h=e.getShallow("textBorderColor"),c=N(e.getShallow("opacity"),n.opacity),u=("inherit"!==u&&"auto"!==u||(u=l||null),"inherit"!==h&&"auto"!==h||(h=l||null),o||(u=u||n.color,h=h||n.textBorderColor),null!=u&&(t.fill=u),null!=h&&(t.stroke=h),N(e.getShallow("textBorderWidth"),n.textBorderWidth)),h=(null!=u&&(t.lineWidth=u),N(e.getShallow("textBorderType"),n.textBorderType)),u=(null!=h&&(t.lineDash=h),N(e.getShallow("textBorderDashOffset"),n.textBorderDashOffset));null!=u&&(t.lineDashOffset=u),null!=(c=r||null!=c||s?c:i&&i.defaultOpacity)&&(t.opacity=c),r||o||null==t.fill&&i.inheritColor&&(t.fill=i.inheritColor);for(var p=0;p<dc.length;p++){var d=dc[p];null!=(f=N(e.getShallow(d),n[d]))&&(t[d]=f)}for(var p=0;p<fc.length;p++){d=fc[p];null!=(f=e.getShallow(d))&&(t[d]=f)}if(null==t.verticalAlign&&null!=(h=e.getShallow("baseline"))&&(t.verticalAlign=h),!a||!i.disableBox){for(p=0;p<gc.length;p++){var f,d=gc[p];null!=(f=e.getShallow(d))&&(t[d]=f)}u=e.getShallow("borderType");null!=u&&(t.borderDash=u),"auto"!==t.backgroundColor&&"inherit"!==t.backgroundColor||!l||(t.backgroundColor=l),"auto"!==t.borderColor&&"inherit"!==t.borderColor||!l||(t.borderColor=l)}}var mc=Po();function vc(n,i,r,t,o){var a,s,l,u=mc(n);u.valueAnimation&&u.prevValue!==u.value&&(a=u.defaultInterpolatedText,s=N(u.interpolatedValue,u.prevValue),l=u.value,n.percent=0,(null==u.prevValue?Dh:kh)(n,{percent:1},t,i,null,function(t){var e=Bo(r,u.precision,s,l,t),t=(u.interpolatedValue=1===t?null:e,lc({labelDataIndex:i,labelFetcher:o,defaultText:a?a(e):e+""},u.statesModels,e));sc(n,t)}))}var _c=["textStyle","color"],xc=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],wc=new Ns,qh=(bc.prototype.getTextColor=function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(_c):null)},bc.prototype.getFont=function(){return t={fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},e=(e=this.ecModel)&&e.getModel("textStyle"),Ct([t.fontStyle||e&&e.getShallow("fontStyle")||"",t.fontWeight||e&&e.getShallow("fontWeight")||"",(t.fontSize||e&&e.getShallow("fontSize")||12)+"px",t.fontFamily||e&&e.getShallow("fontFamily")||"sans-serif"].join(" "));var t,e},bc.prototype.getTextRect=function(t){for(var e={text:t,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},n=0;n<xc.length;n++)e[xc[n]]=this.getShallow(xc[n]);return wc.useStyle(e),wc.update(),wc.getBoundingRect()},bc);function bc(){}var Sc=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],Mc=jo(Sc),Tc=(Cc.prototype.getLineStyle=function(t){return Mc(this,t)},Cc);function Cc(){}var Ic=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],kc=jo(Ic),Dc=(Ac.prototype.getItemStyle=function(t,e){return kc(this,t,e)},Ac);function Ac(){}Oc.prototype.init=function(t,e,n){},Oc.prototype.mergeOption=function(t,e){d(this.option,t,!0)},Oc.prototype.get=function(t,e){return null==t?this.option:this._doGet(this.parsePath(t),!e&&this.parentModel)},Oc.prototype.getShallow=function(t,e){var n=this.option,n=null==n?n:n[t];return null!=n||e||(e=this.parentModel)&&(n=e.getShallow(t)),n},Oc.prototype.getModel=function(t,e){var n=null!=t,t=n?this.parsePath(t):null;return new Oc(n?this._doGet(t):this.option,e=e||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(t)),this.ecModel)},Oc.prototype.isEmpty=function(){return null==this.option},Oc.prototype.restoreData=function(){},Oc.prototype.clone=function(){return new this.constructor(y(this.option))},Oc.prototype.parsePath=function(t){return"string"==typeof t?t.split("."):t},Oc.prototype.resolveParentPath=function(t){return t},Oc.prototype.isAnimationEnabled=function(){if(!b.node&&this.option)return null!=this.option.animation?!!this.option.animation:this.parentModel?this.parentModel.isAnimationEnabled():void 0},Oc.prototype._doGet=function(t,e){var n=this.option;if(t){for(var i=0;i<t.length&&(!t[i]||null!=(n=n&&"object"==typeof n?n[t[i]]:null));i++);null==n&&e&&(n=e._doGet(this.resolveParentPath(t),e.parentModel))}return n};var Pc,Lc=Oc;function Oc(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}Wo(Lc),Zc=Lc,Pc=["__\0is_clz",Xo++].join("_"),Zc.prototype[Pc]=!0,Zc.isInstance=function(t){return!(!t||!t[Pc])},at(Lc,Tc),at(Lc,Dc),at(Lc,$o),at(Lc,qh);var Rc=Math.round(10*Math.random());function Nc(t){return[t||"",Rc++].join("_")}var Ec="ZH",zc="EN",Bc=zc,Fc={},Vc={},Hc=b.domSupported&&-1<(document.documentElement.lang||navigator.language||navigator.browserLanguage||Bc).toUpperCase().indexOf(Ec)?Ec:Bc;function Gc(t,e){t=t.toUpperCase(),Vc[t]=new Lc(e),Fc[t]=e}Gc(zc,{time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst",custom:"Custom chart",chart:"Chart"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}}),Gc(Ec,{time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图",custom:"自定义图表",chart:"图表"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}});var Wc=1e3,Uc=60*Wc,Xc=60*Uc,Yc=24*Xc,Xo=365*Yc,qc={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},Zc="{yyyy}-{MM}-{dd}",jc={year:"{yyyy}",month:"{yyyy}-{MM}",day:Zc,hour:Zc+" "+qc.hour,minute:Zc+" "+qc.minute,second:Zc+" "+qc.second,millisecond:qc.none},Kc=["year","month","day","hour","minute","second","millisecond"],$c=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function Qc(t,e){return"0000".substr(0,e-(t+="").length)+t}function Jc(t){switch(t){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return t}}function tp(t,e,n,i){var t=lo(t),r=t[ip(n)](),o=t[rp(n)]()+1,a=Math.floor((o-1)/3)+1,s=t[op(n)](),l=t["get"+(n?"UTC":"")+"Day"](),u=t[ap(n)](),h=(u-1)%12+1,c=t[sp(n)](),p=t[lp(n)](),t=t[up(n)](),n=12<=u?"pm":"am",d=n.toUpperCase(),i=(i instanceof Lc?i:Vc[i||Hc]||Vc[Bc]).getModel("time"),f=i.get("month"),g=i.get("monthAbbr"),y=i.get("dayOfWeek"),i=i.get("dayOfWeekAbbr");return(e||"").replace(/{a}/g,n+"").replace(/{A}/g,d+"").replace(/{yyyy}/g,r+"").replace(/{yy}/g,Qc(r%100+"",2)).replace(/{Q}/g,a+"").replace(/{MMMM}/g,f[o-1]).replace(/{MMM}/g,g[o-1]).replace(/{MM}/g,Qc(o,2)).replace(/{M}/g,o+"").replace(/{dd}/g,Qc(s,2)).replace(/{d}/g,s+"").replace(/{eeee}/g,y[l]).replace(/{ee}/g,i[l]).replace(/{e}/g,l+"").replace(/{HH}/g,Qc(u,2)).replace(/{H}/g,u+"").replace(/{hh}/g,Qc(h+"",2)).replace(/{h}/g,h+"").replace(/{mm}/g,Qc(c,2)).replace(/{m}/g,c+"").replace(/{ss}/g,Qc(p,2)).replace(/{s}/g,p+"").replace(/{SSS}/g,Qc(t,3)).replace(/{S}/g,t+"")}function ep(t,e){var t=lo(t),n=t[rp(e)]()+1,i=t[op(e)](),r=t[ap(e)](),o=t[sp(e)](),a=t[lp(e)](),t=0===t[up(e)](),e=t&&0===a,a=e&&0===o,o=a&&0===r,r=o&&1===i;return r&&1===n?"year":r?"month":o?"day":a?"hour":e?"minute":t?"second":"millisecond"}function np(t,e,n){var i=H(t)?lo(t):t;switch(e=e||ep(t,n)){case"year":return i[ip(n)]();case"half-year":return 6<=i[rp(n)]()?1:0;case"quarter":return Math.floor((i[rp(n)]()+1)/4);case"month":return i[rp(n)]();case"day":return i[op(n)]();case"half-day":return i[ap(n)]()/24;case"hour":return i[ap(n)]();case"minute":return i[sp(n)]();case"second":return i[lp(n)]();case"millisecond":return i[up(n)]()}}function ip(t){return t?"getUTCFullYear":"getFullYear"}function rp(t){return t?"getUTCMonth":"getMonth"}function op(t){return t?"getUTCDate":"getDate"}function ap(t){return t?"getUTCHours":"getHours"}function sp(t){return t?"getUTCMinutes":"getMinutes"}function lp(t){return t?"getUTCSeconds":"getSeconds"}function up(t){return t?"getUTCMilliseconds":"getMilliseconds"}function hp(t){return t?"setUTCMonth":"setMonth"}function cp(t){return t?"setUTCDate":"setDate"}function pp(t){return t?"setUTCHours":"setHours"}function dp(t){return t?"setUTCMinutes":"setMinutes"}function fp(t){return t?"setUTCSeconds":"setSeconds"}function gp(t){return t?"setUTCMilliseconds":"setMilliseconds"}function yp(t){var e;return fo(t)?(e=(t+"").split("."))[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(1<e.length?"."+e[1]:""):V(t)?t:"-"}function mp(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()}),t=e?t&&t.charAt(0).toUpperCase()+t.slice(1):t}var vp=Mt;function _p(t,e,n){function i(t){return t&&Ct(t)?t:"-"}function r(t){return null!=t&&!isNaN(t)&&isFinite(t)}var o="time"===e,a=t instanceof Date;if(o||a){var o=o?lo(t):t;if(!isNaN(+o))return tp(o,"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss}",n);if(a)return"-"}return"ordinal"===e?dt(t)?i(t):H(t)&&r(t)?t+"":"-":r(o=po(t))?yp(o):dt(t)?i(t):"boolean"==typeof t?t+"":"-"}function xp(t,e){return"{"+t+(null==e?"":e)+"}"}var wp=["a","b","c","d","e","f","g"];function bp(t,e,n){var i=(e=F(e)?e:[e]).length;if(!i)return"";for(var r=e[0].$vars||[],o=0;o<r.length;o++){var a=wp[o];t=t.replace(xp(a),xp(a,0))}for(var s=0;s<i;s++)for(var l=0;l<r.length;l++){var u=e[s][r[l]];t=t.replace(xp(wp[l],s),n?_e(u):u)}return t}function Sp(t,e){var t=V(t)?{color:t,extraCssText:e}:t||{},n=t.color,i=t.type,r=(e=t.extraCssText,t.renderMode||"html");return n?"html"===r?"subItem"===i?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+_e(n)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+_e(n)+";"+(e||"")+'"></span>':{renderMode:r,content:"{"+(t.markerId||"markerX")+"|}  ",style:"subItem"===i?{width:4,height:4,borderRadius:2,backgroundColor:n}:{width:10,height:10,borderRadius:5,backgroundColor:n}}:""}function Mp(t,e){return e=e||"transparent",V(t)?t:R(t)&&t.colorStops&&(t.colorStops[0]||{}).color||e}function Tp(t,e){var n;"_blank"===e||"blank"===e?((n=window.open()).opener=null,n.location.href=t):window.open(t,e)}var Cp=O,Ip=["left","right","top","bottom","width","height"],kp=[["width","left","right"],["height","top","bottom"]];function Dp(a,s,l,u,h){var c=0,p=0,d=(null==u&&(u=1/0),null==h&&(h=1/0),0);s.eachChild(function(t,e){var n,i,r,o=t.getBoundingRect(),e=s.childAt(e+1),e=e&&e.getBoundingRect();d="horizontal"===a?(i=o.width+(e?-e.x+o.x:0),u<(n=c+i)||t.newline?(c=0,n=i,p+=d+l,o.height):Math.max(d,o.height)):(i=o.height+(e?-e.y+o.y:0),h<(r=p+i)||t.newline?(c+=d+l,p=0,r=i,o.width):Math.max(d,o.width)),t.newline||(t.x=c,t.y=p,t.markRedraw(),"horizontal"===a?c=n+l:p=r+l)})}var Ap=Dp;function Pp(t,e,n){n=vp(n||0);var i=e.width,r=e.height,o=to(t.left,i),a=to(t.top,r),e=to(t.right,i),s=to(t.bottom,r),l=to(t.width,i),u=to(t.height,r),h=n[2]+n[0],c=n[1]+n[3],p=t.aspect;switch(isNaN(l)&&(l=i-e-c-o),isNaN(u)&&(u=r-s-h-a),null!=p&&(isNaN(l)&&isNaN(u)&&(i/r<p?l=.8*i:u=.8*r),isNaN(l)&&(l=p*u),isNaN(u))&&(u=l/p),isNaN(o)&&(o=i-e-l-c),isNaN(a)&&(a=r-s-u-h),t.left||t.right){case"center":o=i/2-l/2-n[3];break;case"right":o=i-l-c}switch(t.top||t.bottom){case"middle":case"center":a=r/2-u/2-n[0];break;case"bottom":a=r-u-h}o=o||0,a=a||0,isNaN(l)&&(l=i-c-o-(e||0)),isNaN(u)&&(u=r-h-a-(s||0));p=new X(o+n[3],a+n[0],l,u);return p.margin=n,p}function Lp(t){t=t.layoutMode||t.constructor.layoutMode;return R(t)?t:t?{type:t}:null}function Op(l,u,t){var h=t&&t.ignoreSize,t=(F(h)||(h=[h,h]),n(kp[0],0)),e=n(kp[1],1);function n(t,e){var n={},i=0,r={},o=0;if(Cp(t,function(t){r[t]=l[t]}),Cp(t,function(t){c(u,t)&&(n[t]=r[t]=u[t]),p(n,t)&&i++,p(r,t)&&o++}),h[e])p(u,t[1])?r[t[2]]=null:p(u,t[2])&&(r[t[1]]=null);else if(2!==o&&i){if(!(2<=i))for(var a=0;a<t.length;a++){var s=t[a];if(!c(n,s)&&c(l,s)){n[s]=l[s];break}}return n}return r}function c(t,e){return t.hasOwnProperty(e)}function p(t,e){return null!=t[e]&&"auto"!==t[e]}function i(t,e,n){Cp(t,function(t){e[t]=n[t]})}i(kp[0],l,t),i(kp[1],l,e)}function Rp(t){return e={},(n=t)&&e&&Cp(Ip,function(t){n.hasOwnProperty(t)&&(e[t]=n[t])}),e;var e,n}pt(Dp,"vertical"),pt(Dp,"horizontal");var Np,Ep,zp,Bp,Fp=Po(),g=(u(Vp,Np=Lc),Vp.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n)},Vp.prototype.mergeDefaultAndTheme=function(t,e){var n=Lp(this),i=n?Rp(t):{};d(t,e.getTheme().get(this.mainType)),d(t,this.getDefaultOption()),n&&Op(t,i,n)},Vp.prototype.mergeOption=function(t,e){d(this.option,t,!0);var n=Lp(this);n&&Op(this.option,t,n)},Vp.prototype.optionUpdated=function(t,e){},Vp.prototype.getDefaultOption=function(){var t=this.constructor;if(!(e=t)||!e[Ho])return t.defaultOption;var e=Fp(this);if(!e.defaultOption){for(var n=[],i=t;i;){var r=i.prototype.defaultOption;r&&n.push(r),i=i.superClass}for(var o={},a=n.length-1;0<=a;a--)o=d(o,n[a],!0);e.defaultOption=o}return e.defaultOption},Vp.prototype.getReferringComponents=function(t,e){var n=t+"Id";return Eo(this.ecModel,t,{index:this.get(t+"Index",!0),id:this.get(n,!0)},e)},Vp.prototype.getBoxLayoutParams=function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}},Vp.prototype.getZLevelKey=function(){return""},Vp.prototype.setZLevel=function(t){this.option.zlevel=t},Vp.protoInitialize=((Tc=Vp.prototype).type="component",Tc.id="",Tc.name="",Tc.mainType="",Tc.subType="",void(Tc.componentIndex=0)),Vp);function Vp(t,e,n){t=Np.call(this,t,e,n)||this;return t.uid=Nc("ec_cpt_model"),t}function Hp(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}Uo(g,Lc),Zo(g),zp={},(Ep=g).registerSubTypeDefaulter=function(t,e){t=Go(t);zp[t.main]=e},Ep.determineSubType=function(t,e){var n,i=e.type;return i||(n=Go(t).main,Ep.hasSubTypes(t)&&zp[n]&&(i=zp[n](e))),i},Bp=function(t){var e=[];O(g.getClassesByMainType(t),function(t){e=e.concat(t.dependencies||t.prototype.dependencies||[])}),e=B(e,function(t){return Go(t).main}),"dataset"!==t&&I(e,"dataset")<=0&&e.unshift("dataset");return e},g.topologicalTravel=function(t,e,n,i){if(t.length){a={},s=[],O(o=e,function(n){var e,i,r=Hp(a,n),t=r.originalDeps=Bp(n),t=(e=o,i=[],O(t,function(t){0<=I(e,t)&&i.push(t)}),i);r.entryCount=t.length,0===r.entryCount&&s.push(n),O(t,function(t){I(r.predecessor,t)<0&&r.predecessor.push(t);var e=Hp(a,t);I(e.successor,t)<0&&e.successor.push(n)})});var o,a,s,e={graph:a,noEntryList:s},r=e.graph,l=e.noEntryList,u={};for(O(t,function(t){u[t]=!0});l.length;){var h=l.pop(),c=r[h],p=!!u[h];p&&(n.call(i,h,c.originalDeps.slice()),delete u[h]),O(c.successor,p?f:d)}O(u,function(){throw new Error("")})}function d(t){r[t].entryCount--,0===r[t].entryCount&&l.push(t)}function f(t){u[t]=!0,d(t)}};var Dc="",$o=("undefined"!=typeof navigator&&(Dc=navigator.platform||""),"rgba(0, 0, 0, 0.2)"),Gp={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:$o,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:$o,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:$o,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:$o,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:$o,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:$o,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:Dc.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},Wp=E(["tooltip","label","itemName","itemId","itemGroupId","itemChildGroupId","seriesName"]),Up="original",Xp="arrayRows",Yp="objectRows",qp="keyedColumns",Zp="typedArray",jp="unknown",Kp="column",$p="row",Qp={Must:1,Might:2,Not:3},Jp=Po();function td(n,t,e){var r,o,a,i,s,l={},u=ed(t);return u&&n&&(r=[],o=[],t=t.ecModel,t=Jp(t).datasetMap,u=u.uid+"_"+e.seriesLayoutBy,O(n=n.slice(),function(t,e){t=R(t)?t:n[e]={name:t};"ordinal"===t.type&&null==a&&(a=e,i=c(t)),l[t.name]=[]}),s=t.get(u)||t.set(u,{categoryWayDim:i,valueWayDim:0}),O(n,function(t,e){var n,i=t.name,t=c(t);null==a?(n=s.valueWayDim,h(l[i],n,t),h(o,n,t),s.valueWayDim+=t):a===e?(h(l[i],0,t),h(r,0,t)):(n=s.categoryWayDim,h(l[i],n,t),h(o,n,t),s.categoryWayDim+=t)}),r.length&&(l.itemName=r),o.length)&&(l.seriesName=o),l;function h(t,e,n){for(var i=0;i<n;i++)t.push(e+i)}function c(t){t=t.dimsDef;return t?t.length:1}}function ed(t){if(!t.get("data",!0))return Eo(t.ecModel,"dataset",{index:t.get("datasetIndex",!0),id:t.get("datasetId",!0)},No).models[0]}function nd(t,e){var n,i,r,o=t.data,a=t.sourceFormat,s=t.seriesLayoutBy,l=t.dimensionsDefine,u=t.startIndex,h=e;if(!gt(o)){if(l&&(R(l=l[h])?(i=l.name,r=l.type):V(l)&&(i=l)),null!=r)return"ordinal"===r?Qp.Must:Qp.Not;if(a===Xp){var c=o;if(s===$p){for(var p=c[h],d=0;d<(p||[]).length&&d<5;d++)if(null!=(n=_(p[u+d])))return n}else for(d=0;d<c.length&&d<5;d++){var f=c[u+d];if(f&&null!=(n=_(f[h])))return n}}else if(a===Yp){var g=o;if(!i)return Qp.Not;for(d=0;d<g.length&&d<5;d++)if((m=g[d])&&null!=(n=_(m[i])))return n}else if(a===qp){l=o;if(!i)return Qp.Not;if(!(p=l[i])||gt(p))return Qp.Not;for(d=0;d<p.length&&d<5;d++)if(null!=(n=_(p[d])))return n}else if(a===Up)for(var y=o,d=0;d<y.length&&d<5;d++){var m,v=bo(m=y[d]);if(!F(v))return Qp.Not;if(null!=(n=_(v[h])))return n}}return Qp.Not;function _(t){var e=V(t);return null!=t&&Number.isFinite(Number(t))&&""!==t?e?Qp.Might:Qp.Not:e&&"-"!==t?Qp.Must:void 0}}var id=E();var rd,od,ad,sd=Po(),ld=(Po(),ud.prototype.getColorFromPalette=function(t,e,n){var i=_o(this.get("color",!0)),r=this.get("colorLayer",!0),o=this,a=sd;return a=a(e=e||o),o=a.paletteIdx||0,(e=a.paletteNameMap=a.paletteNameMap||{}).hasOwnProperty(t)?e[t]:(r=(r=n==null||!r?i:hd(r,n))||i)&&r.length?(n=r[o],t&&(e[t]=n),a.paletteIdx=(o+1)%r.length,n):void 0},ud.prototype.clearColorPalette=function(){var t,e;(e=sd)(t=this).paletteIdx=0,e(t).paletteNameMap={}},ud);function ud(){}function hd(t,e){for(var n=t.length,i=0;i<n;i++)if(t[i].length>e)return t[i];return t[n-1]}var cd,pd="\0_ec_inner",dd=(u(l,cd=Lc),l.prototype.init=function(t,e,n,i,r,o){i=i||{},this.option=null,this._theme=new Lc(i),this._locale=new Lc(r),this._optionManager=o},l.prototype.setOption=function(t,e,n){e=yd(e);this._optionManager.setOption(t,n,e),this._resetOption(null,e)},l.prototype.resetOption=function(t,e){return this._resetOption(t,yd(e))},l.prototype._resetOption=function(t,e){var n,i=!1,r=this._optionManager;return t&&"recreate"!==t||(n=r.mountOption("recreate"===t),this.option&&"recreate"!==t?(this.restoreData(),this._mergeOption(n,e)):ad(this,n),i=!0),"timeline"!==t&&"media"!==t||this.restoreData(),t&&"recreate"!==t&&"timeline"!==t||(n=r.getTimelineOption(this))&&(i=!0,this._mergeOption(n,e)),t&&"recreate"!==t&&"media"!==t||(n=r.getMediaOption(this)).length&&O(n,function(t){i=!0,this._mergeOption(t,e)},this),i},l.prototype.mergeOption=function(t){this._mergeOption(t,null)},l.prototype._mergeOption=function(i,t){var r=this.option,h=this._componentsMap,c=this._componentsCount,n=[],o=E(),p=t&&t.replaceMergeMainTypeMap;Jp(this).datasetMap=E(),O(i,function(t,e){null!=t&&(g.hasClass(e)?e&&(n.push(e),o.set(e,!0)):r[e]=null==r[e]?y(t):d(r[e],t,!0))}),p&&p.each(function(t,e){g.hasClass(e)&&!o.get(e)&&(n.push(e),o.set(e,!0))}),g.topologicalTravel(n,g.getAllClassMainTypes(),function(o){var a,t=function(t,e,n){return(e=(e=id.get(e))&&e(t))?n.concat(e):n}(this,o,_o(i[o])),e=h.get(o),n=e?p&&p.get(o)?"replaceMerge":"normalMerge":"replaceAll",e=So(e,t,n),s=(Do(e,o,g),r[o]=null,h.set(o,null),c.set(o,0),[]),l=[],u=0;O(e,function(t,e){var n=t.existing,i=t.newOption;if(i){var r=g.getClass(o,t.keyInfo.subType,!("series"===o));if(!r)return;if("tooltip"===o){if(a)return;a=!0}n&&n.constructor===r?(n.name=t.keyInfo.name,n.mergeOption(i,this),n.optionUpdated(i,!1)):(e=L({componentIndex:e},t.keyInfo),L(n=new r(i,this,this,e),e),t.brandNew&&(n.__requireNewView=!0),n.init(i,this,this),n.optionUpdated(null,!0))}else n&&(n.mergeOption({},this),n.optionUpdated({},!1));n?(s.push(n.option),l.push(n),u++):(s.push(void 0),l.push(void 0))},this),r[o]=s,h.set(o,l),c.set(o,u),"series"===o&&rd(this)},this),this._seriesIndices||rd(this)},l.prototype.getOption=function(){var a=y(this.option);return O(a,function(t,e){if(g.hasClass(e)){for(var n=_o(t),i=n.length,r=!1,o=i-1;0<=o;o--)n[o]&&!ko(n[o])?r=!0:(n[o]=null,r||i--);n.length=i,a[e]=n}}),delete a[pd],a},l.prototype.getTheme=function(){return this._theme},l.prototype.getLocaleModel=function(){return this._locale},l.prototype.setUpdatePayload=function(t){this._payload=t},l.prototype.getUpdatePayload=function(){return this._payload},l.prototype.getComponent=function(t,e){var n=this._componentsMap.get(t);if(n){t=n[e||0];if(t)return t;if(null==e)for(var i=0;i<n.length;i++)if(n[i])return n[i]}},l.prototype.queryComponents=function(t){var e,n,i,r,o,a=t.mainType;return a&&(e=t.index,n=t.id,i=t.name,r=this._componentsMap.get(a))&&r.length?(null!=e?(o=[],O(_o(e),function(t){r[t]&&o.push(r[t])})):o=null!=n?fd("id",n,r):null!=i?fd("name",i,r):ut(r,function(t){return!!t}),gd(o,t)):[]},l.prototype.findComponents=function(t){var e,n=t.query,i=t.mainType,r=(r=i+"Index",o=i+"Id",e=i+"Name",!(n=n)||null==n[r]&&null==n[o]&&null==n[e]?null:{mainType:i,index:n[r],id:n[o],name:n[e]}),o=r?this.queryComponents(r):ut(this._componentsMap.get(i),function(t){return!!t});return n=gd(o,t),t.filter?ut(n,t.filter):n},l.prototype.eachComponent=function(t,e,n){var i=this._componentsMap;if(k(t)){var r=e,o=t;i.each(function(t,e){for(var n=0;t&&n<t.length;n++){var i=t[n];i&&o.call(r,e,i,i.componentIndex)}})}else for(var a=V(t)?i.get(t):R(t)?this.findComponents(t):null,s=0;a&&s<a.length;s++){var l=a[s];l&&e.call(n,l,l.componentIndex)}},l.prototype.getSeriesByName=function(t){var e=Co(t,null);return ut(this._componentsMap.get("series"),function(t){return!!t&&null!=e&&t.name===e})},l.prototype.getSeriesByIndex=function(t){return this._componentsMap.get("series")[t]},l.prototype.getSeriesByType=function(e){return ut(this._componentsMap.get("series"),function(t){return!!t&&t.subType===e})},l.prototype.getSeries=function(){return ut(this._componentsMap.get("series"),function(t){return!!t})},l.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},l.prototype.eachSeries=function(n,i){od(this),O(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];n.call(i,e,t)},this)},l.prototype.eachRawSeries=function(e,n){O(this._componentsMap.get("series"),function(t){t&&e.call(n,t,t.componentIndex)})},l.prototype.eachSeriesByType=function(n,i,r){od(this),O(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];e.subType===n&&i.call(r,e,t)},this)},l.prototype.eachRawSeriesByType=function(t,e,n){return O(this.getSeriesByType(t),e,n)},l.prototype.isSeriesFiltered=function(t){return od(this),null==this._seriesIndicesMap.get(t.componentIndex)},l.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},l.prototype.filterSeries=function(n,i){od(this);var r=[];O(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];n.call(i,e,t)&&r.push(t)},this),this._seriesIndices=r,this._seriesIndicesMap=E(r)},l.prototype.restoreData=function(n){rd(this);var t=this._componentsMap,i=[];t.each(function(t,e){g.hasClass(e)&&i.push(e)}),g.topologicalTravel(i,g.getAllClassMainTypes(),function(e){O(t.get(e),function(t){!t||"series"===e&&function(t,e){{var n,i;if(e)return n=e.seriesIndex,i=e.seriesId,e=e.seriesName,null!=n&&t.componentIndex!==n||null!=i&&t.id!==i||null!=e&&t.name!==e}}(t,n)||t.restoreData()})})},l.internalField=(rd=function(t){var e=t._seriesIndices=[];O(t._componentsMap.get("series"),function(t){t&&e.push(t.componentIndex)}),t._seriesIndicesMap=E(e)},od=function(t){},void(ad=function(t,e){t.option={},t.option[pd]=1,t._componentsMap=E({series:[]}),t._componentsCount=E();var n,i,r=e.aria;R(r)&&null==r.enabled&&(r.enabled=!0),n=e,r=t._theme.option,i=n.color&&!n.colorLayer,O(r,function(t,e){"colorLayer"===e&&i||g.hasClass(e)||("object"==typeof t?n[e]=n[e]?d(n[e],t,!1):y(t):null==n[e]&&(n[e]=t))}),d(e,Gp,!1),t._mergeOption(e,null)})),l);function l(){return null!==cd&&cd.apply(this,arguments)||this}function fd(e,t,n){var i,r;return F(t)?(i=E(),O(t,function(t){null!=t&&null!=Co(t,null)&&i.set(t,!0)}),ut(n,function(t){return t&&i.get(t[e])})):(r=Co(t,null),ut(n,function(t){return t&&null!=r&&t[e]===r}))}function gd(t,e){return e.hasOwnProperty("subType")?ut(t,function(t){return t&&t.subType===e.subType}):t}function yd(t){var e=E();return t&&O(_o(t.replaceMerge),function(t){e.set(t,!0)}),{replaceMergeMainTypeMap:e}}at(dd,ld);function md(e){O(vd,function(t){this[t]=ct(e[t],e)},this)}var vd=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"],_d={},xd=(wd.prototype.create=function(n,i){var r=[];O(_d,function(t,e){t=t.create(n,i);r=r.concat(t||[])}),this._coordinateSystems=r},wd.prototype.update=function(e,n){O(this._coordinateSystems,function(t){t.update&&t.update(e,n)})},wd.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},wd.register=function(t,e){_d[t]=e},wd.get=function(t){return _d[t]},wd);function wd(){this._coordinateSystems=[]}var bd=/^(min|max)?(.+)$/,Sd=(Md.prototype.setOption=function(t,e,n){t&&(O(_o(t.series),function(t){t&&t.data&&gt(t.data)&&kt(t.data)}),O(_o(t.dataset),function(t){t&&t.source&&gt(t.source)&&kt(t.source)})),t=y(t);var i=this._optionBackup,t=function(t,n,i){var e,r,o=[],a=t.baseOption,s=t.timeline,l=t.options,u=t.media,h=!!t.media,c=!!(l||s||a&&a.timeline);a?(r=a).timeline||(r.timeline=s):((c||h)&&(t.options=t.media=null),r=t);h&&F(u)&&O(u,function(t){t&&t.option&&(t.query?o.push(t):e=e||t)});function p(e){O(n,function(t){t(e,i)})}return p(r),O(l,p),O(o,function(t){return p(t.option)}),{baseOption:r,timelineOptions:l||[],mediaDefault:e,mediaList:o}}(t,e,!i);this._newBaseOption=t.baseOption,i?(t.timelineOptions.length&&(i.timelineOptions=t.timelineOptions),t.mediaList.length&&(i.mediaList=t.mediaList),t.mediaDefault&&(i.mediaDefault=t.mediaDefault)):this._optionBackup=t},Md.prototype.mountOption=function(t){var e=this._optionBackup;return this._timelineOptions=e.timelineOptions,this._mediaList=e.mediaList,this._mediaDefault=e.mediaDefault,this._currentMediaIndices=[],y(t?e.baseOption:this._newBaseOption)},Md.prototype.getTimelineOption=function(t){var e,n=this._timelineOptions;return e=n.length&&(t=t.getComponent("timeline"))?y(n[t.getCurrentIndex()]):e},Md.prototype.getMediaOption=function(t){var e=this._api.getWidth(),n=this._api.getHeight(),i=this._mediaList,r=this._mediaDefault,o=[],a=[];if(i.length||r){for(var s,l,u=0,h=i.length;u<h;u++)!function(t,e,n){var i={width:e,height:n,aspectratio:e/n},r=!0;return O(t,function(t,e){var n,e=e.match(bd);e&&e[1]&&e[2]&&(n=e[1],e=e[2].toLowerCase(),e=i[e],t=t,("min"===(n=n)?t<=e:"max"===n?e<=t:e===t)||(r=!1))}),r}(i[u].query,e,n)||o.push(u);(o=!o.length&&r?[-1]:o).length&&(s=o,l=this._currentMediaIndices,s.join(",")!==l.join(","))&&(a=B(o,function(t){return y((-1===t?r:i[t]).option)})),this._currentMediaIndices=o}return a},Md);function Md(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}var Td=O,Cd=R,Id=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function kd(t){var e=t&&t.itemStyle;if(e)for(var n=0,i=Id.length;n<i;n++){var r=Id[n],o=e.normal,a=e.emphasis;o&&o[r]&&(t[r]=t[r]||{},t[r].normal?d(t[r].normal,o[r]):t[r].normal=o[r],o[r]=null),a&&a[r]&&(t[r]=t[r]||{},t[r].emphasis?d(t[r].emphasis,a[r]):t[r].emphasis=a[r],a[r]=null)}}function Dd(t,e,n){var i,r;t&&t[e]&&(t[e].normal||t[e].emphasis)&&(i=t[e].normal,r=t[e].emphasis,i&&(n?(t[e].normal=t[e].emphasis=null,z(t[e],i)):t[e]=i),r)&&(t.emphasis=t.emphasis||{},(t.emphasis[e]=r).focus&&(t.emphasis.focus=r.focus),r.blurScope)&&(t.emphasis.blurScope=r.blurScope)}function Ad(t){Dd(t,"itemStyle"),Dd(t,"lineStyle"),Dd(t,"areaStyle"),Dd(t,"label"),Dd(t,"labelLine"),Dd(t,"upperLabel"),Dd(t,"edgeLabel")}function Pd(t,e){var n=Cd(t)&&t[e],i=Cd(n)&&n.textStyle;if(i)for(var r=0,o=wo.length;r<o;r++){var a=wo[r];i.hasOwnProperty(a)&&(n[a]=i[a])}}function Ld(t){t&&(Ad(t),Pd(t,"label"),t.emphasis)&&Pd(t.emphasis,"label")}function Od(t){return F(t)?t:t?[t]:[]}function Rd(t){return(F(t)?t[0]:t)||{}}function Nd(e,t){Td(Od(e.series),function(t){if(Cd(t))if(Cd(t)){kd(t),Ad(t),Pd(t,"label"),Pd(t,"upperLabel"),Pd(t,"edgeLabel"),t.emphasis&&(Pd(t.emphasis,"label"),Pd(t.emphasis,"upperLabel"),Pd(t.emphasis,"edgeLabel"));var e=t.markPoint,n=(e&&(kd(e),Ld(e)),t.markLine),i=(n&&(kd(n),Ld(n)),t.markArea),r=(i&&Ld(i),t.data);if("graph"===t.type){var r=r||t.nodes,o=t.links||t.edges;if(o&&!gt(o))for(var a=0;a<o.length;a++)Ld(o[a]);O(t.categories,function(t){Ad(t)})}if(r&&!gt(r))for(a=0;a<r.length;a++)Ld(r[a]);if((e=t.markPoint)&&e.data)for(var s=e.data,a=0;a<s.length;a++)Ld(s[a]);if((n=t.markLine)&&n.data)for(var l=n.data,a=0;a<l.length;a++)F(l[a])?(Ld(l[a][0]),Ld(l[a][1])):Ld(l[a]);"gauge"===t.type?(Pd(t,"axisLabel"),Pd(t,"title"),Pd(t,"detail")):"treemap"===t.type?(Dd(t.breadcrumb,"itemStyle"),O(t.levels,function(t){Ad(t)})):"tree"===t.type&&Ad(t.leaves)}});var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];t&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),Td(n,function(t){Td(Od(e[t]),function(t){t&&(Pd(t,"axisLabel"),Pd(t.axisPointer,"label"))})}),Td(Od(e.parallel),function(t){t=t&&t.parallelAxisDefault;Pd(t,"axisLabel"),Pd(t&&t.axisPointer,"label")}),Td(Od(e.calendar),function(t){Dd(t,"itemStyle"),Pd(t,"dayLabel"),Pd(t,"monthLabel"),Pd(t,"yearLabel")}),Td(Od(e.radar),function(t){Pd(t,"name"),t.name&&null==t.axisName&&(t.axisName=t.name,delete t.name),null!=t.nameGap&&null==t.axisNameGap&&(t.axisNameGap=t.nameGap,delete t.nameGap)}),Td(Od(e.geo),function(t){Cd(t)&&(Ld(t),Td(Od(t.regions),function(t){Ld(t)}))}),Td(Od(e.timeline),function(t){Ld(t),Dd(t,"label"),Dd(t,"itemStyle"),Dd(t,"controlStyle",!0);t=t.data;F(t)&&O(t,function(t){R(t)&&(Dd(t,"label"),Dd(t,"itemStyle"))})}),Td(Od(e.toolbox),function(t){Dd(t,"iconStyle"),Td(t.feature,function(t){Dd(t,"iconStyle")})}),Pd(Rd(e.axisPointer),"label"),Pd(Rd(e.tooltip).axisPointer,"label")}function Ed(e){e&&O(zd,function(t){t[0]in e&&!(t[1]in e)&&(e[t[1]]=e[t[0]])})}var zd=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],Bd=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],Fd=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function Vd(t){var e=t&&t.itemStyle;if(e)for(var n=0;n<Fd.length;n++){var i=Fd[n][1],r=Fd[n][0];null!=e[i]&&(e[r]=e[i])}}function Hd(t){t&&"edge"===t.alignTo&&null!=t.margin&&null==t.edgeDistance&&(t.edgeDistance=t.margin)}function Gd(t){t&&t.downplay&&!t.blur&&(t.blur=t.downplay)}function Wd(e,t){Nd(e,t),e.series=_o(e.series),O(e.series,function(t){if(R(t)){var e,n=t.type;if("line"===n)null!=t.clipOverflow&&(t.clip=t.clipOverflow);else if("pie"===n||"gauge"===n){if(null!=t.clockWise&&(t.clockwise=t.clockWise),Hd(t.label),(e=t.data)&&!gt(e))for(var i=0;i<e.length;i++)Hd(e[i]);null!=t.hoverOffset&&(t.emphasis=t.emphasis||{},t.emphasis.scaleSize=null)&&(t.emphasis.scaleSize=t.hoverOffset)}else if("gauge"===n){var r=function(t,e){for(var n=e.split(","),i=t,r=0;r<n.length&&null!=(i=i&&i[n[r]]);r++);return i}(t,"pointer.color");if(null!=r){var o=t;var a="itemStyle.color";var s=void 0;for(var l,u=a.split(","),h=o,c=0;c<u.length-1;c++)null==h[l=u[c]]&&(h[l]={}),h=h[l];!s&&null!=h[u[c]]||(h[u[c]]=r)}}else if("bar"===n){if(Vd(t),Vd(t.backgroundStyle),Vd(t.emphasis),(e=t.data)&&!gt(e))for(i=0;i<e.length;i++)"object"==typeof e[i]&&(Vd(e[i]),Vd(e[i]&&e[i].emphasis))}else"sunburst"===n?((a=t.highlightPolicy)&&(t.emphasis=t.emphasis||{},t.emphasis.focus||(t.emphasis.focus=a)),Gd(t),function t(e,n){if(e)for(var i=0;i<e.length;i++)n(e[i]),e[i]&&t(e[i].children,n)}(t.data,Gd)):"graph"===n||"sankey"===n?(o=t)&&null!=o.focusNodeAdjacency&&(o.emphasis=o.emphasis||{},null==o.emphasis.focus)&&(o.emphasis.focus="adjacency"):"map"===n&&(t.mapType&&!t.map&&(t.map=t.mapType),t.mapLocation)&&z(t,t.mapLocation);null!=t.hoverAnimation&&(t.emphasis=t.emphasis||{},t.emphasis)&&null==t.emphasis.scale&&(t.emphasis.scale=t.hoverAnimation),Ed(t)}}),e.dataRange&&(e.visualMap=e.dataRange),O(Bd,function(t){t=e[t];t&&O(t=F(t)?t:[t],function(t){Ed(t)})})}function Ud(_){O(_,function(p,d){var f=[],g=[NaN,NaN],t=[p.stackResultDimension,p.stackedOverDimension],y=p.data,m=p.isStackedByIndex,v=p.seriesModel.get("stackStrategy")||"samesign";y.modify(t,function(t,e,n){var i,r,o=y.get(p.stackedDimension,n);if(isNaN(o))return g;m?r=y.getRawIndex(n):i=y.get(p.stackedByDimension,n);for(var a,s,l,u=NaN,h=d-1;0<=h;h--){var c=_[h];if(0<=(r=m?r:c.data.rawIndexOf(c.stackedByDimension,i))){c=c.data.getByRawIndex(c.stackResultDimension,r);if("all"===v||"positive"===v&&0<c||"negative"===v&&c<0||"samesign"===v&&0<=o&&0<c||"samesign"===v&&o<=0&&c<0){a=o,s=c,l=void 0,l=Math.max(no(a),no(s)),a+=s,o=Qr<l?a:eo(a,l),u=c;break}}}return f[0]=o,f[1]=u,f})})}var Xd,Yd,qd=function(t){this.data=t.data||(t.sourceFormat===qp?{}:[]),this.sourceFormat=t.sourceFormat||jp,this.seriesLayoutBy=t.seriesLayoutBy||Kp,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var e=this.dimensionsDefine=t.dimensionsDefine;if(e)for(var n=0;n<e.length;n++){var i=e[n];null==i.type&&nd(this,n)===Qp.Must&&(i.type="ordinal")}};function Zd(t){return t instanceof qd}function jd(t,e,n){n=n||$d(t);var i=e.seriesLayoutBy,r=function(t,e,n,i,r){var o,a;if(!t)return{dimensionsDefine:Qd(r),startIndex:a,dimensionsDetectedCount:o};{var s;e===Xp?(s=t,"auto"===i||null==i?Jd(function(t){null!=t&&"-"!==t&&(V(t)?null==a&&(a=1):a=0)},n,s,10):a=H(i)?i:i?1:0,r||1!==a||(r=[],Jd(function(t,e){r[e]=null!=t?t+"":""},n,s,1/0)),o=r?r.length:n===$p?s.length:s[0]?s[0].length:null):e===Yp?r=r||function(t){var e,n=0;for(;n<t.length&&!(e=t[n++]););if(e)return ht(e)}(t):e===qp?r||(r=[],O(t,function(t,e){r.push(e)})):e===Up&&(i=bo(t[0]),o=F(i)&&i.length||1)}return{startIndex:a,dimensionsDefine:Qd(r),dimensionsDetectedCount:o}}(t,n,i,e.sourceHeader,e.dimensions);return new qd({data:t,sourceFormat:n,seriesLayoutBy:i,dimensionsDefine:r.dimensionsDefine,startIndex:r.startIndex,dimensionsDetectedCount:r.dimensionsDetectedCount,metaRawOption:y(e)})}function Kd(t){return new qd({data:t,sourceFormat:gt(t)?Zp:Up})}function $d(t){var e=jp;if(gt(t))e=Zp;else if(F(t)){0===t.length&&(e=Xp);for(var n=0,i=t.length;n<i;n++){var r=t[n];if(null!=r){if(F(r)||gt(r)){e=Xp;break}if(R(r)){e=Yp;break}}}}else if(R(t))for(var o in t)if(Bt(t,o)&&st(t[o])){e=qp;break}return e}function Qd(t){var i;if(t)return i=E(),B(t,function(t,e){var n,t={name:(t=R(t)?t:{name:t}).name,displayName:t.displayName,type:t.type};return null!=t.name&&(t.name+="",null==t.displayName&&(t.displayName=t.name),(n=i.get(t.name))?t.name+="-"+n.count++:i.set(t.name,{count:1})),t})}function Jd(t,e,n,i){if(e===$p)for(var r=0;r<n.length&&r<i;r++)t(n[r]?n[r][0]:null,r);else for(var o=n[0]||[],r=0;r<o.length&&r<i;r++)t(o[r],r)}function tf(t){t=t.sourceFormat;return t===Yp||t===qp}af.prototype.getSource=function(){return this._source},af.prototype.count=function(){return 0},af.prototype.getItem=function(t,e){},af.prototype.appendData=function(t){},af.prototype.clean=function(){},af.protoInitialize=((qh=af.prototype).pure=!1,void(qh.persistent=!0)),af.internalField=(Yd=function(t,e,n){var i,r=n.sourceFormat,o=n.seriesLayoutBy,a=n.startIndex,n=n.dimensionsDefine;L(t,Xd[mf(r,o)]),r===Zp?(t.getItem=ef,t.count=rf,t.fillStorage=nf):(i=hf(r,o),t.getItem=ct(i,null,e,a,n),i=df(r,o),t.count=ct(i,null,e,a,n))},ef=function(t,e){t-=this._offset,e=e||[];for(var n=this._data,i=this._dimSize,r=i*t,o=0;o<i;o++)e[o]=n[r+o];return e},nf=function(t,e,n,i){for(var r=this._data,o=this._dimSize,a=0;a<o;a++){for(var s=i[a],l=null==s[0]?1/0:s[0],u=null==s[1]?-1/0:s[1],h=e-t,c=n[a],p=0;p<h;p++){var d=r[p*o+a];(c[t+p]=d)<l&&(l=d),u<d&&(u=d)}s[0]=l,s[1]=u}},rf=function(){return this._data?this._data.length/this._dimSize:0},(qh={})[Xp+"_"+Kp]={pure:!0,appendData:sf},qh[Xp+"_"+$p]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},qh[Yp]={pure:!0,appendData:sf},qh[qp]={pure:!0,appendData:function(t){var r=this._data;O(t,function(t,e){for(var n=r[e]||(r[e]=[]),i=0;i<(t||[]).length;i++)n.push(t[i])})}},qh[Up]={appendData:sf},qh[Zp]={persistent:!1,pure:!0,appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}},void(Xd=qh));var ef,nf,rf,of=af;function af(t,e){var t=Zd(t)?t:Kd(t),n=(this._source=t,this._data=t.data);t.sourceFormat===Zp&&(this._offset=0,this._dimSize=e,this._data=n),Yd(this,n,t)}function sf(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}function lf(t,e,n,i){return t[i]}(Zc={})[Xp+"_"+Kp]=function(t,e,n,i){return t[i+e]},Zc[Xp+"_"+$p]=function(t,e,n,i,r){i+=e;for(var o=r||[],a=t,s=0;s<a.length;s++){var l=a[s];o[s]=l?l[i]:null}return o},Zc[Yp]=lf,Zc[qp]=function(t,e,n,i,r){for(var o=r||[],a=0;a<n.length;a++){var s=t[n[a].name];o[a]=s?s[i]:null}return o},Zc[Up]=lf;var uf=Zc;function hf(t,e){return uf[mf(t,e)]}function cf(t,e,n){return t.length}(Tc={})[Xp+"_"+Kp]=function(t,e,n){return Math.max(0,t.length-e)},Tc[Xp+"_"+$p]=function(t,e,n){t=t[0];return t?Math.max(0,t.length-e):0},Tc[Yp]=cf,Tc[qp]=function(t,e,n){t=t[n[0].name];return t?t.length:0},Tc[Up]=cf;var pf=Tc;function df(t,e){return pf[mf(t,e)]}function ff(t,e,n){return t[e]}($o={})[Xp]=ff,$o[Yp]=function(t,e,n){return t[n]},$o[qp]=ff,$o[Up]=function(t,e,n){t=bo(t);return t instanceof Array?t[e]:t},$o[Zp]=ff;var gf=$o;function yf(t){return gf[t]}function mf(t,e){return t===Xp?t+"_"+e:t}function vf(t,e,n){if(t){var i,r,e=t.getRawDataItem(e);if(null!=e)return i=(r=t.getStore()).getSource().sourceFormat,null!=n?(t=t.getDimensionIndex(n),n=r.getDimensionProperty(t),yf(i)(e,t,n)):(r=e,i===Up?bo(e):r)}}var _f=/\{@(.+?)\}/g,Dc=(xf.prototype.getDataParams=function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),r=n.getRawIndex(t),o=n.getName(t),a=n.getRawDataItem(t),s=n.getItemVisual(t,"style"),t=s&&s[n.getItemVisual(t,"drawType")||"fill"],s=s&&s.stroke,l=this.mainType,u="series"===l,n=n.userOutput&&n.userOutput.get();return{componentType:l,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:u?this.subType:null,seriesIndex:this.seriesIndex,seriesId:u?this.id:null,seriesName:u?this.name:null,name:o,dataIndex:r,data:a,dataType:e,value:i,color:t,borderColor:s,dimensionNames:n?n.fullDimensions:null,encode:n?n.encode:null,$vars:["seriesName","name","value"]}},xf.prototype.getFormattedLabel=function(i,t,e,n,r,o){t=t||"normal";var a=this.getData(e),e=this.getDataParams(i,e);return o&&(e.value=o.interpolatedValue),null!=n&&F(e.value)&&(e.value=e.value[n]),k(r=r||a.getItemModel(i).get("normal"===t?["label","formatter"]:[t,"label","formatter"]))?(e.status=t,e.dimensionIndex=n,r(e)):V(r)?bp(r,e).replace(_f,function(t,e){var n=e.length,n=("["===e.charAt(0)&&"]"===e.charAt(n-1)&&(e=+e.slice(1,n-1)),vf(a,i,e));return null!=(n=o&&F(o.interpolatedValue)&&0<=(e=a.getDimensionIndex(e))?o.interpolatedValue[e]:n)?n+"":""}):void 0},xf.prototype.getRawValue=function(t,e){return vf(this.getData(e),t)},xf.prototype.formatTooltip=function(t,e,n){},xf);function xf(){}function wf(t){var e,n;return R(t)?t.type&&(n=t):e=t,{text:e,frag:n}}function bf(t){return new Sf(t)}Mf.prototype.perform=function(t){var e,n,i=this._upstream,r=t&&t.skip,o=(this._dirty&&i&&((o=this.context).data=o.outputData=i.context.outputData),this.__pipeline&&(this.__pipeline.currentTask=this),this._plan&&!r&&(e=this._plan(this.context)),u(this._modBy)),a=this._modDataCount||0,s=u(t&&t.modBy),l=t&&t.modDataCount||0;function u(t){return t=1<=t?t:1}o===s&&a===l||(e="reset"),!this._dirty&&"reset"!==e||(this._dirty=!1,n=this._doReset(r)),this._modBy=s,this._modDataCount=l;o=t&&t.step;if(this._dueEnd=i?i._outputDueEnd:this._count?this._count(this.context):1/0,this._progress){var h=this._dueIndex,c=Math.min(null!=o?this._dueIndex+o:1/0,this._dueEnd);if(!r&&(n||h<c)){var p=this._progress;if(F(p))for(var d=0;d<p.length;d++)this._doProgress(p[d],h,c,s,l);else this._doProgress(p,h,c,s,l)}this._dueIndex=c;a=null!=this._settedOutputEnd?this._settedOutputEnd:c;this._outputDueEnd=a}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()},Mf.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},Mf.prototype._doProgress=function(t,e,n,i,r){Pf.reset(e,n,i,r),this._callingProgress=t,this._callingProgress({start:e,end:n,count:n-e,next:Pf.next},this.context)},Mf.prototype._doReset=function(t){this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null,!t&&this._reset&&((e=this._reset(this.context))&&e.progress&&(n=e.forceFirstProgress,e=e.progress),F(e))&&!e.length&&(e=null),this._progress=e,this._modBy=this._modDataCount=null;var e,n,t=this._downstream;return t&&t.dirty(),n},Mf.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},Mf.prototype.pipe=function(t){this._downstream===t&&!this._dirty||((this._downstream=t)._upstream=this,t.dirty())},Mf.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},Mf.prototype.getUpstream=function(){return this._upstream},Mf.prototype.getDownstream=function(){return this._downstream},Mf.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t};var Sf=Mf;function Mf(t){this._reset=(t=t||{}).reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}var Tf,Cf,If,kf,Df,Af,Pf=Af={reset:function(t,e,n,i){Cf=t,Tf=e,If=n,kf=i,Df=Math.ceil(kf/If),Af.next=1<If&&0<kf?Of:Lf}};function Lf(){return Cf<Tf?Cf++:null}function Of(){var t=Cf%Df*If+Math.ceil(Cf/Df),t=Tf<=Cf?null:t<kf?t:Cf;return Cf++,t}function Rf(t,e){e=e&&e.type;return"ordinal"===e?t:null==(t="time"!==e||H(t)||null==t||"-"===t?t:+lo(t))||""===t?NaN:Number(t)}var Nf=E({number:function(t){return parseFloat(t)},time:function(t){return+lo(t)},trim:function(t){return V(t)?Ct(t):t}});function Ef(t){return Nf.get(t)}var zf={lt:function(t,e){return t<e},lte:function(t,e){return t<=e},gt:function(t,e){return e<t},gte:function(t,e){return e<=t}},Bf=(Ff.prototype.evaluate=function(t){return H(t)?this._opFn(t,this._rvalFloat):this._opFn(po(t),this._rvalFloat)},Ff);function Ff(t,e){H(e)||f(""),this._opFn=zf[t],this._rvalFloat=po(e)}Hf.prototype.evaluate=function(t,e){var n=H(t)?t:po(t),i=H(e)?e:po(e),r=isNaN(n),o=isNaN(i);return r&&(n=this._incomparable),o&&(i=this._incomparable),r&&o&&(r=V(t),o=V(e),r&&(n=o?t:0),o)&&(i=r?e:0),n<i?this._resultLT:i<n?-this._resultLT:0};var Vf=Hf;function Hf(t,e){t="desc"===t;this._resultLT=t?1:-1,this._incomparable="min"===(e=null==e?t?"min":"max":e)?-1/0:1/0}Wf.prototype.evaluate=function(t){var e,n=t===this._rval;return n||(e=typeof t)===this._rvalTypeof||"number"!=e&&"number"!==this._rvalTypeof||(n=po(t)===this._rvalFloat),this._isEQ?n:!n};var Gf=Wf;function Wf(t,e){this._rval=e,this._isEQ=t,this._rvalTypeof=typeof e,this._rvalFloat=po(e)}Xf.prototype.getRawData=function(){throw new Error("not supported")},Xf.prototype.getRawDataItem=function(t){throw new Error("not supported")},Xf.prototype.cloneRawData=function(){},Xf.prototype.getDimensionInfo=function(t){},Xf.prototype.cloneAllDimensionInfo=function(){},Xf.prototype.count=function(){},Xf.prototype.retrieveValue=function(t,e){},Xf.prototype.retrieveValueFromItem=function(t,e){},Xf.prototype.convertValue=Rf;var Uf=Xf;function Xf(){}function Yf(t){return Qf(t.sourceFormat)||f(""),t.data}function qf(t){var e=t.sourceFormat,n=t.data;if(Qf(e)||f(""),e===Xp){for(var i=[],r=0,o=n.length;r<o;r++)i.push(n[r].slice());return i}if(e===Yp){for(i=[],r=0,o=n.length;r<o;r++)i.push(L({},n[r]));return i}}function Zf(t,e,n){if(null!=n)return H(n)||!isNaN(n)&&!Bt(e,n)?t[n]:Bt(e,n)?e[n]:void 0}function jf(t){return y(t)}var Kf=E();function $f(t,e){var n=_o(t),t=n.length;t||f("");for(var i=0,r=t;i<r;i++)e=function(t,i){i.length||f("");R(t)||f("");var e=t.type,d=Kf.get(e);d||f("");e=B(i,function(t){var e=t,t=d,n=new Uf,i=e.data,r=n.sourceFormat=e.sourceFormat,o=e.startIndex,a=(e.seriesLayoutBy!==Kp&&f(""),[]),s={};if(h=e.dimensionsDefine)O(h,function(t,e){var n=t.name,e={index:e,name:n,displayName:t.displayName};a.push(e),null!=n&&(Bt(s,n)&&f(""),s[n]=e)});else for(var l=0;l<e.dimensionsDetectedCount;l++)a.push({index:l});var u=hf(r,Kp),h=(t.__isBuiltIn&&(n.getRawDataItem=function(t){return u(i,o,a,t)},n.getRawData=ct(Yf,null,e)),n.cloneRawData=ct(qf,null,e),df(r,Kp)),c=(n.count=ct(h,null,i,o,a),yf(r)),p=(n.retrieveValue=function(t,e){t=u(i,o,a,t);return p(t,e)},n.retrieveValueFromItem=function(t,e){var n;return null!=t&&(n=a[e])?c(t,e,n.name):void 0});return n.getDimensionInfo=ct(Zf,null,a,s),n.cloneAllDimensionInfo=ct(jf,null,a),n});return B(_o(d.transform({upstream:e[0],upstreamList:e,config:y(t.config)})),function(t,e){R(t)||f(""),t.data||f("");Qf($d(t.data))||f("");var n=i[0],e=n&&0===e&&!t.dimensions?((e=n.startIndex)&&(t.data=n.data.slice(0,e).concat(t.data)),{seriesLayoutBy:Kp,sourceHeader:e,dimensions:n.metaRawOption.dimensions}):{seriesLayoutBy:Kp,sourceHeader:0,dimensions:t.dimensions};return jd(t.data,e,null)})}(n[i],e),i!==r-1&&(e.length=Math.max(e.length,1));return e}function Qf(t){return t===Xp||t===Yp}var Jf,qh="undefined",tg=typeof Uint32Array==qh?Array:Uint32Array,eg=typeof Uint16Array==qh?Array:Uint16Array,ng=typeof Int32Array==qh?Array:Int32Array,Zc=typeof Float64Array==qh?Array:Float64Array,ig={float:Zc,int:ng,ordinal:Array,number:Array,time:Zc};function rg(t){return 65535<t?tg:eg}function og(){return[1/0,-1/0]}function ag(t,e,n,i,r){n=ig[n||"float"];if(r){var o=t[e],a=o&&o.length;if(a!==i){for(var s=new n(i),l=0;l<a;l++)s[l]=o[l];t[e]=s}}else t[e]=new n(i)}h.prototype.initData=function(t,e,n){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var i=t.getSource(),r=this.defaultDimValueGetter=Jf[i.sourceFormat];this._dimValueGetter=n||r,this._rawExtent=[],tf(i),this._dimensions=B(e,function(t){return{type:t.type,property:t.property}}),this._initDataFromProvider(0,t.count())},h.prototype.getProvider=function(){return this._provider},h.prototype.getSource=function(){return this._provider.getSource()},h.prototype.ensureCalculationDimension=function(t,e){var n=this._calcDimNameToIdx,i=this._dimensions,r=n.get(t);if(null!=r){if(i[r].type===e)return r}else r=i.length;return i[r]={type:e},n.set(t,r),this._chunks[r]=new ig[e||"float"](this._rawCount),this._rawExtent[r]=og(),r},h.prototype.collectOrdinalMeta=function(t,e){for(var n=this._chunks[t],i=this._dimensions[t],r=this._rawExtent,o=i.ordinalOffset||0,a=n.length,s=(0===o&&(r[t]=og()),r[t]),l=o;l<a;l++){var u=n[l]=e.parseAndCollect(n[l]);isNaN(u)||(s[0]=Math.min(u,s[0]),s[1]=Math.max(u,s[1]))}i.ordinalMeta=e,i.ordinalOffset=a,i.type="ordinal"},h.prototype.getOrdinalMeta=function(t){return this._dimensions[t].ordinalMeta},h.prototype.getDimensionProperty=function(t){t=this._dimensions[t];return t&&t.property},h.prototype.appendData=function(t){var e=this._provider,n=this.count(),t=(e.appendData(t),e.count());return e.persistent||(t+=n),n<t&&this._initDataFromProvider(n,t,!0),[n,t]},h.prototype.appendValues=function(t,e){for(var n=this._chunks,i=this._dimensions,r=i.length,o=this._rawExtent,a=this.count(),s=a+Math.max(t.length,e||0),l=0;l<r;l++)ag(n,l,(d=i[l]).type,s,!0);for(var u=[],h=a;h<s;h++)for(var c=h-a,p=0;p<r;p++){var d=i[p],f=Jf.arrayRows.call(this,t[c]||u,d.property,c,p),g=(n[p][h]=f,o[p]);f<g[0]&&(g[0]=f),f>g[1]&&(g[1]=f)}return{start:a,end:this._rawCount=this._count=s}},h.prototype._initDataFromProvider=function(t,e,n){for(var i=this._provider,r=this._chunks,o=this._dimensions,a=o.length,s=this._rawExtent,l=B(o,function(t){return t.property}),u=0;u<a;u++){var h=o[u];s[u]||(s[u]=og()),ag(r,u,h.type,e,n)}if(i.fillStorage)i.fillStorage(t,e,r,s);else for(var c=[],p=t;p<e;p++)for(var c=i.getItem(p,c),d=0;d<a;d++){var f=r[d],g=this._dimValueGetter(c,l[d],p,d),f=(f[p]=g,s[d]);g<f[0]&&(f[0]=g),g>f[1]&&(f[1]=g)}!i.persistent&&i.clean&&i.clean(),this._rawCount=this._count=e,this._extent=[]},h.prototype.count=function(){return this._count},h.prototype.get=function(t,e){return 0<=e&&e<this._count&&(t=this._chunks[t])?t[this.getRawIndex(e)]:NaN},h.prototype.getValues=function(t,e){var n=[],i=[];if(null==e){e=t,t=[];for(var r=0;r<this._dimensions.length;r++)i.push(r)}else i=t;for(var r=0,o=i.length;r<o;r++)n.push(this.get(i[r],e));return n},h.prototype.getByRawIndex=function(t,e){return 0<=e&&e<this._rawCount&&(t=this._chunks[t])?t[e]:NaN},h.prototype.getSum=function(t){var e=0;if(this._chunks[t])for(var n=0,i=this.count();n<i;n++){var r=this.get(t,n);isNaN(r)||(e+=r)}return e},h.prototype.getMedian=function(t){var e=[],t=(this.each([t],function(t){isNaN(t)||e.push(t)}),e.sort(function(t,e){return t-e})),n=this.count();return 0===n?0:n%2==1?t[(n-1)/2]:(t[n/2]+t[n/2-1])/2},h.prototype.indexOfRawIndex=function(t){if(!(t>=this._rawCount||t<0)){if(!this._indices)return t;var e=this._indices,n=e[t];if(null!=n&&n<this._count&&n===t)return t;for(var i=0,r=this._count-1;i<=r;){var o=(i+r)/2|0;if(e[o]<t)i=1+o;else{if(!(e[o]>t))return o;r=o-1}}}return-1},h.prototype.indicesOfNearest=function(t,e,n){var i=this._chunks[t],r=[];if(i){null==n&&(n=1/0);for(var o=1/0,a=-1,s=0,l=0,u=this.count();l<u;l++){var h=e-i[this.getRawIndex(l)],c=Math.abs(h);c<=n&&((c<o||c===o&&0<=h&&a<0)&&(o=c,a=h,s=0),h===a)&&(r[s++]=l)}r.length=s}return r},h.prototype.getIndices=function(){var t=this._indices;if(t){var e=t.constructor,n=this._count;if(e===Array)for(var i=new e(n),r=0;r<n;r++)i[r]=t[r];else i=new e(t.buffer,0,n)}else{i=new(e=rg(this._rawCount))(this.count());for(r=0;r<i.length;r++)i[r]=r}return i},h.prototype.filter=function(t,e){if(!this._count)return this;for(var n=this.clone(),i=n.count(),r=new(rg(n._rawCount))(i),o=[],a=t.length,s=0,l=t[0],u=n._chunks,h=0;h<i;h++){var c=void 0,p=n.getRawIndex(h);if(0===a)c=e(h);else if(1===a)c=e(u[l][p],h);else{for(var d=0;d<a;d++)o[d]=u[t[d]][p];o[d]=h,c=e.apply(null,o)}c&&(r[s++]=p)}return s<i&&(n._indices=r),n._count=s,n._extent=[],n._updateGetRawIdx(),n},h.prototype.selectRange=function(t){var e=this.clone(),n=e._count;if(!n)return this;var i=ht(t),r=i.length;if(!r)return this;var o=e.count(),a=new(rg(e._rawCount))(o),s=0,l=i[0],u=t[l][0],h=t[l][1],c=e._chunks,l=!1;if(!e._indices){var p=0;if(1===r){for(var d=c[i[0]],f=0;f<n;f++)(u<=(v=d[f])&&v<=h||isNaN(v))&&(a[s++]=p),p++;l=!0}else if(2===r){for(var d=c[i[0]],g=c[i[1]],y=t[i[1]][0],m=t[i[1]][1],f=0;f<n;f++){var v=d[f],_=g[f];(u<=v&&v<=h||isNaN(v))&&(y<=_&&_<=m||isNaN(_))&&(a[s++]=p),p++}l=!0}}if(!l)if(1===r)for(f=0;f<o;f++){var x=e.getRawIndex(f);(u<=(v=c[i[0]][x])&&v<=h||isNaN(v))&&(a[s++]=x)}else for(f=0;f<o;f++){for(var w=!0,x=e.getRawIndex(f),b=0;b<r;b++){var S=i[b];((v=c[S][x])<t[S][0]||v>t[S][1])&&(w=!1)}w&&(a[s++]=e.getRawIndex(f))}return s<o&&(e._indices=a),e._count=s,e._extent=[],e._updateGetRawIdx(),e},h.prototype.map=function(t,e){var n=this.clone(t);return this._updateDims(n,t,e),n},h.prototype.modify=function(t,e){this._updateDims(this,t,e)},h.prototype._updateDims=function(t,e,n){for(var i=t._chunks,r=[],o=e.length,a=t.count(),s=[],l=t._rawExtent,u=0;u<e.length;u++)l[e[u]]=og();for(var h=0;h<a;h++){for(var c=t.getRawIndex(h),p=0;p<o;p++)s[p]=i[e[p]][c];s[o]=h;var d=n&&n.apply(null,s);if(null!=d){"object"!=typeof d&&(r[0]=d,d=r);for(u=0;u<d.length;u++){var f=e[u],g=d[u],y=l[f],f=i[f];f&&(f[c]=g),g<y[0]&&(y[0]=g),g>y[1]&&(y[1]=g)}}}},h.prototype.lttbDownSample=function(t,e){var n,i=this.clone([t],!0),r=i._chunks[t],o=this.count(),a=0,s=Math.floor(1/e),l=this.getRawIndex(0),u=new(rg(this._rawCount))(Math.min(2*(Math.ceil(o/s)+2),o));u[a++]=l;for(var h=1;h<o-1;h+=s){for(var c=Math.min(h+s,o-1),p=Math.min(h+2*s,o),d=(p+c)/2,f=0,g=c;g<p;g++){var y=r[M=this.getRawIndex(g)];isNaN(y)||(f+=y)}f/=p-c;for(var c=h,m=Math.min(h+s,o),v=h-1,_=r[l],x=-1,w=c,b=-1,S=0,g=c;g<m;g++){var M,y=r[M=this.getRawIndex(g)];isNaN(y)?(S++,b<0&&(b=M)):x<(n=Math.abs((v-d)*(y-_)-(v-g)*(f-_)))&&(x=n,w=M)}0<S&&S<m-c&&(u[a++]=Math.min(b,w),w=Math.max(b,w)),l=u[a++]=w}return u[a++]=this.getRawIndex(o-1),i._count=a,i._indices=u,i.getRawIndex=this._getRawIdx,i},h.prototype.minmaxDownSample=function(t,e){for(var n=this.clone([t],!0),i=n._chunks,r=Math.floor(1/e),o=i[t],a=this.count(),s=new(rg(this._rawCount))(2*Math.ceil(a/r)),l=0,u=0;u<a;u+=r){var h=u,c=o[this.getRawIndex(h)],p=u,d=o[this.getRawIndex(p)],f=r;a<u+r&&(f=a-u);for(var g=0;g<f;g++){var y=o[this.getRawIndex(u+g)];y<c&&(c=y,h=u+g),d<y&&(d=y,p=u+g)}var m=this.getRawIndex(h),v=this.getRawIndex(p);h<p?(s[l++]=m,s[l++]=v):(s[l++]=v,s[l++]=m)}return n._count=l,n._indices=s,n._updateGetRawIdx(),n},h.prototype.downSample=function(t,e,n,i){for(var r=this.clone([t],!0),o=r._chunks,a=[],s=Math.floor(1/e),l=o[t],u=this.count(),h=r._rawExtent[t]=og(),c=new(rg(this._rawCount))(Math.ceil(u/s)),p=0,d=0;d<u;d+=s){u-d<s&&(a.length=s=u-d);for(var f=0;f<s;f++){var g=this.getRawIndex(d+f);a[f]=l[g]}var y=n(a),m=this.getRawIndex(Math.min(d+i(a,y)||0,u-1));(l[m]=y)<h[0]&&(h[0]=y),y>h[1]&&(h[1]=y),c[p++]=m}return r._count=p,r._indices=c,r._updateGetRawIdx(),r},h.prototype.each=function(t,e){if(this._count)for(var n=t.length,i=this._chunks,r=0,o=this.count();r<o;r++){var a=this.getRawIndex(r);switch(n){case 0:e(r);break;case 1:e(i[t[0]][a],r);break;case 2:e(i[t[0]][a],i[t[1]][a],r);break;default:for(var s=0,l=[];s<n;s++)l[s]=i[t[s]][a];l[s]=r,e.apply(null,l)}}},h.prototype.getDataExtent=function(t){var e=this._chunks[t],n=og();if(!e)return n;var i=this.count();if(!this._indices)return this._rawExtent[t].slice();if(r=this._extent[t])return r.slice();for(var r,o=(r=n)[0],a=r[1],s=0;s<i;s++){var l=e[this.getRawIndex(s)];l<o&&(o=l),a<l&&(a=l)}return this._extent[t]=r=[o,a]},h.prototype.getRawDataItem=function(t){var e=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(e);for(var n=[],i=this._chunks,r=0;r<i.length;r++)n.push(i[r][e]);return n},h.prototype.clone=function(t,e){var n,i,r=new h,o=this._chunks,a=t&&lt(t,function(t,e){return t[e]=!0,t},{});if(a)for(var s=0;s<o.length;s++)r._chunks[s]=a[s]?(n=o[s],i=void 0,(i=n.constructor)===Array?n.slice():new i(n)):o[s];else r._chunks=o;return this._copyCommonProps(r),e||(r._indices=this._cloneIndices()),r._updateGetRawIdx(),r},h.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=y(this._extent),t._rawExtent=y(this._rawExtent)},h.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,e=void 0;if(t===Array)for(var n=this._indices.length,e=new t(n),i=0;i<n;i++)e[i]=this._indices[i];else e=new t(this._indices);return e}return null},h.prototype._getRawIdxIdentity=function(t){return t},h.prototype._getRawIdx=function(t){return t<this._count&&0<=t?this._indices[t]:-1},h.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},h.internalField=void(Jf={arrayRows:lg,objectRows:function(t,e,n,i){return Rf(t[e],this._dimensions[i])},keyedColumns:lg,original:function(t,e,n,i){t=t&&(null==t.value?t:t.value);return Rf(t instanceof Array?t[i]:t,this._dimensions[i])},typedArray:function(t,e,n,i){return t[i]}});var sg=h;function h(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=E()}function lg(t,e,n,i){return Rf(t[i],this._dimensions[i])}hg.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},hg.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,9e10<this._versionSignBase&&(this._versionSignBase=0)},hg.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},hg.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},hg.prototype._createSource=function(){this._setLocalSource([],[]);var t,e,n,i,r,o,a,s=this._sourceHost,l=this._getUpstreamSourceManagers(),u=!!l.length;pg(s)?(i=s,r=t=o=void 0,e=u?((e=l[0]).prepareSource(),o=(r=e.getSource()).data,t=r.sourceFormat,[e._getVersionSign()]):(t=gt(o=i.get("data",!0))?Zp:Up,[]),i=this._getSourceMetaRawOption()||{},r=r&&r.metaRawOption||{},a=N(i.seriesLayoutBy,r.seriesLayoutBy)||null,n=N(i.sourceHeader,r.sourceHeader),i=N(i.dimensions,r.dimensions),r=a!==r.seriesLayoutBy||!!n!=!!r.sourceHeader||i?[jd(o,{seriesLayoutBy:a,sourceHeader:n,dimensions:i},t)]:[]):(o=s,e=u?(r=(a=this._applyTransform(l)).sourceList,a.upstreamSignList):(r=[jd(o.get("source",!0),this._getSourceMetaRawOption(),null)],[])),this._setLocalSource(r,e)},hg.prototype._applyTransform=function(t){var e,n=this._sourceHost,i=n.get("transform",!0),r=n.get("fromTransformResult",!0),o=(null!=r&&1!==t.length&&dg(""),[]),a=[];return O(t,function(t){t.prepareSource();var e=t.getSource(r||0);null==r||e||dg(""),o.push(e),a.push(t._getVersionSign())}),i?e=$f(i,o,n.componentIndex):null!=r&&(e=[new qd({data:(t=o[0]).data,sourceFormat:t.sourceFormat,seriesLayoutBy:t.seriesLayoutBy,dimensionsDefine:y(t.dimensionsDefine),startIndex:t.startIndex,dimensionsDetectedCount:t.dimensionsDetectedCount})]),{sourceList:e,upstreamSignList:a}},hg.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var n=t[e];if(n._isDirty()||this._upstreamSignList[e]!==n._getVersionSign())return!0}},hg.prototype.getSource=function(t){var e=this._sourceList[t=t||0];return e||(e=this._getUpstreamSourceManagers())[0]&&e[0].getSource(t)},hg.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},hg.prototype._innerGetDataStore=function(t,e,n){var i,r=this._storeList,o=r[0],r=(o=o||(r[0]={}))[n];return r||(i=this._getUpstreamSourceManagers()[0],pg(this._sourceHost)&&i?r=i._innerGetDataStore(t,e,n):(r=new sg).initData(new of(e,t.length),t),o[n]=r),r},hg.prototype._getUpstreamSourceManagers=function(){var t,e=this._sourceHost;return pg(e)?(t=ed(e))?[t.getSourceManager()]:[]:B((t=e).get("transform",!0)||t.get("fromTransformResult",!0)?Eo(t.ecModel,"dataset",{index:t.get("fromDatasetIndex",!0),id:t.get("fromDatasetId",!0)},No).models:[],function(t){return t.getSourceManager()})},hg.prototype._getSourceMetaRawOption=function(){var t,e,n,i=this._sourceHost;return pg(i)?(t=i.get("seriesLayoutBy",!0),e=i.get("sourceHeader",!0),n=i.get("dimensions",!0)):this._getUpstreamSourceManagers().length||(t=(i=i).get("seriesLayoutBy",!0),e=i.get("sourceHeader",!0),n=i.get("dimensions",!0)),{seriesLayoutBy:t,sourceHeader:e,dimensions:n}};var ug=hg;function hg(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}function cg(t){t.option.transform&&kt(t.option.transform)}function pg(t){return"series"===t.mainType}function dg(t){throw new Error(t)}var fg="line-height:1";function gg(t){t=t.lineHeight;return null==t?fg:"line-height:"+_e(t+"")+"px"}function yg(t,e){var n=t.color||"#6e7079",i=t.fontSize||12,r=t.fontWeight||"400",o=t.color||"#464646",a=t.fontSize||14,t=t.fontWeight||"900";return"html"===e?{nameStyle:"font-size:"+_e(i+"")+"px;color:"+_e(n)+";font-weight:"+_e(r+""),valueStyle:"font-size:"+_e(a+"")+"px;color:"+_e(o)+";font-weight:"+_e(t+"")}:{nameStyle:{fontSize:i,fill:n,fontWeight:r},valueStyle:{fontSize:a,fill:o,fontWeight:t}}}var mg=[0,10,20,30],vg=["","\n","\n\n","\n\n\n"];function _g(t,e){return e.type=t,e}function xg(t){return"section"===t.type}function wg(t){return xg(t)?bg:Sg}function bg(i,r,t,o){var n,e=r.noHeader,a=(l=function n(t){var i,e,r;return xg(t)?(i=0,e=t.blocks.length,r=1<e||0<e&&!t.noHeader,O(t.blocks,function(t){var e=n(t);i<=e&&(i=e+ +(r&&(!e||xg(t)&&!t.noHeader)))}),i):0}(r),{html:mg[l],richText:vg[l]}),s=[],l=r.blocks||[],u=(Tt(!l||F(l)),l=l||[],i.orderMode),h=(r.sortBlocks&&u&&(l=l.slice(),Bt(h={valueAsc:"asc",valueDesc:"desc"},u)?(n=new Vf(h[u],null),l.sort(function(t,e){return n.evaluate(t.sortParam,e.sortParam)})):"seriesDesc"===u&&l.reverse()),O(l,function(t,e){var n=r.valueFormatter,n=wg(t)(n?L(L({},i),{valueFormatter:n}):i,t,0<e?a.html:0,o);null!=n&&s.push(n)}),"richText"===i.renderMode?s.join(a.richText):Tg(o,s.join(""),e?t:a.html));return e?h:(u=_p(r.header,"ordinal",i.useUTC),l=yg(o,i.renderMode).nameStyle,e=gg(o),"richText"===i.renderMode?Cg(i,u,l)+a.richText+h:Tg(o,'<div style="'+l+";"+e+';">'+_e(u)+"</div>"+h,t))}function Sg(t,e,n,i){var r,o,a,s,l,u=t.renderMode,h=e.noName,c=e.noValue,p=!e.markerType,d=e.name,f=t.useUTC,g=e.valueFormatter||t.valueFormatter||function(t){return B(t=F(t)?t:[t],function(t,e){return _p(t,F(o)?o[e]:o,f)})};if(!h||!c)return r=p?"":t.markupStyleCreator.makeTooltipMarker(e.markerType,e.markerColor||"#333",u),d=h?"":_p(d,"ordinal",f),o=e.valueType,g=c?[]:g(e.value,e.dataIndex),e=!p||!h,a=!p&&h,l=yg(i,u),s=l.nameStyle,l=l.valueStyle,"richText"===u?(p?"":r)+(h?"":Cg(t,d,s))+(c?"":function(t,e,n,i,r){r=[r],i=i?10:20;return n&&r.push({padding:[0,0,0,i],align:"right"}),t.markupStyleCreator.wrapRichTextStyle(F(e)?e.join("  "):e,r)}(t,g,e,a,l)):Tg(i,(p?"":r)+(h?"":'<span style="'+s+";"+(!p?"margin-left:2px":"")+'">'+_e(d)+"</span>")+(c?"":function(t,e,n,i){n=n?"10px":"20px",e=e?"float:right;margin-left:"+n:"";return t=F(t)?t:[t],'<span style="'+e+";"+i+'">'+B(t,_e).join("&nbsp;&nbsp;")+"</span>"}(g,e,a,l)),n)}function Mg(t,e,n,i,r,o){if(t)return wg(t)({useUTC:r,renderMode:n,orderMode:i,markupStyleCreator:e,valueFormatter:t.valueFormatter},t,0,o)}function Tg(t,e,n){return'<div style="'+("margin: "+n+"px 0 0")+";"+gg(t)+';">'+e+'<div style="clear:both"></div></div>'}function Cg(t,e,n){return t.markupStyleCreator.wrapRichTextStyle(e,n)}function Ig(t,e){t=t.get("padding");return null!=t?t:"richText"===e?[8,10]:10}Dg.prototype._generateStyleName=function(){return"__EC_aUTo_"+this._nextStyleNameId++},Dg.prototype.makeTooltipMarker=function(t,e,n){var i="richText"===n?this._generateStyleName():null,e=Sp({color:e,type:t,renderMode:n,markerId:i});return V(e)?e:(this.richTextStyles[i]=e.style,e.content)},Dg.prototype.wrapRichTextStyle=function(t,e){var n={},e=(F(e)?O(e,function(t){return L(n,t)}):L(n,e),this._generateStyleName());return this.richTextStyles[e]=n,"{"+e+"|"+t+"}"};var kg=Dg;function Dg(){this.richTextStyles={},this._nextStyleNameId=go()}function Ag(t){var e,n,i,r,o,a,s,l,u,h,c,p=t.series,d=t.dataIndex,t=t.multipleSeries,f=p.getData(),g=f.mapDimensionsAll("defaultedTooltip"),y=g.length,m=p.getRawValue(d),v=F(m),_=(_=d,Mp((w=p).getData().getItemVisual(_,"style")[w.visualDrawType]));function x(t,e){e=s.getDimensionInfo(e);e&&!1!==e.otherDims.tooltip&&(l?c.push(_g("nameValue",{markerType:"subItem",markerColor:a,name:e.displayName,value:t,valueType:e.type})):(u.push(t),h.push(e.type)))}1<y||v&&!y?(w=m,r=d,o=g,a=_,s=p.getData(),l=lt(w,function(t,e,n){n=s.getDimensionInfo(n);return t||n&&!1!==n.tooltip&&null!=n.displayName},!1),u=[],h=[],c=[],o.length?O(o,function(t){x(vf(s,r,t),t)}):O(w,x),e=(o={inlineValues:u,inlineValueTypes:h,blocks:c}).inlineValueTypes,n=o.blocks,i=(o=o.inlineValues)[0]):y?(w=f.getDimensionInfo(g[0]),i=o=vf(f,d,g[0]),e=w.type):i=o=v?m[0]:m;var y=Io(p),g=y&&p.name||"",w=f.getName(d),v=t?g:w;return _g("section",{header:g,noHeader:t||!y,sortParam:i,blocks:[_g("nameValue",{markerType:"item",markerColor:_,name:v,noName:!Ct(v),value:o,valueType:e,dataIndex:d})].concat(n||[])})}var Pg=Po();function Lg(t,e){return t.getName(e)||t.getId(e)}u(c,Og=g),c.prototype.init=function(t,e,n){this.seriesIndex=this.componentIndex,this.dataTask=bf({count:Eg,reset:zg}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,n);(Pg(this).sourceManager=new ug(this)).prepareSource();t=this.getInitialData(t,n);Fg(t,this),this.dataTask.context.data=t,Pg(this).dataBeforeProcessed=t,Ng(this),this._initSelectedMapFromData(t)},c.prototype.mergeDefaultAndTheme=function(t,e){var n=Lp(this),i=n?Rp(t):{},r=this.subType;g.hasClass(r),d(t,e.getTheme().get(this.subType)),d(t,this.getDefaultOption()),xo(t,"label",["show"]),this.fillDataTextStyle(t.data),n&&Op(t,i,n)},c.prototype.mergeOption=function(t,e){t=d(this.option,t,!0),this.fillDataTextStyle(t.data);var n=Lp(this),n=(n&&Op(this.option,t,n),Pg(this).sourceManager),n=(n.dirty(),n.prepareSource(),this.getInitialData(t,e));Fg(n,this),this.dataTask.dirty(),this.dataTask.context.data=n,Pg(this).dataBeforeProcessed=n,Ng(this),this._initSelectedMapFromData(n)},c.prototype.fillDataTextStyle=function(t){if(t&&!gt(t))for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&xo(t[n],"label",e)},c.prototype.getInitialData=function(t,e){},c.prototype.appendData=function(t){this.getRawData().appendData(t.data)},c.prototype.getData=function(t){var e=Hg(this);return e?(e=e.context.data,null!=t&&e.getLinkedData?e.getLinkedData(t):e):Pg(this).data},c.prototype.getAllData=function(){var t=this.getData();return t&&t.getLinkedDataAll?t.getLinkedDataAll():[{data:t}]},c.prototype.setData=function(t){var e,n=Hg(this);n&&((e=n.context).outputData=t,n!==this.dataTask)&&(e.data=t),Pg(this).data=t},c.prototype.getEncode=function(){var t=this.get("encode",!0);if(t)return E(t)},c.prototype.getSourceManager=function(){return Pg(this).sourceManager},c.prototype.getSource=function(){return this.getSourceManager().getSource()},c.prototype.getRawData=function(){return Pg(this).dataBeforeProcessed},c.prototype.getColorBy=function(){return this.get("colorBy")||"series"},c.prototype.isColorBySeries=function(){return"series"===this.getColorBy()},c.prototype.getBaseAxis=function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},c.prototype.formatTooltip=function(t,e,n){return Ag({series:this,dataIndex:t,multipleSeries:e})},c.prototype.isAnimationEnabled=function(){var t=this.ecModel;return!!(!b.node||t&&t.ssr)&&!!(t=(t=this.getShallow("animation"))&&this.getData().count()>this.getShallow("animationThreshold")?!1:t)},c.prototype.restoreData=function(){this.dataTask.dirty()},c.prototype.getColorFromPalette=function(t,e,n){var i=this.ecModel;return ld.prototype.getColorFromPalette.call(this,t,e,n)||i.getColorFromPalette(t,e,n)},c.prototype.coordDimToDataDim=function(t){return this.getRawData().mapDimensionsAll(t)},c.prototype.getProgressive=function(){return this.get("progressive")},c.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},c.prototype.select=function(t,e){this._innerSelect(this.getData(e),t)},c.prototype.unselect=function(t,e){var n=this.option.selectedMap;if(n){var i=this.option.selectedMode,r=this.getData(e);if("series"===i||"all"===n)this.option.selectedMap={},this._selectedDataIndicesMap={};else for(var o=0;o<t.length;o++){var a=Lg(r,t[o]);n[a]=!1,this._selectedDataIndicesMap[a]=-1}}},c.prototype.toggleSelect=function(t,e){for(var n=[],i=0;i<t.length;i++)n[0]=t[i],this.isSelected(t[i],e)?this.unselect(n,e):this.select(n,e)},c.prototype.getSelectedDataIndices=function(){if("all"===this.option.selectedMap)return[].slice.call(this.getData().getIndices());for(var t=this._selectedDataIndicesMap,e=ht(t),n=[],i=0;i<e.length;i++){var r=t[e[i]];0<=r&&n.push(r)}return n},c.prototype.isSelected=function(t,e){var n=this.option.selectedMap;return!!n&&(e=this.getData(e),"all"===n||n[Lg(e,t)])&&!e.getItemModel(t).get(["select","disabled"])},c.prototype.isUniversalTransitionEnabled=function(){var t;return!!this.__universalTransitionEnabled||!!(t=this.option.universalTransition)&&(!0===t||t&&t.enabled)},c.prototype._innerSelect=function(t,e){var n=this.option,i=n.selectedMode,r=e.length;if(i&&r)if("series"===i)n.selectedMap="all";else if("multiple"===i){R(n.selectedMap)||(n.selectedMap={});for(var o=n.selectedMap,a=0;a<r;a++){var s,l=e[a];o[s=Lg(t,l)]=!0,this._selectedDataIndicesMap[s]=t.getRawIndex(l)}}else"single"!==i&&!0!==i||(s=Lg(t,i=e[r-1]),n.selectedMap=((n={})[s]=!0,n),this._selectedDataIndicesMap=((n={})[s]=t.getRawIndex(i),n))},c.prototype._initSelectedMapFromData=function(n){var i;this.option.selectedMap||(i=[],n.hasItemOption&&n.each(function(t){var e=n.getRawDataItem(t);e&&e.selected&&i.push(t)}),0<i.length&&this._innerSelect(n,i))},c.registerClass=function(t){return g.registerClass(t)},c.protoInitialize=((Tc=c.prototype).type="series.__base__",Tc.seriesIndex=0,Tc.ignoreStyleOnData=!1,Tc.hasSymbolVisual=!1,Tc.defaultSymbol="circle",Tc.visualStyleAccessPath="itemStyle",void(Tc.visualDrawType="fill"));var Og,Rg=c;function c(){var t=null!==Og&&Og.apply(this,arguments)||this;return t._selectedDataIndicesMap={},t}function Ng(t){var e,n,i=t.name;Io(t)||(t.name=(t=(e=(t=t).getRawData()).mapDimensionsAll("seriesName"),n=[],O(t,function(t){t=e.getDimensionInfo(t);t.displayName&&n.push(t.displayName)}),n.join(" ")||i))}function Eg(t){return t.model.getRawData().count()}function zg(t){t=t.model;return t.setData(t.getRawData().cloneShallow()),Bg}function Bg(t,e){e.outputData&&t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function Fg(e,n){O(Nt(e.CHANGABLE_METHODS,e.DOWNSAMPLE_METHODS),function(t){e.wrapMethod(t,pt(Vg,n))})}function Vg(t,e){t=Hg(t);return t&&t.setOutputEnd((e||this).count()),e}function Hg(t){var e,n=(t.ecModel||{}).scheduler,n=n&&n.getPipeline(t.uid);if(n)return(n=n.currentTask)&&(e=n.agentStubMap)?e.get(t.uid):n}at(Rg,Dc),at(Rg,ld),Uo(Rg,g);Wg.prototype.init=function(t,e){},Wg.prototype.render=function(t,e,n,i){},Wg.prototype.dispose=function(t,e){},Wg.prototype.updateView=function(t,e,n,i){},Wg.prototype.updateLayout=function(t,e,n,i){},Wg.prototype.updateVisual=function(t,e,n,i){},Wg.prototype.toggleBlurSeries=function(t,e,n){},Wg.prototype.eachRendered=function(t){var e=this.group;e&&e.traverse(t)};var Gg=Wg;function Wg(){this.group=new Hr,this.uid=Nc("viewComponent")}function Ug(){var o=Po();return function(t){var e=o(t),t=t.pipelineContext,n=!!e.large,i=!!e.progressiveRender,r=e.large=!(!t||!t.large),e=e.progressiveRender=!(!t||!t.progressiveRender);return!(n==r&&i==e)&&"reset"}}Wo(Gg),Zo(Gg);var Xg=Po(),Yg=Ug(),qg=(Zg.prototype.init=function(t,e){},Zg.prototype.render=function(t,e,n,i){},Zg.prototype.highlight=function(t,e,n,i){t=t.getData(i&&i.dataType);t&&Kg(t,i,"emphasis")},Zg.prototype.downplay=function(t,e,n,i){t=t.getData(i&&i.dataType);t&&Kg(t,i,"normal")},Zg.prototype.remove=function(t,e){this.group.removeAll()},Zg.prototype.dispose=function(t,e){},Zg.prototype.updateView=function(t,e,n,i){this.render(t,e,n,i)},Zg.prototype.updateLayout=function(t,e,n,i){this.render(t,e,n,i)},Zg.prototype.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},Zg.prototype.eachRendered=function(t){rc(this.group,t)},Zg.markUpdateMethod=function(t,e){Xg(t).updateMethod=e},Zg.protoInitialize=void(Zg.prototype.type="chart"),Zg);function Zg(){this.group=new Hr,this.uid=Nc("viewChart"),this.renderTask=bf({plan:$g,reset:Qg}),this.renderTask.context={view:this}}function jg(t,e,n){t&&Vl(t)&&("emphasis"===e?Sl:Ml)(t,n)}function Kg(e,t,n){var i,r=Ao(e,t),o=t&&null!=t.highlightKey?(t=t.highlightKey,i=null==(i=Zs[t])&&qs<=32?Zs[t]=qs++:i):null;null!=r?O(_o(r),function(t){jg(e.getItemGraphicEl(t),n,o)}):e.eachItemGraphicEl(function(t){jg(t,n,o)})}function $g(t){return Yg(t.model)}function Qg(t){var e=t.model,n=t.ecModel,i=t.api,r=t.payload,o=e.pipelineContext.progressiveRender,t=t.view,a=r&&Xg(r).updateMethod,o=o?"incrementalPrepareRender":a&&t[a]?a:"render";return"render"!==o&&t[o](e,n,i,r),Jg[o]}Wo(qg),Zo(qg);var Jg={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}},ty="\0__throttleOriginMethod",ey="\0__throttleRate",ny="\0__throttleType";function iy(t,r,o){var a,s,l,u,h,c=0,p=0,d=null;function f(){p=(new Date).getTime(),d=null,t.apply(l,u||[])}r=r||0;function e(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];a=(new Date).getTime(),l=this,u=t;var n=h||r,i=h||o;h=null,s=a-(i?c:p)-n,clearTimeout(d),i?d=setTimeout(f,n):0<=s?f():d=setTimeout(f,-s),c=a}return e.clear=function(){d&&(clearTimeout(d),d=null)},e.debounceNextCall=function(t){h=t},e}function ry(t,e,n,i){var r=t[e];if(r){var o=r[ty]||r,a=r[ny];if(r[ey]!==n||a!==i){if(null==n||!i)return t[e]=o;(r=t[e]=iy(o,n,"debounce"===i))[ty]=o,r[ny]=i,r[ey]=n}}}function oy(t,e){var n=t[e];n&&n[ty]&&(n.clear&&n.clear(),t[e]=n[ty])}var ay=Po(),sy={itemStyle:jo(Ic,!0),lineStyle:jo(Sc,!0)},ly={lineStyle:"stroke",itemStyle:"fill"};function uy(t,e){t=t.visualStyleMapper||sy[e];return t||(console.warn("Unknown style type '"+e+"'."),sy.itemStyle)}function hy(t,e){t=t.visualDrawType||ly[e];return t||(console.warn("Unknown style type '"+e+"'."),"fill")}var $o={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){var e=r.getData(),n=r.visualStyleAccessPath||"itemStyle",i=r.getModel(n),o=uy(r,n)(i),i=i.getShallow("decal"),a=(i&&(e.setVisual("decal",i),i.dirty=!0),hy(r,n)),i=o[a],s=k(i)?i:null,n="auto"===o.fill||"auto"===o.stroke;if(o[a]&&!s&&!n||(i=r.getColorFromPalette(r.name,null,t.getSeriesCount()),o[a]||(o[a]=i,e.setVisual("colorFromPalette",!0)),o.fill="auto"===o.fill||k(o.fill)?i:o.fill,o.stroke="auto"===o.stroke||k(o.stroke)?i:o.stroke),e.setVisual("style",o),e.setVisual("drawType",a),!t.isSeriesFiltered(r)&&s)return e.setVisual("colorFromPalette",!1),{dataEach:function(t,e){var n=r.getDataParams(e),i=L({},o);i[a]=s(n),t.setItemVisual(e,"style",i)}}}},cy=new Lc,qh={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var i,r,o;if(!t.ignoreStyleOnData&&!e.isSeriesFiltered(t))return e=t.getData(),i=t.visualStyleAccessPath||"itemStyle",r=uy(t,i),o=e.getVisual("drawType"),{dataEach:e.hasItemOption?function(t,e){var n=t.getRawDataItem(e);n&&n[i]&&(cy.option=n[i],n=r(cy),L(t.ensureUniqueItemVisual(e,"style"),n),cy.option.decal&&(t.setItemVisual(e,"decal",cy.option.decal),cy.option.decal.dirty=!0),o in n)&&t.setItemVisual(e,"colorFromPalette",!1)}:null}}},Zc={performRawSeries:!0,overallReset:function(e){var i=E();e.eachSeries(function(t){var e,n=t.getColorBy();t.isColorBySeries()||(n=t.type+"-"+n,(e=i.get(n))||i.set(n,e={}),ay(t).scope=e)}),e.eachSeries(function(i){var r,o,a,s,t,l;i.isColorBySeries()||e.isSeriesFiltered(i)||(r=i.getRawData(),o={},a=i.getData(),s=ay(i).scope,t=i.visualStyleAccessPath||"itemStyle",l=hy(i,t),a.each(function(t){var e=a.getRawIndex(t);o[e]=t}),r.each(function(t){var e,n=o[t];a.getItemVisual(n,"colorFromPalette")&&(n=a.ensureUniqueItemVisual(n,"style"),t=r.getName(t)||t+"",e=r.count(),n[l]=i.getColorFromPalette(t,s,e))}))})}},py=Math.PI;fy.prototype.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(t){t=t.overallTask;t&&t.dirty()})},fy.prototype.getPerformArgs=function(t,e){var n,i;if(t.__pipeline)return i=(n=this._pipelineMap.get(t.__pipeline.id)).context,{step:e=!e&&n.progressiveEnabled&&(!i||i.progressiveRender)&&t.__idxInPipeline>n.blockIndex?n.step:null,modBy:null!=(t=i&&i.modDataCount)?Math.ceil(t/e):null,modDataCount:t}},fy.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},fy.prototype.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),i=t.getData().count(),e=n.progressiveEnabled&&e.incrementalPrepareRender&&i>=n.threshold,r=t.get("large")&&i>=t.get("largeThreshold"),i="mod"===t.get("progressiveChunkMode")?i:null;t.pipelineContext=n.context={progressiveRender:e,modDataCount:i,large:r}},fy.prototype.restorePipelines=function(t){var i=this,r=i._pipelineMap=E();t.eachSeries(function(t){var e=t.getProgressive(),n=t.uid;r.set(n,{id:n,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:e&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(e||700),count:0}),i._pipe(t,t.dataTask)})},fy.prototype.prepareStageTasks=function(){var n=this._stageTaskMap,i=this.api.getModel(),r=this.api;O(this._allHandlers,function(t){var e=n.get(t.uid)||n.set(t.uid,{});Tt(!(t.reset&&t.overallReset),""),t.reset&&this._createSeriesStageTask(t,e,i,r),t.overallReset&&this._createOverallStageTask(t,e,i,r)},this)},fy.prototype.prepareView=function(t,e,n,i){var r=t.renderTask,o=r.context;o.model=e,o.ecModel=n,o.api=i,r.__block=!t.incrementalPrepareRender,this._pipe(e,r)},fy.prototype.performDataProcessorTasks=function(t,e){this._performStageTasks(this._dataProcessorHandlers,t,e,{block:!0})},fy.prototype.performVisualTasks=function(t,e,n){this._performStageTasks(this._visualHandlers,t,e,n)},fy.prototype._performStageTasks=function(t,s,l,u){u=u||{};var h=!1,c=this;function p(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}O(t,function(i,t){var e,n,r,o,a;u.visualType&&u.visualType!==i.visualType||(e=(n=c._stageTaskMap.get(i.uid)).seriesTaskMap,(n=n.overallTask)?((o=n.agentStubMap).each(function(t){p(u,t)&&(t.dirty(),r=!0)}),r&&n.dirty(),c.updatePayload(n,l),a=c.getPerformArgs(n,u.block),o.each(function(t){t.perform(a)}),n.perform(a)&&(h=!0)):e&&e.each(function(t,e){p(u,t)&&t.dirty();var n=c.getPerformArgs(t,u.block);n.skip=!i.performRawSeries&&s.isSeriesFiltered(t.context.model),c.updatePayload(t,l),t.perform(n)&&(h=!0)}))}),this.unfinished=h||this.unfinished},fy.prototype.performSeriesTasks=function(t){var e;t.eachSeries(function(t){e=t.dataTask.perform()||e}),this.unfinished=e||this.unfinished},fy.prototype.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}}while(e=e.getUpstream())})},fy.prototype.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)},fy.prototype._createSeriesStageTask=function(n,t,i,r){var o=this,a=t.seriesTaskMap,s=t.seriesTaskMap=E(),t=n.seriesType,e=n.getTargetSeries;function l(t){var e=t.uid,e=s.set(e,a&&a.get(e)||bf({plan:_y,reset:xy,count:Sy}));e.context={model:t,ecModel:i,api:r,useClearVisual:n.isVisual&&!n.isLayout,plan:n.plan,reset:n.reset,scheduler:o},o._pipe(t,e)}n.createOnAllSeries?i.eachRawSeries(l):t?i.eachRawSeriesByType(t,l):e&&e(i,r).each(l)},fy.prototype._createOverallStageTask=function(t,e,n,i){var r=this,o=e.overallTask=e.overallTask||bf({reset:gy}),a=(o.context={ecModel:n,api:i,overallReset:t.overallReset,scheduler:r},o.agentStubMap),s=o.agentStubMap=E(),e=t.seriesType,l=t.getTargetSeries,u=!0,h=!1;function c(t){var e=t.uid,e=s.set(e,a&&a.get(e)||(h=!0,bf({reset:yy,onDirty:vy})));e.context={model:t,overallProgress:u},e.agent=o,e.__block=u,r._pipe(t,e)}Tt(!t.createOnAllSeries,""),e?n.eachRawSeriesByType(e,c):l?l(n,i).each(c):(u=!1,O(n.getSeries(),c)),h&&o.dirty()},fy.prototype._pipe=function(t,e){t=t.uid,t=this._pipelineMap.get(t);t.head||(t.head=e),t.tail&&t.tail.pipe(e),(t.tail=e).__idxInPipeline=t.count++,e.__pipeline=t},fy.wrapStageHandler=function(t,e){return(t=k(t)?{overallReset:t,seriesType:function(t){My=null;try{t(Ty,Cy)}catch(t){}return My}(t)}:t).uid=Nc("stageHandler"),e&&(t.visualType=e),t};var dy=fy;function fy(t,e,n,i){this._stageTaskMap=E(),this.ecInstance=t,this.api=e,n=this._dataProcessorHandlers=n.slice(),i=this._visualHandlers=i.slice(),this._allHandlers=n.concat(i)}function gy(t){t.overallReset(t.ecModel,t.api,t.payload)}function yy(t){return t.overallProgress&&my}function my(){this.agent.dirty(),this.getDownstream().dirty()}function vy(){this.agent&&this.agent.dirty()}function _y(t){return t.plan?t.plan(t.model,t.ecModel,t.api,t.payload):null}function xy(t){t.useClearVisual&&t.data.clearAllVisual();t=t.resetDefines=_o(t.reset(t.model,t.ecModel,t.api,t.payload));return 1<t.length?B(t,function(t,e){return by(e)}):wy}var wy=by(0);function by(o){return function(t,e){var n=e.data,i=e.resetDefines[o];if(i&&i.dataEach)for(var r=t.start;r<t.end;r++)i.dataEach(n,r);else i&&i.progress&&i.progress(t,n)}}function Sy(t){return t.data.count()}var My,Ty={},Cy={};function Iy(t,e){for(var n in e.prototype)t[n]=Ft}Iy(Ty,dd),Iy(Cy,md),Ty.eachSeriesByType=Ty.eachRawSeriesByType=function(t){My=t},Ty.eachComponent=function(t){"series"===t.mainType&&t.subType&&(My=t.subType)};function ky(){return{axisLine:{lineStyle:{color:Dy}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}}var Tc=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],Dc={color:Tc,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],Tc]},Dy="#B9B8CE",Ic="#100C2A",Sc=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],Tc={darkMode:!0,color:Sc,backgroundColor:Ic,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:Dy},pageTextStyle:{color:Dy}},textStyle:{color:Dy},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:Dy}},dataZoom:{borderColor:"#71708A",textStyle:{color:Dy},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:Dy}},timeline:{lineStyle:{color:Dy},label:{color:Dy},controlStyle:{color:Dy,borderColor:Dy}},calendar:{itemStyle:{color:Ic},dayLabel:{color:Dy},monthLabel:{color:Dy},yearLabel:{color:Dy}},timeAxis:ky(),logAxis:ky(),valueAxis:ky(),categoryAxis:ky(),line:{symbol:"circle"},graph:{color:Sc},gauge:{title:{color:Dy},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:Dy},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}},Ay=(Tc.categoryAxis.splitLine.show=!1,Py.prototype.normalizeQuery=function(t){var e,a,s,l={},u={},h={};return V(t)?(e=Go(t),l.mainType=e.main||null,l.subType=e.sub||null):(a=["Index","Name","Id"],s={name:1,dataIndex:1,dataType:1},O(t,function(t,e){for(var n=!1,i=0;i<a.length;i++){var r=a[i],o=e.lastIndexOf(r);0<o&&o===e.length-r.length&&"data"!==(o=e.slice(0,o))&&(l.mainType=o,l[r.toLowerCase()]=t,n=!0)}s.hasOwnProperty(e)&&(u[e]=t,n=!0),n||(h[e]=t)})),{cptQuery:l,dataQuery:u,otherQuery:h}},Py.prototype.filter=function(t,e){var n,i,r,o,a,s=this.eventInfo;return!s||(n=s.targetEl,i=s.packedEvent,r=s.model,s=s.view,!r)||!s||(o=e.cptQuery,a=e.dataQuery,l(o,r,"mainType")&&l(o,r,"subType")&&l(o,r,"index","componentIndex")&&l(o,r,"name")&&l(o,r,"id")&&l(a,i,"name")&&l(a,i,"dataIndex")&&l(a,i,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,n,i)));function l(t,e,n,i){return null==t[n]||e[i||n]===t[n]}},Py.prototype.afterTrigger=function(){this.eventInfo=null},Py);function Py(){}var Ly=["symbol","symbolSize","symbolRotate","symbolOffset"],Oy=Ly.concat(["symbolKeepAspect"]),Ic={createOnAllSeries:!0,performRawSeries:!0,reset:function(a,t){var e=a.getData();if(a.legendIcon&&e.setVisual("legendIcon",a.legendIcon),a.hasSymbolVisual){for(var s,n={},l={},i=!1,r=0;r<Ly.length;r++){var o=Ly[r],u=a.get(o);k(u)?(i=!0,l[o]=u):n[o]=u}if(n.symbol=n.symbol||a.defaultSymbol,e.setVisual(L({legendIcon:a.legendIcon||n.symbol,symbolKeepAspect:a.get("symbolKeepAspect")},n)),!t.isSeriesFiltered(a))return s=ht(l),{dataEach:i?function(t,e){for(var n=a.getRawValue(e),i=a.getDataParams(e),r=0;r<s.length;r++){var o=s[r];t.setItemVisual(e,o,l[o](n,i))}}:null}}}},Sc={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){if(t.hasSymbolVisual&&!e.isSeriesFiltered(t))return{dataEach:t.getData().hasItemOption?function(t,e){for(var n=t.getItemModel(e),i=0;i<Oy.length;i++){var r=Oy[i],o=n.getShallow(r,!0);null!=o&&t.setItemVisual(e,r,o)}}:null}}};function Ry(t,e,s,n,l){var u=t+e;s.isSilent(u)||n.eachComponent({mainType:"series",subType:"pie"},function(t){for(var e,n,i=t.seriesIndex,r=t.option.selectedMap,o=l.selected,a=0;a<o.length;a++)o[a].seriesIndex===i&&(n=Ao(e=t.getData(),l.fromActionPayload),s.trigger(u,{type:u,seriesId:t.id,name:F(n)?e.getName(n[0]):e.getName(n),selected:V(r)?r:L({},r)}))})}function Ny(t,e,n){for(var i;t&&(!e(t)||(i=t,!n));)t=t.__hostTarget||t.parent;return i}var Ey=Math.round(9*Math.random()),zy="function"==typeof Object.defineProperty,By=(Fy.prototype.get=function(t){return this._guard(t)[this._id]},Fy.prototype.set=function(t,e){t=this._guard(t);return zy?Object.defineProperty(t,this._id,{value:e,enumerable:!1,configurable:!0}):t[this._id]=e,this},Fy.prototype.delete=function(t){return!!this.has(t)&&(delete this._guard(t)[this._id],!0)},Fy.prototype.has=function(t){return!!this._guard(t)[this._id]},Fy.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},Fy);function Fy(){this._id="__ec_inner_"+Ey++}var Vy=ds.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,e=e.height/2;t.moveTo(n,i-e),t.lineTo(n+r,i+e),t.lineTo(n-r,i+e),t.closePath()}}),Hy=ds.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,e=e.height/2;t.moveTo(n,i-e),t.lineTo(n+r,i),t.lineTo(n,i+e),t.lineTo(n-r,i),t.closePath()}}),Gy=ds.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,r=e.width/5*3,e=Math.max(r,e.height),r=r/2,o=r*r/(e-r),e=i-e+r+o,a=Math.asin(o/r),s=Math.cos(a)*r,l=Math.sin(a),u=Math.cos(a),h=.6*r,c=.7*r;t.moveTo(n-s,e+o),t.arc(n,e,r,Math.PI-a,2*Math.PI+a),t.bezierCurveTo(n+s-l*h,e+o+u*h,n,i-c,n,i),t.bezierCurveTo(n,i-c,n-s+l*h,e+o+u*h,n-s,e+o),t.closePath()}}),Wy=ds.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,i=e.width,r=e.x,e=e.y,i=i/3*2;t.moveTo(r,e),t.lineTo(r+i,e+n),t.lineTo(r,e+n/4*3),t.lineTo(r-i,e+n),t.lineTo(r,e),t.closePath()}}),Uy={line:function(t,e,n,i,r){r.x1=t,r.y1=e+i/2,r.x2=t+n,r.y2=e+i/2},rect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i},roundRect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i,r.r=Math.min(n,i)/4},square:function(t,e,n,i,r){n=Math.min(n,i);r.x=t,r.y=e,r.width=n,r.height=n},circle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.r=Math.min(n,i)/2},diamond:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i},pin:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},arrow:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},triangle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i}},Xy={},Yy=(O({line:ju,rect:As,roundRect:As,square:As,circle:hu,diamond:Hy,pin:Gy,arrow:Wy,triangle:Vy},function(t,e){Xy[e]=new t}),ds.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,n){var t=kr(t,e,n),i=this.shape;return i&&"pin"===i.symbolType&&"inside"===e.position&&(t.y=n.y+.4*n.height),t},buildPath:function(t,e,n){var i,r=e.symbolType;"none"!==r&&(i=(i=Xy[r])||Xy[r="rect"],Uy[r](e.x,e.y,e.width,e.height,i.shape),i.buildPath(t,i.shape,n))}}));function qy(t,e){var n;"image"!==this.type&&(n=this.style,this.__isEmptyBrush?(n.stroke=t,n.fill=e||"#fff",n.lineWidth=2):"line"===this.shape.symbolType?n.stroke=t:n.fill=t,this.markRedraw())}function Zy(t,e,n,i,r,o,a){var s=0===t.indexOf("empty");return(a=0===(t=s?t.substr(5,1).toLowerCase()+t.substr(6):t).indexOf("image://")?Gh(t.slice(8),new X(e,n,i,r),a?"center":"cover"):0===t.indexOf("path://")?Hh(t.slice(7),{},new X(e,n,i,r),a?"center":"cover"):new Yy({shape:{symbolType:t,x:e,y:n,width:i,height:r}})).__isEmptyBrush=s,a.setColor=qy,o&&a.setColor(o),a}function jy(t,e){if(null!=t)return[to((t=F(t)?t:[t,t])[0],e[0])||0,to(N(t[1],t[0]),e[1])||0]}function Ky(t){return isFinite(t)}function $y(t,e,n){for(var i,r,o,a,s,l,u,h,c,p="radial"===e.type?(i=t,r=e,a=(o=n).width,s=o.height,l=Math.min(a,s),u=null==r.x?.5:r.x,h=null==r.y?.5:r.y,c=null==r.r?.5:r.r,r.global||(u=u*a+o.x,h=h*s+o.y,c*=l),u=Ky(u)?u:.5,h=Ky(h)?h:.5,c=0<=c&&Ky(c)?c:.5,i.createRadialGradient(u,h,0,u,h,c)):(r=t,a=n,o=null==(s=e).x?0:s.x,l=null==s.x2?1:s.x2,i=null==s.y?0:s.y,u=null==s.y2?0:s.y2,s.global||(o=o*a.width+a.x,l=l*a.width+a.x,i=i*a.height+a.y,u=u*a.height+a.y),o=Ky(o)?o:0,l=Ky(l)?l:1,i=Ky(i)?i:0,u=Ky(u)?u:0,r.createLinearGradient(o,i,l,u)),d=e.colorStops,f=0;f<d.length;f++)p.addColorStop(d[f].offset,d[f].color);return p}function Qy(t){return parseInt(t,10)}function Jy(t,e,n){var i=["width","height"][e],r=["clientWidth","clientHeight"][e],o=["paddingLeft","paddingTop"][e],e=["paddingRight","paddingBottom"][e];return null!=n[i]&&"auto"!==n[i]?parseFloat(n[i]):(n=document.defaultView.getComputedStyle(t),(t[r]||Qy(n[i])||Qy(t.style[i]))-(Qy(n[o])||0)-(Qy(n[e])||0)|0)}function tm(t){var e,n=t.style,i=n.lineDash&&0<n.lineWidth&&(r=n.lineDash,i=n.lineWidth,r&&"solid"!==r&&0<i?"dashed"===r?[4*i,2*i]:"dotted"===r?[i]:H(r)?[r]:F(r)?r:null:null),r=n.lineDashOffset;return i&&(e=n.strokeNoScale&&t.getLineScale?t.getLineScale():1)&&1!==e&&(i=B(i,function(t){return t/e}),r/=e),[i,r]}var em=new ja(!0);function nm(t){var e=t.stroke;return!(null==e||"none"===e||!(0<t.lineWidth))}function im(t){return"string"==typeof t&&"none"!==t}function rm(t){t=t.fill;return null!=t&&"none"!==t}function om(t,e){var n;null!=e.fillOpacity&&1!==e.fillOpacity?(n=t.globalAlpha,t.globalAlpha=e.fillOpacity*e.opacity,t.fill(),t.globalAlpha=n):t.fill()}function am(t,e){var n;null!=e.strokeOpacity&&1!==e.strokeOpacity?(n=t.globalAlpha,t.globalAlpha=e.strokeOpacity*e.opacity,t.stroke(),t.globalAlpha=n):t.stroke()}function sm(t,e,n){var n=ta(e.image,e.__image,n);if(na(n))return t=t.createPattern(n,e.repeat||"repeat"),"function"==typeof DOMMatrix&&t&&t.setTransform&&((n=new DOMMatrix).translateSelf(e.x||0,e.y||0),n.rotateSelf(0,0,(e.rotation||0)*Vt),n.scaleSelf(e.scaleX||1,e.scaleY||1),t.setTransform(n)),t}var lm=["shadowBlur","shadowOffsetX","shadowOffsetY"],um=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function hm(t,e,n,i,r){var o,a=!1;if(!i&&e===(n=n||{}))return!1;!i&&e.opacity===n.opacity||(mm(t,r),a=!0,o=Math.max(Math.min(e.opacity,1),0),t.globalAlpha=isNaN(o)?ya.opacity:o),!i&&e.blend===n.blend||(a||(mm(t,r),a=!0),t.globalCompositeOperation=e.blend||ya.blend);for(var s=0;s<lm.length;s++){var l=lm[s];!i&&e[l]===n[l]||(a||(mm(t,r),a=!0),t[l]=t.dpr*(e[l]||0))}return!i&&e.shadowColor===n.shadowColor||(a||(mm(t,r),a=!0),t.shadowColor=e.shadowColor||ya.shadowColor),a}function cm(t,e,n,i,r){var o=vm(e,r.inHover),a=i?null:n&&vm(n,r.inHover)||{};if(o!==a){var s=hm(t,o,a,i,r);(i||o.fill!==a.fill)&&(s||(mm(t,r),s=!0),im(o.fill))&&(t.fillStyle=o.fill),(i||o.stroke!==a.stroke)&&(s||(mm(t,r),s=!0),im(o.stroke))&&(t.strokeStyle=o.stroke),!i&&o.opacity===a.opacity||(s||(mm(t,r),s=!0),t.globalAlpha=null==o.opacity?1:o.opacity),e.hasStroke()&&(n=o.lineWidth/(o.strokeNoScale&&e.getLineScale?e.getLineScale():1),t.lineWidth!==n)&&(s||(mm(t,r),s=!0),t.lineWidth=n);for(var l=0;l<um.length;l++){var u=um[l],h=u[0];!i&&o[h]===a[h]||(s||(mm(t,r),s=!0),t[h]=o[h]||u[1])}}}function pm(t,e){var e=e.transform,n=t.dpr||1;e?t.setTransform(n*e[0],n*e[1],n*e[2],n*e[3],n*e[4],n*e[5]):t.setTransform(n,0,0,n,0,0)}var dm=1,fm=2,gm=3,ym=4;function mm(t,e){e.batchFill&&t.fill(),e.batchStroke&&t.stroke(),e.batchFill="",e.batchStroke=""}function vm(t,e){return e&&t.__hoverStyle||t.style}function _m(t,e){xm(t,e,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function xm(t,e,n,E){var i=e.transform;if(e.shouldBePainted(n.viewWidth,n.viewHeight,!1,!1)){var r=e.__clipPaths,o=n.prevElClipPaths,a=!1,s=!1;if(!o||function(t,e){if(t!==e&&(t||e)){if(!t||!e||t.length!==e.length)return 1;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return 1}}(r,o)){if(o&&o.length&&(mm(t,n),t.restore(),s=a=!0,n.prevElClipPaths=null,n.allClipped=!1,n.prevEl=null),r&&r.length){mm(t,n),t.save();for(var z=r,l=t,o=n,B=!1,F=0;F<z.length;F++){var V=z[F],B=B||V.isZeroArea();pm(l,V),l.beginPath(),V.buildPath(l,V.shape),l.clip()}o.allClipped=B,a=!0}n.prevElClipPaths=r}if(n.allClipped)e.__isRendered=!1;else{e.beforeBrush&&e.beforeBrush(),e.innerBeforeBrush();var u,h,c,p,d,f,g,y,m,v,_,x,w,H,b,S,M,T,C,I,k,D,A,o=n.prevEl,P=(o||(s=a=!0),e instanceof ds&&e.autoBatch&&(r=e.style,P=rm(r),u=nm(r),!(r.lineDash||!(+P^+u)||P&&"string"!=typeof r.fill||u&&"string"!=typeof r.stroke||r.strokePercent<1||r.strokeOpacity<1||r.fillOpacity<1))),a=(a||(u=i,r=o.transform,u&&r?u[0]!==r[0]||u[1]!==r[1]||u[2]!==r[2]||u[3]!==r[3]||u[4]!==r[4]||u[5]!==r[5]:u||r)?(mm(t,n),pm(t,e)):P||mm(t,n),vm(e,n.inHover));if(e instanceof ds)n.lastDrawType!==dm&&(s=!0,n.lastDrawType=dm),cm(t,e,o,s,n),P&&(n.batchFill||n.batchStroke)||t.beginPath(),i=t,r=e,R=P,b=nm(p=a),S=rm(p),M=p.strokePercent,T=M<1,C=!r.path,r.silent&&!T||!C||r.createPathProxy(),I=r.path||em,k=r.__dirty,R||(d=p.fill,A=p.stroke,f=S&&!!d.colorStops,g=b&&!!A.colorStops,y=S&&!!d.image,m=b&&!!A.image,D=w=x=_=v=void 0,(f||g)&&(D=r.getBoundingRect()),f&&(v=k?$y(i,d,D):r.__canvasFillGradient,r.__canvasFillGradient=v),g&&(_=k?$y(i,A,D):r.__canvasStrokeGradient,r.__canvasStrokeGradient=_),y&&(x=k||!r.__canvasFillPattern?sm(i,d,r):r.__canvasFillPattern,r.__canvasFillPattern=x),m&&(w=k||!r.__canvasStrokePattern?sm(i,A,r):r.__canvasStrokePattern,r.__canvasStrokePattern=x),f?i.fillStyle=v:y&&(x?i.fillStyle=x:S=!1),g?i.strokeStyle=_:m&&(w?i.strokeStyle=w:b=!1)),D=r.getGlobalScale(),I.setScale(D[0],D[1],r.segmentIgnoreThreshold),i.setLineDash&&p.lineDash&&(H=(d=tm(r))[0],O=d[1]),A=!0,(C||k&_n)&&(I.setDPR(i.dpr),T?I.setContext(null):(I.setContext(i),A=!1),I.reset(),r.buildPath(I,r.shape,R),I.toStatic(),r.pathUpdated()),A&&I.rebuildPath(i,T?M:1),H&&(i.setLineDash(H),i.lineDashOffset=O),R||(p.strokeFirst?(b&&am(i,p),S&&om(i,p)):(S&&om(i,p),b&&am(i,p))),H&&i.setLineDash([]),P&&(n.batchFill=a.fill||"",n.batchStroke=a.stroke||"");else if(e instanceof ys)n.lastDrawType!==gm&&(s=!0,n.lastDrawType=gm),cm(t,e,o,s,n),f=t,v=e,null!=(x=(y=a).text)&&(x+=""),x&&(f.font=y.font||j,f.textAlign=y.textAlign,f.textBaseline=y.textBaseline,_=g=void 0,f.setLineDash&&y.lineDash&&(g=(v=tm(v))[0],_=v[1]),g&&(f.setLineDash(g),f.lineDashOffset=_),y.strokeFirst?(nm(y)&&f.strokeText(x,y.x,y.y),rm(y)&&f.fillText(x,y.x,y.y)):(rm(y)&&f.fillText(x,y.x,y.y),nm(y)&&f.strokeText(x,y.x,y.y)),g)&&f.setLineDash([]);else if(e instanceof ws)n.lastDrawType!==fm&&(s=!0,n.lastDrawType=fm),m=o,w=s,hm(t,vm(e,(D=n).inHover),m&&vm(m,D.inHover),w,D),d=t,C=a,(r=(k=e).__image=ta(C.image,k.__image,k,k.onload))&&na(r)&&(A=C.x||0,I=C.y||0,T=k.getWidth(),k=k.getHeight(),M=r.width/r.height,null==T&&null!=k?T=k*M:null==k&&null!=T?k=T/M:null==T&&null==k&&(T=r.width,k=r.height),C.sWidth&&C.sHeight?(h=C.sx||0,c=C.sy||0,d.drawImage(r,h,c,C.sWidth,C.sHeight,A,I,T,k)):C.sx&&C.sy?(h=C.sx,c=C.sy,d.drawImage(r,h,c,T-h,k-c,A,I,T,k)):d.drawImage(r,A,I,T,k));else if(e.getTemporalDisplayables){n.lastDrawType!==ym&&(s=!0,n.lastDrawType=ym);var L,G,W=t,O=e,R=n,U=O.getDisplayables(),X=O.getTemporalDisplayables(),Y=(W.save(),{prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:R.viewWidth,viewHeight:R.viewHeight,inHover:R.inHover});for(L=O.getCursor(),G=U.length;L<G;L++)(N=U[L]).beforeBrush&&N.beforeBrush(),N.innerBeforeBrush(),xm(W,N,Y,L===G-1),N.innerAfterBrush(),N.afterBrush&&N.afterBrush(),Y.prevEl=N;for(var N,q=0,Z=X.length;q<Z;q++)(N=X[q]).beforeBrush&&N.beforeBrush(),N.innerBeforeBrush(),xm(W,N,Y,q===Z-1),N.innerAfterBrush(),N.afterBrush&&N.afterBrush(),Y.prevEl=N;O.clearTemporalDisplayables(),O.notClear=!0,W.restore()}P&&E&&mm(t,n),e.innerAfterBrush(),e.afterBrush&&e.afterBrush(),(n.prevEl=e).__dirty=0,e.__isRendered=!0}}else e.__dirty&=~vn,e.__isRendered=!1}var wm=new By,bm=new ei(100),Sm=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function Mm(t,e){if("none"===t)return null;var a=e.getDevicePixelRatio(),s=e.getZr(),l="svg"===s.painter.type,e=(t.dirty&&wm.delete(t),wm.get(t));if(e)return e;for(var n,u=z(t,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512}),e=("none"===u.backgroundColor&&(u.backgroundColor=null),{repeat:"repeat"}),i=e,r=[a],o=!0,h=0;h<Sm.length;++h){var c=u[Sm[h]];if(null!=c&&!F(c)&&!V(c)&&!H(c)&&"boolean"!=typeof c){o=!1;break}r.push(c)}o&&(n=r.join(",")+(l?"-svg":""),v=bm.get(n))&&(l?i.svgElement=v:i.image=v);var p,d=function t(e){if(!e||0===e.length)return[[0,0]];if(H(e))return[[o=Math.ceil(e),o]];var n=!0;for(var i=0;i<e.length;++i)if(!H(e[i])){n=!1;break}if(n)return t([e]);var r=[];for(i=0;i<e.length;++i){var o;H(e[i])?(o=Math.ceil(e[i]),r.push([o,o])):(o=B(e[i],function(t){return Math.ceil(t)})).length%2==1?r.push(o.concat(o)):r.push(o)}return r}(u.dashArrayX),f=function(t){if(!t||"object"==typeof t&&0===t.length)return[0,0];if(H(t))return[e=Math.ceil(t),e];var e=B(t,function(t){return Math.ceil(t)});return t.length%2?e.concat(e):e}(u.dashArrayY),g=function t(e){if(!e||0===e.length)return[["rect"]];if(V(e))return[[e]];var n=!0;for(var i=0;i<e.length;++i)if(!V(e[i])){n=!1;break}if(n)return t([e]);var r=[];for(i=0;i<e.length;++i)V(e[i])?r.push([e[i]]):r.push(e[i]);return r}(u.symbol),y=function(t){return B(t,Tm)}(d),m=Tm(f),v=!l&&G.createCanvas(),_=l&&{tag:"g",attrs:{},key:"dcl",children:[]},x=function(){for(var t=1,e=0,n=y.length;e<n;++e)t=yo(t,y[e]);for(var i=1,e=0,n=g.length;e<n;++e)i=yo(i,g[e].length);t*=i;var r=m*y.length*g.length;return{width:Math.max(1,Math.min(t,u.maxTileWidth)),height:Math.max(1,Math.min(r,u.maxTileHeight))}}();v&&(v.width=x.width*a,v.height=x.height*a,p=v.getContext("2d")),p&&(p.clearRect(0,0,v.width,v.height),u.backgroundColor)&&(p.fillStyle=u.backgroundColor,p.fillRect(0,0,v.width,v.height));for(var w=0,b=0;b<f.length;++b)w+=f[b];if(!(w<=0))for(var S=-m,M=0,T=0,C=0;S<x.height;){if(M%2==0){for(var I=T/2%g.length,k=0,D=0,A=0;k<2*x.width;){for(var P,L,O,R,N,E=0,b=0;b<d[C].length;++b)E+=d[C][b];if(E<=0)break;D%2==0&&(L=.5*(1-u.symbolSize),P=k+d[C][D]*L,L=S+f[M]*L,O=d[C][D]*u.symbolSize,R=f[M]*u.symbolSize,N=A/2%g[I].length,function(t,e,n,i,r){var o=l?1:a,r=Zy(r,t*o,e*o,n*o,i*o,u.color,u.symbolKeepAspect);l?(t=s.painter.renderOneToVNode(r))&&_.children.push(t):_m(p,r)}(P,L,O,R,g[I][N])),k+=d[C][D],++A,++D===d[C].length&&(D=0)}++C===d.length&&(C=0)}S+=f[M],++T,++M===f.length&&(M=0)}return o&&bm.put(n,v||_),i.image=v,i.svgElement=_,i.svgWidth=x.width,i.svgHeight=x.height,e.rotation=u.rotation,e.scaleX=e.scaleY=l?1:1/a,wm.set(t,e),t.dirty=!1,e}function Tm(t){for(var e=0,n=0;n<t.length;++n)e+=t[n];return t.length%2==1?2*e:e}var Cm=new le,Im={};var Hy={PROCESSOR:{FILTER:1e3,SERIES_FILTER:800,STATISTIC:5e3},VISUAL:{LAYOUT:1e3,PROGRESSIVE_LAYOUT:1100,GLOBAL:2e3,CHART:3e3,POST_CHART_LAYOUT:4600,COMPONENT:4e3,BRUSH:5e3,CHART_ITEM:4500,ARIA:6e3,DECAL:7e3}},km="__flagInMainProcess",Dm="__pendingUpdate",Am="__needsUpdateStatus",Pm=/^[a-zA-Z0-9_]+$/,Lm="__connectUpdateStatus";function Om(n){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(!this.isDisposed())return Nm(this,n,t);this.id}}function Rm(n){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return Nm(this,n,t)}}function Nm(t,e,n){return n[0]=n[0]&&n[0].toLowerCase(),le.prototype[e].apply(t,n)}u(Bm,Em=le);var Em,zm=Bm;function Bm(){return null!==Em&&Em.apply(this,arguments)||this}var Fm,Vm,Hm,Gm,Wm,Um,Xm,Ym,qm,Zm,jm,Km,$m,Qm,Jm,t0,e0,n0,i0,Gy=zm.prototype,r0=(Gy.on=Rm("on"),Gy.off=Rm("off"),u(p,i0=le),p.prototype._onframe=function(){if(!this._disposed){n0(this);var t=this._scheduler;if(this[Dm]){var e=this[Dm].silent;this[km]=!0;try{Fm(this),Gm.update.call(this,null,this[Dm].updateParams)}catch(t){throw this[km]=!1,this[Dm]=null,t}this._zr.flush(),this[km]=!1,this[Dm]=null,Ym.call(this,e),qm.call(this,e)}else if(t.unfinished){var n=1,i=this._model,r=this._api;t.unfinished=!1;do{var o=+new Date}while(t.performSeriesTasks(i),t.performDataProcessorTasks(i),Um(this,i),t.performVisualTasks(i),Qm(this,this._model,r,"remain",{}),0<(n-=+new Date-o)&&t.unfinished);t.unfinished||this._zr.flush()}}},p.prototype.getDom=function(){return this._dom},p.prototype.getId=function(){return this.id},p.prototype.getZr=function(){return this._zr},p.prototype.isSSR=function(){return this._ssr},p.prototype.setOption=function(t,e,n){if(!this[km])if(this._disposed)this.id;else{R(e)&&(n=e.lazyUpdate,i=e.silent,r=e.replaceMerge,o=e.transition,e=e.notMerge),this[km]=!0,this._model&&!e||(e=new Sd(this._api),a=this._theme,(s=this._model=new dd).scheduler=this._scheduler,s.ssr=this._ssr,s.init(null,null,null,a,this._locale,e)),this._model.setOption(t,{replaceMerge:r},f0);var i,r,o,a,s={seriesTransition:o,optionChanged:!0};if(n)this[Dm]={silent:i,updateParams:s},this[km]=!1,this.getZr().wakeUp();else{try{Fm(this),Gm.update.call(this,null,s)}catch(t){throw this[Dm]=null,this[km]=!1,t}this._ssr||this._zr.flush(),this[Dm]=null,this[km]=!1,Ym.call(this,i),qm.call(this,i)}}},p.prototype.setTheme=function(){},p.prototype.getModel=function(){return this._model},p.prototype.getOption=function(){return this._model&&this._model.getOption()},p.prototype.getWidth=function(){return this._zr.getWidth()},p.prototype.getHeight=function(){return this._zr.getHeight()},p.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||b.hasGlobalWindow&&window.devicePixelRatio||1},p.prototype.getRenderedCanvas=function(t){return this.renderToCanvas(t)},p.prototype.renderToCanvas=function(t){return this._zr.painter.getRenderedCanvas({backgroundColor:(t=t||{}).backgroundColor||this._model.get("backgroundColor"),pixelRatio:t.pixelRatio||this.getDevicePixelRatio()})},p.prototype.renderToSVGString=function(t){return this._zr.painter.renderToString({useViewBox:(t=t||{}).useViewBox})},p.prototype.getSvgDataURL=function(){var t;if(b.svgSupported)return O((t=this._zr).storage.getDisplayList(),function(t){t.stopAnimation(null,!0)}),t.painter.toDataURL()},p.prototype.getDataURL=function(t){var e,n,i,r;if(!this._disposed)return r=(t=t||{}).excludeComponents,e=this._model,n=[],i=this,O(r,function(t){e.eachComponent({mainType:t},function(t){t=i._componentsMap[t.__viewId];t.group.ignore||(n.push(t),t.group.ignore=!0)})}),r="svg"===this._zr.painter.getType()?this.getSvgDataURL():this.renderToCanvas(t).toDataURL("image/"+(t&&t.type||"png")),O(n,function(t){t.group.ignore=!1}),r;this.id},p.prototype.getConnectedDataURL=function(i){var r,o,a,s,l,u,h,c,p,e,t,n,d,f,g;if(!this._disposed)return r="svg"===i.type,o=this.group,a=Math.min,s=Math.max,_0[o]?(u=l=1/0,c=h=-1/0,p=[],e=i&&i.pixelRatio||this.getDevicePixelRatio(),O(v0,function(t,e){var n;t.group===o&&(n=r?t.getZr().painter.getSvgDom().innerHTML:t.renderToCanvas(y(i)),t=t.getDom().getBoundingClientRect(),l=a(t.left,l),u=a(t.top,u),h=s(t.right,h),c=s(t.bottom,c),p.push({dom:n,left:t.left,top:t.top}))}),t=(h*=e)-(l*=e),n=(c*=e)-(u*=e),d=G.createCanvas(),(f=Zr(d,{renderer:r?"svg":"canvas"})).resize({width:t,height:n}),r?(g="",O(p,function(t){var e=t.left-l,n=t.top-u;g+='<g transform="translate('+e+","+n+')">'+t.dom+"</g>"}),f.painter.getSvgRoot().innerHTML=g,i.connectedBackgroundColor&&f.painter.setBackgroundColor(i.connectedBackgroundColor),f.refreshImmediately(),f.painter.toDataURL()):(i.connectedBackgroundColor&&f.add(new As({shape:{x:0,y:0,width:t,height:n},style:{fill:i.connectedBackgroundColor}})),O(p,function(t){t=new ws({style:{x:t.left*e-l,y:t.top*e-u,image:t.dom}});f.add(t)}),f.refreshImmediately(),d.toDataURL("image/"+(i&&i.type||"png")))):this.getDataURL(i);this.id},p.prototype.convertToPixel=function(t,e){return Wm(this,"convertToPixel",t,e)},p.prototype.convertFromPixel=function(t,e){return Wm(this,"convertFromPixel",t,e)},p.prototype.containPixel=function(t,i){var r;if(!this._disposed)return O(Oo(this._model,t),function(t,n){0<=n.indexOf("Models")&&O(t,function(t){var e=t.coordinateSystem;e&&e.containPoint?r=r||!!e.containPoint(i):"seriesModels"===n&&(e=this._chartsMap[t.__viewId])&&e.containPoint&&(r=r||e.containPoint(i,t))},this)},this),!!r;this.id},p.prototype.getVisual=function(t,e){var t=Oo(this._model,t,{defaultMainType:"series"}),n=t.seriesModel.getData(),t=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty("dataIndex")?n.indexOfRawIndex(t.dataIndex):null;if(null!=t){var i=n,r=t,o=e;switch(o){case"color":return i.getItemVisual(r,"style")[i.getVisual("drawType")];case"opacity":return i.getItemVisual(r,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return i.getItemVisual(r,o)}}else{var a=n,s=e;switch(s){case"color":return a.getVisual("style")[a.getVisual("drawType")];case"opacity":return a.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return a.getVisual(s)}}},p.prototype.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},p.prototype.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]},p.prototype._initEvents=function(){var t,n,i,s=this;O(h0,function(a){function t(t){var n,e,i,r=s.getModel(),o=t.target;"globalout"===a?n={}:o&&Ny(o,function(t){var e,t=D(t);return t&&null!=t.dataIndex?(e=t.dataModel||r.getSeriesByIndex(t.seriesIndex),n=e&&e.getDataParams(t.dataIndex,t.dataType,o)||{},1):t.eventData&&(n=L({},t.eventData),1)},!0),n&&(e=n.componentType,i=n.componentIndex,"markLine"!==e&&"markPoint"!==e&&"markArea"!==e||(e="series",i=n.seriesIndex),i=(e=e&&null!=i&&r.getComponent(e,i))&&s["series"===e.mainType?"_chartsMap":"_componentsMap"][e.__viewId],n.event=t,n.type=a,s._$eventProcessor.eventInfo={targetEl:o,packedEvent:n,model:e,view:i},s.trigger(a,n))}t.zrEventfulCallAtLast=!0,s._zr.on(a,t,s)}),O(p0,function(t,e){s._messageCenter.on(e,function(t){this.trigger(e,t)},s)}),O(["selectchanged"],function(e){s._messageCenter.on(e,function(t){this.trigger(e,t)},s)}),t=this._messageCenter,i=(n=this)._api,t.on("selectchanged",function(t){var e=i.getModel();t.isFromClick?(Ry("map","selectchanged",n,e,t),Ry("pie","selectchanged",n,e,t)):"select"===t.fromAction?(Ry("map","selected",n,e,t),Ry("pie","selected",n,e,t)):"unselect"===t.fromAction&&(Ry("map","unselected",n,e,t),Ry("pie","unselected",n,e,t))})},p.prototype.isDisposed=function(){return this._disposed},p.prototype.clear=function(){this._disposed?this.id:this.setOption({series:[]},!0)},p.prototype.dispose=function(){var t,e,n;this._disposed?this.id:(this._disposed=!0,this.getDom()&&zo(this.getDom(),b0,""),e=(t=this)._api,n=t._model,O(t._componentsViews,function(t){t.dispose(n,e)}),O(t._chartsViews,function(t){t.dispose(n,e)}),t._zr.dispose(),t._dom=t._model=t._chartsMap=t._componentsMap=t._chartsViews=t._componentsViews=t._scheduler=t._api=t._zr=t._throttledZrFlush=t._theme=t._coordSysMgr=t._messageCenter=null,delete v0[t.id])},p.prototype.resize=function(t){if(!this[km])if(this._disposed)this.id;else{this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var e=e.resetOption("media"),n=t&&t.silent;this[Dm]&&(null==n&&(n=this[Dm].silent),e=!0,this[Dm]=null),this[km]=!0;try{e&&Fm(this),Gm.update.call(this,{type:"resize",animation:L({duration:0},t&&t.animation)})}catch(t){throw this[km]=!1,t}this[km]=!1,Ym.call(this,n),qm.call(this,n)}}},p.prototype.showLoading=function(t,e){this._disposed?this.id:(R(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),m0[t]&&(t=m0[t](this._api,e),e=this._zr,this._loadingFX=t,e.add(t)))},p.prototype.hideLoading=function(){this._disposed?this.id:(this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null)},p.prototype.makeActionFromEvent=function(t){var e=L({},t);return e.type=p0[t.type],e},p.prototype.dispatchAction=function(t,e){var n;this._disposed?this.id:(R(e)||(e={silent:!!e}),c0[t.type]&&this._model&&(this[km]?this._pendingActions.push(t):(n=e.silent,Xm.call(this,t,n),(t=e.flush)?this._zr.flush():!1!==t&&b.browser.weChat&&this._throttledZrFlush(),Ym.call(this,n),qm.call(this,n))))},p.prototype.updateLabelLayout=function(){Cm.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},p.prototype.appendData=function(t){var e;this._disposed?this.id:(e=t.seriesIndex,this.getModel().getSeriesByIndex(e).appendData(t),this._scheduler.unfinished=!0,this.getZr().wakeUp())},p.internalField=(Fm=function(t){var e=t._scheduler;e.restorePipelines(t._model),e.prepareStageTasks(),Vm(t,!0),Vm(t,!1),e.plan()},Vm=function(t,r){for(var o=t._model,a=t._scheduler,s=r?t._componentsViews:t._chartsViews,l=r?t._componentsMap:t._chartsMap,u=t._zr,h=t._api,e=0;e<s.length;e++)s[e].__alive=!1;function n(t){var e,n=t.__requireNewView,i=(t.__requireNewView=!1,"_ec_"+t.id+"_"+t.type),n=!n&&l[i];n||(e=Go(t.type),(n=new(r?Gg.getClass(e.main,e.sub):qg.getClass(e.sub))).init(o,h),l[i]=n,s.push(n),u.add(n.group)),t.__viewId=n.__id=i,n.__alive=!0,n.__model=t,n.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},r||a.prepareView(n,t,o,h)}r?o.eachComponent(function(t,e){"series"!==t&&n(e)}):o.eachSeries(n);for(e=0;e<s.length;){var i=s[e];i.__alive?e++:(r||i.renderTask.dispose(),u.remove(i.group),i.dispose(o,h),s.splice(e,1),l[i.__id]===i&&delete l[i.__id],i.__id=i.group.__ecComponentInfo=null)}},Hm=function(c,e,p,n,t){var i,d,r=c._model;function o(t){t&&t.__alive&&t[e]&&t[e](t.__model,r,c._api,p)}r.setUpdatePayload(p),n?((i={})[n+"Id"]=p[n+"Id"],i[n+"Index"]=p[n+"Index"],i[n+"Name"]=p[n+"Name"],i={mainType:n,query:i},t&&(i.subType=t),null!=(t=p.excludeSeriesId)&&(d=E(),O(_o(t),function(t){t=Co(t,null);null!=t&&d.set(t,!0)})),r&&r.eachComponent(i,function(t){var e,n,i=d&&null!=d.get(t.id);if(!i)if(Gl(p))if(t instanceof Rg){if(p.type===nl&&!p.notBlur&&!t.get(["emphasis","disabled"])){var i=t,r=p,o=c._api,a=i.seriesIndex,s=i.getData(r.dataType);if(s){var r=(F(r=Ao(s,r))?r[0]:r)||0,l=s.getItemGraphicEl(r);if(!l)for(var u=s.count(),h=0;!l&&h<u;)l=s.getItemGraphicEl(h++);l?Al(a,(r=D(l)).focus,r.blurScope,o):(r=i.get(["emphasis","focus"]),i=i.get(["emphasis","blurScope"]),null!=r&&Al(a,r,i,o))}}}else{a=Ll(t.mainType,t.componentIndex,p.name,c._api),r=a.focusSelf,i=a.dispatchers;p.type===nl&&r&&!p.notBlur&&Pl(t.mainType,t.componentIndex,c._api),i&&O(i,function(t){(p.type===nl?Sl:Ml)(t)})}else Hl(p)&&t instanceof Rg&&(o=t,i=p,c._api,Hl(i)&&(e=i.dataType,F(n=Ao(o.getData(e),i))||(n=[n]),o[i.type===al?"toggleSelect":i.type===rl?"select":"unselect"](n,e)),Ol(t),e0(c))},c),r&&r.eachComponent(i,function(t){d&&null!=d.get(t.id)||o(c["series"===n?"_chartsMap":"_componentsMap"][t.__viewId])},c)):O([].concat(c._componentsViews).concat(c._chartsViews),o)},Gm={prepareAndUpdate:function(t){Fm(this),Gm.update.call(this,t,{optionChanged:null!=t.newOption})},update:function(t,e){var n=this._model,i=this._api,r=this._zr,o=this._coordSysMgr,a=this._scheduler;n&&(n.setUpdatePayload(t),a.restoreData(n,t),a.performSeriesTasks(n),o.create(n,i),a.performDataProcessorTasks(n,t),Um(this,n),o.update(n,i),o0(n),a.performVisualTasks(n,t),Km(this,n,i,t,e),o=n.get("backgroundColor")||"transparent",a=n.get("darkMode"),r.setBackgroundColor(o),null!=a&&"auto"!==a&&r.setDarkMode(a),Cm.trigger("afterupdate",n,i))},updateTransform:function(n){var i,r,o=this,a=this._model,s=this._api;a&&(a.setUpdatePayload(n),i=[],a.eachComponent(function(t,e){"series"!==t&&(t=o.getViewOfComponentModel(e))&&t.__alive&&(!t.updateTransform||(e=t.updateTransform(e,a,s,n))&&e.update)&&i.push(t)}),r=E(),a.eachSeries(function(t){var e=o._chartsMap[t.__viewId];(!e.updateTransform||(e=e.updateTransform(t,a,s,n))&&e.update)&&r.set(t.uid,1)}),o0(a),this._scheduler.performVisualTasks(a,n,{setDirty:!0,dirtyMap:r}),Qm(this,a,s,n,{},r),Cm.trigger("afterupdate",a,s))},updateView:function(t){var e=this._model;e&&(e.setUpdatePayload(t),qg.markUpdateMethod(t,"updateView"),o0(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0}),Km(this,e,this._api,t,{}),Cm.trigger("afterupdate",e,this._api))},updateVisual:function(n){var i=this,r=this._model;r&&(r.setUpdatePayload(n),r.eachSeries(function(t){t.getData().clearAllVisual()}),qg.markUpdateMethod(n,"updateVisual"),o0(r),this._scheduler.performVisualTasks(r,n,{visualType:"visual",setDirty:!0}),r.eachComponent(function(t,e){"series"!==t&&(t=i.getViewOfComponentModel(e))&&t.__alive&&t.updateVisual(e,r,i._api,n)}),r.eachSeries(function(t){i._chartsMap[t.__viewId].updateVisual(t,r,i._api,n)}),Cm.trigger("afterupdate",r,this._api))},updateLayout:function(t){Gm.update.call(this,t)}},Wm=function(t,e,n,i){if(t._disposed)t.id;else for(var r=t._model,o=t._coordSysMgr.getCoordinateSystems(),a=Oo(r,n),s=0;s<o.length;s++){var l=o[s];if(l[e]&&null!=(l=l[e](r,a,i)))return l}},Um=function(t,e){var n=t._chartsMap,i=t._scheduler;e.eachSeries(function(t){i.updateStreamModes(t,n[t.__viewId])})},Xm=function(i,t){var r,o,a=this,e=this.getModel(),n=i.type,s=i.escapeConnect,l=c0[n],u=l.actionInfo,h=(u.update||"update").split(":"),c=h.pop(),p=null!=h[0]&&Go(h[0]),h=(this[km]=!0,[i]),d=!1,f=(i.batch&&(d=!0,h=B(i.batch,function(t){return(t=z(L({},t),i)).batch=null,t})),[]),g=Hl(i),y=Gl(i);if(y&&Dl(this._api),O(h,function(t){var e,n;(r=(r=l.action(t,a._model,a._api))||L({},t)).type=u.event||r.type,f.push(r),y?(e=(n=Ro(i)).queryOptionMap,n=n.mainTypeSpecified?e.keys()[0]:"series",Hm(a,c,t,n),e0(a)):g?(Hm(a,c,t,"series"),e0(a)):p&&Hm(a,c,t,p.main,p.sub)}),"none"!==c&&!y&&!g&&!p)try{this[Dm]?(Fm(this),Gm.update.call(this,i),this[Dm]=null):Gm[c].call(this,i)}catch(t){throw this[km]=!1,t}r=d?{type:u.event||n,escapeConnect:s,batch:f}:f[0],this[km]=!1,t||((h=this._messageCenter).trigger(r.type,r),g&&(d={type:"selectchanged",escapeConnect:s,selected:(o=[],e.eachSeries(function(n){O(n.getAllData(),function(t){t.data;var t=t.type,e=n.getSelectedDataIndices();0<e.length&&(e={dataIndex:e,seriesIndex:n.seriesIndex},null!=t&&(e.dataType=t),o.push(e))})}),o),isFromClick:i.isFromClick||!1,fromAction:i.type,fromActionPayload:i},h.trigger(d.type,d)))},Ym=function(t){for(var e=this._pendingActions;e.length;){var n=e.shift();Xm.call(this,n,t)}},qm=function(t){t||this.trigger("updated")},Zm=function(e,n){e.on("rendered",function(t){n.trigger("rendered",t),!e.animation.isFinished()||n[Dm]||n._scheduler.unfinished||n._pendingActions.length||n.trigger("finished")})},jm=function(t,a){t.on("mouseover",function(t){var e,n,i,r,o=Ny(t.target,Vl);o&&(o=o,e=t,t=a._api,n=D(o),i=(r=Ll(n.componentMainType,n.componentIndex,n.componentHighDownName,t)).dispatchers,r=r.focusSelf,i?(r&&Pl(n.componentMainType,n.componentIndex,t),O(i,function(t){return wl(t,e)})):(Al(n.seriesIndex,n.focus,n.blurScope,t),"self"===n.focus&&Pl(n.componentMainType,n.componentIndex,t),wl(o,e)),e0(a))}).on("mouseout",function(t){var e,n,i=Ny(t.target,Vl);i&&(i=i,e=t,Dl(t=a._api),(n=Ll((n=D(i)).componentMainType,n.componentIndex,n.componentHighDownName,t).dispatchers)?O(n,function(t){return bl(t,e)}):bl(i,e),e0(a))}).on("click",function(t){var e,t=Ny(t.target,function(t){return null!=D(t).dataIndex},!0);t&&(e=t.selected?"unselect":"select",t=D(t),a._api.dispatchAction({type:e,dataType:t.dataType,dataIndexInside:t.dataIndex,seriesIndex:t.seriesIndex,isFromClick:!0}))})},Km=function(t,e,n,i,r){var o,a,s,l,u,h,c;u=[],c=!(h=[]),(o=e).eachComponent(function(t,e){var n=e.get("zlevel")||0,i=e.get("z")||0,r=e.getZLevelKey();c=c||!!r,("series"===t?h:u).push({zlevel:n,z:i,idx:e.componentIndex,type:t,key:r})}),c&&(mn(a=u.concat(h),function(t,e){return t.zlevel===e.zlevel?t.z-e.z:t.zlevel-e.zlevel}),O(a,function(t){var e=o.getComponent(t.type,t.idx),n=t.zlevel,t=t.key;null!=s&&(n=Math.max(s,n)),t?(n===s&&t!==l&&n++,l=t):l&&(n===s&&n++,l=""),s=n,e.setZLevel(n)})),$m(t,e,n,i,r),O(t._chartsViews,function(t){t.__alive=!1}),Qm(t,e,n,i,r),O(t._chartsViews,function(t){t.__alive||t.remove(e,n)})},$m=function(t,n,i,r,e,o){O(o||t._componentsViews,function(t){var e=t.__model;l0(0,t),t.render(e,n,i,r),s0(e,t),u0(e,t)})},Qm=function(r,t,e,o,n,a){var i,s,l,u,h=r._scheduler,c=(n=L(n||{},{updatedSeries:t.getSeries()}),Cm.trigger("series:beforeupdate",t,e,n),!1);t.eachSeries(function(t){var e,n=r._chartsMap[t.__viewId],i=(n.__alive=!0,n.renderTask);h.updatePayload(i,o),l0(0,n),a&&a.get(t.uid)&&i.dirty(),i.perform(h.getPerformArgs(i))&&(c=!0),n.group.silent=!!t.get("silent"),i=n,e=t.get("blendMode")||null,i.eachRendered(function(t){t.isGroup||(t.style.blend=e)}),Ol(t)}),h.unfinished=c||h.unfinished,Cm.trigger("series:layoutlabels",t,e,n),Cm.trigger("series:transition",t,e,n),t.eachSeries(function(t){var e=r._chartsMap[t.__viewId];s0(t,e),u0(t,e)}),s=t,l=(i=r)._zr.storage,u=0,l.traverse(function(t){t.isGroup||u++}),u>s.get("hoverLayerThreshold")&&!b.node&&!b.worker&&s.eachSeries(function(t){t.preventUsingHoverLayer||(t=i._chartsMap[t.__viewId]).__alive&&t.eachRendered(function(t){t.states.emphasis&&(t.states.emphasis.hoverLayer=!0)})}),Cm.trigger("series:afterupdate",t,e,n)},e0=function(t){t[Am]=!0,t.getZr().wakeUp()},n0=function(t){t[Am]&&(t.getZr().storage.traverse(function(t){Ah(t)||a0(t)}),t[Am]=!1)},Jm=function(n){return u(t,e=md),t.prototype.getCoordinateSystems=function(){return n._coordSysMgr.getCoordinateSystems()},t.prototype.getComponentByElement=function(t){for(;t;){var e=t.__ecComponentInfo;if(null!=e)return n._model.getComponent(e.mainType,e.index);t=t.parent}},t.prototype.enterEmphasis=function(t,e){Sl(t,e),e0(n)},t.prototype.leaveEmphasis=function(t,e){Ml(t,e),e0(n)},t.prototype.enterBlur=function(t){yl(t,cl),e0(n)},t.prototype.leaveBlur=function(t){Tl(t),e0(n)},t.prototype.enterSelect=function(t){Cl(t),e0(n)},t.prototype.leaveSelect=function(t){Il(t),e0(n)},t.prototype.getModel=function(){return n.getModel()},t.prototype.getViewOfComponentModel=function(t){return n.getViewOfComponentModel(t)},t.prototype.getViewOfSeriesModel=function(t){return n.getViewOfSeriesModel(t)},new t(n);function t(){return null!==e&&e.apply(this,arguments)||this}var e},void(t0=function(i){function r(t,e){for(var n=0;n<t.length;n++)t[n][Lm]=e}O(p0,function(t,e){i._messageCenter.on(e,function(t){var e,n;!_0[i.group]||0===i[Lm]||t&&t.escapeConnect||(e=i.makeActionFromEvent(t),n=[],O(v0,function(t){t!==i&&t.group===i.group&&n.push(t)}),r(n,0),O(n,function(t){1!==t[Lm]&&t.dispatchAction(e)}),r(n,2))})})})),p);function p(t,e,n){var i=i0.call(this,new Ay)||this,t=(i._chartsViews=[],i._chartsMap={},i._componentsViews=[],i._componentsMap={},i._pendingActions=[],n=n||{},V(e)&&(e=y0[e]),i._dom=t,n.ssr&&Kr(function(t){var e,t=D(t),n=t.dataIndex;if(null!=n)return(e=E()).set("series_index",t.seriesIndex),e.set("data_index",n),t.ssrType&&e.set("ssr_type",t.ssrType),e}),i._zr=Zr(t,{renderer:n.renderer||"canvas",devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height,ssr:n.ssr,useDirtyRect:N(n.useDirtyRect,!1),useCoarsePointer:N(n.useCoarsePointer,"auto"),pointerSize:n.pointerSize})),n=(i._ssr=n.ssr,i._throttledZrFlush=iy(ct(t.flush,t),17),(e=y(e))&&Wd(e,!0),i._theme=e,i._locale=V(e=n.locale||Hc)?(n=Fc[e.toUpperCase()]||{},e===Ec||e===zc?y(n):d(y(n),y(Fc[Bc]),!1)):d(y(e),y(Fc[Bc]),!1),i._coordSysMgr=new xd,i._api=Jm(i));function r(t,e){return t.__prio-e.__prio}return mn(g0,r),mn(d0,r),i._scheduler=new dy(i,n,d0,g0),i._messageCenter=new zm,i._initEvents(),i.resize=ct(i.resize,i),t.animation.on("frame",i._onframe,i),Zm(t,i),jm(t,i),kt(i),i}function o0(t){t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()})}function a0(t){for(var e=[],n=t.currentStates,i=0;i<n.length;i++){var r=n[i];"emphasis"!==r&&"blur"!==r&&"select"!==r&&e.push(r)}t.selected&&t.states.select&&e.push("select"),t.hoverState===Js&&t.states.emphasis?e.push("emphasis"):t.hoverState===Qs&&t.states.blur&&e.push("blur"),t.useStates(e)}function s0(t,e){var n,i;t.preventAutoZ||(n=t.get("z")||0,i=t.get("zlevel")||0,e.eachRendered(function(t){return function t(e,n,i,r){var o=e.getTextContent();var a=e.getTextGuideLine();var s=e.isGroup;if(s)for(var l=e.childrenRef(),u=0;u<l.length;u++)r=Math.max(t(l[u],n,i,r),r);else e.z=n,e.zlevel=i,r=Math.max(e.z2,r);o&&(o.z=n,o.zlevel=i,isFinite(r))&&(o.z2=r+2);a&&(s=e.textGuideLineConfig,a.z=n,a.zlevel=i,isFinite(r))&&(a.z2=r+(s&&s.showAbove?1:-1));return r}(t,n,i,-1/0),!0}))}function l0(t,e){e.eachRendered(function(t){var e,n;Ah(t)||(e=t.getTextContent(),n=t.getTextGuideLine(),t.stateTransition&&(t.stateTransition=null),e&&e.stateTransition&&(e.stateTransition=null),n&&n.stateTransition&&(n.stateTransition=null),t.hasState()?(t.prevStates=t.currentStates,t.clearStates()):t.prevStates&&(t.prevStates=null))})}function u0(t,e){var n=t.getModel("stateAnimation"),r=t.isAnimationEnabled(),t=n.get("duration"),o=0<t?{duration:t,delay:n.get("delay"),easing:n.get("easing")}:null;e.eachRendered(function(t){var e,n,i;t.states&&t.states.emphasis&&(Ah(t)||(t instanceof ds&&((i=js(n=t)).normalFill=n.style.fill,i.normalStroke=n.style.stroke,n=n.states.select||{},i.selectFill=n.style&&n.style.fill||null,i.selectStroke=n.style&&n.style.stroke||null),t.__dirty&&(i=t.prevStates)&&t.useStates(i),r&&(t.stateTransition=o,n=t.getTextContent(),e=t.getTextGuideLine(),n&&(n.stateTransition=o),e)&&(e.stateTransition=o),t.__dirty&&a0(t)))})}var Wy=r0.prototype,h0=(Wy.on=Om("on"),Wy.off=Om("off"),Wy.one=function(i,r,t){var o=this;this.on.call(this,i,function t(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];r&&r.apply&&r.apply(this,e),o.off(i,t)},t)},["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"]);var c0={},p0={},d0=[],f0=[],g0=[],y0={},m0={},v0={},_0={},x0=+new Date,w0=+new Date,b0="_echarts_instance_";function S0(t){_0[t]=!1}Vy=S0;function M0(t){return v0[e=b0,(t=t).getAttribute?t.getAttribute(e):t[e]];var e}function T0(t,e){y0[t]=e}function C0(t){I(f0,t)<0&&f0.push(t)}function I0(t,e){E0(d0,t,e,2e3)}function k0(t){A0("afterinit",t)}function D0(t){A0("afterupdate",t)}function A0(t,e){Cm.on(t,e)}function P0(t,e,n){k(e)&&(n=e,e="");var i=R(t)?t.type:[t,t={event:e}][0];t.event=(t.event||i).toLowerCase(),e=t.event,p0[e]||(Tt(Pm.test(i)&&Pm.test(e)),c0[i]||(c0[i]={action:n,actionInfo:t}),p0[e]=i)}function L0(t,e){xd.register(t,e)}function O0(t,e){E0(g0,t,e,1e3,"layout")}function R0(t,e){E0(g0,t,e,3e3,"visual")}var N0=[];function E0(t,e,n,i,r){(k(e)||R(e))&&(n=e,e=i),0<=I(N0,n)||(N0.push(n),(i=dy.wrapStageHandler(n,r)).__prio=e,i.__raw=n,t.push(i))}function z0(t,e){m0[t]=e}function B0(t,e,n){var i=Im.registerMap;i&&i(t,e,n)}function F0(t){var e=(t=y(t)).type,n=(e||f(""),e.split(":")),i=(2!==n.length&&f(""),!1);"echarts"===n[0]&&(e=n[1],i=!0),t.__isBuiltIn=i,Kf.set(e,t)}R0(2e3,$o),R0(4500,qh),R0(4500,Zc),R0(2e3,Ic),R0(4500,Sc),R0(7e3,function(e,i){e.eachRawSeries(function(t){var n;!e.isSeriesFiltered(t)&&((n=t.getData()).hasItemVisual()&&n.each(function(t){var e=n.getItemVisual(t,"decal");e&&(n.ensureUniqueItemVisual(t,"style").decal=Mm(e,i))}),t=n.getVisual("decal"))&&(n.getVisual("style").decal=Mm(t,i))})}),C0(Wd),I0(900,function(t){var i=E();t.eachSeries(function(t){var e,n=t.get("stack");n&&(n=i.get(n)||i.set(n,[]),(t={stackResultDimension:(e=t.getData()).getCalculationInfo("stackResultDimension"),stackedOverDimension:e.getCalculationInfo("stackedOverDimension"),stackedDimension:e.getCalculationInfo("stackedDimension"),stackedByDimension:e.getCalculationInfo("stackedByDimension"),isStackedByIndex:e.getCalculationInfo("isStackedByIndex"),data:e,seriesModel:t}).stackedDimension)&&(t.isStackedByIndex||t.stackedByDimension)&&(n.length&&e.setCalculationInfo("stackedOnSeries",n[n.length-1].seriesModel),n.push(t))}),i.each(Ud)}),z0("default",function(i,r){z(r=r||{},{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var o,t=new Hr,a=new As({style:{fill:r.maskColor},zlevel:r.zlevel,z:1e4}),s=(t.add(a),new Ns({style:{text:r.text,fill:r.textColor,fontSize:r.fontSize,fontWeight:r.fontWeight,fontStyle:r.fontStyle,fontFamily:r.fontFamily},zlevel:r.zlevel,z:10001})),l=new As({style:{fill:"none"},textContent:s,textConfig:{position:"right",distance:10},zlevel:r.zlevel,z:10001});return t.add(l),r.showSpinner&&((o=new oh({shape:{startAngle:-py/2,endAngle:-py/2+.1,r:r.spinnerRadius},style:{stroke:r.color,lineCap:"round",lineWidth:r.lineWidth},zlevel:r.zlevel,z:10001})).animateShape(!0).when(1e3,{endAngle:3*py/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:3*py/2}).delay(300).start("circularInOut"),t.add(o)),t.resize=function(){var t=s.getBoundingRect().width,e=r.showSpinner?r.spinnerRadius:0,t=(i.getWidth()-2*e-(r.showSpinner&&t?10:0)-t)/2-(r.showSpinner&&t?0:5+t/2)+(r.showSpinner?0:t/2)+(t?0:e),n=i.getHeight()/2;r.showSpinner&&o.setShape({cx:t,cy:n}),l.setShape({x:t-e,y:n-e,width:2*e,height:2*e}),a.setShape({x:0,y:0,width:i.getWidth(),height:i.getHeight()})},t.resize(),t}),P0({type:nl,event:nl,update:nl},Ft),P0({type:il,event:il,update:il},Ft),P0({type:rl,event:rl,update:rl},Ft),P0({type:ol,event:ol,update:ol},Ft),P0({type:al,event:al,update:al},Ft),T0("light",Dc),T0("dark",Tc);function V0(t){return null==t?0:t.length||1}function H0(t){return t}W0.prototype.add=function(t){return this._add=t,this},W0.prototype.update=function(t){return this._update=t,this},W0.prototype.updateManyToOne=function(t){return this._updateManyToOne=t,this},W0.prototype.updateOneToMany=function(t){return this._updateOneToMany=t,this},W0.prototype.updateManyToMany=function(t){return this._updateManyToMany=t,this},W0.prototype.remove=function(t){return this._remove=t,this},W0.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},W0.prototype._executeOneToOne=function(){var t=this._old,e=this._new,n={},i=new Array(t.length),r=new Array(e.length);this._initIndexMap(t,null,i,"_oldKeyGetter"),this._initIndexMap(e,n,r,"_newKeyGetter");for(var o=0;o<t.length;o++){var a,s=i[o],l=n[s],u=V0(l);1<u?(a=l.shift(),1===l.length&&(n[s]=l[0]),this._update&&this._update(a,o)):1===u?(n[s]=null,this._update&&this._update(l,o)):this._remove&&this._remove(o)}this._performRestAdd(r,n)},W0.prototype._executeMultiple=function(){var t=this._old,e=this._new,n={},i={},r=[],o=[];this._initIndexMap(t,n,r,"_oldKeyGetter"),this._initIndexMap(e,i,o,"_newKeyGetter");for(var a=0;a<r.length;a++){var s=r[a],l=n[s],u=i[s],h=V0(l),c=V0(u);if(1<h&&1===c)this._updateManyToOne&&this._updateManyToOne(u,l),i[s]=null;else if(1===h&&1<c)this._updateOneToMany&&this._updateOneToMany(u,l),i[s]=null;else if(1===h&&1===c)this._update&&this._update(u,l),i[s]=null;else if(1<h&&1<c)this._updateManyToMany&&this._updateManyToMany(u,l),i[s]=null;else if(1<h)for(var p=0;p<h;p++)this._remove&&this._remove(l[p]);else this._remove&&this._remove(l)}this._performRestAdd(o,i)},W0.prototype._performRestAdd=function(t,e){for(var n=0;n<t.length;n++){var i=t[n],r=e[i],o=V0(r);if(1<o)for(var a=0;a<o;a++)this._add&&this._add(r[a]);else 1===o&&this._add&&this._add(r);e[i]=null}},W0.prototype._initIndexMap=function(t,e,n,i){for(var r=this._diffModeMultiple,o=0;o<t.length;o++){var a,s,l="_ec_"+this[i](t[o],o);r||(n[o]=l),e&&(0===(s=V0(a=e[l]))?(e[l]=o,r&&n.push(l)):1===s?e[l]=[a,o]:a.push(o))}};var G0=W0;function W0(t,e,n,i,r,o){this._old=t,this._new=e,this._oldKeyGetter=n||H0,this._newKeyGetter=i||H0,this.context=r,this._diffModeMultiple="multiple"===o}X0.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},X0.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames};var U0=X0;function X0(t,e){this._encode=t,this._schema=e}function Y0(o,t){var e={},a=e.encode={},s=E(),l=[],u=[],h={},i=(O(o.dimensions,function(t){var e,n,i=o.getDimensionInfo(t),r=i.coordDim;r&&(e=i.coordDimIndex,q0(a,r)[e]=t,i.isExtraCoord||(s.set(r,1),"ordinal"!==(n=i.type)&&"time"!==n&&(l[0]=t),q0(h,r)[e]=o.getDimensionIndex(i.name)),i.defaultTooltip)&&u.push(t),Wp.each(function(t,e){var n=q0(a,e),e=i.otherDims[e];null!=e&&!1!==e&&(n[e]=i.name)})}),[]),r={},n=(s.each(function(t,e){var n=a[e];r[e]=n[0],i=i.concat(n)}),e.dataDimsOnCoord=i,e.dataDimIndicesOnCoord=B(i,function(t){return o.getDimensionInfo(t).storeDimIndex}),e.encodeFirstDimNotExtra=r,a.label),n=(n&&n.length&&(l=n.slice()),a.tooltip);return n&&n.length?u=n.slice():u.length||(u=l.slice()),a.defaultedLabel=l,a.defaultedTooltip=u,e.userOutput=new U0(h,t),e}function q0(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}var Z0=function(t){this.otherDims={},null!=t&&L(this,t)},j0=Po(),K0={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},$0=(Q0.prototype.isDimensionOmitted=function(){return this._dimOmitted},Q0.prototype._updateDimOmitted=function(t){(this._dimOmitted=t)&&!this._dimNameMap&&(this._dimNameMap=ev(this.source))},Q0.prototype.getSourceDimensionIndex=function(t){return N(this._dimNameMap.get(t),-1)},Q0.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},Q0.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=tf(this.source),n=!(30<t),i="",r=[],o=0,a=0;o<t;o++){var s,l=void 0,u=void 0,h=void 0,c=this.dimensions[a];c&&c.storeDimIndex===o?(l=e?c.name:null,u=c.type,h=c.ordinalMeta,a++):(s=this.getSourceDimension(o))&&(l=e?s.name:null,u=s.type),r.push({property:l,type:u,ordinalMeta:h}),!e||null==l||c&&c.isCalculationCoord||(i+=n?l.replace(/\`/g,"`1").replace(/\$/g,"`2"):l),i=i+"$"+(K0[u]||"f"),h&&(i+=h.uid),i+="$"}var p=this.source;return{dimensions:r,hash:[p.seriesLayoutBy,p.startIndex,i].join("$$")}},Q0.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,n=0;e<this._fullDimCount;e++){var i=void 0,r=this.dimensions[n];r&&r.storeDimIndex===e?(r.isCalculationCoord||(i=r.name),n++):(r=this.getSourceDimension(e))&&(i=r.name),t.push(i)}return t},Q0.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},Q0);function Q0(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}function J0(t){return t instanceof $0}function tv(t){for(var e=E(),n=0;n<(t||[]).length;n++){var i=t[n],i=R(i)?i.name:i;null!=i&&null==e.get(i)&&e.set(i,n)}return e}function ev(t){var e=j0(t);return e.dimNameMap||(e.dimNameMap=tv(t.dimensionsDefine))}var nv,iv,rv,ov,av,sv,lv,uv=R,hv=B,cv="undefined"==typeof Int32Array?Array:Int32Array,pv=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],dv=["_approximateExtent"],fv=(m.prototype.getDimension=function(t){var e;return null==(e=this._recognizeDimIndex(t))?t:(e=t,this._dimOmitted?null!=(t=this._dimIdxToName.get(e))?t:(t=this._schema.getSourceDimension(e))?t.name:void 0:this.dimensions[e])},m.prototype.getDimensionIndex=function(t){var e=this._recognizeDimIndex(t);return null!=e?e:null==t?-1:(e=this._getDimInfo(t))?e.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(t):-1},m.prototype._recognizeDimIndex=function(t){if(H(t)||null!=t&&!isNaN(t)&&!this._getDimInfo(t)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(t)<0))return+t},m.prototype._getStoreDimIndex=function(t){return this.getDimensionIndex(t)},m.prototype.getDimensionInfo=function(t){return this._getDimInfo(this.getDimension(t))},m.prototype._initGetDimensionInfo=function(t){var e=this._dimInfos;this._getDimInfo=t?function(t){return e.hasOwnProperty(t)?e[t]:void 0}:function(t){return e[t]}},m.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},m.prototype.mapDimension=function(t,e){var n=this._dimSummary;return null==e?n.encodeFirstDimNotExtra[t]:(n=n.encode[t])?n[e]:null},m.prototype.mapDimensionsAll=function(t){return(this._dimSummary.encode[t]||[]).slice()},m.prototype.getStore=function(){return this._store},m.prototype.initData=function(t,e,n){var i,r,o=this;(i=t instanceof sg?t:i)||(r=this.dimensions,t=Zd(t)||st(t)?new of(t,r.length):t,i=new sg,r=hv(r,function(t){return{type:o._dimInfos[t].type,property:t}}),i.initData(t,r,n)),this._store=i,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,i.count()),this._dimSummary=Y0(this,this._schema),this.userOutput=this._dimSummary.userOutput},m.prototype.appendData=function(t){t=this._store.appendData(t);this._doInit(t[0],t[1])},m.prototype.appendValues=function(t,e){var t=this._store.appendValues(t,e&&e.length),n=t.start,i=t.end,r=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var o=n;o<i;o++)this._nameList[o]=e[o-n],r&&lv(this,o)},m.prototype._updateOrdinalMeta=function(){for(var t=this._store,e=this.dimensions,n=0;n<e.length;n++){var i=this._dimInfos[e[n]];i.ordinalMeta&&t.collectOrdinalMeta(i.storeDimIndex,i.ordinalMeta)}},m.prototype._shouldMakeIdFromName=function(){var t=this._store.getProvider();return null==this._idDimIdx&&t.getSource().sourceFormat!==Zp&&!t.fillStorage},m.prototype._doInit=function(t,e){if(!(e<=t)){var n=this._store.getProvider(),i=(this._updateOrdinalMeta(),this._nameList),r=this._idList;if(n.getSource().sourceFormat===Up&&!n.pure)for(var o=[],a=t;a<e;a++){var s,l=n.getItem(a,o);this.hasItemOption||!R(s=l)||s instanceof Array||(this.hasItemOption=!0),l&&(s=l.name,null==i[a]&&null!=s&&(i[a]=Co(s,null)),l=l.id,null==r[a])&&null!=l&&(r[a]=Co(l,null))}if(this._shouldMakeIdFromName())for(a=t;a<e;a++)lv(this,a);nv(this)}},m.prototype.getApproximateExtent=function(t){return this._approximateExtent[t]||this._store.getDataExtent(this._getStoreDimIndex(t))},m.prototype.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},m.prototype.getCalculationInfo=function(t){return this._calculationInfo[t]},m.prototype.setCalculationInfo=function(t,e){uv(t)?L(this._calculationInfo,t):this._calculationInfo[t]=e},m.prototype.getName=function(t){var t=this.getRawIndex(t),e=this._nameList[t];return e=null==(e=null==e&&null!=this._nameDimIdx?rv(this,this._nameDimIdx,t):e)?"":e},m.prototype._getCategory=function(t,e){e=this._store.get(t,e),t=this._store.getOrdinalMeta(t);return t?t.categories[e]:e},m.prototype.getId=function(t){return iv(this,this.getRawIndex(t))},m.prototype.count=function(){return this._store.count()},m.prototype.get=function(t,e){var n=this._store,t=this._dimInfos[t];if(t)return n.get(t.storeDimIndex,e)},m.prototype.getByRawIndex=function(t,e){var n=this._store,t=this._dimInfos[t];if(t)return n.getByRawIndex(t.storeDimIndex,e)},m.prototype.getIndices=function(){return this._store.getIndices()},m.prototype.getDataExtent=function(t){return this._store.getDataExtent(this._getStoreDimIndex(t))},m.prototype.getSum=function(t){return this._store.getSum(this._getStoreDimIndex(t))},m.prototype.getMedian=function(t){return this._store.getMedian(this._getStoreDimIndex(t))},m.prototype.getValues=function(t,e){var n=this,i=this._store;return F(t)?i.getValues(hv(t,function(t){return n._getStoreDimIndex(t)}),e):i.getValues(t)},m.prototype.hasValue=function(t){for(var e=this._dimSummary.dataDimIndicesOnCoord,n=0,i=e.length;n<i;n++)if(isNaN(this._store.get(e[n],t)))return!1;return!0},m.prototype.indexOfName=function(t){for(var e=0,n=this._store.count();e<n;e++)if(this.getName(e)===t)return e;return-1},m.prototype.getRawIndex=function(t){return this._store.getRawIndex(t)},m.prototype.indexOfRawIndex=function(t){return this._store.indexOfRawIndex(t)},m.prototype.rawIndexOf=function(t,e){t=t&&this._invertedIndicesMap[t],t=t&&t[e];return null==t||isNaN(t)?-1:t},m.prototype.indicesOfNearest=function(t,e,n){return this._store.indicesOfNearest(this._getStoreDimIndex(t),e,n)},m.prototype.each=function(t,e,n){k(t)&&(n=e,e=t,t=[]);n=n||this,t=hv(ov(t),this._getStoreDimIndex,this);this._store.each(t,n?ct(e,n):e)},m.prototype.filterSelf=function(t,e,n){k(t)&&(n=e,e=t,t=[]);n=n||this,t=hv(ov(t),this._getStoreDimIndex,this);return this._store=this._store.filter(t,n?ct(e,n):e),this},m.prototype.selectRange=function(n){var i=this,r={};return O(ht(n),function(t){var e=i._getStoreDimIndex(t);r[e]=n[t]}),this._store=this._store.selectRange(r),this},m.prototype.mapArray=function(t,e,n){k(t)&&(n=e,e=t,t=[]);var i=[];return this.each(t,function(){i.push(e&&e.apply(this,arguments))},n=n||this),i},m.prototype.map=function(t,e,n,i){n=n||i||this,i=hv(ov(t),this._getStoreDimIndex,this),t=sv(this);return t._store=this._store.map(i,n?ct(e,n):e),t},m.prototype.modify=function(t,e,n,i){n=n||i||this,i=hv(ov(t),this._getStoreDimIndex,this);this._store.modify(i,n?ct(e,n):e)},m.prototype.downSample=function(t,e,n,i){var r=sv(this);return r._store=this._store.downSample(this._getStoreDimIndex(t),e,n,i),r},m.prototype.minmaxDownSample=function(t,e){var n=sv(this);return n._store=this._store.minmaxDownSample(this._getStoreDimIndex(t),e),n},m.prototype.lttbDownSample=function(t,e){var n=sv(this);return n._store=this._store.lttbDownSample(this._getStoreDimIndex(t),e),n},m.prototype.getRawDataItem=function(t){return this._store.getRawDataItem(t)},m.prototype.getItemModel=function(t){var e=this.hostModel,t=this.getRawDataItem(t);return new Lc(t,e,e&&e.ecModel)},m.prototype.diff=function(e){var n=this;return new G0(e?e.getStore().getIndices():[],this.getStore().getIndices(),function(t){return iv(e,t)},function(t){return iv(n,t)})},m.prototype.getVisual=function(t){var e=this._visual;return e&&e[t]},m.prototype.setVisual=function(t,e){this._visual=this._visual||{},uv(t)?L(this._visual,t):this._visual[t]=e},m.prototype.getItemVisual=function(t,e){t=this._itemVisuals[t],t=t&&t[e];return null==t?this.getVisual(e):t},m.prototype.hasItemVisual=function(){return 0<this._itemVisuals.length},m.prototype.ensureUniqueItemVisual=function(t,e){var n=this._itemVisuals,i=n[t],n=(i=i||(n[t]={}))[e];return null==n&&(F(n=this.getVisual(e))?n=n.slice():uv(n)&&(n=L({},n)),i[e]=n),n},m.prototype.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{};this._itemVisuals[t]=i,uv(e)?L(i,e):i[e]=n},m.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},m.prototype.setLayout=function(t,e){uv(t)?L(this._layout,t):this._layout[t]=e},m.prototype.getLayout=function(t){return this._layout[t]},m.prototype.getItemLayout=function(t){return this._itemLayouts[t]},m.prototype.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?L(this._itemLayouts[t]||{},e):e},m.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},m.prototype.setItemGraphicEl=function(t,e){var n,i,r,o,a=this.hostModel&&this.hostModel.seriesIndex;n=a,i=this.dataType,r=t,(a=e)&&((o=D(a)).dataIndex=r,o.dataType=i,o.seriesIndex=n,o.ssrType="chart","group"===a.type)&&a.traverse(function(t){t=D(t);t.seriesIndex=n,t.dataIndex=r,t.dataType=i,t.ssrType="chart"}),this._graphicEls[t]=e},m.prototype.getItemGraphicEl=function(t){return this._graphicEls[t]},m.prototype.eachItemGraphicEl=function(n,i){O(this._graphicEls,function(t,e){t&&n&&n.call(i,t,e)})},m.prototype.cloneShallow=function(t){return t=t||new m(this._schema||hv(this.dimensions,this._getDimInfo,this),this.hostModel),av(t,this),t._store=this._store,t},m.prototype.wrapMethod=function(t,e){var n=this[t];k(n)&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(St(arguments)))})},m.internalField=(nv=function(a){var s=a._invertedIndicesMap;O(s,function(t,e){var n=a._dimInfos[e],i=n.ordinalMeta,r=a._store;if(i){t=s[e]=new cv(i.categories.length);for(var o=0;o<t.length;o++)t[o]=-1;for(o=0;o<r.count();o++)t[r.get(n.storeDimIndex,o)]=o}})},rv=function(t,e,n){return Co(t._getCategory(e,n),null)},iv=function(t,e){var n=t._idList[e];return n=null==(n=null==n&&null!=t._idDimIdx?rv(t,t._idDimIdx,e):n)?"e\0\0"+e:n},ov=function(t){return t=F(t)?t:null!=t?[t]:[]},sv=function(t){var e=new m(t._schema||hv(t.dimensions,t._getDimInfo,t),t.hostModel);return av(e,t),e},av=function(e,n){O(pv.concat(n.__wrappedMethods||[]),function(t){n.hasOwnProperty(t)&&(e[t]=n[t])}),e.__wrappedMethods=n.__wrappedMethods,O(dv,function(t){e[t]=y(n[t])}),e._calculationInfo=L({},n._calculationInfo)},void(lv=function(t,e){var n=t._nameList,i=t._idList,r=t._nameDimIdx,o=t._idDimIdx,a=n[e],s=i[e];null==a&&null!=r&&(n[e]=a=rv(t,r,e)),null==s&&null!=o&&(i[e]=s=rv(t,o,e)),null==s&&null!=a&&(s=a,1<(r=(n=t._nameRepeatCount)[a]=(n[a]||0)+1)&&(s+="__ec__"+r),i[e]=s)})),m);function m(t,e){this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","minmaxDownSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"];for(var n,i,r=!(this.DOWNSAMPLE_METHODS=["downSample","minmaxDownSample","lttbDownSample"]),o=(J0(t)?(n=t.dimensions,this._dimOmitted=t.isDimensionOmitted(),this._schema=t):(r=!0,n=t),n=n||["x","y"],{}),a=[],s={},l=!1,u={},h=0;h<n.length;h++){var c=n[h],c=V(c)?new Z0({name:c}):c instanceof Z0?c:new Z0(c),p=c.name,d=(c.type=c.type||"float",c.coordDim||(c.coordDim=p,c.coordDimIndex=0),c.otherDims=c.otherDims||{});a.push(p),null!=u[p]&&(l=!0),(o[p]=c).createInvertedIndices&&(s[p]=[]),0===d.itemName&&(this._nameDimIdx=h),0===d.itemId&&(this._idDimIdx=h),r&&(c.storeDimIndex=h)}this.dimensions=a,this._dimInfos=o,this._initGetDimensionInfo(l),this.hostModel=e,this._invertedIndicesMap=s,this._dimOmitted&&(i=this._dimIdxToName=E(),O(a,function(t){i.set(o[t].storeDimIndex,t)}))}function gv(t,e){Zd(t)||(t=Kd(t));for(var n,i,r=(e=e||{}).coordDimensions||[],o=e.dimensionsDefine||t.dimensionsDefine||[],a=E(),s=[],l=(u=t,n=r,p=e.dimensionsCount,i=Math.max(u.dimensionsDetectedCount||1,n.length,o.length,p||0),O(n,function(t){R(t)&&(t=t.dimsDef)&&(i=Math.max(i,t.length))}),i),u=e.canOmitUnusedDimensions&&30<l,h=o===t.dimensionsDefine,c=h?ev(t):tv(o),p=e.encodeDefine,d=E(p=!p&&e.encodeDefaulter?e.encodeDefaulter(t,l):p),f=new ng(l),g=0;g<f.length;g++)f[g]=-1;function y(t){var e,n,i,r=f[t];return r<0?(e=R(e=o[t])?e:{name:e},n=new Z0,null!=(i=e.name)&&null!=c.get(i)&&(n.name=n.displayName=i),null!=e.type&&(n.type=e.type),null!=e.displayName&&(n.displayName=e.displayName),f[t]=s.length,n.storeDimIndex=t,s.push(n),n):s[r]}if(!u)for(g=0;g<l;g++)y(g);d.each(function(t,n){var i,t=_o(t).slice();1===t.length&&!V(t[0])&&t[0]<0?d.set(n,!1):(i=d.set(n,[]),O(t,function(t,e){t=V(t)?c.get(t):t;null!=t&&t<l&&v(y(i[e]=t),n,e)}))});var m=0;function v(t,e,n){null!=Wp.get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,a.set(e,!0))}O(r,function(t){V(t)?(o=t,r={}):(o=(r=t).name,t=r.ordinalMeta,r.ordinalMeta=null,(r=L({},r)).ordinalMeta=t,n=r.dimsDef,i=r.otherDims,r.name=r.coordDim=r.coordDimIndex=r.dimsDef=r.otherDims=null);var n,i,r,o,e=d.get(o);if(!1!==e){if(!(e=_o(e)).length)for(var a=0;a<(n&&n.length||1);a++){for(;m<l&&null!=y(m).coordDim;)m++;m<l&&e.push(m++)}O(e,function(t,e){t=y(t);h&&null!=r.type&&(t.type=r.type),v(z(t,r),o,e),null==t.name&&n&&(R(e=n[e])||(e={name:e}),t.name=t.displayName=e.name,t.defaultTooltip=e.defaultTooltip),i&&z(t.otherDims,i)})}});var _=e.generateCoord,x=null!=(w=e.generateCoordCount),w=_?w||1:0,b=_||"value";function S(t){null==t.name&&(t.name=t.coordDim)}if(u)O(s,function(t){S(t)}),s.sort(function(t,e){return t.storeDimIndex-e.storeDimIndex});else for(var M=0;M<l;M++){var T=y(M);null==T.coordDim&&(T.coordDim=function(t,e,n){if(n||e.hasKey(t)){for(var i=0;e.hasKey(t+i);)i++;t+=i}return e.set(t,!0),t}(b,a,x),T.coordDimIndex=0,(!_||w<=0)&&(T.isExtraCoord=!0),w--),S(T),null!=T.type||nd(t,M)!==Qp.Must&&(!T.isExtraCoord||null==T.otherDims.itemName&&null==T.otherDims.seriesName)||(T.type="ordinal")}for(var C=s,I=E(),k=0;k<C.length;k++){var D=C[k],A=D.name,P=I.get(A)||0;0<P&&(D.name=A+(P-1)),P++,I.set(A,P)}return new $0({source:t,dimensions:s,fullDimensionCount:l,dimensionOmitted:u})}var yv=function(t){this.coordSysDims=[],this.axisMap=E(),this.categoryAxisMap=E(),this.coordSysName=t};var mv={cartesian2d:function(t,e,n,i){var r=t.getReferringComponents("xAxis",No).models[0],t=t.getReferringComponents("yAxis",No).models[0];e.coordSysDims=["x","y"],n.set("x",r),n.set("y",t),vv(r)&&(i.set("x",r),e.firstCategoryDimIndex=0),vv(t)&&(i.set("y",t),null==e.firstCategoryDimIndex)&&(e.firstCategoryDimIndex=1)},singleAxis:function(t,e,n,i){t=t.getReferringComponents("singleAxis",No).models[0];e.coordSysDims=["single"],n.set("single",t),vv(t)&&(i.set("single",t),e.firstCategoryDimIndex=0)},polar:function(t,e,n,i){var t=t.getReferringComponents("polar",No).models[0],r=t.findAxisModel("radiusAxis"),t=t.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],n.set("radius",r),n.set("angle",t),vv(r)&&(i.set("radius",r),e.firstCategoryDimIndex=0),vv(t)&&(i.set("angle",t),null==e.firstCategoryDimIndex)&&(e.firstCategoryDimIndex=1)},geo:function(t,e,n,i){e.coordSysDims=["lng","lat"]},parallel:function(t,i,r,o){var a=t.ecModel,t=a.getComponent("parallel",t.get("parallelIndex")),s=i.coordSysDims=t.dimensions.slice();O(t.parallelAxisIndex,function(t,e){var t=a.getComponent("parallelAxis",t),n=s[e];r.set(n,t),vv(t)&&(o.set(n,t),null==i.firstCategoryDimIndex)&&(i.firstCategoryDimIndex=e)})}};function vv(t){return"category"===t.get("type")}function _v(t,e,n){var i,r,o,a,s,l,u,h,c,p=(n=n||{}).byIndex,d=n.stackedCoordDimension,f=(J0(e.schema)?(r=e.schema,i=r.dimensions,o=e.store):i=e,!(!t||!t.get("stack")));return O(i,function(t,e){V(t)&&(i[e]=t={name:t}),f&&!t.isExtraCoord&&(p||a||!t.ordinalMeta||(a=t),s||"ordinal"===t.type||"time"===t.type||d&&d!==t.coordDim||(s=t))}),!s||p||a||(p=!0),s&&(l="__\0ecstackresult_"+t.id,u="__\0ecstackedover_"+t.id,a&&(a.createInvertedIndices=!0),h=s.coordDim,n=s.type,c=0,O(i,function(t){t.coordDim===h&&c++}),e={name:l,coordDim:h,coordDimIndex:c,type:n,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length},t={name:u,coordDim:u,coordDimIndex:c+1,type:n,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length+1},r?(o&&(e.storeDimIndex=o.ensureCalculationDimension(u,n),t.storeDimIndex=o.ensureCalculationDimension(l,n)),r.appendCalculationDimension(e),r.appendCalculationDimension(t)):(i.push(e),i.push(t))),{stackedDimension:s&&s.name,stackedByDimension:a&&a.name,isStackedByIndex:p,stackedOverDimension:u,stackResultDimension:l}}function xv(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function wv(t,e){return xv(t,e)?t.getCalculationInfo("stackResultDimension"):e}function bv(t,e,n){n=n||{};var i,r,o,a,s,l,u=e.getSourceManager(),h=!1,t=(t?(h=!0,i=Kd(t)):h=(i=u.getSource()).sourceFormat===Up,function(t){var e=t.get("coordinateSystem"),n=new yv(e);if(e=mv[e])return e(t,n,n.axisMap,n.categoryAxisMap),n}(e)),c=(r=t,c=(c=e).get("coordinateSystem"),c=xd.get(c),p=(p=r&&r.coordSysDims?B(r.coordSysDims,function(t){var e={name:t},t=r.axisMap.get(t);return t&&(t=t.get("type"),e.type="category"===(t=t)?"ordinal":"time"===t?"time":"float"),e}):p)||c&&(c.getDimensionsInfo?c.getDimensionsInfo():c.dimensions.slice())||["x","y"]),p=n.useEncodeDefaulter,p=k(p)?p:p?pt(td,c,e):null,c={coordDimensions:c,generateCoord:n.generateCoord,encodeDefine:e.getEncode(),encodeDefaulter:p,canOmitUnusedDimensions:!h},p=gv(i,c),d=(c=p.dimensions,o=n.createInvertedIndices,(a=t)&&O(c,function(t,e){var n=t.coordDim,n=a.categoryAxisMap.get(n);n&&(null==s&&(s=e),t.ordinalMeta=n.getOrdinalMeta(),o)&&(t.createInvertedIndices=!0),null!=t.otherDims.itemName&&(l=!0)}),l||null==s||(c[s].otherDims.itemName=0),s),n=h?null:u.getSharedDataStore(p),t=_v(e,{schema:p,store:n}),c=new fv(p,e),p=(c.setCalculationInfo(t),null==d||(u=i).sourceFormat!==Up||F(bo(function(t){var e=0;for(;e<t.length&&null==t[e];)e++;return t[e]}(u.data||[])))?null:function(t,e,n,i){return i===d?n:this.defaultDimValueGetter(t,e,n,i)});return c.hasItemOption=!1,c.initData(h?i:n,null,p),c}Mv.prototype.getSetting=function(t){return this._setting[t]},Mv.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},Mv.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},Mv.prototype.getExtent=function(){return this._extent.slice()},Mv.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},Mv.prototype.isInExtentRange=function(t){return this._extent[0]<=t&&this._extent[1]>=t},Mv.prototype.isBlank=function(){return this._isBlank},Mv.prototype.setBlank=function(t){this._isBlank=t};var Sv=Mv;function Mv(t){this._setting=t||{},this._extent=[1/0,-1/0]}Zo(Sv);var Tv=0,Cv=(Iv.createByAxisModel=function(t){var t=t.option,e=t.data,e=e&&B(e,kv);return new Iv({categories:e,needCollect:!e,deduplication:!1!==t.dedplication})},Iv.prototype.getOrdinal=function(t){return this._getOrCreateMap().get(t)},Iv.prototype.parseAndCollect=function(t){var e,n,i=this._needCollect;return V(t)||i?(i&&!this._deduplication?(n=this.categories.length,this.categories[n]=t):null==(n=(e=this._getOrCreateMap()).get(t))&&(i?(n=this.categories.length,this.categories[n]=t,e.set(t,n)):n=NaN),n):t},Iv.prototype._getOrCreateMap=function(){return this._map||(this._map=E(this.categories))},Iv);function Iv(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this.uid=++Tv}function kv(t){return R(t)&&null!=t.value?t.value:t+""}function Dv(t){return"interval"===t.type||"log"===t.type}function Av(t,e,n,i){var r={},o=t[1]-t[0],o=r.interval=co(o/e,!0),e=(null!=n&&o<n&&(o=r.interval=n),null!=i&&i<o&&(o=r.interval=i),r.intervalPrecision=Lv(o)),n=r.niceTickExtent=[eo(Math.ceil(t[0]/o)*o,e),eo(Math.floor(t[1]/o)*o,e)];return i=n,o=t,isFinite(i[0])||(i[0]=o[0]),isFinite(i[1])||(i[1]=o[1]),Ov(i,0,o),Ov(i,1,o),i[0]>i[1]&&(i[0]=i[1]),r}function Pv(t){var e=Math.pow(10,ho(t)),t=t/e;return t?2===t?t=3:3===t?t=5:t*=2:t=1,eo(t*e)}function Lv(t){return no(t)+2}function Ov(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}function Rv(t,e){return t>=e[0]&&t<=e[1]}function Nv(t,e){return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])}function Ev(t,e){return t*(e[1]-e[0])+e[0]}u(Fv,zv=Sv),Fv.prototype.parse=function(t){return null==t?NaN:V(t)?this._ordinalMeta.getOrdinal(t):Math.round(t)},Fv.prototype.contain=function(t){return Rv(t=this.parse(t),this._extent)&&null!=this._ordinalMeta.categories[t]},Fv.prototype.normalize=function(t){return Nv(t=this._getTickNumber(this.parse(t)),this._extent)},Fv.prototype.scale=function(t){return t=Math.round(Ev(t,this._extent)),this.getRawOrdinalNumber(t)},Fv.prototype.getTicks=function(){for(var t=[],e=this._extent,n=e[0];n<=e[1];)t.push({value:n}),n++;return t},Fv.prototype.getMinorTicks=function(t){},Fv.prototype.setSortInfo=function(t){if(null==t)this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null;else{for(var e=t.ordinalNumbers,n=this._ordinalNumbersByTick=[],i=this._ticksByOrdinalNumber=[],r=0,o=this._ordinalMeta.categories.length,a=Math.min(o,e.length);r<a;++r){var s=e[r];i[n[r]=s]=r}for(var l=0;r<o;++r){for(;null!=i[l];)l++;n.push(l),i[l]=r}}},Fv.prototype._getTickNumber=function(t){var e=this._ticksByOrdinalNumber;return e&&0<=t&&t<e.length?e[t]:t},Fv.prototype.getRawOrdinalNumber=function(t){var e=this._ordinalNumbersByTick;return e&&0<=t&&t<e.length?e[t]:t},Fv.prototype.getLabel=function(t){if(!this.isBlank())return t=this.getRawOrdinalNumber(t.value),null==(t=this._ordinalMeta.categories[t])?"":t+""},Fv.prototype.count=function(){return this._extent[1]-this._extent[0]+1},Fv.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},Fv.prototype.isInExtentRange=function(t){return t=this._getTickNumber(t),this._extent[0]<=t&&this._extent[1]>=t},Fv.prototype.getOrdinalMeta=function(){return this._ordinalMeta},Fv.prototype.calcNiceTicks=function(){},Fv.prototype.calcNiceExtent=function(){},Fv.type="ordinal";var zv,Bv=Fv;function Fv(t){var t=zv.call(this,t)||this,e=(t.type="ordinal",t.getSetting("ordinalMeta"));return F(e=e||new Cv({}))&&(e=new Cv({categories:B(e,function(t){return R(t)?t.value:t})})),t._ordinalMeta=e,t._extent=t.getSetting("extent")||[0,e.categories.length-1],t}Sv.registerClass(Bv);var Vv,Hv=eo,Gv=(u(Wv,Vv=Sv),Wv.prototype.parse=function(t){return t},Wv.prototype.contain=function(t){return Rv(t,this._extent)},Wv.prototype.normalize=function(t){return Nv(t,this._extent)},Wv.prototype.scale=function(t){return Ev(t,this._extent)},Wv.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=parseFloat(t)),isNaN(e)||(n[1]=parseFloat(e))},Wv.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),this.setExtent(e[0],e[1])},Wv.prototype.getInterval=function(){return this._interval},Wv.prototype.setInterval=function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=Lv(t)},Wv.prototype.getTicks=function(t){var e=this._interval,n=this._extent,i=this._niceExtent,r=this._intervalPrecision,o=[];if(e){n[0]<i[0]&&o.push(t?{value:Hv(i[0]-e,r)}:{value:n[0]});for(var a=i[0];a<=i[1]&&(o.push({value:a}),(a=Hv(a+e,r))!==o[o.length-1].value);)if(1e4<o.length)return[];var s=o.length?o[o.length-1].value:i[1];n[1]>s&&o.push(t?{value:Hv(s+e,r)}:{value:n[1]})}return o},Wv.prototype.getMinorTicks=function(t){for(var e=this.getTicks(!0),n=[],i=this.getExtent(),r=1;r<e.length;r++){for(var o=e[r],a=e[r-1],s=0,l=[],u=(o.value-a.value)/t;s<t-1;){var h=Hv(a.value+(s+1)*u);h>i[0]&&h<i[1]&&l.push(h),s++}n.push(l)}return n},Wv.prototype.getLabel=function(t,e){return null==t?"":(null==(e=e&&e.precision)?e=no(t.value)||0:"auto"===e&&(e=this._intervalPrecision),yp(Hv(t.value,e,!0)))},Wv.prototype.calcNiceTicks=function(t,e,n){t=t||5;var i=this._extent,r=i[1]-i[0];isFinite(r)&&(r<0&&i.reverse(),r=Av(i,t,e,n),this._intervalPrecision=r.intervalPrecision,this._interval=r.interval,this._niceExtent=r.niceTickExtent)},Wv.prototype.calcNiceExtent=function(t){var e=this._extent,n=(e[0]===e[1]&&(0!==e[0]?(n=Math.abs(e[0]),t.fixMax||(e[1]+=n/2),e[0]-=n/2):e[1]=1),e[1]-e[0]),n=(isFinite(n)||(e[0]=0,e[1]=1),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval),this._interval);t.fixMin||(e[0]=Hv(Math.floor(e[0]/n)*n)),t.fixMax||(e[1]=Hv(Math.ceil(e[1]/n)*n))},Wv.prototype.setNiceExtent=function(t,e){this._niceExtent=[t,e]},Wv.type="interval",Wv);function Wv(){var t=null!==Vv&&Vv.apply(this,arguments)||this;return t.type="interval",t._interval=0,t._intervalPrecision=2,t}Sv.registerClass(Gv);var Uv="undefined"!=typeof Float32Array,Xv=Uv?Float32Array:Array;function Yv(t){return F(t)?Uv?new Float32Array(t):t:new Xv(t)}function qv(t){return t.get("stack")||"__ec_stack_"+t.seriesIndex}function Zv(t){return t.dim+t.index}function jv(t,e){var n=[];return e.eachSeriesByType(t,function(t){var e;(e=t).coordinateSystem&&"cartesian2d"===e.coordinateSystem.type&&n.push(t)}),n}function Kv(t){var a,d,u=function(t){var e,l={},n=(O(t,function(t){var e=t.coordinateSystem.getBaseAxis();if("time"===e.type||"value"===e.type)for(var t=t.getData(),n=e.dim+"_"+e.index,i=t.getDimensionIndex(t.mapDimension(e.dim)),r=t.getStore(),o=0,a=r.count();o<a;++o){var s=r.get(i,o);l[n]?l[n].push(s):l[n]=[s]}}),{});for(e in l)if(l.hasOwnProperty(e)){var i=l[e];if(i){i.sort(function(t,e){return t-e});for(var r=null,o=1;o<i.length;++o){var a=i[o]-i[o-1];0<a&&(r=null===r?a:Math.min(r,a))}n[e]=r}}return n}(t),h=[];return O(t,function(t){var e,n,i=t.coordinateSystem.getBaseAxis(),r=i.getExtent(),o=(e="category"===i.type?i.getBandWidth():"value"===i.type||"time"===i.type?(e=i.dim+"_"+i.index,e=u[e],o=Math.abs(r[1]-r[0]),n=i.scale.getExtent(),n=Math.abs(n[1]-n[0]),e?o/n*e:o):(n=t.getData(),Math.abs(r[1]-r[0])/n.count()),to(t.get("barWidth"),e)),r=to(t.get("barMaxWidth"),e),a=to(t.get("barMinWidth")||((n=t).pipelineContext&&n.pipelineContext.large?.5:1),e),s=t.get("barGap"),l=t.get("barCategoryGap");h.push({bandWidth:e,barWidth:o,barMaxWidth:r,barMinWidth:a,barGap:s,barCategoryGap:l,axisKey:Zv(i),stackId:qv(t)})}),a={},O(h,function(t,e){var n=t.axisKey,i=t.bandWidth,i=a[n]||{bandWidth:i,remainedWidth:i,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},r=i.stacks,n=(a[n]=i,t.stackId),o=(r[n]||i.autoWidthCount++,r[n]=r[n]||{width:0,maxWidth:0},t.barWidth),o=(o&&!r[n].width&&(r[n].width=o,o=Math.min(i.remainedWidth,o),i.remainedWidth-=o),t.barMaxWidth),o=(o&&(r[n].maxWidth=o),t.barMinWidth),r=(o&&(r[n].minWidth=o),t.barGap),n=(null!=r&&(i.gap=r),t.barCategoryGap);null!=n&&(i.categoryGap=n)}),d={},O(a,function(t,n){d[n]={};var i,e=t.stacks,r=t.bandWidth,o=t.categoryGap,a=(null==o&&(a=ht(e).length,o=Math.max(35-4*a,15)+"%"),to(o,r)),s=to(t.gap,1),l=t.remainedWidth,u=t.autoWidthCount,h=(l-a)/(u+(u-1)*s),h=Math.max(h,0),c=(O(e,function(t){var e,n=t.maxWidth,i=t.minWidth;t.width?(e=t.width,n&&(e=Math.min(e,n)),i&&(e=Math.max(e,i)),t.width=e,l-=e+s*e,u--):(e=h,n&&n<e&&(e=Math.min(n,l)),(e=i&&e<i?i:e)!==h&&(t.width=e,l-=e+s*e,u--))}),h=(l-a)/(u+(u-1)*s),h=Math.max(h,0),0),p=(O(e,function(t,e){t.width||(t.width=h),c+=(i=t).width*(1+s)}),i&&(c-=i.width*s),-c/2);O(e,function(t,e){d[n][e]=d[n][e]||{bandWidth:r,offset:p,width:t.width},p+=t.width*(1+s)})}),d}u(Jv,$v=Gv),Jv.prototype.getLabel=function(t){var e=this.getSetting("useUTC");return tp(t.value,jc[function(t){switch(t){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}(Jc(this._minLevelUnit))]||jc.second,e,this.getSetting("locale"))},Jv.prototype.getFormattedLabel=function(t,e,n){var i=this.getSetting("useUTC"),r=this.getSetting("locale"),o=null;if(V(n))o=n;else if(k(n))o=n(t.value,e,{level:t.level});else{var a=L({},qc);if(0<t.level)for(var s=0;s<Kc.length;++s)a[Kc[s]]="{primary|"+a[Kc[s]]+"}";var l=n?!1===n.inherit?n:z(n,a):a,u=ep(t.value,i);if(l[u])o=l[u];else if(l.inherit){for(s=$c.indexOf(u)-1;0<=s;--s)if(l[u]){o=l[u];break}o=o||a.none}F(o)&&(e=null==t.level?0:0<=t.level?t.level:o.length+t.level,o=o[e=Math.min(e,o.length-1)])}return tp(new Date(t.value),o,i,r)},Jv.prototype.getTicks=function(){var t=this._interval,e=this._extent,n=[];return t&&(n.push({value:e[0],level:0}),t=this.getSetting("useUTC"),t=function(t,b,S,M){var e=$c,n=0;function i(t,e,n){var i=[],r=!e.length;if(!function(t,e,n,i){function r(t){return np(c,t,i)===np(p,t,i)}function o(){return r("year")}function a(){return o()&&r("month")}function s(){return a()&&r("day")}function l(){return s()&&r("hour")}function u(){return l()&&r("minute")}function h(){return u()&&r("second")}var c=lo(e),p=lo(n);switch(t){case"year":return o();case"month":return a();case"day":return s();case"hour":return l();case"minute":return u();case"second":return h();case"millisecond":return h()&&r("millisecond")}}(Jc(t),M[0],M[1],S)){r&&(e=[{value:function(t,e,n){var i=new Date(t);switch(Jc(e)){case"year":case"month":i[hp(n)](0);case"day":i[cp(n)](1);case"hour":i[pp(n)](0);case"minute":i[dp(n)](0);case"second":i[fp(n)](0),i[gp(n)](0)}return i.getTime()}(new Date(M[0]),t,S)},{value:M[1]}]);for(var o,a,s=0;s<e.length-1;s++){var l=e[s].value,u=e[s+1].value;if(l!==u){var h=void 0,c=void 0,p=void 0;switch(t){case"year":h=Math.max(1,Math.round(b/Yc/365)),c=ip(S),p=S?"setUTCFullYear":"setFullYear";break;case"half-year":case"quarter":case"month":a=b,h=6<(a/=30*Yc)?6:3<a?3:2<a?2:1,c=rp(S),p=hp(S);break;case"week":case"half-week":case"day":a=b,h=16<(a/=Yc)?16:7.5<a?7:3.5<a?4:1.5<a?2:1,c=op(S),p=cp(S),0;break;case"half-day":case"quarter-day":case"hour":o=b,h=12<(o/=Xc)?12:6<o?6:3.5<o?4:2<o?2:1,c=ap(S),p=pp(S);break;case"minute":h=e_(b,!0),c=sp(S),p=dp(S);break;case"second":h=e_(b,!1),c=lp(S),p=fp(S);break;case"millisecond":h=co(b,!0),c=up(S),p=gp(S)}w=x=_=v=m=y=g=f=d=void 0;for(var d=h,f=l,g=u,y=c,m=p,v=i,_=new Date(f),x=f,w=_[y]();x<g&&x<=M[1];)v.push({value:x}),_[m](w+=d),x=_.getTime();v.push({value:x,notAdd:!0}),"year"===t&&1<n.length&&0===s&&n.unshift({value:n[0].value-h})}}for(s=0;s<i.length;s++)n.push(i[s])}}for(var r=[],o=[],a=0,s=0,l=0;l<e.length&&n++<1e4;++l){var u=Jc(e[l]);if(function(t){return t===Jc(t)}(e[l])){i(e[l],r[r.length-1]||[],o);var h=e[l+1]?Jc(e[l+1]):null;if(u!==h){if(o.length){s=a,o.sort(function(t,e){return t.value-e.value});for(var c=[],p=0;p<o.length;++p){var d=o[p].value;0!==p&&o[p-1].value===d||(c.push(o[p]),d>=M[0]&&d<=M[1]&&a++)}u=(M[1]-M[0])/b;if(1.5*u<a&&u/1.5<s)break;if(r.push(c),u<a||t===e[l])break}o=[]}}}for(var f=ut(B(r,function(t){return ut(t,function(t){return t.value>=M[0]&&t.value<=M[1]&&!t.notAdd})}),function(t){return 0<t.length}),g=[],y=f.length-1,l=0;l<f.length;++l)for(var m=f[l],v=0;v<m.length;++v)g.push({value:m[v].value,level:y-l});g.sort(function(t,e){return t.value-e.value});for(var _=[],l=0;l<g.length;++l)0!==l&&g[l].value===g[l-1].value||_.push(g[l]);return _}(this._minLevelUnit,this._approxInterval,t,e),(n=n.concat(t)).push({value:e[1],level:0})),n},Jv.prototype.calcNiceExtent=function(t){var e,n=this._extent;n[0]===n[1]&&(n[0]-=Yc,n[1]+=Yc),n[1]===-1/0&&n[0]===1/0&&(e=new Date,n[1]=+new Date(e.getFullYear(),e.getMonth(),e.getDate()),n[0]=n[1]-Yc),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval)},Jv.prototype.calcNiceTicks=function(t,e,n){var i=this._extent,i=i[1]-i[0],i=(this._approxInterval=i/(t=t||10),null!=e&&this._approxInterval<e&&(this._approxInterval=e),null!=n&&this._approxInterval>n&&(this._approxInterval=n),t_.length),t=Math.min(function(t,e,n,i){for(;n<i;){var r=n+i>>>1;t[r][1]<e?n=1+r:i=r}return n}(t_,this._approxInterval,0,i),i-1);this._interval=t_[t][1],this._minLevelUnit=t_[Math.max(t-1,0)][0]},Jv.prototype.parse=function(t){return H(t)?t:+lo(t)},Jv.prototype.contain=function(t){return Rv(this.parse(t),this._extent)},Jv.prototype.normalize=function(t){return Nv(this.parse(t),this._extent)},Jv.prototype.scale=function(t){return Ev(t,this._extent)},Jv.type="time";var $v,Qv=Jv;function Jv(t){t=$v.call(this,t)||this;return t.type="time",t}var t_=[["second",Wc],["minute",Uc],["hour",Xc],["quarter-day",6*Xc],["half-day",12*Xc],["day",1.2*Yc],["half-week",3.5*Yc],["week",7*Yc],["month",31*Yc],["quarter",95*Yc],["half-year",Xo/2],["year",Xo]];function e_(t,e){return 30<(t/=e?Uc:Wc)?30:20<t?20:15<t?15:10<t?10:5<t?5:2<t?2:1}Sv.registerClass(Qv);var n_,i_=Sv.prototype,r_=Gv.prototype,o_=eo,a_=Math.floor,s_=Math.ceil,l_=Math.pow,u_=Math.log,h_=(u(c_,n_=Sv),c_.prototype.getTicks=function(t){var e=this._originalScale,n=this._extent,i=e.getExtent();return B(r_.getTicks.call(this,t),function(t){var t=t.value,e=eo(l_(this.base,t)),e=t===n[0]&&this._fixMin?p_(e,i[0]):e;return{value:t===n[1]&&this._fixMax?p_(e,i[1]):e}},this)},c_.prototype.setExtent=function(t,e){var n=u_(this.base);t=u_(Math.max(0,t))/n,e=u_(Math.max(0,e))/n,r_.setExtent.call(this,t,e)},c_.prototype.getExtent=function(){var t=this.base,e=i_.getExtent.call(this);e[0]=l_(t,e[0]),e[1]=l_(t,e[1]);t=this._originalScale.getExtent();return this._fixMin&&(e[0]=p_(e[0],t[0])),this._fixMax&&(e[1]=p_(e[1],t[1])),e},c_.prototype.unionExtent=function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=u_(t[0])/u_(e),t[1]=u_(t[1])/u_(e),i_.unionExtent.call(this,t)},c_.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},c_.prototype.calcNiceTicks=function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(!(n==1/0||n<=0)){var i=uo(n);for(t/n*i<=.5&&(i*=10);!isNaN(i)&&Math.abs(i)<1&&0<Math.abs(i);)i*=10;t=[eo(s_(e[0]/i)*i),eo(a_(e[1]/i)*i)];this._interval=i,this._niceExtent=t}},c_.prototype.calcNiceExtent=function(t){r_.calcNiceExtent.call(this,t),this._fixMin=t.fixMin,this._fixMax=t.fixMax},c_.prototype.parse=function(t){return t},c_.prototype.contain=function(t){return Rv(t=u_(t)/u_(this.base),this._extent)},c_.prototype.normalize=function(t){return Nv(t=u_(t)/u_(this.base),this._extent)},c_.prototype.scale=function(t){return t=Ev(t,this._extent),l_(this.base,t)},c_.type="log",c_);function c_(){var t=null!==n_&&n_.apply(this,arguments)||this;return t.type="log",t.base=10,t._originalScale=new Gv,t._interval=0,t}By=h_.prototype;function p_(t,e){return o_(t,no(e))}By.getMinorTicks=r_.getMinorTicks,By.getLabel=r_.getLabel,Sv.registerClass(h_);f_.prototype._prepareParams=function(t,e,n){n[1]<n[0]&&(n=[NaN,NaN]),this._dataMin=n[0],this._dataMax=n[1];var i=this._isOrdinal="ordinal"===t.type,r=(this._needCrossZero="interval"===t.type&&e.getNeedCrossZero&&e.getNeedCrossZero(),e.get("min",!0)),r=(null==r&&(r=e.get("startValue",!0)),this._modelMinRaw=r),r=(k(r)?this._modelMinNum=m_(t,r({min:n[0],max:n[1]})):"dataMin"!==r&&(this._modelMinNum=m_(t,r)),this._modelMaxRaw=e.get("max",!0));k(r)?this._modelMaxNum=m_(t,r({min:n[0],max:n[1]})):"dataMax"!==r&&(this._modelMaxNum=m_(t,r)),i?this._axisDataLen=e.getCategories().length:"boolean"==typeof(t=F(n=e.get("boundaryGap"))?n:[n||0,n||0])[0]||"boolean"==typeof t[1]?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[Ir(t[0],1),Ir(t[1],1)]},f_.prototype.calculate=function(){var t=this._isOrdinal,e=this._dataMin,n=this._dataMax,i=this._axisDataLen,r=this._boundaryGapInner,o=t?null:n-e||Math.abs(e),a="dataMin"===this._modelMinRaw?e:this._modelMinNum,s="dataMax"===this._modelMaxRaw?n:this._modelMaxNum,l=null!=a,u=null!=s,e=(null==a&&(a=t?i?0:NaN:e-r[0]*o),null==s&&(s=t?i?i-1:NaN:n+r[1]*o),null!=a&&isFinite(a)||(a=NaN),null!=s&&isFinite(s)||(s=NaN),xt(a)||xt(s)||t&&!i),n=(this._needCrossZero&&(a=0<a&&0<s&&!l?0:a)<0&&s<0&&!u&&(s=0),this._determinedMin),r=this._determinedMax;return null!=n&&(a=n,l=!0),null!=r&&(s=r,u=!0),{min:a,max:s,minFixed:l,maxFixed:u,isBlank:e}},f_.prototype.modifyDataMinMax=function(t,e){this[y_[t]]=e},f_.prototype.setDeterminedMinMax=function(t,e){this[g_[t]]=e},f_.prototype.freeze=function(){this.frozen=!0};var d_=f_;function f_(t,e,n){this._prepareParams(t,e,n)}var g_={min:"_determinedMin",max:"_determinedMax"},y_={min:"_dataMin",max:"_dataMax"};function m_(t,e){return null==e?null:xt(e)?NaN:t.parse(e)}function v_(t,e){var n,i,r,o,a,s=t.type,l=(l=e,u=(h=t).getExtent(),(c=h.rawExtentInfo)||(c=new d_(h,l,u),h.rawExtentInfo=c),c.calculate()),u=(t.setBlank(l.isBlank),l.min),h=l.max,c=e.ecModel;return c&&"time"===s&&(t=jv("bar",c),n=!1,O(t,function(t){n=n||t.getBaseAxis()===e.axis}),n)&&(s=Kv(t),c=u,t=h,s=s,a=(i=e).axis.getExtent(),a=Math.abs(a[1]-a[0]),void 0!==(s=function(t,e,n){if(t&&e)return null!=(t=t[Zv(e)])&&null!=n?t[qv(n)]:t}(s,i.axis))&&(r=1/0,O(s,function(t){r=Math.min(t.offset,r)}),o=-1/0,O(s,function(t){o=Math.max(t.offset+t.width,o)}),r=Math.abs(r),o=Math.abs(o),t+=o/(i=r+o)*(a=(s=t-c)/(1-(r+o)/a)-s),c-=r/i*a),u=(s={min:c,max:t}).min,h=s.max),{extent:[u,h],fixMin:l.minFixed,fixMax:l.maxFixed}}function __(t,e){var n=v_(t,e),i=n.extent,r=e.get("splitNumber"),o=(t instanceof h_&&(t.base=e.get("logBase")),t.type),a=e.get("interval"),o="interval"===o||"time"===o;t.setExtent(i[0],i[1]),t.calcNiceExtent({splitNumber:r,fixMin:n.fixMin,fixMax:n.fixMax,minInterval:o?e.get("minInterval"):null,maxInterval:o?e.get("maxInterval"):null}),null!=a&&t.setInterval&&t.setInterval(a)}function x_(t,e){if(e=e||t.get("type"))switch(e){case"category":return new Bv({ordinalMeta:t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),extent:[1/0,-1/0]});case"time":return new Qv({locale:t.ecModel.getLocaleModel(),useUTC:t.ecModel.get("useUTC")});default:return new(Sv.getClass(e)||Gv)}}function w_(n){var i,e,r,t=n.getLabelModel().get("formatter"),o="category"===n.type?n.scale.getExtent()[0]:null;return"time"===n.scale.type?(r=t,function(t,e){return n.scale.getFormattedLabel(t,e,r)}):V(t)?(e=t,function(t){t=n.scale.getLabel(t);return e.replace("{value}",null!=t?t:"")}):k(t)?(i=t,function(t,e){return null!=o&&(e=t.value-o),i(b_(n,t),e,null!=t.level?{level:t.level}:null)}):function(t){return n.scale.getLabel(t)}}function b_(t,e){return"category"===t.type?t.scale.getLabel(e):e.value}function S_(t){var e=t.model,n=t.scale;if(e.get(["axisLabel","show"])&&!n.isBlank()){var i,r,o=n.getExtent(),a=n instanceof Bv?n.count():(i=n.getTicks()).length,s=t.getLabelModel(),l=w_(t),u=1;40<a&&(u=Math.ceil(a/40));for(var h,c,p,d=0;d<a;d+=u){var f=l(i?i[d]:{value:o[0]+d},d),f=s.getTextRect(f),g=(f=f,h=s.get("rotate")||0,c=p=g=c=void 0,h=h*Math.PI/180,c=f.width,g=f.height,p=c*Math.abs(Math.cos(h))+Math.abs(g*Math.sin(h)),c=c*Math.abs(Math.sin(h))+Math.abs(g*Math.cos(h)),new X(f.x,f.y,p,c));r?r.union(g):r=g}return r}}function M_(t){t=t.get("interval");return null==t?"auto":t}function T_(t){return"category"===t.type&&0===M_(t.getLabelModel())}I_.prototype.getNeedCrossZero=function(){return!this.option.scale},I_.prototype.getCoordSysModel=function(){};var C_=I_;function I_(){}var Gy=Object.freeze({__proto__:null,createDimensions:function(t,e){return gv(t,e).dimensions},createList:function(t){return bv(null,t)},createScale:function(t,e){var n=e;return(e=x_(n=e instanceof Lc?n:new Lc(e))).setExtent(t[0],t[1]),__(e,n),e},createSymbol:Zy,createTextStyle:function(t,e){return cc(t,null,null,"normal"!==(e=e||{}).state)},dataStack:{isDimensionStacked:xv,enableDataStack:_v,getStackedDimension:wv},enableHoverEmphasis:Rl,getECData:D,getLayoutRect:Pp,mixinAxisModelCommonMethods:function(t){at(t,C_)}}),k_=[],D_={registerPreprocessor:C0,registerProcessor:I0,registerPostInit:k0,registerPostUpdate:D0,registerUpdateLifecycle:A0,registerAction:P0,registerCoordinateSystem:L0,registerLayout:O0,registerVisual:R0,registerTransform:F0,registerLoading:z0,registerMap:B0,registerImpl:function(t,e){Im[t]=e},PRIORITY:Hy,ComponentModel:g,ComponentView:Gg,SeriesModel:Rg,ChartView:qg,registerComponentModel:function(t){g.registerClass(t)},registerComponentView:function(t){Gg.registerClass(t)},registerSeriesModel:function(t){Rg.registerClass(t)},registerChartView:function(t){qg.registerClass(t)},registerSubTypeDefaulter:function(t,e){g.registerSubTypeDefaulter(t,e)},registerPainter:function(t,e){jr(t,e)}};function A_(t){F(t)?O(t,function(t){A_(t)}):0<=I(k_,t)||(k_.push(t),(t=k(t)?{install:t}:t).install(D_))}var P_=1e-8;function L_(t,e){return Math.abs(t-e)<P_}function O_(t,e,n){var i=0,r=t[0];if(r){for(var o=1;o<t.length;o++){var a=t[o];i+=ts(r[0],r[1],a[0],a[1],e,n),r=a}var s=t[0];return L_(r[0],s[0])&&L_(r[1],s[1])||(i+=ts(r[0],r[1],s[0],s[1],e,n)),0!==i}}var R_=[];function N_(t,e){for(var n=0;n<t.length;n++)ee(t[n],t[n],e)}function E_(t,e,n,i){for(var r=0;r<t.length;r++){var o=t[r];(o=i?i.project(o):o)&&isFinite(o[0])&&isFinite(o[1])&&(ne(e,e,o),ie(n,n,o))}}z_.prototype.setCenter=function(t){this._center=t},z_.prototype.getCenter=function(){return this._center||(this._center=this.calcCenter())};Wy=z_;function z_(t){this.name=t}var B_,F_,V_=function(t,e){this.type="polygon",this.exterior=t,this.interiors=e},H_=function(t){this.type="linestring",this.points=t},G_=(u(W_,B_=Wy),W_.prototype.calcCenter=function(){for(var t,e=this.geometries,n=0,i=0;i<e.length;i++){var r=e[i],o=r.exterior,o=o&&o.length;n<o&&(t=r,n=o)}if(t){for(var a=t.exterior,s=0,l=0,u=0,h=a.length,c=a[h-1][0],p=a[h-1][1],d=0;d<h;d++){var f=a[d][0],g=a[d][1],y=c*g-f*p;s+=y,l+=(c+f)*y,u+=(p+g)*y,c=f,p=g}return s?[l/s/3,u/s/3,s]:[a[0][0]||0,a[0][1]||0]}var m=this.getBoundingRect();return[m.x+m.width/2,m.y+m.height/2]},W_.prototype.getBoundingRect=function(e){var n,i,t=this._rect;return t&&!e||(n=[1/0,1/0],i=[-1/0,-1/0],O(this.geometries,function(t){"polygon"===t.type?E_(t.exterior,n,i,e):O(t.points,function(t){E_(t,n,i,e)})}),isFinite(n[0])&&isFinite(n[1])&&isFinite(i[0])&&isFinite(i[1])||(n[0]=n[1]=i[0]=i[1]=0),t=new X(n[0],n[1],i[0]-n[0],i[1]-n[1]),e)||(this._rect=t),t},W_.prototype.contain=function(t){var e=this.getBoundingRect(),n=this.geometries;if(e.contain(t[0],t[1]))t:for(var i=0,r=n.length;i<r;i++){var o=n[i];if("polygon"===o.type){var a=o.exterior,s=o.interiors;if(O_(a,t[0],t[1])){for(var l=0;l<(s?s.length:0);l++)if(O_(s[l],t[0],t[1]))continue t;return!0}}}return!1},W_.prototype.transformTo=function(t,e,n,i){for(var r=this.getBoundingRect(),o=r.width/r.height,o=(n?i=i||n/o:n=o*i,new X(t,e,n,i)),a=r.calculateTransform(o),s=this.geometries,l=0;l<s.length;l++){var u=s[l];"polygon"===u.type?(N_(u.exterior,a),O(u.interiors,function(t){N_(t,a)})):O(u.points,function(t){N_(t,a)})}(r=this._rect).copy(o),this._center=[r.x+r.width/2,r.y+r.height/2]},W_.prototype.cloneShallow=function(t){t=new W_(t=null==t?this.name:t,this.geometries,this._center);return t._rect=this._rect,t.transformTo=null,t},W_);function W_(t,e,n){t=B_.call(this,t)||this;return t.type="geoJSON",t.geometries=e,t._center=n&&[n[0],n[1]],t}function U_(t,e){t=F_.call(this,t)||this;return t.type="geoSVG",t._elOnlyForCalculate=e,t}function X_(t,e,n){for(var i=0;i<t.length;i++)t[i]=Y_(t[i],e[i],n)}function Y_(t,e,n){for(var i=[],r=e[0],o=e[1],a=0;a<t.length;a+=2){var s=(s=t.charCodeAt(a)-64)>>1^-(1&s),l=(l=t.charCodeAt(a+1)-64)>>1^-(1&l);i.push([(r=s+=r)/n,(o=l+=o)/n])}return i}function q_(t,o){var e,n,r;return B(ut((t=(e=t).UTF8Encoding?(null==(r=(n=e).UTF8Scale)&&(r=1024),O(n.features,function(t){var e=t.geometry,n=e.encodeOffsets,i=e.coordinates;if(n)switch(e.type){case"LineString":e.coordinates=Y_(i,n,r);break;case"Polygon":case"MultiLineString":X_(i,n,r);break;case"MultiPolygon":O(i,function(t,e){return X_(t,n[e],r)})}}),n.UTF8Encoding=!1,n):e).features,function(t){return t.geometry&&t.properties&&0<t.geometry.coordinates.length}),function(t){var e=t.properties,n=t.geometry,i=[];switch(n.type){case"Polygon":var r=n.coordinates;i.push(new V_(r[0],r.slice(1)));break;case"MultiPolygon":O(n.coordinates,function(t){t[0]&&i.push(new V_(t[0],t.slice(1)))});break;case"LineString":i.push(new H_([n.coordinates]));break;case"MultiLineString":i.push(new H_(n.coordinates))}t=new G_(e[o||"name"],i,e.cp);return t.properties=e,t})}u(U_,F_=Wy),U_.prototype.calcCenter=function(){for(var t=this._elOnlyForCalculate,e=t.getBoundingRect(),e=[e.x+e.width/2,e.y+e.height/2],n=Oe(R_),i=t;i&&!i.isGeoSVGGraphicRoot;)Ne(n,i.getLocalTransform(),n),i=i.parent;return Fe(n,n),ee(e,e,n),e};var $o=Object.freeze({__proto__:null,MAX_SAFE_INTEGER:9007199254740991,asc:function(t){return t.sort(function(t,e){return t-e}),t},getPercentWithPrecision:function(t,e,n){return t[e]&&function(t,e){var n=lt(t,function(t,e){return t+(isNaN(e)?0:e)},0);if(0===n)return[];var i=Math.pow(10,e),e=B(t,function(t){return(isNaN(t)?0:t)/n*i*100}),r=100*i,o=B(e,function(t){return Math.floor(t)}),a=lt(o,function(t,e){return t+e},0),s=B(e,function(t,e){return t-o[e]});for(;a<r;){for(var l=Number.NEGATIVE_INFINITY,u=null,h=0,c=s.length;h<c;++h)s[h]>l&&(l=s[h],u=h);++o[u],s[u]=0,++a}return B(o,function(t){return t/i})}(t,n)[e]||0},getPixelPrecision:ro,getPrecision:no,getPrecisionSafe:io,isNumeric:fo,isRadianAroundZero:ao,linearMap:Jr,nice:co,numericToNumber:po,parseDate:lo,quantile:function(t,e){var e=(t.length-1)*e+1,n=Math.floor(e),i=+t[n-1];return(e=e-n)?i+e*(t[n]-i):i},quantity:uo,quantityExponent:ho,reformIntervals:function(t){t.sort(function(t,e){return function t(e,n,i){return e.interval[i]<n.interval[i]||e.interval[i]===n.interval[i]&&(e.close[i]-n.close[i]==(i?-1:1)||!i&&t(e,n,1))}(t,e,0)?-1:1});for(var e=-1/0,n=1,i=0;i<t.length;){for(var r=t[i].interval,o=t[i].close,a=0;a<2;a++)r[a]<=e&&(r[a]=e,o[a]=a?1:1-n),e=r[a],n=o[a];r[0]===r[1]&&o[0]*o[1]!=1?t.splice(i,1):i++}return t},remRadian:oo,round:eo}),qh=Object.freeze({__proto__:null,format:tp,parse:lo}),Zc=Object.freeze({__proto__:null,Arc:oh,BezierCurve:eh,BoundingRect:X,Circle:hu,CompoundPath:lh,Ellipse:fu,Group:Hr,Image:ws,IncrementalDisplayable:n,Line:ju,LinearGradient:dh,Polygon:Vu,Polyline:Uu,RadialGradient:hh,Rect:As,Ring:Nu,Sector:Pu,Text:Ns,clipPointsByRect:Qh,clipRectByRect:Jh,createIcon:tc,extendPath:Bh,extendShape:Eh,getShapeClass:Vh,getTransform:Zh,initProps:Dh,makeImage:Gh,makePath:Hh,mergePath:Uh,registerShape:Fh,resizePath:Xh,updateProps:kh}),Ic=Object.freeze({__proto__:null,addCommas:yp,capitalFirst:function(t){return t&&t.charAt(0).toUpperCase()+t.substr(1)},encodeHTML:_e,formatTime:function(t,e,n){"week"!==t&&"month"!==t&&"quarter"!==t&&"half-year"!==t&&"year"!==t||(t="MM-dd\nyyyy");var i=(e=lo(e))[(n=n?"getUTC":"get")+"FullYear"](),r=e[n+"Month"]()+1,o=e[n+"Date"](),a=e[n+"Hours"](),s=e[n+"Minutes"](),l=e[n+"Seconds"](),e=e[n+"Milliseconds"]();return t=t.replace("MM",Qc(r,2)).replace("M",r).replace("yyyy",i).replace("yy",Qc(i%100+"",2)).replace("dd",Qc(o,2)).replace("d",o).replace("hh",Qc(a,2)).replace("h",a).replace("mm",Qc(s,2)).replace("m",s).replace("ss",Qc(l,2)).replace("s",l).replace("SSS",Qc(e,3))},formatTpl:bp,getTextRect:function(t,e,n,i,r,o,a,s){return new Ns({style:{text:t,font:e,align:n,verticalAlign:i,padding:r,rich:o,overflow:a?"truncate":null,lineHeight:s}}).getBoundingRect()},getTooltipMarker:Sp,normalizeCssArray:vp,toCamelCase:mp,truncateText:function(t,e,n,i,r){var o={};return ra(o,t,e,n,i,r),o.text}}),Sc=Object.freeze({__proto__:null,bind:ct,clone:y,curry:pt,defaults:z,each:O,extend:L,filter:ut,indexOf:I,inherits:ot,isArray:F,isFunction:k,isObject:R,isString:V,map:B,merge:d,reduce:lt}),Z_=Po();function j_(e,t){t=B(t,function(t){return e.scale.parse(t)});return"time"===e.type&&0<t.length&&(t.sort(),t.unshift(t[0]),t.push(t[t.length-1])),t}function K_(n){var i,e,r,o,t,a,s=n.getLabelModel().get("customValues");return s?(i=w_(n),e=n.scale.getExtent(),{labels:B(ut(j_(n,s),function(t){return t>=e[0]&&t<=e[1]}),function(t){var e={value:t};return{formattedLabel:i(e),rawLabel:n.scale.getLabel(e),tickValue:t}})}):"category"===n.type?(t=(s=n).getLabelModel(),a=Q_(s,t),!t.get("show")||s.scale.isBlank()?{labels:[],labelCategoryInterval:a.labelCategoryInterval}:a):(t=(r=n).scale.getTicks(),o=w_(r),{labels:B(t,function(t,e){return{level:t.level,formattedLabel:o(t,e),rawLabel:r.scale.getLabel(t),tickValue:t.value}})})}function $_(t,e){var n,i,r,o,a,s,l=t.getTickModel().get("customValues");return l?(n=t.scale.getExtent(),{ticks:ut(j_(t,l),function(t){return t>=n[0]&&t<=n[1]})}):"category"===t.type?(l=e,o=J_(e=t,"ticks"),a=M_(l),(s=t1(o,a))||(l.get("show")&&!e.scale.isBlank()||(i=[]),i=k(a)?i1(e,a,!0):"auto"===a?(s=Q_(e,e.getLabelModel()),r=s.labelCategoryInterval,B(s.labels,function(t){return t.tickValue})):n1(e,r=a,!0),e1(o,a,{ticks:i,tickCategoryInterval:r}))):{ticks:B(t.scale.getTicks(),function(t){return t.value})}}function Q_(t,e){var n,i=J_(t,"labels"),e=M_(e),r=t1(i,e);return r||e1(i,e,{labels:k(e)?i1(t,e):n1(t,n="auto"===e?null!=(i=Z_(r=t).autoInterval)?i:Z_(r).autoInterval=r.calculateCategoryInterval():e),labelCategoryInterval:n})}function J_(t,e){return Z_(t)[e]||(Z_(t)[e]=[])}function t1(t,e){for(var n=0;n<t.length;n++)if(t[n].key===e)return t[n].value}function e1(t,e,n){return t.push({key:e,value:n}),n}function n1(t,e,n){for(var i=w_(t),r=t.scale,o=r.getExtent(),a=t.getLabelModel(),s=[],l=Math.max((e||0)+1,1),e=o[0],u=r.count(),u=(0!==e&&1<l&&2<u/l&&(e=Math.round(Math.ceil(e/l)*l)),T_(t)),t=a.get("showMinLabel")||u,a=a.get("showMaxLabel")||u,h=(t&&e!==o[0]&&c(o[0]),e);h<=o[1];h+=l)c(h);function c(t){var e={value:t};s.push(n?t:{formattedLabel:i(e),rawLabel:r.getLabel(e),tickValue:t})}return a&&h-l!==o[1]&&c(o[1]),s}function i1(t,i,r){var o=t.scale,a=w_(t),s=[];return O(o.getTicks(),function(t){var e=o.getLabel(t),n=t.value;i(t.value,e)&&s.push(r?n:{formattedLabel:a(t),rawLabel:e,tickValue:n})}),s}var r1=[0,1],Dc=(o1.prototype.contain=function(t){var e=this._extent,n=Math.min(e[0],e[1]),e=Math.max(e[0],e[1]);return n<=t&&t<=e},o1.prototype.containData=function(t){return this.scale.contain(t)},o1.prototype.getExtent=function(){return this._extent.slice()},o1.prototype.getPixelPrecision=function(t){return ro(t||this.scale.getExtent(),this._extent)},o1.prototype.setExtent=function(t,e){var n=this._extent;n[0]=t,n[1]=e},o1.prototype.dataToCoord=function(t,e){var n=this._extent,i=this.scale;return t=i.normalize(t),this.onBand&&"ordinal"===i.type&&a1(n=n.slice(),i.count()),Jr(t,r1,n,e)},o1.prototype.coordToData=function(t,e){var n=this._extent,i=this.scale,i=(this.onBand&&"ordinal"===i.type&&a1(n=n.slice(),i.count()),Jr(t,n,r1,e));return this.scale.scale(i)},o1.prototype.pointToData=function(t,e){},o1.prototype.getTicksCoords=function(t){var e,n,i,r,o,a,s,l=(t=t||{}).tickModel||this.getTickModel(),u=B($_(this,l).ticks,function(t){return{coord:this.dataToCoord("ordinal"===this.scale.type?this.scale.getRawOrdinalNumber(t):t),tickValue:t}},this),l=l.get("alignWithLabel");function h(t,e){return t=eo(t),e=eo(e),a?e<t:t<e}return e=this,n=u,l=l,t=t.clamp,s=n.length,e.onBand&&!l&&s&&(l=e.getExtent(),1===s?(n[0].coord=l[0],i=n[1]={coord:l[1],tickValue:n[0].tickValue}):(o=n[s-1].tickValue-n[0].tickValue,r=(n[s-1].coord-n[0].coord)/o,O(n,function(t){t.coord-=r/2}),e=1+(o=e.scale.getExtent())[1]-n[s-1].tickValue,i={coord:n[s-1].coord+r*e,tickValue:o[1]+1},n.push(i)),a=l[0]>l[1],h(n[0].coord,l[0])&&(t?n[0].coord=l[0]:n.shift()),t&&h(l[0],n[0].coord)&&n.unshift({coord:l[0]}),h(l[1],i.coord)&&(t?i.coord=l[1]:n.pop()),t)&&h(i.coord,l[1])&&n.push({coord:l[1]}),u},o1.prototype.getMinorTicksCoords=function(){var t;return"ordinal"===this.scale.type?[]:(t=this.model.getModel("minorTick").get("splitNumber"),B(this.scale.getMinorTicks(t=0<t&&t<100?t:5),function(t){return B(t,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this)},this))},o1.prototype.getViewLabels=function(){return K_(this).labels},o1.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},o1.prototype.getTickModel=function(){return this.model.getModel("axisTick")},o1.prototype.getBandWidth=function(){var t=this._extent,e=this.scale.getExtent(),e=e[1]-e[0]+(this.onBand?1:0),t=(0===e&&(e=1),Math.abs(t[1]-t[0]));return Math.abs(t)/e},o1.prototype.calculateCategoryInterval=function(){r=(n=d=this).getLabelModel();var t={axisRotate:n.getRotate?n.getRotate():n.isHorizontal&&!n.isHorizontal()?90:0,labelRotate:r.get("rotate")||0,font:r.getFont()},e=w_(d),n=(t.axisRotate-t.labelRotate)/180*Math.PI,i=(r=d.scale).getExtent(),r=r.count();if(i[1]-i[0]<1)return 0;for(var o=1,a=(40<r&&(o=Math.max(1,Math.floor(r/40))),i[0]),s=d.dataToCoord(a+1)-d.dataToCoord(a),l=Math.abs(s*Math.cos(n)),s=Math.abs(s*Math.sin(n)),u=0,h=0;a<=i[1];a+=o)var c=1.3*(p=Sr(e({value:a}),t.font,"center","top")).width,p=1.3*p.height,u=Math.max(u,c,7),h=Math.max(h,p,7);var n=u/l,l=h/s,s=(isNaN(n)&&(n=1/0),isNaN(l)&&(l=1/0),Math.max(0,Math.floor(Math.min(n,l)))),n=Z_(d.model),l=d.getExtent(),d=n.lastAutoInterval,f=n.lastTickCount;return null!=d&&null!=f&&Math.abs(d-s)<=1&&Math.abs(f-r)<=1&&s<d&&n.axisExtent0===l[0]&&n.axisExtent1===l[1]?s=d:(n.lastTickCount=r,n.lastAutoInterval=s,n.axisExtent0=l[0],n.axisExtent1=l[1]),s},o1);function o1(t,e,n){this.onBand=!1,this.inverse=!1,this.dim=t,this.scale=e,this._extent=n||[0,0]}function a1(t,e){e=(t[1]-t[0])/e/2;t[0]+=e,t[1]-=e}var s1=2*Math.PI,l1=ja.CMD,u1=["top","right","bottom","left"];function h1(t,e,n,i,r,o,a,s){var l=r-t,u=o-e,n=n-t,i=i-e,h=Math.sqrt(n*n+i*i),l=(l*(n/=h)+u*(i/=h))/h,u=(s&&(l=Math.min(Math.max(l,0),1)),a[0]=t+(l*=h)*n),s=a[1]=e+l*i;return Math.sqrt((u-r)*(u-r)+(s-o)*(s-o))}function c1(t,e,n,i,r,o,a){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i);n=t+n,i=e+i,t=a[0]=Math.min(Math.max(r,t),n),n=a[1]=Math.min(Math.max(o,e),i);return Math.sqrt((t-r)*(t-r)+(n-o)*(n-o))}var p1=[];function d1(t,e,n){for(var i,r,o,a,s,l,u,h,c,p=0,d=0,f=0,g=0,y=1/0,m=e.data,v=t.x,_=t.y,x=0;x<m.length;){var w=m[x++],b=(1===x&&(f=p=m[x],g=d=m[x+1]),y);switch(w){case l1.M:p=f=m[x++],d=g=m[x++];break;case l1.L:b=h1(p,d,m[x],m[x+1],v,_,p1,!0),p=m[x++],d=m[x++];break;case l1.C:b=Gn(p,d,m[x++],m[x++],m[x++],m[x++],m[x],m[x+1],v,_,p1),p=m[x++],d=m[x++];break;case l1.Q:b=qn(p,d,m[x++],m[x++],m[x],m[x+1],v,_,p1),p=m[x++],d=m[x++];break;case l1.A:var S=m[x++],M=m[x++],T=m[x++],C=m[x++],I=m[x++],k=m[x++],D=(x+=1,!!(1-m[x++])),A=Math.cos(I)*T+S,P=Math.sin(I)*C+M;x<=1&&(f=A,g=P),P=(A=I)+k,D=D,a=(v-S)*(o=C)/T+S,s=_,l=p1,c=h=u=void 0,a-=i=S,s-=r=M,u=Math.sqrt(a*a+s*s),h=(a/=u)*o+i,c=(s/=u)*o+r,b=Math.abs(A-P)%s1<1e-4||((P=D?(D=A,A=Qa(P),Qa(D)):(A=Qa(A),Qa(P)))<A&&(P+=s1),(D=Math.atan2(s,a))<0&&(D+=s1),A<=D&&D<=P)||A<=D+s1&&D+s1<=P?(l[0]=h,l[1]=c,u-o):(c=((D=o*Math.cos(A)+i)-a)*(D-a)+((h=o*Math.sin(A)+r)-s)*(h-s))<(i=((u=o*Math.cos(P)+i)-a)*(u-a)+((A=o*Math.sin(P)+r)-s)*(A-s))?(l[0]=D,l[1]=h,Math.sqrt(c)):(l[0]=u,l[1]=A,Math.sqrt(i)),p=Math.cos(I+k)*T+S,d=Math.sin(I+k)*C+M;break;case l1.R:b=c1(f=p=m[x++],g=d=m[x++],m[x++],m[x++],v,_,p1);break;case l1.Z:b=h1(p,d,f,g,v,_,p1,!0),p=f,d=g}b<y&&(y=b,n.set(p1[0],p1[1]))}return y}var f1=new M,g1=new M,y1=new M,m1=new M,v1=new M;function _1(t,e){if(t){var n=t.getTextGuideLine(),i=t.getTextContent();if(i&&n){var r=t.textGuideLineConfig||{},o=[[0,0],[0,0],[0,0]],a=r.candidates||u1,s=i.getBoundingRect().clone(),l=(s.applyTransform(i.getComputedTransform()),1/0),u=r.anchor,h=t.getComputedTransform(),c=h&&Fe([],h),p=e.get("length2")||0;u&&y1.copy(u);for(var d,f,g=0;g<a.length;g++){var y=a[g],m=(S=b=w=x=_=v=m=void 0,y),v=0,_=s,x=f1,w=m1,b=_.width,S=_.height;switch(m){case"top":x.set(_.x+b/2,_.y-v),w.set(0,-1);break;case"bottom":x.set(_.x+b/2,_.y+S+v),w.set(0,1);break;case"left":x.set(_.x-v,_.y+S/2),w.set(-1,0);break;case"right":x.set(_.x+b+v,_.y+S/2),w.set(1,0)}M.scaleAndAdd(g1,f1,m1,p),g1.transform(c);y=t.getBoundingRect(),y=u?u.distance(g1):t instanceof ds?d1(g1,t.path,y1):(m=y1,d=c1((d=y).x,y.y,y.width,y.height,g1.x,g1.y,p1),m.set(p1[0],p1[1]),d);y<l&&(l=y,g1.transform(h),y1.transform(h),y1.toArray(o[0]),g1.toArray(o[1]),f1.toArray(o[2]))}i=o,(r=e.get("minTurnAngle"))<=180&&0<r&&(r=r/180*Math.PI,f1.fromArray(i[0]),g1.fromArray(i[1]),y1.fromArray(i[2]),M.sub(m1,f1,g1),M.sub(v1,y1,g1),e=m1.len(),f=v1.len(),e<.001||f<.001||(m1.scale(1/e),v1.scale(1/f),e=m1.dot(v1),Math.cos(r)<e&&(f=h1(g1.x,g1.y,y1.x,y1.y,f1.x,f1.y,x1,!1),w1.fromArray(x1),w1.scaleAndAdd(v1,f/Math.tan(Math.PI-r)),e=y1.x!==g1.x?(w1.x-g1.x)/(y1.x-g1.x):(w1.y-g1.y)/(y1.y-g1.y),isNaN(e)||(e<0?M.copy(w1,g1):1<e&&M.copy(w1,y1),w1.toArray(i[1]))))),n.setShape({points:o})}}}var x1=[],w1=new M;function b1(t,e,n,i){var r="normal"===n,n=r?t:t.ensureState(n),e=(n.ignore=e,i.get("smooth")),e=(e&&!0===e&&(e=.3),n.shape=n.shape||{},0<e&&(n.shape.smooth=e),i.getModel("lineStyle").getLineStyle());r?t.useStyle(e):n.style=e}function S1(t,e){var n=e.smooth,i=e.points;if(i)if(t.moveTo(i[0][0],i[0][1]),0<n&&3<=i.length){var e=$t(i[0],i[1]),r=$t(i[1],i[2]);e&&r?(n=Math.min(e,r)*n,e=te([],i[1],i[0],n/e),n=te([],i[1],i[2],n/r),r=te([],e,n,.5),t.bezierCurveTo(e[0],e[1],e[0],e[1],r[0],r[1]),t.bezierCurveTo(n[0],n[1],n[0],n[1],i[2][0],i[2][1])):(t.lineTo(i[1][0],i[1][1]),t.lineTo(i[2][0],i[2][1]))}else for(var o=1;o<i.length;o++)t.lineTo(i[o][0],i[o][1])}function M1(t){for(var e=[],n=0;n<t.length;n++){var i,r,o,a,s,l,u=t[n];u.defaultAttr.ignore||(r=(i=u.label).getComputedTransform(),o=i.getBoundingRect(),a=!r||r[1]<1e-5&&r[2]<1e-5,l=i.style.margin||0,(s=o.clone()).applyTransform(r),s.x-=l/2,s.y-=l/2,s.width+=l,s.height+=l,l=a?new wh(o,r):null,e.push({label:i,labelLine:u.labelLine,rect:s,localRect:o,obb:l,priority:u.priority,defaultAttr:u.defaultAttr,layoutOption:u.computedLayoutOption,axisAligned:a,transform:r}))}return e}function T1(s,l,u,t,e,n){var h=s.length;if(!(h<2)){s.sort(function(t,e){return t.rect[l]-e.rect[l]});for(var i=0,o=!1,r=0,a=0;a<h;a++){var c,p=s[a],d=p.rect;(c=d[l]-i)<0&&(d[l]-=c,p.label[l]-=c,o=!0),r+=Math.max(-c,0),i=d[l]+d[u]}0<r&&n&&x(-r/h,0,h);var f,g,y=s[0],m=s[h-1];return v(),f<0&&w(-f,.8),g<0&&w(g,.8),v(),_(f,g,1),_(g,f,-1),v(),f<0&&b(-f),g<0&&b(g),o}function v(){f=y.rect[l]-t,g=e-m.rect[l]-m.rect[u]}function _(t,e,n){t<0&&(0<(e=Math.min(e,-t))?(x(e*n,0,h),(e=e+t)<0&&w(-e*n,1)):w(-t*n,1))}function x(t,e,n){0!==t&&(o=!0);for(var i=e;i<n;i++){var r=s[i];r.rect[l]+=t,r.label[l]+=t}}function w(t,e){for(var n=[],i=0,r=1;r<h;r++){var o=s[r-1].rect,o=Math.max(s[r].rect[l]-o[l]-o[u],0);n.push(o),i+=o}if(i){var a=Math.min(Math.abs(t)/i,e);if(0<t)for(r=0;r<h-1;r++)x(n[r]*a,0,r+1);else for(r=h-1;0<r;r--)x(-(n[r-1]*a),r,h)}}function b(t){for(var e=t<0?-1:1,n=(t=Math.abs(t),Math.ceil(t/(h-1))),i=0;i<h-1;i++)if(0<e?x(n,0,i+1):x(-n,h-i-1,h),(t-=n)<=0)return}}function C1(t){var e=[],n=(t.sort(function(t,e){return e.priority-t.priority}),new X(0,0,0,0));function i(t){var e;t.ignore||null==(e=t.ensureState("emphasis")).ignore&&(e.ignore=!1),t.ignore=!0}for(var r=0;r<t.length;r++){for(var o=t[r],a=o.axisAligned,s=o.localRect,l=o.transform,u=o.label,h=o.labelLine,c=(n.copy(o.rect),n.width-=.1,n.height-=.1,n.x+=.05,n.y+=.05,o.obb),p=!1,d=0;d<e.length;d++){var f=e[d];if(n.intersect(f.rect)){if(a&&f.axisAligned){p=!0;break}if(f.obb||(f.obb=new wh(f.localRect,f.transform)),(c=c||new wh(s,l)).intersect(f.obb)){p=!0;break}}}p?(i(u),h&&i(h)):(u.attr("ignore",o.defaultAttr.ignore),h&&h.attr("ignore",o.defaultAttr.labelGuideIgnore),e.push(o))}}function I1(t,e){var n=t.label,e=e&&e.getTextGuideLine();return{dataIndex:t.dataIndex,dataType:t.dataType,seriesIndex:t.seriesModel.seriesIndex,text:t.label.style.text,rect:t.hostRect,labelRect:t.rect,align:n.style.align,verticalAlign:n.style.verticalAlign,labelLinePoints:function(t){if(t){for(var e=[],n=0;n<t.length;n++)e.push(t[n].slice());return e}}(e&&e.shape.points)}}var k1=["align","verticalAlign","width","height","fontSize"],D1=new mr,A1=Po(),P1=Po();function L1(t,e,n){for(var i=0;i<n.length;i++){var r=n[i];null!=e[r]&&(t[r]=e[r])}}var O1=["x","y","rotation"],R1=(N1.prototype.clearLabels=function(){this._labelList=[],this._chartViewList=[]},N1.prototype._addLabel=function(t,e,n,i,r){var o,a=i.style,s=i.__hostTarget.textConfig||{},l=i.getComputedTransform(),u=i.getBoundingRect().plain(),l=(X.applyTransform(u,u,l),l?D1.setLocalTransform(l):(D1.x=D1.y=D1.rotation=D1.originX=D1.originY=0,D1.scaleX=D1.scaleY=1),D1.rotation=Qa(D1.rotation),i.__hostTarget),h=(l&&(o=l.getBoundingRect().plain(),h=l.getComputedTransform(),X.applyTransform(o,o,h)),o&&l.getTextGuideLine());this._labelList.push({label:i,labelLine:h,seriesModel:n,dataIndex:t,dataType:e,layoutOption:r,computedLayoutOption:null,rect:u,hostRect:o,priority:o?o.width*o.height:0,defaultAttr:{ignore:i.ignore,labelGuideIgnore:h&&h.ignore,x:D1.x,y:D1.y,scaleX:D1.scaleX,scaleY:D1.scaleY,rotation:D1.rotation,style:{x:a.x,y:a.y,align:a.align,verticalAlign:a.verticalAlign,width:a.width,height:a.height,fontSize:a.fontSize},cursor:i.cursor,attachedPos:s.position,attachedRot:s.rotation}})},N1.prototype.addLabelsOfSeries=function(t){var n=this,i=(this._chartViewList.push(t),t.__model),r=i.get("labelLayout");(k(r)||ht(r).length)&&t.group.traverse(function(t){if(t.ignore)return!0;var e=t.getTextContent(),t=D(t);e&&!e.disableLabelLayout&&n._addLabel(t.dataIndex,t.dataType,i,e,r)})},N1.prototype.updateLayoutConfig=function(t){var e=t.getWidth(),n=t.getHeight();for(var i=0;i<this._labelList.length;i++){var r=this._labelList[i],o=r.label,a=o.__hostTarget,s=r.defaultAttr,l=void 0,l=k(r.layoutOption)?r.layoutOption(I1(r,a)):r.layoutOption,u=(r.computedLayoutOption=l=l||{},Math.PI/180),h=(a&&a.setTextConfig({local:!1,position:null!=l.x||null!=l.y?null:s.attachedPos,rotation:null!=l.rotate?l.rotate*u:s.attachedRot,offset:[l.dx||0,l.dy||0]}),!1);null!=l.x?(o.x=to(l.x,e),o.setStyle("x",0),h=!0):(o.x=s.x,o.setStyle("x",s.style.x)),null!=l.y?(o.y=to(l.y,n),o.setStyle("y",0),h=!0):(o.y=s.y,o.setStyle("y",s.style.y)),l.labelLinePoints&&(c=a.getTextGuideLine())&&(c.setShape({points:l.labelLinePoints}),h=!1),A1(o).needsUpdateLabelLine=h,o.rotation=null!=l.rotate?l.rotate*u:s.rotation,o.scaleX=s.scaleX,o.scaleY=s.scaleY;for(var c,p=0;p<k1.length;p++){var d=k1[p];o.setStyle(d,(null!=l[d]?l:s.style)[d])}l.draggable?(o.draggable=!0,o.cursor="move",a&&(c=r.seriesModel,null!=r.dataIndex&&(c=r.seriesModel.getData(r.dataType).getItemModel(r.dataIndex)),o.on("drag",function(t,e){return function(){_1(t,e)}}(a,c.getModel("labelLine"))))):(o.off("drag"),o.cursor=s.cursor)}},N1.prototype.layout=function(t){var e,n,i=t.getWidth(),t=t.getHeight(),r=M1(this._labelList),o=ut(r,function(t){return"shiftX"===t.layoutOption.moveOverlap}),a=ut(r,function(t){return"shiftY"===t.layoutOption.moveOverlap});T1(o,"x","width",0,i,e),T1(a,"y","height",0,t,n),C1(ut(r,function(t){return t.layoutOption.hideOverlap}))},N1.prototype.processLabelsOverall=function(){var a=this;O(this._chartViewList,function(t){var i=t.__model,r=t.ignoreLabelLineUpdate,o=i.isAnimationEnabled();t.group.traverse(function(t){if(t.ignore&&!t.forceLabelAnimation)return!0;var e=!r,n=t.getTextContent();(e=!e&&n?A1(n).needsUpdateLabelLine:e)&&a._updateLabelLine(t,i),o&&a._animateLabels(t,i)})})},N1.prototype._updateLabelLine=function(t,e){var n=t.getTextContent(),i=D(t),r=i.dataIndex;if(n&&null!=r){var n=e.getData(i.dataType),e=n.getItemModel(r),i={},r=n.getItemVisual(r,"style"),r=(r&&(n=n.getVisual("drawType"),i.stroke=r[n]),e.getModel("labelLine")),o=t,a=function(t,e){for(var n={normal:t.getModel(e=e||"labelLine")},i=0;i<tl.length;i++){var r=tl[i];n[r]=t.getModel([r,e])}return n}(e),n=i,s=o.getTextGuideLine(),l=o.getTextContent();if(l){for(var e=a.normal,u=e.get("show"),h=l.ignore,c=0;c<el.length;c++){var p,d=el[c],f=a[d],g="normal"===d;f&&(p=f.get("show"),(g?h:N(l.states[d]&&l.states[d].ignore,h))||!N(p,u)?((p=g?s:s&&s.states[d])&&(p.ignore=!0),s&&b1(s,!0,d,f)):(s||(s=new Uu,o.setTextGuideLine(s),g||!h&&u||b1(s,!0,"normal",a.normal),o.stateProxy&&(s.stateProxy=o.stateProxy)),b1(s,!1,d,f)))}s&&(z(s.style,n),s.style.fill=null,n=e.get("showAbove"),(o.textGuideLineConfig=o.textGuideLineConfig||{}).showAbove=n||!1,s.buildPath=S1)}else s&&o.removeTextGuideLine();_1(t,r)}},N1.prototype._animateLabels=function(t,e){var n,i,r,o,a,s=t.getTextContent(),l=t.getTextGuideLine();!s||!t.forceLabelAnimation&&(s.ignore||s.invisible||t.disableLabelAnimation||Ah(t))||(o=(r=A1(s)).oldLayout,n=(i=D(t)).dataIndex,a={x:s.x,y:s.y,rotation:s.rotation},i=e.getData(i.dataType),o?(s.attr(o),(t=t.prevStates)&&(0<=I(t,"select")&&s.attr(r.oldLayoutSelect),0<=I(t,"emphasis"))&&s.attr(r.oldLayoutEmphasis),kh(s,a,e,n)):(s.attr(a),mc(s).valueAnimation||(t=N(s.style.opacity,1),s.style.opacity=0,Dh(s,{style:{opacity:t}},e,n))),r.oldLayout=a,s.states.select&&(L1(t=r.oldLayoutSelect={},a,O1),L1(t,s.states.select,O1)),s.states.emphasis&&(L1(t=r.oldLayoutEmphasis={},a,O1),L1(t,s.states.emphasis,O1)),vc(s,n,i,e,e)),!l||l.ignore||l.invisible||(o=(r=P1(l)).oldLayout,a={points:l.shape.points},o?(l.attr({shape:o}),kh(l,{shape:a},e)):(l.setShape(a),l.style.strokePercent=0,Dh(l,{style:{strokePercent:1}},e)),r.oldLayout=a)},N1);function N1(){this._labelList=[],this._chartViewList=[]}var E1=Po();function z1(t){t.registerUpdateLifecycle("series:beforeupdate",function(t,e,n){(E1(e).labelManager||(E1(e).labelManager=new R1)).clearLabels()}),t.registerUpdateLifecycle("series:layoutlabels",function(t,e,n){var i=E1(e).labelManager;n.updatedSeries.forEach(function(t){i.addLabelsOfSeries(e.getViewOfSeriesModel(t))}),i.updateLayoutConfig(e),i.layout(e),i.processLabelsOverall()})}function B1(t,e,n){var i=G.createCanvas(),r=e.getWidth(),e=e.getHeight(),o=i.style;return o&&(o.position="absolute",o.left="0",o.top="0",o.width=r+"px",o.height=e+"px",i.setAttribute("data-zr-dom-id",t)),i.width=r*n,i.height=e*n,i}A_(z1);u(H1,F1=le),H1.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},H1.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},H1.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},H1.prototype.setUnpainted=function(){this.__firstTimePaint=!0},H1.prototype.createBackBuffer=function(){var t=this.dpr;this.domBack=B1("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},H1.prototype.createRepaintRects=function(t,e,n,i){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var l=[],u=this.maxRepaintRectCount,h=!1,c=new X(0,0,0,0);function r(t){if(t.isFinite()&&!t.isZero())if(0===l.length)(e=new X(0,0,0,0)).copy(t),l.push(e);else{for(var e,n=!1,i=1/0,r=0,o=0;o<l.length;++o){var a=l[o];if(a.intersect(t)){var s=new X(0,0,0,0);s.copy(a),s.union(t),l[o]=s,n=!0;break}h&&(c.copy(t),c.union(a),s=t.width*t.height,a=a.width*a.height,(a=c.width*c.height-s-a)<i)&&(i=a,r=o)}h&&(l[r].union(t),n=!0),n||((e=new X(0,0,0,0)).copy(t),l.push(e)),h=h||l.length>=u}}for(var o,a=this.__startIndex;a<this.__endIndex;++a)(s=t[a])&&(d=s.shouldBePainted(n,i,!0,!0),(p=s.__isRendered&&(s.__dirty&vn||!d)?s.getPrevPaintRect():null)&&r(p),o=d&&(s.__dirty&vn||!s.__isRendered)?s.getPaintRect():null)&&r(o);for(a=this.__prevStartIndex;a<this.__prevEndIndex;++a){var s,p,d=(s=e[a])&&s.shouldBePainted(n,i,!0,!0);!s||d&&s.__zr||!s.__isRendered||(p=s.getPrevPaintRect())&&r(p)}do{for(var f=!1,a=0;a<l.length;)if(l[a].isZero())l.splice(a,1);else{for(var g=a+1;g<l.length;)l[a].intersect(l[g])?(f=!0,l[a].union(l[g]),l.splice(g,1)):g++;a++}}while(f);return this._paintRects=l},H1.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},H1.prototype.resize=function(t,e){var n=this.dpr,i=this.dom,r=i.style,o=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),i.width=t*n,i.height=e*n,o&&(o.width=t*n,o.height=e*n,1!==n)&&this.ctxBack.scale(n,n)},H1.prototype.clear=function(t,o,e){var n=this.dom,a=this.ctx,i=n.width,r=n.height,s=(o=o||this.clearColor,this.motionBlur&&!t),l=this.lastFrameAlpha,u=this.dpr,h=this,c=(s&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(n,0,0,i/u,r/u)),this.domBack);function p(t,e,n,i){var r;a.clearRect(t,e,n,i),o&&"transparent"!==o&&(r=void 0,mt(o)?(r=(o.global||o.__width===n&&o.__height===i)&&o.__canvasGradient||$y(a,o,{x:0,y:0,width:n,height:i}),o.__canvasGradient=r,o.__width=n,o.__height=i):vt(o)&&(o.scaleX=o.scaleX||u,o.scaleY=o.scaleY||u,r=sm(a,o,{dirty:function(){h.setUnpainted(),h.painter.refresh()}})),a.save(),a.fillStyle=r||o,a.fillRect(t,e,n,i),a.restore()),s&&(a.save(),a.globalAlpha=l,a.drawImage(c,t,e,n,i),a.restore())}!e||s?p(0,0,i,r):e.length&&O(e,function(t){p(t.x*u,t.y*u,t.width*u,t.height*u)})};var F1,V1=H1;function H1(t,e,n){var i,r=F1.call(this)||this,t=(r.motionBlur=!1,r.lastFrameAlpha=.7,r.dpr=1,r.virtual=!1,r.config={},r.incremental=!1,r.zlevel=0,r.maxRepaintRectCount=5,r.__dirty=!0,r.__firstTimePaint=!0,r.__used=!1,r.__drawIndex=0,r.__startIndex=0,r.__endIndex=0,r.__prevStartIndex=null,r.__prevEndIndex=null,n=n||lr,"string"==typeof t?i=B1(t,e,n):R(t)&&(t=(i=t).id),r.id=t,(r.dom=i).style);return t&&(zt(i),i.onselectstart=function(){return!1},t.padding="0",t.margin="0",t.borderWidth="0"),r.painter=e,r.dpr=n,r}var G1=314159;v.prototype.getType=function(){return"canvas"},v.prototype.isSingleCanvas=function(){return this._singleCanvas},v.prototype.getViewportRoot=function(){return this._domRoot},v.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},v.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),n=this._prevDisplayList,i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,n,t,this._redrawId);for(var r=0;r<i.length;r++){var o,a=i[r],a=this._layers[a];!a.__builtin__&&a.refresh&&(o=0===r?this._backgroundColor:null,a.refresh(o))}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},v.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},v.prototype._paintHoverList=function(t){var e=t.length,n=this._hoverlayer;if(n&&n.clear(),e){for(var i,r={inHover:!0,viewWidth:this._width,viewHeight:this._height},o=0;o<e;o++){var a=t[o];a.__inHover&&(n=n||(this._hoverlayer=this.getLayer(1e5)),i||(i=n.ctx).save(),xm(i,a,r,o===e-1))}i&&i.restore()}},v.prototype.getHoverLayer=function(){return this.getLayer(1e5)},v.prototype.paintOne=function(t,e){_m(t,e)},v.prototype._paintList=function(t,e,n,i){var r,o,a;this._redrawId===i&&(n=n||!1,this._updateLayerStatus(t),r=(o=this._doPaintList(t,e,n)).finished,o=o.needsRefreshHover,this._needsManuallyCompositing&&this._compositeManually(),o&&this._paintHoverList(t),r?this.eachLayer(function(t){t.afterBrush&&t.afterBrush()}):(a=this,Tn(function(){a._paintList(t,e,n,i)})))},v.prototype._compositeManually=function(){var e=this.getLayer(G1).ctx,n=this._domRoot.width,i=this._domRoot.height;e.clearRect(0,0,n,i),this.eachBuiltinLayer(function(t){t.virtual&&e.drawImage(t.dom,0,0,n,i)})},v.prototype._doPaintList=function(d,f,g){for(var y=this,m=[],v=this._opts.useDirtyRect,t=0;t<this._zlevelList.length;t++){var e=this._zlevelList[t],e=this._layers[e];e.__builtin__&&e!==this._hoverlayer&&(e.__dirty||g)&&m.push(e)}for(var _=!0,x=!1,n=function(t){function e(t){var e={inHover:!1,allClipped:!1,prevEl:null,viewWidth:y._width,viewHeight:y._height};for(i=s;i<r.__endIndex;i++){var n=d[i];if(n.__inHover&&(x=!0),y._doPaintEl(n,r,v,t,e,i===r.__endIndex-1),l)if(15<Date.now()-u)break}e.prevElClipPaths&&o.restore()}var n,i,r=m[t],o=r.ctx,a=v&&r.createRepaintRects(d,f,w._width,w._height),s=g?r.__startIndex:r.__drawIndex,l=!g&&r.incremental&&Date.now,u=l&&Date.now(),t=r.zlevel===w._zlevelList[0]?w._backgroundColor:null;r.__startIndex!==r.__endIndex&&(s!==r.__startIndex||(n=d[s]).incremental&&n.notClear&&!g)||r.clear(!1,t,a),-1===s&&(console.error("For some unknown reason. drawIndex is -1"),s=r.__startIndex);if(a)if(0===a.length)i=r.__endIndex;else for(var h=w.dpr,c=0;c<a.length;++c){var p=a[c];o.save(),o.beginPath(),o.rect(p.x*h,p.y*h,p.width*h,p.height*h),o.clip(),e(p),o.restore()}else o.save(),e(),o.restore();r.__drawIndex=i,r.__drawIndex<r.__endIndex&&(_=!1)},w=this,i=0;i<m.length;i++)n(i);return b.wxa&&O(this._layers,function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()}),{finished:_,needsRefreshHover:x}},v.prototype._doPaintEl=function(t,e,n,i,r,o){e=e.ctx;n?(n=t.getPaintRect(),(!i||n&&n.intersect(i))&&(xm(e,t,r,o),t.setPrevPaintRect(n))):xm(e,t,r,o)},v.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=G1);var n=this._layers[t];return n||((n=new V1("zr_"+t,this,this.dpr)).zlevel=t,n.__builtin__=!0,this._layerConfig[t]?d(n,this._layerConfig[t],!0):this._layerConfig[t-.01]&&d(n,this._layerConfig[t-.01],!0),e&&(n.virtual=e),this.insertLayer(t,n),n.initContext()),n},v.prototype.insertLayer=function(t,e){var n,i=this._layers,r=this._zlevelList,o=r.length,a=this._domRoot,s=null,l=-1;if(!i[t]&&(n=e)&&(n.__builtin__||"function"==typeof n.resize&&"function"==typeof n.refresh)){if(0<o&&t>r[0]){for(l=0;l<o-1&&!(r[l]<t&&r[l+1]>t);l++);s=i[r[l]]}r.splice(l+1,0,t),(i[t]=e).virtual||(s?(n=s.dom).nextSibling?a.insertBefore(e.dom,n.nextSibling):a.appendChild(e.dom):a.firstChild?a.insertBefore(e.dom,a.firstChild):a.appendChild(e.dom)),e.painter||(e.painter=this)}},v.prototype.eachLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i];t.call(e,this._layers[r],r)}},v.prototype.eachBuiltinLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__&&t.call(e,o,r)}},v.prototype.eachOtherLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__||t.call(e,o,r)}},v.prototype.getLayers=function(){return this._layers},v.prototype._updateLayerStatus=function(t){function e(t){r&&(r.__endIndex!==t&&(r.__dirty=!0),r.__endIndex=t)}if(this.eachBuiltinLayer(function(t,e){t.__dirty=t.__used=!1}),this._singleCanvas)for(var n=1;n<t.length;n++)if((s=t[n]).zlevel!==t[n-1].zlevel||s.incremental){this._needsManuallyCompositing=!0;break}for(var i,r=null,o=0,a=0;a<t.length;a++){var s,l=(s=t[a]).zlevel,u=void 0;i!==l&&(i=l,o=0),s.incremental?((u=this.getLayer(l+.001,this._needsManuallyCompositing)).incremental=!0,o=1):u=this.getLayer(l+(0<o?.01:0),this._needsManuallyCompositing),u.__builtin__||it("ZLevel "+l+" has been used by unkown layer "+u.id),u!==r&&(u.__used=!0,u.__startIndex!==a&&(u.__dirty=!0),u.__startIndex=a,u.incremental?u.__drawIndex=-1:u.__drawIndex=a,e(a),r=u),s.__dirty&vn&&!s.__inHover&&(u.__dirty=!0,u.incremental)&&u.__drawIndex<0&&(u.__drawIndex=a)}e(a),this.eachBuiltinLayer(function(t,e){!t.__used&&0<t.getElementCount()&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)})},v.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},v.prototype._clearLayer=function(t){t.clear()},v.prototype.setBackgroundColor=function(t){this._backgroundColor=t,O(this._layers,function(t){t.setUnpainted()})},v.prototype.configLayer=function(t,e){if(e){var n=this._layerConfig;n[t]?d(n[t],e,!0):n[t]=e;for(var i=0;i<this._zlevelList.length;i++){var r=this._zlevelList[i];r!==t&&r!==t+.01||d(this._layers[r],n[t],!0)}}},v.prototype.delLayer=function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(I(n,t),1))},v.prototype.resize=function(t,e){if(this._domRoot.style){var n=this._domRoot,i=(n.style.display="none",this._opts),r=this.root;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=Jy(r,0,i),e=Jy(r,1,i),n.style.display="",this._width!==t||e!==this._height){for(var o in n.style.width=t+"px",n.style.height=e+"px",this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(G1).resize(t,e)}return this},v.prototype.clearLayer=function(t){t=this._layers[t];t&&t.clear()},v.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},v.prototype.getRenderedCanvas=function(t){if(this._singleCanvas&&!this._compositeManually)return this._layers[G1].dom;var e=new V1("image",this,(t=t||{}).pixelRatio||this.dpr),n=(e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor),e.ctx);if(t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,r=e.dom.height;this.eachLayer(function(t){t.__builtin__?n.drawImage(t.dom,0,0,i,r):t.renderToCanvas&&(n.save(),t.renderToCanvas(n),n.restore())})}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},a=this.storage.getDisplayList(!0),s=0,l=a.length;s<l;s++){var u=a[s];xm(n,u,o,s===l-1)}return e.dom},v.prototype.getWidth=function(){return this._width},v.prototype.getHeight=function(){return this._height};var W1=v;function v(t,e,n,i){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var r=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=L({},n||{}),this.dpr=n.devicePixelRatio||lr,this._singleCanvas=r;(this.root=t).style&&(zt(t),t.innerHTML=""),this.storage=e;var o,a,e=this._zlevelList,s=(this._prevDisplayList=[],this._layers);r?(o=(r=t).width,a=r.height,null!=n.width&&(o=n.width),null!=n.height&&(a=n.height),this.dpr=n.devicePixelRatio||1,r.width=o*this.dpr,r.height=a*this.dpr,this._width=o,this._height=a,(o=new V1(r,this,this.dpr)).__builtin__=!0,o.initContext(),(s[G1]=o).zlevel=G1,e.push(G1),this._domRoot=t):(this._width=Jy(t,0,n),this._height=Jy(t,1,n),o=this._domRoot=(a=this._width,r=this._height,(s=document.createElement("div")).style.cssText=["position:relative","width:"+a+"px","height:"+r+"px","padding:0","margin:0","border-width:0"].join(";")+";",s),t.appendChild(o))}u(Y1,U1=g),Y1.prototype.init=function(t,e,n){U1.prototype.init.call(this,t,e,n),this._sourceManager=new ug(this),cg(this)},Y1.prototype.mergeOption=function(t,e){U1.prototype.mergeOption.call(this,t,e),cg(this)},Y1.prototype.optionUpdated=function(){this._sourceManager.dirty()},Y1.prototype.getSourceManager=function(){return this._sourceManager},Y1.type="dataset",Y1.defaultOption={seriesLayoutBy:Kp};var U1,X1=Y1;function Y1(){var t=null!==U1&&U1.apply(this,arguments)||this;return t.type="dataset",t}u(j1,q1=Gg),j1.type="dataset";var q1,Z1=j1;function j1(){var t=null!==q1&&q1.apply(this,arguments)||this;return t.type="dataset",t}function K1(t){t.registerComponentModel(X1),t.registerComponentView(Z1)}A_([function(t){t.registerPainter("canvas",W1)},K1]),A_(z1);u(J1,$1=Rg),J1.prototype.getInitialData=function(t){return bv(null,this,{useEncodeDefaulter:!0})},J1.prototype.getLegendIcon=function(t){var e=new Hr,n=Zy("line",0,t.itemHeight/2,t.itemWidth,0,t.lineStyle.stroke,!1),n=(e.add(n),n.setStyle(t.lineStyle),this.getData().getVisual("symbol")),i=this.getData().getVisual("symbolRotate"),n="none"===n?"circle":n,r=.8*t.itemHeight,r=Zy(n,(t.itemWidth-r)/2,(t.itemHeight-r)/2,r,r,t.itemStyle.fill),i=(e.add(r),r.setStyle(t.itemStyle),"inherit"===t.iconRotate?i:t.iconRotate||0);return r.rotation=i*Math.PI/180,r.setOrigin([t.itemWidth/2,t.itemHeight/2]),-1<n.indexOf("empty")&&(r.style.stroke=r.style.fill,r.style.fill="#fff",r.style.lineWidth=2),e},J1.type="series.line",J1.dependencies=["grid","polar"],J1.defaultOption={z:3,coordinateSystem:"cartesian2d",legendHoverLink:!0,clip:!0,label:{position:"top"},endLabel:{show:!1,valueAnimation:!0,distance:8},lineStyle:{width:2,type:"solid"},emphasis:{scale:!0},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0,universalTransition:{divideShape:"clone"},triggerLineEvent:!1};var $1,Q1=J1;function J1(){var t=null!==$1&&$1.apply(this,arguments)||this;return t.type=J1.type,t.hasSymbolVisual=!0,t}function tx(t,e){var n,i=t.mapDimensionsAll("defaultedLabel"),r=i.length;if(1===r)return null!=(n=vf(t,e,i[0]))?n+"":null;if(r){for(var o=[],a=0;a<i.length;a++)o.push(vf(t,e,i[a]));return o.join(" ")}}u(ix,ex=Hr),ix.prototype._createSymbol=function(t,e,n,i,r){this.removeAll();r=Zy(t,-1,-1,2,2,null,r);r.attr({z2:100,culling:!0,scaleX:i[0]/2,scaleY:i[1]/2}),r.drift=rx,this._symbolType=t,this.add(r)},ix.prototype.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(null,t)},ix.prototype.getSymbolType=function(){return this._symbolType},ix.prototype.getSymbolPath=function(){return this.childAt(0)},ix.prototype.highlight=function(){Sl(this.childAt(0))},ix.prototype.downplay=function(){Ml(this.childAt(0))},ix.prototype.setZ=function(t,e){var n=this.childAt(0);n.zlevel=t,n.z=e},ix.prototype.setDraggable=function(t,e){var n=this.childAt(0);n.draggable=t,n.cursor=!e&&t?"move":n.cursor},ix.prototype.updateData=function(t,e,n,i){this.silent=!1;var r,o,a,s=t.getItemVisual(e,"symbol")||"circle",l=t.hostModel,u=ix.getSymbolSize(t,e),h=s!==this._symbolType,c=i&&i.disableAnimation;h?(r=t.getItemVisual(e,"symbolKeepAspect"),this._createSymbol(s,t,e,u,r)):((o=this.childAt(0)).silent=!1,a={scaleX:u[0]/2,scaleY:u[1]/2},c?o.attr(a):kh(o,a,l,e),Ch(s=o).oldStyle=s.style),this._updateCommon(t,e,u,n,i),h&&(o=this.childAt(0),c||(a={scaleX:this._sizeX,scaleY:this._sizeY,style:{opacity:o.style.opacity}},o.scaleX=o.scaleY=0,o.style.opacity=0,Dh(o,a,l,e))),c&&this.childAt(0).stopAnimation("leave")},ix.prototype._updateCommon=function(e,t,n,i,r){var o,a,s,l,u,h,c,p,d=this.childAt(0),f=e.hostModel,g=(i&&(o=i.emphasisItemStyle,s=i.blurItemStyle,a=i.selectItemStyle,l=i.focus,u=i.blurScope,c=i.labelStatesModels,p=i.hoverScale,y=i.cursorStyle,h=i.emphasisDisabled),i&&!e.hasItemOption||(o=(g=(i=i&&i.itemModel?i.itemModel:e.getItemModel(t)).getModel("emphasis")).getModel("itemStyle").getItemStyle(),a=i.getModel(["select","itemStyle"]).getItemStyle(),s=i.getModel(["blur","itemStyle"]).getItemStyle(),l=g.get("focus"),u=g.get("blurScope"),h=g.get("disabled"),c=hc(i),p=g.getShallow("scale"),y=i.getShallow("cursor")),e.getItemVisual(t,"symbolRotate")),i=(d.attr("rotation",(g||0)*Math.PI/180||0),jy(e.getItemVisual(t,"symbolOffset"),n)),g=(i&&(d.x=i[0],d.y=i[1]),y&&d.attr("cursor",y),e.getItemVisual(t,"style")),i=g.fill,y=(d instanceof ws?(y=d.style,d.useStyle(L({image:y.image,x:y.x,y:y.y,width:y.width,height:y.height},g))):(d.__isEmptyBrush?d.useStyle(L({},g)):d.useStyle(g),d.style.decal=null,d.setColor(i,r&&r.symbolInnerColor),d.style.strokeNoScale=!0),e.getItemVisual(t,"liftZ")),m=this._z2,v=(null!=y?null==m&&(this._z2=d.z2,d.z2+=y):null!=m&&(d.z2=m,this._z2=null),r&&r.useNameLabel);uc(d,c,{labelFetcher:f,labelDataIndex:t,defaultText:function(t){return v?e.getName(t):tx(e,t)},inheritColor:i,defaultOpacity:g.opacity}),this._sizeX=n[0]/2,this._sizeY=n[1]/2;y=d.ensureState("emphasis"),y.style=o,d.ensureState("select").style=a,d.ensureState("blur").style=s,m=null==p||!0===p?Math.max(1.1,3/this._sizeY):isFinite(p)&&0<p?+p:1;y.scaleX=this._sizeX*m,y.scaleY=this._sizeY*m,this.setSymbolScale(1),Nl(this,l,u,h)},ix.prototype.setSymbolScale=function(t){this.scaleX=this.scaleY=t},ix.prototype.fadeOut=function(t,e,n){var i=this.childAt(0),r=D(this).dataIndex,o=n&&n.animation;this.silent=i.silent=!0,n&&n.fadeLabel?(n=i.getTextContent())&&Ph(n,{style:{opacity:0}},e,{dataIndex:r,removeOpt:o,cb:function(){i.removeTextContent()}}):i.removeTextContent(),Ph(i,{style:{opacity:0},scaleX:0,scaleY:0},e,{dataIndex:r,cb:t,removeOpt:o})},ix.getSymbolSize=function(t,e){return[(t=F(t=t.getItemVisual(e,"symbolSize"))?t:[+t,+t])[0]||0,t[1]||0]};var ex,nx=ix;function ix(t,e,n,i){var r=ex.call(this)||this;return r.updateData(t,e,n,i),r}function rx(t,e){this.parent.drift(t,e)}function ox(t,e,n,i){return e&&!isNaN(e[0])&&!isNaN(e[1])&&(!i.isIgnore||!i.isIgnore(n))&&(!i.clipShape||i.clipShape.contain(e[0],e[1]))&&"none"!==t.getItemVisual(n,"symbol")}function ax(t){return(t=null==t||R(t)?t:{isIgnore:t})||{}}function sx(t){var t=t.hostModel,e=t.getModel("emphasis");return{emphasisItemStyle:e.getModel("itemStyle").getItemStyle(),blurItemStyle:t.getModel(["blur","itemStyle"]).getItemStyle(),selectItemStyle:t.getModel(["select","itemStyle"]).getItemStyle(),focus:e.get("focus"),blurScope:e.get("blurScope"),emphasisDisabled:e.get("disabled"),hoverScale:e.get("scale"),labelStatesModels:hc(t),cursorStyle:t.get("cursor")}}ux.prototype.updateData=function(o,a){this._progressiveEls=null,a=ax(a);var s=this.group,l=o.hostModel,u=this._data,h=this._SymbolCtor,c=a.disableAnimation,p=sx(o),d={disableAnimation:c},f=a.getSymbolPoint||function(t){return o.getItemLayout(t)};u||s.removeAll(),o.diff(u).add(function(t){var e,n=f(t);ox(o,n,t,a)&&((e=new h(o,t,p,d)).setPosition(n),o.setItemGraphicEl(t,e),s.add(e))}).update(function(t,e){var n,i,e=u.getItemGraphicEl(e),r=f(t);ox(o,r,t,a)?(n=o.getItemVisual(t,"symbol")||"circle",i=e&&e.getSymbolType&&e.getSymbolType(),!e||i&&i!==n?(s.remove(e),(e=new h(o,t,p,d)).setPosition(r)):(e.updateData(o,t,p,d),i={x:r[0],y:r[1]},c?e.attr(i):kh(e,i,l)),s.add(e),o.setItemGraphicEl(t,e)):s.remove(e)}).remove(function(t){var e=u.getItemGraphicEl(t);e&&e.fadeOut(function(){s.remove(e)},l)}).execute(),this._getSymbolPoint=f,this._data=o},ux.prototype.updateLayout=function(){var n=this,t=this._data;t&&t.eachItemGraphicEl(function(t,e){e=n._getSymbolPoint(e);t.setPosition(e),t.markRedraw()})},ux.prototype.incrementalPrepareUpdate=function(t){this._seriesScope=sx(t),this._data=null,this.group.removeAll()},ux.prototype.incrementalUpdate=function(t,e,n){function i(t){t.isGroup||(t.incremental=!0,t.ensureState("emphasis").hoverLayer=!0)}this._progressiveEls=[],n=ax(n);for(var r=t.start;r<t.end;r++){var o,a=e.getItemLayout(r);ox(e,a,r,n)&&((o=new this._SymbolCtor(e,r,this._seriesScope)).traverse(i),o.setPosition(a),this.group.add(o),e.setItemGraphicEl(r,o),this._progressiveEls.push(o))}},ux.prototype.eachRendered=function(t){rc(this._progressiveEls||this.group,t)},ux.prototype.remove=function(t){var e=this.group,n=this._data;n&&t?n.eachItemGraphicEl(function(t){t.fadeOut(function(){e.remove(t)},n.hostModel)}):e.removeAll()};var lx=ux;function ux(t){this.group=new Hr,this._SymbolCtor=t||nx}function hx(t,e,n){var i=t.getBaseAxis(),r=t.getOtherAxis(i),n=function(t,e){var n=0,t=t.scale.getExtent();"start"===e?n=t[0]:"end"===e?n=t[1]:H(e)&&!isNaN(e)?n=e:0<t[0]?n=t[0]:t[1]<0&&(n=t[1]);return n}(r,n),i=i.dim,r=r.dim,o=e.mapDimension(r),a=e.mapDimension(i),s="x"===r||"radius"===r?1:0,t=B(t.dimensions,function(t){return e.mapDimension(t)}),l=!1,u=e.getCalculationInfo("stackResultDimension");return xv(e,t[0])&&(l=!0,t[0]=u),xv(e,t[1])&&(l=!0,t[1]=u),{dataDimsForPoint:t,valueStart:n,valueAxisDim:r,baseAxisDim:i,stacked:!!l,valueDim:o,baseDim:a,baseDataOffset:s,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function cx(t,e,n,i){var r=NaN,o=(t.stacked&&(r=n.get(n.getCalculationInfo("stackedOverDimension"),i)),isNaN(r)&&(r=t.valueStart),t.baseDataOffset),a=[];return a[o]=n.get(t.baseDim,i),a[1-o]=r,e.dataToPoint(a)}var px=Math.min,dx=Math.max;function fx(t,e){return isNaN(t)||isNaN(e)}function gx(t,e,n,i,r,o,a,s,l){for(var u,h,c,p,d=n,f=0;f<i;f++){var g=e[2*d],y=e[2*d+1];if(r<=d||d<0)break;if(fx(g,y)){if(l){d+=o;continue}break}if(d===n)t[0<o?"moveTo":"lineTo"](g,y),c=g,p=y;else{var m=g-u,v=y-h;if(m*m+v*v<.5){d+=o;continue}if(0<a){for(var _=d+o,x=e[2*_],w=e[2*_+1];x===g&&w===y&&f<i;)f++,d+=o,x=e[2*(_+=o)],w=e[2*_+1],g=e[2*d],y=e[2*d+1];var b=f+1;if(l)for(;fx(x,w)&&b<i;)b++,x=e[2*(_+=o)],w=e[2*_+1];var S,M,T,C,I,k,D,A,P,m=0,v=0,L=void 0,O=void 0;i<=b||fx(x,w)?(D=g,A=y):(m=x-u,v=w-h,S=g-u,M=x-g,T=y-h,C=w-y,k=I=void 0,O="x"===s?(D=g-(P=0<m?1:-1)*(I=Math.abs(S))*a,A=y,L=g+P*(k=Math.abs(M))*a,y):"y"===s?(A=y-(P=0<v?1:-1)*(I=Math.abs(T))*a,L=D=g,y+P*(k=Math.abs(C))*a):(I=Math.sqrt(S*S+T*T),D=g-m*a*(1-(S=(k=Math.sqrt(M*M+C*C))/(k+I))),A=y-v*a*(1-S),O=y+v*a*S,L=px(L=g+m*a*S,dx(x,g)),O=px(O,dx(w,y)),L=dx(L,px(x,g)),A=y-(v=(O=dx(O,px(w,y)))-y)*I/k,D=px(D=g-(m=L-g)*I/k,dx(u,g)),A=px(A,dx(h,y)),L=g+(m=g-(D=dx(D,px(u,g))))*k/I,y+(v=y-(A=dx(A,px(h,y))))*k/I)),t.bezierCurveTo(c,p,D,A,g,y),c=L,p=O}else t.lineTo(g,y)}u=g,h=y,d+=o}return f}var yx,mx=function(){this.smooth=0,this.smoothConstraint=!0},vx=(u(_x,yx=ds),_x.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},_x.prototype.getDefaultShape=function(){return new mx},_x.prototype.buildPath=function(t,e){var n=e.points,i=0,r=n.length/2;if(e.connectNulls){for(;0<r&&fx(n[2*r-2],n[2*r-1]);r--);for(;i<r&&fx(n[2*i],n[2*i+1]);i++);}for(;i<r;)i+=gx(t,n,i,r,r,1,e.smooth,e.smoothMonotone,e.connectNulls)+1},_x.prototype.getPointOn=function(t,e){this.path||(this.createPathProxy(),this.buildPath(this.path,this.shape));for(var n,i,r=this.path.data,o=ja.CMD,a="x"===e,s=[],l=0;l<r.length;){var u=void 0,h=void 0;switch(r[l++]){case o.M:n=r[l++],i=r[l++];break;case o.L:var c,u=r[l++],h=r[l++];if((c=a?(t-n)/(u-n):(t-i)/(h-i))<=1&&0<=c)return v=a?(h-i)*c+i:(u-n)*c+n,a?[t,v]:[v,t];n=u,i=h;break;case o.C:u=r[l++],h=r[l++];var p=r[l++],d=r[l++],f=r[l++],g=r[l++],y=a?Fn(n,u,p,f,t,s):Fn(i,h,d,g,t,s);if(0<y)for(var m=0;m<y;m++){var v,_=s[m];if(_<=1&&0<=_)return v=a?zn(i,h,d,g,_):zn(n,u,p,f,_),a?[t,v]:[v,t]}n=f,i=g}}},_x);function _x(t){t=yx.call(this,t)||this;return t.type="ec-polyline",t}u(Sx,xx=mx);var xx,bx=Sx;function Sx(){return null!==xx&&xx.apply(this,arguments)||this}u(Cx,Mx=ds),Cx.prototype.getDefaultShape=function(){return new bx},Cx.prototype.buildPath=function(t,e){var n=e.points,i=e.stackedOnPoints,r=0,o=n.length/2,a=e.smoothMonotone;if(e.connectNulls){for(;0<o&&fx(n[2*o-2],n[2*o-1]);o--);for(;r<o&&fx(n[2*r],n[2*r+1]);r++);}for(;r<o;){var s=gx(t,n,r,o,o,1,e.smooth,a,e.connectNulls);gx(t,i,r+s-1,s,o,-1,e.stackedOnSmooth,a,e.connectNulls),r+=s+1,t.closePath()}};var Mx,Tx=Cx;function Cx(t){t=Mx.call(this,t)||this;return t.type="ec-polygon",t}function Ix(t,e){if(t.length===e.length){for(var n=0;n<t.length;n++)if(t[n]!==e[n])return;return 1}}function kx(t){for(var e=1/0,n=1/0,i=-1/0,r=-1/0,o=0;o<t.length;){var a=t[o++],s=t[o++];isNaN(a)||(e=Math.min(a,e),i=Math.max(a,i)),isNaN(s)||(n=Math.min(s,n),r=Math.max(s,r))}return[[e,n],[i,r]]}function Dx(t,e){var t=kx(t),n=t[0],t=t[1],e=kx(e),i=e[0],e=e[1];return Math.max(Math.abs(n[0]-i[0]),Math.abs(n[1]-i[1]),Math.abs(t[0]-e[0]),Math.abs(t[1]-e[1]))}function Ax(t){return H(t)?t:t?.5:0}function Px(t,e,n,i,r){var n=n.getBaseAxis(),o="x"===n.dim||"radius"===n.dim?0:1,a=[],s=0,l=[],u=[],h=[],c=[];if(r){for(s=0;s<t.length;s+=2){var p=e||t;isNaN(p[s])||isNaN(p[s+1])||c.push(t[s],t[s+1])}t=c}for(s=0;s<t.length-2;s+=2)switch(h[0]=t[s+2],h[1]=t[s+3],u[0]=t[s],u[1]=t[s+1],a.push(u[0],u[1]),i){case"end":l[o]=h[o],l[1-o]=u[1-o],a.push(l[0],l[1]);break;case"middle":var d=[];l[o]=d[o]=(u[o]+h[o])/2,l[1-o]=u[1-o],d[1-o]=h[1-o],a.push(l[0],l[1]),a.push(d[0],d[1]);break;default:l[o]=u[o],l[1-o]=h[1-o],a.push(l[0],l[1])}return a.push(t[s++],t[s++]),a}function Lx(t,e,n){var i=t.getVisual("visualMeta");if(i&&i.length&&t.count()&&"cartesian2d"===e.type){for(var r,o=i.length-1;0<=o;o--){var a,s=t.getDimensionInfo(i[o].dimension);if("x"===(a=s&&s.coordDim)||"y"===a){r=i[o];break}}if(r){var l=e.getAxis(a),e=B(r.stops,function(t){return{coord:l.toGlobalCoord(l.dataToCoord(t.value)),color:t.color}}),u=e.length,h=r.outerColors.slice(),n=(u&&e[0].coord>e[u-1].coord&&(e.reverse(),h.reverse()),function(t,e){var n,i,r=[],o=t.length;function a(t,e,n){var i=t.coord;return{coord:n,color:xi((n-i)/(e.coord-i),[t.color,e.color])}}for(var s=0;s<o;s++){var l=t[s],u=l.coord;if(u<0)n=l;else{if(e<u){i?r.push(a(i,l,e)):n&&r.push(a(n,l,0),a(n,l,e));break}n&&(r.push(a(n,l,0)),n=null),r.push(l),i=l}}return r}(e,"x"===a?n.getWidth():n.getHeight())),c=n.length;if(!c&&u)return e[0].coord<0?h[1]||e[u-1].color:h[0]||e[0].color;var p=n[0].coord-10,u=n[c-1].coord+10,d=u-p;if(d<.001)return"transparent";O(n,function(t){t.offset=(t.coord-p)/d}),n.push({offset:c?n[c-1].offset:.5,color:h[1]||"transparent"}),n.unshift({offset:c?n[0].offset:.5,color:h[0]||"transparent"});e=new dh(0,0,0,0,n,!0);return e[a]=p,e[a+"2"]=u,e}}}function Ox(t,e,n){var t=t.get("showAllSymbol"),i="auto"===t;if(!t||i){var r,o,a=n.getAxesByScale("ordinal")[0];if(a)if(!i||!function(t,e){for(var n=t.getExtent(),i=Math.abs(n[1]-n[0])/t.scale.count(),r=(isNaN(i)&&(i=0),e.count()),o=Math.max(1,Math.round(r/5)),a=0;a<r;a+=o)if(1.5*nx.getSymbolSize(e,a)[t.isHorizontal()?1:0]>i)return;return 1}(a,e))return r=e.mapDimension(a.dim),o={},O(a.getViewLabels(),function(t){t=a.scale.getRawOrdinalNumber(t.tickValue);o[t]=1}),function(t){return!o.hasOwnProperty(e.get(r,t))}}}function Rx(t){for(var e,n,i=t.length/2;0<i&&(e=t[2*i-2],n=t[2*i-1],isNaN(e)||isNaN(n));i--);return i-1}function Nx(t,e){return[t[2*e],t[2*e+1]]}function Ex(t){if(t.get(["endLabel","show"]))return 1;for(var e=0;e<tl.length;e++)if(t.get([tl[e],"endLabel","show"]))return 1}function zx(n,i,e,t){var r,o,a,s,l,u,h,c,p,d,f,g,y,m,v,_,x;return x="cartesian2d",i.type===x?(r=t.getModel("endLabel"),o=r.get("valueAnimation"),a=t.getData(),s={lastFrameIndex:0},x=Ex(t)?function(t,e){n._endLabelOnDuring(t,e,a,s,o,r,i)}:null,l=i.getBaseAxis().isHorizontal(),h=e,c=t,p=function(){var t=n._endLabel;t&&e&&null!=s.originalX&&t.attr({x:s.originalX,y:s.originalY})},d=x,f=(m=(u=i).getArea()).x,g=m.y,y=m.width,m=m.height,v=c.get(["lineStyle","width"])||0,f-=v/2,g-=v/2,y+=v,m+=v,y=Math.ceil(y),f!==Math.floor(f)&&(f=Math.floor(f),y++),_=new As({shape:{x:f,y:g,width:y,height:m}}),h&&(h=(v=u.getBaseAxis()).isHorizontal(),u=v.inverse,h?(u&&(_.shape.x+=y),_.shape.width=0):(u||(_.shape.y+=m),_.shape.height=0),v=k(d)?function(t){d(t,_)}:null,Dh(_,{shape:{width:y,height:m,x:f,y:g}},c,null,p,v)),h=_,t.get("clip",!0)||(u=h.shape,y=Math.max(u.width,u.height),l?(u.y-=y,u.height+=2*y):(u.x-=y,u.width+=2*y)),x&&x(1,h),h):(m=e,f=t,c=(g=i).getArea(),p=eo(c.r0,1),v=eo(c.r,1),l=new Pu({shape:{cx:eo(g.cx,1),cy:eo(g.cy,1),r0:p,r:v,startAngle:c.startAngle,endAngle:c.endAngle,clockwise:c.clockwise}}),m&&("angle"===g.getBaseAxis().dim?l.shape.endAngle=c.startAngle:l.shape.r=p,Dh(l,{shape:{endAngle:c.endAngle,r:v}},f)),l)}u(Vx,Bx=qg),Vx.prototype.init=function(){var t=new Hr,e=new lx;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t,this._changePolyState=ct(this._changePolyState,this)},Vx.prototype.render=function(t,e,n){var i,r=t.coordinateSystem,o=this.group,a=t.getData(),s=t.getModel("lineStyle"),l=t.getModel("areaStyle"),u=a.getLayout("points")||[],h="polar"===r.type,c=this._coordSys,p=this._symbolDraw,d=this._polyline,f=this._polygon,g=this._lineGroup,e=!e.ssr&&t.get("animation"),y=!l.isEmpty(),m=l.get("origin"),v=hx(r,a,m),v=y&&function(t,e,n){if(!n.valueDim)return[];for(var i=e.count(),r=Yv(2*i),o=0;o<i;o++){var a=cx(n,t,e,o);r[2*o]=a[0],r[2*o+1]=a[1]}return r}(r,a,v),_=t.get("showSymbol"),x=t.get("connectNulls"),w=_&&!h&&Ox(t,a,r),b=this._data,S=(b&&b.eachItemGraphicEl(function(t,e){t.__temp&&(o.remove(t),b.setItemGraphicEl(e,null))}),_||p.remove(),o.add(g),!h&&t.get("step")),M=(r&&r.getArea&&t.get("clip",!0)&&(null!=(i=r.getArea()).width?(i.x-=.1,i.y-=.1,i.width+=.2,i.height+=.2):i.r0&&(i.r0-=.5,i.r+=.5)),this._clipShapeForSymbol=i,Lx(a,r,n)||a.getVisual("style")[a.getVisual("drawType")]),c=(d&&c.type===r.type&&S===this._step?(y&&!f?f=this._newPolygon(u,v):f&&!y&&(g.remove(f),f=this._polygon=null),h||this._initOrUpdateEndLabel(t,r,Mp(M)),(c=g.getClipPath())?Dh(c,{shape:zx(this,r,!1,t).shape},t):g.setClipPath(zx(this,r,!0,t)),_&&p.updateData(a,{isIgnore:w,clipShape:i,disableAnimation:!0,getSymbolPoint:function(t){return[u[2*t],u[2*t+1]]}}),Ix(this._stackedOnPoints,v)&&Ix(this._points,u)||(e?this._doUpdateAnimation(a,v,r,n,S,m,x):(S&&(v=v&&Px(v,u,r,S,x),u=Px(u,null,r,S,x)),d.setShape({points:u}),f&&f.setShape({points:u,stackedOnPoints:v})))):(_&&p.updateData(a,{isIgnore:w,clipShape:i,disableAnimation:!0,getSymbolPoint:function(t){return[u[2*t],u[2*t+1]]}}),e&&this._initSymbolLabelAnimation(a,r,i),S&&(v=v&&Px(v,u,r,S,x),u=Px(u,null,r,S,x)),d=this._newPolyline(u),y?f=this._newPolygon(u,v):f&&(g.remove(f),f=this._polygon=null),h||this._initOrUpdateEndLabel(t,r,Mp(M)),g.setClipPath(zx(this,r,!0,t))),t.getModel("emphasis")),n=c.get("focus"),_=c.get("blurScope"),p=c.get("disabled"),w=(d.useStyle(z(s.getLineStyle(),{fill:"none",stroke:M,lineJoin:"bevel"})),Bl(d,t,"lineStyle"),0<d.style.lineWidth&&"bolder"===t.get(["emphasis","lineStyle","width"])&&(d.getState("emphasis").style.lineWidth=+d.style.lineWidth+1),D(d).seriesIndex=t.seriesIndex,Nl(d,n,_,p),Ax(t.get("smooth"))),e=t.get("smoothMonotone"),T=(d.setShape({smooth:w,smoothMonotone:e,connectNulls:x}),f&&(i=a.getCalculationInfo("stackedOnSeries"),y=0,f.useStyle(z(l.getAreaStyle(),{fill:M,opacity:.7,lineJoin:"bevel",decal:a.getVisual("style").decal})),i&&(y=Ax(i.get("smooth"))),f.setShape({smooth:w,stackedOnSmooth:y,smoothMonotone:e,connectNulls:x}),Bl(f,t,"areaStyle"),D(f).seriesIndex=t.seriesIndex,Nl(f,n,_,p)),this._changePolyState);a.eachItemGraphicEl(function(t){t&&(t.onHoverStateChange=T)}),this._polyline.onHoverStateChange=T,this._data=a,this._coordSys=r,this._stackedOnPoints=v,this._points=u,this._step=S,this._valueOrigin=m,t.get("triggerLineEvent")&&(this.packEventData(t,d),f)&&this.packEventData(t,f)},Vx.prototype.packEventData=function(t,e){D(e).eventData={componentType:"series",componentSubType:"line",componentIndex:t.componentIndex,seriesIndex:t.seriesIndex,seriesName:t.name,seriesType:"line"}},Vx.prototype.highlight=function(t,e,n,i){var r=t.getData(),o=Ao(r,i);if(this._changePolyState("emphasis"),!(o instanceof Array)&&null!=o&&0<=o){var a=r.getLayout("points");if(!(l=r.getItemGraphicEl(o))){var s=a[2*o],a=a[2*o+1];if(isNaN(s)||isNaN(a))return;if(this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(s,a))return;var l,u=t.get("zlevel")||0,h=t.get("z")||0,s=((l=new nx(r,o)).x=s,l.y=a,l.setZ(u,h),l.getSymbolPath().getTextContent());s&&(s.zlevel=u,s.z=h,s.z2=this._polyline.z2+1),l.__temp=!0,r.setItemGraphicEl(o,l),l.stopSymbolAnimation(!0),this.group.add(l)}l.highlight()}else qg.prototype.highlight.call(this,t,e,n,i)},Vx.prototype.downplay=function(t,e,n,i){var r,o=t.getData(),a=Ao(o,i);this._changePolyState("normal"),null!=a&&0<=a?(r=o.getItemGraphicEl(a))&&(r.__temp?(o.setItemGraphicEl(a,null),this.group.remove(r)):r.downplay()):qg.prototype.downplay.call(this,t,e,n,i)},Vx.prototype._changePolyState=function(t){var e=this._polygon;ml(this._polyline,t),e&&ml(e,t)},Vx.prototype._newPolyline=function(t){var e=this._polyline;return e&&this._lineGroup.remove(e),e=new vx({shape:{points:t},segmentIgnoreThreshold:2,z2:10}),this._lineGroup.add(e),this._polyline=e},Vx.prototype._newPolygon=function(t,e){var n=this._polygon;return n&&this._lineGroup.remove(n),n=new Tx({shape:{points:t,stackedOnPoints:e},segmentIgnoreThreshold:2}),this._lineGroup.add(n),this._polygon=n},Vx.prototype._initSymbolLabelAnimation=function(t,l,u){var h,c,e=l.getBaseAxis(),p=e.inverse,e=("cartesian2d"===l.type?(h=e.isHorizontal(),c=!1):"polar"===l.type&&(h="angle"===e.dim,c=!0),t.hostModel),d=e.get("animationDuration"),f=(k(d)&&(d=d(null)),e.get("animationDelay")||0),g=k(f)?f(null):f;t.eachItemGraphicEl(function(t,e){var n,i,r,o,a,s=t;s&&(o=[t.x,t.y],a=i=n=void 0,u&&(a=c?(r=u,o=l.pointToCoord(o),h?(n=r.startAngle,i=r.endAngle,-o[1]/180*Math.PI):(n=r.r0,i=r.r,o[0])):h?(n=u.x,i=u.x+u.width,t.x):(n=u.y+u.height,i=u.y,t.y)),r=i===n?0:(a-n)/(i-n),p&&(r=1-r),o=k(f)?f(e):d*r+g,a=(t=s.getSymbolPath()).getTextContent(),s.attr({scaleX:0,scaleY:0}),s.animateTo({scaleX:1,scaleY:1},{duration:200,setToFinal:!0,delay:o}),a&&a.animateFrom({style:{opacity:0}},{duration:300,delay:o}),t.disableLabelAnimation=!0)})},Vx.prototype._initOrUpdateEndLabel=function(t,e,n){var u,i,r,o=t.getModel("endLabel");Ex(t)?(u=t.getData(),i=this._polyline,(r=u.getLayout("points"))?(this._endLabel||((this._endLabel=new Ns({z2:200})).ignoreClip=!0,i.setTextContent(this._endLabel),i.disableLabelAnimation=!0),0<=(r=Rx(r))&&(uc(i,hc(t,"endLabel"),{inheritColor:n,labelFetcher:t,labelDataIndex:r,defaultText:function(t,e,n){if(null==n)return tx(u,t);var i=u,r=n,o=i.mapDimensionsAll("defaultedLabel");if(!F(r))return r+"";for(var a=[],s=0;s<o.length;s++){var l=i.getDimensionIndex(o[s]);0<=l&&a.push(r[l])}return a.join(" ")},enableTextSetter:!0},(n=o,r=(t=(t=e).getBaseAxis()).isHorizontal(),t=t.inverse,o=r?t?"right":"left":"center",r=r?"middle":t?"top":"bottom",{normal:{align:n.get("align")||o,verticalAlign:n.get("verticalAlign")||r}})),i.textConfig.position=null)):(i.removeTextContent(),this._endLabel=null)):this._endLabel&&(this._polyline.removeTextContent(),this._endLabel=null)},Vx.prototype._endLabelOnDuring=function(t,e,n,i,r,o,a){var s,l,u,h,c,p,d,f,g,y,m=this._endLabel,v=this._polyline;m&&(t<1&&null==i.originalX&&(i.originalX=m.x,i.originalY=m.y),s=n.getLayout("points"),g=(l=n.hostModel).get("connectNulls"),u=o.get("precision"),o=o.get("distance")||0,c=(a=a.getBaseAxis()).isHorizontal(),a=a.inverse,e=e.shape,h=(c?o:0)*(a?-1:1),o=(c?0:-o)*(a?-1:1),d=void 0,1<=(f=(p=(c=function(t,e,n){for(var i,r,o=t.length/2,a="x"===n?0:1,s=0,l=-1,u=0;u<o;u++)if(r=t[2*u+a],!isNaN(r)&&!isNaN(t[2*u+1-a])){if(0!==u){if(i<=e&&e<=r||e<=i&&r<=e){l=u;break}s=u}i=r}return{range:[s,l],t:(e-i)/(r-i)}}(s,a=a?c?e.x:e.y+e.height:c?e.x+e.width:e.y,e=c?"x":"y")).range)[1]-p[0])?(1<f&&!g?(y=Nx(s,p[0]),m.attr({x:y[0]+h,y:y[1]+o}),r&&(d=l.getRawValue(p[0]))):((y=v.getPointOn(a,e))&&m.attr({x:y[0]+h,y:y[1]+o}),f=l.getRawValue(p[0]),g=l.getRawValue(p[1]),r&&(d=Bo(n,u,f,g,c.t))),i.lastFrameIndex=p[0]):(y=Nx(s,v=1===t||0<i.lastFrameIndex?p[0]:0),r&&(d=l.getRawValue(v)),m.attr({x:y[0]+h,y:y[1]+o})),r)&&"function"==typeof(a=mc(m)).setLabelText&&a.setLabelText(d)},Vx.prototype._doUpdateAnimation=function(t,e,n,i,r,o,a){var s=this._polyline,l=this._polygon,u=t.hostModel,e=function(t,e,n,i,r,o){a=[],e.diff(t).add(function(t){a.push({cmd:"+",idx:t})}).update(function(t,e){a.push({cmd:"=",idx:e,idx1:t})}).remove(function(t){a.push({cmd:"-",idx:t})}).execute();for(var a,s=a,l=[],u=[],h=[],c=[],p=[],d=[],f=[],g=hx(r,e,o),y=t.getLayout("points")||[],m=e.getLayout("points")||[],v=0;v<s.length;v++){var _=s[v],x=!0,w=void 0;switch(_.cmd){case"=":var b=2*_.idx,w=2*_.idx1,S=y[b],M=y[1+b],T=m[w],C=m[w+1];(isNaN(S)||isNaN(M))&&(S=T,M=C),l.push(S,M),u.push(T,C),h.push(n[b],n[1+b]),c.push(i[w],i[w+1]),f.push(e.getRawIndex(_.idx1));break;case"+":S=_.idx,M=g.dataDimsForPoint,T=r.dataToPoint([e.get(M[0],S),e.get(M[1],S)]),C=(w=2*S,l.push(T[0],T[1]),u.push(m[w],m[w+1]),cx(g,r,e,S));h.push(C[0],C[1]),c.push(i[w],i[w+1]),f.push(e.getRawIndex(S));break;case"-":x=!1}x&&(p.push(_),d.push(d.length))}d.sort(function(t,e){return f[t]-f[e]});for(var I=Yv(o=l.length),k=Yv(o),D=Yv(o),A=Yv(o),P=[],v=0;v<d.length;v++){var L=d[v],O=2*v,R=2*L;I[O]=l[R],I[1+O]=l[1+R],k[O]=u[R],k[1+O]=u[1+R],D[O]=h[R],D[1+O]=h[1+R],A[O]=c[R],A[1+O]=c[1+R],P[v]=p[L]}return{current:I,next:k,stackedOnCurrent:D,stackedOnNext:A,status:P}}(this._data,t,this._stackedOnPoints,e,this._coordSys,this._valueOrigin),h=e.current,c=e.stackedOnCurrent,p=e.next,d=e.stackedOnNext;if(r&&(c=Px(e.stackedOnCurrent,e.current,n,r,a),h=Px(e.current,null,n,r,a),d=Px(e.stackedOnNext,e.next,n,r,a),p=Px(e.next,null,n,r,a)),3e3<Dx(h,p)||l&&3e3<Dx(c,d))s.stopAnimation(),s.setShape({points:p}),l&&(l.stopAnimation(),l.setShape({points:p,stackedOnPoints:d}));else{s.shape.__points=e.current,s.shape.points=h;for(var f,n={shape:{points:p}},g=(e.current!==h&&(n.shape.__points=e.next),s.stopAnimation(),kh(s,n,u),l&&(l.setShape({points:h,stackedOnPoints:c}),l.stopAnimation(),kh(l,{shape:{stackedOnPoints:d}},u),s.shape.points!==l.shape.points)&&(l.shape.points=s.shape.points),[]),y=e.status,m=0;m<y.length;m++)"="===y[m].cmd&&(f=t.getItemGraphicEl(y[m].idx1))&&g.push({el:f,ptIdx:m});s.animators&&s.animators.length&&s.animators[0].during(function(){l&&l.dirtyShape();for(var t=s.shape.__points,e=0;e<g.length;e++){var n=g[e].el,i=2*g[e].ptIdx;n.x=t[i],n.y=t[1+i],n.markRedraw()}})}},Vx.prototype.remove=function(t){var n=this.group,i=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),i&&i.eachItemGraphicEl(function(t,e){t.__temp&&(n.remove(t),i.setItemGraphicEl(e,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._endLabel=this._data=null},Vx.type="line";var Bx,Fx=Vx;function Vx(){return null!==Bx&&Bx.apply(this,arguments)||this}var Hx={average:function(t){for(var e=0,n=0,i=0;i<t.length;i++)isNaN(t[i])||(e+=t[i],n++);return 0===n?NaN:e/n},sum:function(t){for(var e=0,n=0;n<t.length;n++)e+=t[n]||0;return e},max:function(t){for(var e=-1/0,n=0;n<t.length;n++)t[n]>e&&(e=t[n]);return isFinite(e)?e:NaN},min:function(t){for(var e=1/0,n=0;n<t.length;n++)t[n]<e&&(e=t[n]);return isFinite(e)?e:NaN},nearest:function(t){return t[0]}},Gx=function(t){return Math.round(t.length/2)};A_(function(t){var i;t.registerChartView(Fx),t.registerSeriesModel(Q1),t.registerLayout((i=!0,{seriesType:"line",plan:Ug(),reset:function(t){var h,e,c,p,d,n=t.getData(),f=t.coordinateSystem,t=t.pipelineContext,g=i||t.large;if(f)return t=B(f.dimensions,function(t){return n.mapDimension(t)}).slice(0,2),h=t.length,e=n.getCalculationInfo("stackResultDimension"),xv(n,t[0])&&(t[0]=e),xv(n,t[1])&&(t[1]=e),c=n.getStore(),p=n.getDimensionIndex(t[0]),d=n.getDimensionIndex(t[1]),h&&{progress:function(t,e){for(var n=t.end-t.start,i=g&&Yv(n*h),r=[],o=[],a=t.start,s=0;a<t.end;a++){var l,u=void 0;u=1===h?(l=c.get(p,a),f.dataToPoint(l,null,o)):(r[0]=c.get(p,a),r[1]=c.get(d,a),f.dataToPoint(r,null,o)),g?(i[s++]=u[0],i[s++]=u[1]):e.setItemLayout(a,u.slice())}g&&e.setLayout("points",i)}}}})),t.registerVisual({seriesType:"line",reset:function(t){var e=t.getData(),t=t.getModel("lineStyle").getLineStyle();t&&!t.stroke&&(t.stroke=e.getVisual("style").fill),e.setVisual("legendLineStyle",t)}}),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,{seriesType:"line",reset:function(t,e,n){var i,r=t.getData(),o=t.get("sampling"),a=t.coordinateSystem,s=r.count();10<s&&"cartesian2d"===a.type&&o&&(i=a.getBaseAxis(),a=a.getOtherAxis(i),i=i.getExtent(),n=n.getDevicePixelRatio(),i=Math.abs(i[1]-i[0])*(n||1),n=Math.round(s/i),isFinite(n))&&1<n&&("lttb"===o?t.setData(r.lttbDownSample(r.mapDimension(a.dim),1/n)):"minmax"===o&&t.setData(r.minmaxDownSample(r.mapDimension(a.dim),1/n)),s=void 0,V(o)?s=Hx[o]:k(o)&&(s=o),s)&&t.setData(r.downSample(r.mapDimension(a.dim),1/n,s,Gx))}})});u(Xx,Wx=g),Xx.type="grid",Xx.dependencies=["xAxis","yAxis"],Xx.layoutMode="box",Xx.defaultOption={show:!1,z:0,left:"10%",top:60,right:"10%",bottom:70,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"};var Wx,Ux=Xx;function Xx(){return null!==Wx&&Wx.apply(this,arguments)||this}u(Zx,Yx=g),Zx.prototype.getCoordSysModel=function(){return this.getReferringComponents("grid",No).models[0]},Zx.type="cartesian2dAxis";var Yx,qx=Zx;function Zx(){return null!==Yx&&Yx.apply(this,arguments)||this}at(qx,C_);var Tc={show:!0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#6E7079",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,showMinLine:!0,showMaxLine:!0,lineStyle:{color:["#E0E6F1"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},Xo=d({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},Tc),By=d({boundaryGap:[0,0],axisLine:{show:"auto"},axisTick:{show:"auto"},splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#F4F7FD",width:1}}},Tc),jx={category:Xo,value:By,time:d({splitNumber:6,axisLabel:{showMinLabel:!1,showMaxLabel:!1,rich:{primary:{fontWeight:"bold"}}},splitLine:{show:!1}},By),log:z({logBase:10},By)},Kx={value:1,category:1,time:1,log:1};function $x(o,a,s,l){O(Kx,function(t,r){var e,n=d(d({},jx[r],!0),l,!0),n=(u(i,e=s),i.prototype.mergeDefaultAndTheme=function(t,e){var n=Lp(this),i=n?Rp(t):{};d(t,e.getTheme().get(r+"Axis")),d(t,this.getDefaultOption()),t.type=Qx(t),n&&Op(t,i,n)},i.prototype.optionUpdated=function(){"category"===this.option.type&&(this.__ordinalMeta=Cv.createByAxisModel(this))},i.prototype.getCategories=function(t){var e=this.option;if("category"===e.type)return t?e.data:this.__ordinalMeta.categories},i.prototype.getOrdinalMeta=function(){return this.__ordinalMeta},i.type=a+"Axis."+r,i.defaultOption=n,i);function i(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=a+"Axis."+r,t}o.registerComponentModel(n)}),o.registerSubTypeDefaulter(a+"Axis",Qx)}function Qx(t){return t.type||(t.data?"category":"value")}function Jx(t){this.type="cartesian",this._dimList=[],this._axes={},this.name=t||""}Jx.prototype.getAxis=function(t){return this._axes[t]},Jx.prototype.getAxes=function(){return B(this._dimList,function(t){return this._axes[t]},this)},Jx.prototype.getAxesByScale=function(e){return e=e.toLowerCase(),ut(this.getAxes(),function(t){return t.scale.type===e})},Jx.prototype.addAxis=function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)};var tw=["x","y"];function ew(t){return"interval"===t.type||"time"===t.type}u(rw,nw=Jx),rw.prototype.calcAffineTransform=function(){this._transform=this._invTransform=null;var t,e,n,i,r=this.getAxis("x").scale,o=this.getAxis("y").scale;ew(r)&&ew(o)&&(r=r.getExtent(),o=o.getExtent(),i=this.dataToPoint([r[0],o[0]]),e=this.dataToPoint([r[1],o[1]]),t=r[1]-r[0],n=o[1]-o[0],t)&&n&&(t=(e[0]-i[0])/t,e=(e[1]-i[1])/n,n=i[0]-r[0]*t,r=i[1]-o[0]*e,i=this._transform=[t,0,0,e,n,r],this._invTransform=Fe([],i))},rw.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},rw.prototype.containPoint=function(t){var e=this.getAxis("x"),n=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&n.contain(n.toLocalCoord(t[1]))},rw.prototype.containData=function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},rw.prototype.containZone=function(t,e){var t=this.dataToPoint(t),e=this.dataToPoint(e),n=this.getArea(),e=new X(t[0],t[1],e[0]-t[0],e[1]-t[1]);return n.intersect(e)},rw.prototype.dataToPoint=function(t,e,n){n=n||[];var i,r=t[0],o=t[1];return this._transform&&null!=r&&isFinite(r)&&null!=o&&isFinite(o)?ee(n,t,this._transform):(t=this.getAxis("x"),i=this.getAxis("y"),n[0]=t.toGlobalCoord(t.dataToCoord(r,e)),n[1]=i.toGlobalCoord(i.dataToCoord(o,e)),n)},rw.prototype.clampData=function(t,e){var n=this.getAxis("x").scale,i=this.getAxis("y").scale,r=n.getExtent(),o=i.getExtent(),n=n.parse(t[0]),i=i.parse(t[1]);return(e=e||[])[0]=Math.min(Math.max(Math.min(r[0],r[1]),n),Math.max(r[0],r[1])),e[1]=Math.min(Math.max(Math.min(o[0],o[1]),i),Math.max(o[0],o[1])),e},rw.prototype.pointToData=function(t,e){var n,i,r=[];return this._invTransform?ee(r,t,this._invTransform):(n=this.getAxis("x"),i=this.getAxis("y"),r[0]=n.coordToData(n.toLocalCoord(t[0]),e),r[1]=i.coordToData(i.toLocalCoord(t[1]),e),r)},rw.prototype.getOtherAxis=function(t){return this.getAxis("x"===t.dim?"y":"x")},rw.prototype.getArea=function(t){t=t||0;var e=this.getAxis("x").getGlobalExtent(),n=this.getAxis("y").getGlobalExtent(),i=Math.min(e[0],e[1])-t,r=Math.min(n[0],n[1])-t,e=Math.max(e[0],e[1])-i+t,n=Math.max(n[0],n[1])-r+t;return new X(i,r,e,n)};var nw,iw=rw;function rw(){var t=null!==nw&&nw.apply(this,arguments)||this;return t.type="cartesian2d",t.dimensions=tw,t}u(sw,ow=Dc),sw.prototype.isHorizontal=function(){var t=this.position;return"top"===t||"bottom"===t},sw.prototype.getGlobalExtent=function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},sw.prototype.pointToData=function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},sw.prototype.setCategorySortInfo=function(t){if("category"!==this.type)return!1;this.model.option.categorySortInfo=t,this.scale.setSortInfo(t)};var ow,aw=sw;function sw(t,e,n,i,r){t=ow.call(this,t,e,n)||this;return t.index=0,t.type=i||"value",t.position=r||"bottom",t}function lw(t,e,n){n=n||{};var t=t.coordinateSystem,i=e.axis,r={},o=i.getAxesOnZeroOf()[0],a=i.position,s=o?"onZero":a,i=i.dim,t=t.getRect(),t=[t.x,t.x+t.width,t.y,t.y+t.height],l={left:0,right:1,top:0,bottom:1,onZero:2},u=e.get("offset")||0,u="x"===i?[t[2]-u,t[3]+u]:[t[0]-u,t[1]+u],h=(o&&(h=o.toGlobalCoord(o.dataToCoord(0)),u[l.onZero]=Math.max(Math.min(h,u[1]),u[0])),r.position=["y"===i?u[l[s]]:t[0],"x"===i?u[l[s]]:t[3]],r.rotation=Math.PI/2*("x"===i?0:1),r.labelDirection=r.tickDirection=r.nameDirection={top:-1,bottom:1,left:-1,right:1}[a],r.labelOffset=o?u[l[a]]-u[l.onZero]:0,e.get(["axisTick","inside"])&&(r.tickDirection=-r.tickDirection),wt(n.labelInside,e.get(["axisLabel","inside"]))&&(r.labelDirection=-r.labelDirection),e.get(["axisLabel","rotate"]));return r.labelRotate="top"===s?-h:h,r.z2=1,r}function uw(t){return"cartesian2d"===t.get("coordinateSystem")}function hw(i){var r={xAxisModel:null,yAxisModel:null};return O(r,function(t,e){var n=e.replace(/Model$/,""),n=i.getReferringComponents(n,No).models[0];r[e]=n}),r}var cw=Math.log;dw.prototype.getRect=function(){return this._rect},dw.prototype.update=function(t,e){var n=this._axesMap;function i(t){var d,e=ht(t),n=e.length;if(n){for(var i=[],r=n-1;0<=r;r--){var o=t[+e[r]],a=o.model,s=o.scale;Dv(s)&&a.get("alignTicks")&&null==a.get("interval")?i.push(o):(__(s,a),Dv(s)&&(d=o))}i.length&&(d||__((d=i.pop()).scale,d.model),O(i,function(t){var e=t.scale,t=t.model,n=d.scale,i=Gv.prototype,r=i.getTicks.call(n),o=i.getTicks.call(n,!0),a=r.length-1,n=i.getInterval.call(n),s=(t=v_(e,t)).extent,l=t.fixMin,t=t.fixMax,u=("log"===e.type&&(u=cw(e.base),s=[cw(s[0])/u,cw(s[1])/u]),e.setExtent(s[0],s[1]),e.calcNiceExtent({splitNumber:a,fixMin:l,fixMax:t}),i.getExtent.call(e)),h=(l&&(s[0]=u[0]),t&&(s[1]=u[1]),i.getInterval.call(e)),c=s[0],p=s[1];if(l&&t)h=(p-c)/a;else if(l)for(p=s[0]+h*a;p<s[1]&&isFinite(p)&&isFinite(s[1]);)h=Pv(h),p=s[0]+h*a;else if(t)for(c=s[1]-h*a;c>s[0]&&isFinite(c)&&isFinite(s[0]);)h=Pv(h),c=s[1]-h*a;else{u=(h=a<e.getTicks().length-1?Pv(h):h)*a;(c=eo((p=Math.ceil(s[1]/h)*h)-u))<0&&0<=s[0]?(c=0,p=eo(u)):0<p&&s[1]<=0&&(p=0,c=-eo(u))}l=(r[0].value-o[0].value)/n,t=(r[a].value-o[a].value)/n,i.setExtent.call(e,c+h*l,p+h*t),i.setInterval.call(e,h),(l||t)&&i.setNiceExtent.call(e,c+h,p-h)}))}}this._updateScale(t,this.model),i(n.x),i(n.y);var r={};O(n.x,function(t){gw(n,"y",t,r)}),O(n.y,function(t){gw(n,"x",t,r)}),this.resize(this.model,e)},dw.prototype.resize=function(t,e,n){var i=t.getBoxLayoutParams(),n=!n&&t.get("containLabel"),a=Pp(i,{width:e.getWidth(),height:e.getHeight()}),r=(this._rect=a,this._axesList);function o(){O(r,function(t){var e,n,i=t.isHorizontal(),r=i?[0,a.width]:[0,a.height],o=t.inverse?1:0;t.setExtent(r[o],r[1-o]),r=t,e=i?a.x:a.y,o=r.getExtent(),n=o[0]+o[1],r.toGlobalCoord="x"===r.dim?function(t){return t+e}:function(t){return n-t+e},r.toLocalCoord="x"===r.dim?function(t){return t-e}:function(t){return n-t+e}})}o(),n&&(O(r,function(t){var e,n,i;t.model.get(["axisLabel","inside"])||(e=S_(t))&&(n=t.isHorizontal()?"height":"width",i=t.model.get(["axisLabel","margin"]),a[n]-=e[n]+i,"top"===t.position?a.y+=e.height+i:"left"===t.position&&(a.x+=e.width+i))}),o()),O(this._coordsList,function(t){t.calcAffineTransform()})},dw.prototype.getAxis=function(t,e){t=this._axesMap[t];if(null!=t)return t[e||0]},dw.prototype.getAxes=function(){return this._axesList.slice()},dw.prototype.getCartesian=function(t,e){if(null!=t&&null!=e)return this._coordsMap["x"+t+"y"+e];R(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var n=0,i=this._coordsList;n<i.length;n++)if(i[n].getAxis("x").index===t||i[n].getAxis("y").index===e)return i[n]},dw.prototype.getCartesians=function(){return this._coordsList.slice()},dw.prototype.convertToPixel=function(t,e,n){e=this._findConvertTarget(e);return e.cartesian?e.cartesian.dataToPoint(n):e.axis?e.axis.toGlobalCoord(e.axis.dataToCoord(n)):null},dw.prototype.convertFromPixel=function(t,e,n){e=this._findConvertTarget(e);return e.cartesian?e.cartesian.pointToData(n):e.axis?e.axis.coordToData(e.axis.toLocalCoord(n)):null},dw.prototype._findConvertTarget=function(t){var e,n,i=t.seriesModel,r=t.xAxisModel||i&&i.getReferringComponents("xAxis",No).models[0],o=t.yAxisModel||i&&i.getReferringComponents("yAxis",No).models[0],t=t.gridModel,a=this._coordsList;return i?I(a,e=i.coordinateSystem)<0&&(e=null):r&&o?e=this.getCartesian(r.componentIndex,o.componentIndex):r?n=this.getAxis("x",r.componentIndex):o?n=this.getAxis("y",o.componentIndex):t&&t.coordinateSystem===this&&(e=this._coordsList[0]),{cartesian:e,axis:n}},dw.prototype.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},dw.prototype._initCartesian=function(o,t,e){var a=this,s=this,l={left:!1,right:!1,top:!1,bottom:!1},u={x:{},y:{}},h={x:0,y:0};function n(r){return function(t,e){var n,i;fw(t,o)&&(n=t.get("position"),"x"===r?"top"!==n&&"bottom"!==n&&(n=l.bottom?"top":"bottom"):"left"!==n&&"right"!==n&&(n=l.left?"right":"left"),l[n]=!0,i="category"===(n=new aw(r,x_(t),[0,0],t.get("type"),n)).type,n.onBand=i&&t.get("boundaryGap"),n.inverse=t.get("inverse"),(t.axis=n).model=t,n.grid=s,n.index=e,s._axesList.push(n),u[r][e]=n,h[r]++)}}t.eachComponent("xAxis",n("x"),this),t.eachComponent("yAxis",n("y"),this),h.x&&h.y?O((this._axesMap=u).x,function(i,r){O(u.y,function(t,e){var e="x"+r+"y"+e,n=new iw(e);n.master=a,n.model=o,a._coordsMap[e]=n,a._coordsList.push(n),n.addAxis(i),n.addAxis(t)})}):(this._axesMap={},this._axesList=[])},dw.prototype._updateScale=function(t,i){function r(e,n){var i,t,r;O((i=e,t=n.dim,r={},O(i.mapDimensionsAll(t),function(t){r[wv(i,t)]=!0}),ht(r)),function(t){n.scale.unionExtentFromData(e,t)})}O(this._axesList,function(t){var e;t.scale.setExtent(1/0,-1/0),"category"===t.type&&(e=t.model.get("categorySortInfo"),t.scale.setSortInfo(e))}),t.eachSeries(function(t){var e,n;uw(t)&&(n=(e=hw(t)).xAxisModel,e=e.yAxisModel,fw(n,i))&&fw(e,i)&&(n=this.getCartesian(n.componentIndex,e.componentIndex),e=t.getData(),t=n.getAxis("x"),n=n.getAxis("y"),r(e,t),r(e,n))},this)},dw.prototype.getTooltipAxes=function(n){var i=[],r=[];return O(this.getCartesians(),function(t){var e=null!=n&&"auto"!==n?t.getAxis(n):t.getBaseAxis(),t=t.getOtherAxis(e);I(i,e)<0&&i.push(e),I(r,t)<0&&r.push(t)}),{baseAxes:i,otherAxes:r}},dw.create=function(i,r){var o=[];return i.eachComponent("grid",function(t,e){var n=new dw(t,i,r);n.name="grid_"+e,n.resize(t,r,!0),t.coordinateSystem=n,o.push(n)}),i.eachSeries(function(t){var e,n,i;uw(t)&&(e=(n=hw(t)).xAxisModel,n=n.yAxisModel,i=e.getCoordSysModel().coordinateSystem,t.coordinateSystem=i.getCartesian(e.componentIndex,n.componentIndex))}),o},dw.dimensions=tw;var pw=dw;function dw(t,e,n){this.type="grid",this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this.axisPointerEnabled=!0,this.dimensions=tw,this._initCartesian(t,e,n),this.model=t}function fw(t,e){return t.getCoordSysModel()===e}function gw(t,e,n,i){n.getAxesOnZeroOf=function(){return r?[r]:[]};var r,o=t[e],t=n.model,e=t.get(["axisLine","onZero"]),n=t.get(["axisLine","onZeroAxisIndex"]);if(e){if(null!=n)yw(o[n])&&(r=o[n]);else for(var a in o)if(o.hasOwnProperty(a)&&yw(o[a])&&!i[s(o[a])]){r=o[a];break}r&&(i[s(r)]=!0)}function s(t){return t.dim+"_"+t.index}}function yw(t){return t&&"category"!==t.type&&"time"!==t.type&&(e=(t=(t=t).scale.getExtent())[0],t=t[1],!(0<e&&0<t||e<0&&t<0));var e}var mw=Math.PI,vw=(_w.prototype.hasBuilder=function(t){return!!xw[t]},_w.prototype.add=function(t){xw[t](this.opt,this.axisModel,this.group,this._transformGroup)},_w.prototype.getGroup=function(){return this.group},_w.innerTextLayout=function(t,e,n){var i,e=oo(e-t),t=ao(e)?(i=0<n?"top":"bottom","center"):ao(e-mw)?(i=0<n?"bottom":"top","center"):(i="middle",0<e&&e<mw?0<n?"right":"left":0<n?"left":"right");return{rotation:e,textAlign:t,textVerticalAlign:i}},_w.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},_w.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},_w);function _w(t,e){this.group=new Hr,this.opt=e,this.axisModel=t,z(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0,handleAutoShown:function(){return!0}});t=new Hr({x:e.position[0],y:e.position[1],rotation:e.rotation});t.updateTransform(),this._transformGroup=t}var xw={axisLine:function(i,t,r,e){var o,a,s,l,u,h,c,n=t.get(["axisLine","show"]);(n="auto"===n&&i.handleAutoShown?i.handleAutoShown("axisLine"):n)&&(n=t.axis.getExtent(),e=e.transform,o=[n[0],0],a=[n[1],0],s=a[0]<o[0],e&&(ee(o,o,e),ee(a,a,e)),l=L({lineCap:"round"},t.getModel(["axisLine","lineStyle"]).getLineStyle()),Yh((n=new ju({shape:{x1:o[0],y1:o[1],x2:a[0],y2:a[1]},style:l,strokeContainThreshold:i.strokeContainThreshold||5,silent:!0,z2:1})).shape,n.style.lineWidth),n.anid="line",r.add(n),null!=(u=t.get(["axisLine","symbol"])))&&(e=t.get(["axisLine","symbolSize"]),V(u)&&(u=[u,u]),(V(e)||H(e))&&(e=[e,e]),n=jy(t.get(["axisLine","symbolOffset"])||0,e),h=e[0],c=e[1],O([{rotate:i.rotation+Math.PI/2,offset:n[0],r:0},{rotate:i.rotation-Math.PI/2,offset:n[1],r:Math.sqrt((o[0]-a[0])*(o[0]-a[0])+(o[1]-a[1])*(o[1]-a[1]))}],function(t,e){var n;"none"!==u[e]&&null!=u[e]&&(e=Zy(u[e],-h/2,-c/2,h,c,l.stroke,!0),n=t.r+t.offset,e.attr({rotation:t.rotate,x:(t=s?a:o)[0]+n*Math.cos(i.rotation),y:t[1]-n*Math.sin(i.rotation),silent:!0,z2:11}),r.add(e))}))},axisTickLabel:function(t,e,n,i){var r,o,a,s,l,u=function(t,e,n,i){var r=n.axis,o=n.getModel("axisTick"),a=o.get("show");"auto"===a&&i.handleAutoShown&&(a=i.handleAutoShown("axisTick"));if(a&&!r.scale.isBlank()){for(var a=o.getModel("lineStyle"),i=i.tickDirection*o.get("length"),s=Mw(r.getTicksCoords(),e.transform,i,z(a.getLineStyle(),{stroke:n.get(["axisLine","lineStyle","color"])}),"ticks"),l=0;l<s.length;l++)t.add(s[l]);return s}}(n,i,e,t),h=function(g,y,m,v){var _,x,w,b,S,M,T,C,I=m.axis,t=wt(v.axisLabelShow,m.get(["axisLabel","show"]));if(t&&!I.scale.isBlank())return _=m.getModel("axisLabel"),x=_.get("margin"),w=I.getViewLabels(),t=(wt(v.labelRotate,_.get("rotate"))||0)*mw/180,b=vw.innerTextLayout(v.rotation,t,v.labelDirection),S=m.getCategories&&m.getCategories(!0),M=[],T=vw.isLabelSilent(m),C=m.get("triggerEvent"),O(w,function(t,e){var n="ordinal"===I.scale.type?I.scale.getRawOrdinalNumber(t.tickValue):t.tickValue,i=t.formattedLabel,r=t.rawLabel,o=_,a=(o=S&&S[n]&&R(a=S[n])&&a.textStyle?new Lc(a.textStyle,_,m.ecModel):o).getTextColor()||m.get(["axisLine","lineStyle","color"]),s=I.dataToCoord(n),l=o.getShallow("align",!0)||b.textAlign,u=N(o.getShallow("alignMinLabel",!0),l),h=N(o.getShallow("alignMaxLabel",!0),l),c=o.getShallow("verticalAlign",!0)||o.getShallow("baseline",!0)||b.textVerticalAlign,p=N(o.getShallow("verticalAlignMinLabel",!0),c),d=N(o.getShallow("verticalAlignMaxLabel",!0),c),f=new Ns({x:s,y:v.labelOffset+v.labelDirection*x,rotation:b.rotation,silent:T,z2:10+(t.level||0),style:cc(o,{text:i,align:0===e?u:e===w.length-1?h:l,verticalAlign:0===e?p:e===w.length-1?d:c,fill:k(a)?a("category"===I.type?r:"value"===I.type?n+"":n,e):a})});f.anid="label_"+n,nc({el:f,componentModel:m,itemName:i,formatterParamsExtra:{isTruncated:function(){return f.isTruncated},value:r,tickIndex:e}}),C&&((s=vw.makeAxisEventDataBase(m)).targetType="axisLabel",s.value=r,s.tickIndex=e,"category"===I.type&&(s.dataIndex=n),D(f).eventData=s),y.add(f),f.updateTransform(),M.push(f),g.add(f),f.decomposeTransform()}),M}(n,i,e,t),c=(o=h,u=u,T_((r=e).axis)||(d=r.get(["axisLabel","showMinLabel"]),r=r.get(["axisLabel","showMaxLabel"]),u=u||[],y=(o=o||[])[0],f=o[1],a=o[o.length-1],o=o[o.length-2],s=u[0],g=u[1],l=u[u.length-1],u=u[u.length-2],!1===d?(ww(y),ww(s)):bw(y,f)&&(d?(ww(f),ww(g)):(ww(y),ww(s))),!1===r?(ww(a),ww(l)):bw(o,a)&&(r?(ww(o),ww(u)):(ww(a),ww(l)))),n),p=i,d=e,f=t.tickDirection,g=d.axis,y=d.getModel("minorTick");if(y.get("show")&&!g.scale.isBlank()){var m=g.getMinorTicksCoords();if(m.length)for(var g=y.getModel("lineStyle"),v=f*y.get("length"),_=z(g.getLineStyle(),z(d.getModel("axisTick").getLineStyle(),{stroke:d.get(["axisLine","lineStyle","color"])})),x=0;x<m.length;x++)for(var w=Mw(m[x],p.transform,v,_,"minorticks_"+x),b=0;b<w.length;b++)c.add(w[b])}e.get(["axisLabel","hideOverlap"])&&C1(M1(B(h,function(t){return{label:t,priority:t.z2,defaultAttr:{ignore:t.ignore}}})))},axisName:function(t,e,n,i){var r,o,a,s,l,u,h,c,p=wt(t.axisName,e.get("name"));p&&(c=e.get("nameLocation"),l=t.nameDirection,r=e.getModel("nameTextStyle"),u=e.get("nameGap")||0,o=(h=e.axis.getExtent())[0]>h[1]?-1:1,o=["start"===c?h[0]-o*u:"end"===c?h[1]+o*u:(h[0]+h[1])/2,Sw(c)?t.labelOffset+l*u:0],null!=(u=e.get("nameRotate"))&&(u=u*mw/180),Sw(c)?a=vw.innerTextLayout(t.rotation,null!=u?u:t.rotation,l):(a=function(t,e,n,i){var r,n=oo(n-t),t=i[0]>i[1],i="start"===e&&!t||"start"!==e&&t;e=ao(n-mw/2)?(r=i?"bottom":"top","center"):ao(n-1.5*mw)?(r=i?"top":"bottom","center"):(r="middle",n<1.5*mw&&mw/2<n?i?"left":"right":i?"right":"left");return{rotation:n,textAlign:e,textVerticalAlign:r}}(t.rotation,c,u||0,h),null!=(s=t.axisNameAvailableWidth)&&(s=Math.abs(s/Math.sin(a.rotation)),isFinite(s)||(s=null))),l=r.getFont(),u=(c=e.get("nameTruncate",!0)||{}).ellipsis,h=wt(t.nameTruncateMaxWidth,c.maxWidth,s),nc({el:t=new Ns({x:o[0],y:o[1],rotation:a.rotation,silent:vw.isLabelSilent(e),style:cc(r,{text:p,font:l,overflow:"truncate",width:h,ellipsis:u,fill:r.getTextColor()||e.get(["axisLine","lineStyle","color"]),align:r.get("align")||a.textAlign,verticalAlign:r.get("verticalAlign")||a.textVerticalAlign}),z2:1}),componentModel:e,itemName:p}),t.__fullText=p,t.anid="name",e.get("triggerEvent")&&((c=vw.makeAxisEventDataBase(e)).targetType="axisName",c.name=p,D(t).eventData=c),i.add(t),t.updateTransform(),n.add(t),t.decomposeTransform())}};function ww(t){t&&(t.ignore=!0)}function bw(t,e){var n,i=t&&t.getBoundingRect().clone(),r=e&&e.getBoundingRect().clone();if(i&&r)return ze(n=Oe([]),n,-t.rotation),i.applyTransform(Ne([],n,t.getLocalTransform())),r.applyTransform(Ne([],n,e.getLocalTransform())),i.intersect(r)}function Sw(t){return"middle"===t||"center"===t}function Mw(t,e,n,i,r){for(var o=[],a=[],s=[],l=0;l<t.length;l++){var u=t[l].coord,u=(a[0]=u,s[a[1]=0]=u,s[1]=n,e&&(ee(a,a,e),ee(s,s,e)),new ju({shape:{x1:a[0],y1:a[1],x2:s[0],y2:s[1]},style:i,z2:2,autoBatch:!0,silent:!0}));Yh(u.shape,u.style.lineWidth),u.anid=r+"_"+t[l].tickValue,o.push(u)}return o}function Tw(t,e){var h,c,r,p,d,f,o,n={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return h=n,e=e,r=(c=t).getComponent("tooltip"),p=c.getComponent("axisPointer"),d=p.get("link",!0)||[],f=[],O(e.getCoordinateSystems(),function(s){var l,u,t,e,n;function i(t,e,n){var i,r,o=n.model.getModel("axisPointer",p),a=o.get("show");a&&("auto"!==a||t||kw(o))&&(null==e&&(e=o.get("triggerTooltip")),a=(o=t?function(t,e,n,i,r,o){var a=e.getModel("axisPointer"),s={};O(["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],function(t){s[t]=y(a.get(t))}),s.snap="category"!==t.type&&!!o,"cross"===a.get("type")&&(s.type="line");e=s.label||(s.label={});null==e.show&&(e.show=!1),"cross"===r&&(r=a.get(["label","show"]),e.show=null==r||r,o||(r=s.lineStyle=a.get("crossStyle"))&&z(e,r.textStyle));return t.model.getModel("axisPointer",new Lc(s,n,i))}(n,u,p,c,t,e):o).get("snap"),t=o.get("triggerEmphasis"),i=Dw(n.model),r=e||a||"category"===n.type,e=h.axesInfo[i]={key:i,axis:n,coordSys:s,axisPointerModel:o,triggerTooltip:e,triggerEmphasis:t,involveSeries:r,snap:a,useHandle:kw(o),seriesModels:[],linkGroup:null},l[i]=e,h.seriesInvolved=h.seriesInvolved||r,null!=(t=function(t,e){for(var n=e.model,i=e.dim,r=0;r<t.length;r++){var o=t[r]||{};if(Cw(o[i+"AxisId"],n.id)||Cw(o[i+"AxisIndex"],n.componentIndex)||Cw(o[i+"AxisName"],n.name))return r}}(d,n)))&&((a=f[t]||(f[t]={axesInfo:{}})).axesInfo[i]=e,a.mapper=d[t].mapper,e.linkGroup=a)}s.axisPointerEnabled&&(t=Dw(s.model),l=h.coordSysAxesInfo[t]={},u=(h.coordSysMap[t]=s).model.getModel("tooltip",r),O(s.getAxes(),pt(i,!1,null)),s.getTooltipAxes)&&r&&u.get("show")&&(t="axis"===u.get("trigger"),e="cross"===u.get(["axisPointer","type"]),n=s.getTooltipAxes(u.get(["axisPointer","axis"])),(t||e)&&O(n.baseAxes,pt(i,!e||"cross",t)),e)&&O(n.otherAxes,pt(i,"cross",!1))}),n.seriesInvolved&&(o=n,t.eachSeries(function(n){var i=n.coordinateSystem,t=n.get(["tooltip","trigger"],!0),e=n.get(["tooltip","show"],!0);i&&"none"!==t&&!1!==t&&"item"!==t&&!1!==e&&!1!==n.get(["axisPointer","show"],!0)&&O(o.coordSysAxesInfo[Dw(i.model)],function(t){var e=t.axis;i.getAxis(e.dim)===e&&(t.seriesModels.push(n),null==t.seriesDataCount&&(t.seriesDataCount=0),t.seriesDataCount+=n.getData().count())})})),n}function Cw(t,e){return"all"===t||F(t)&&0<=I(t,e)||t===e}function Iw(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[Dw(t)]}function kw(t){return!!t.get(["handle","show"])}function Dw(t){return t.type+"||"+t.id}var Aw,Pw={},Lw=(u(Ow,Aw=Gg),Ow.prototype.render=function(t,e,n,i){var r,o,a,s,l,u;this.axisPointerClass&&(r=Iw(r=t))&&(l=r.axisPointerModel,o=r.axis.scale,a=l.option,u=l.get("status"),null!=(s=l.get("value"))&&(s=o.parse(s)),l=kw(l),null==u&&(a.status=l?"show":"hide"),(u=o.getExtent().slice())[0]>u[1]&&u.reverse(),(s=null==s||s>u[1]?u[1]:s)<u[0]&&(s=u[0]),a.value=s,l)&&(a.status=r.axis.scale.isBlank()?"hide":"show"),Aw.prototype.render.apply(this,arguments),this._doUpdateAxisPointerClass(t,n,!0)},Ow.prototype.updateAxisPointer=function(t,e,n,i){this._doUpdateAxisPointerClass(t,n,!1)},Ow.prototype.remove=function(t,e){var n=this._axisPointer;n&&n.remove(e)},Ow.prototype.dispose=function(t,e){this._disposeAxisPointer(e),Aw.prototype.dispose.apply(this,arguments)},Ow.prototype._doUpdateAxisPointerClass=function(t,e,n){var i,r=Ow.getAxisPointerClass(this.axisPointerClass);r&&((i=(i=Iw(i=t))&&i.axisPointerModel)?(this._axisPointer||(this._axisPointer=new r)).render(t,i,e,n):this._disposeAxisPointer(e))},Ow.prototype._disposeAxisPointer=function(t){this._axisPointer&&this._axisPointer.dispose(t),this._axisPointer=null},Ow.registerAxisPointerClass=function(t,e){Pw[t]=e},Ow.getAxisPointerClass=function(t){return t&&Pw[t]},Ow.type="axis",Ow);function Ow(){var t=null!==Aw&&Aw.apply(this,arguments)||this;return t.type=Ow.type,t}var Rw=Po();var Nw,Ew=["axisLine","axisTickLabel","axisName"],zw=["splitArea","splitLine","minorSplitLine"],Wy=(u(Bw,Nw=Lw),Bw.prototype.render=function(i,t,e,n){this.group.removeAll();var r,o,a=this._axisGroup;this._axisGroup=new Hr,this.group.add(this._axisGroup),i.get("show")&&(o=lw(r=i.getCoordSysModel(),i),o=new vw(i,L({handleAutoShown:function(t){for(var e=r.coordinateSystem.getCartesians(),n=0;n<e.length;n++)if(Dv(e[n].getOtherAxis(i.axis).scale))return!0;return!1}},o)),O(Ew,o.add,o),this._axisGroup.add(o.getGroup()),O(zw,function(t){i.get([t,"show"])&&Vw[t](this,this._axisGroup,i,r)},this),n&&"changeAxisOrder"===n.type&&n.isInitSort||$h(a,this._axisGroup,i),Nw.prototype.render.call(this,i,t,e,n))},Bw.prototype.remove=function(){Rw(this).splitAreaColors=null},Bw.type="cartesianAxis",Bw);function Bw(){var t=null!==Nw&&Nw.apply(this,arguments)||this;return t.type=Bw.type,t.axisPointerClass="CartesianAxisPointer",t}var Fw,Vw={splitLine:function(t,e,n,i){var r=n.axis;if(!r.scale.isBlank())for(var n=n.getModel("splitLine"),o=n.getModel("lineStyle"),a=o.get("color"),s=!1!==n.get("showMinLine"),l=!1!==n.get("showMaxLine"),a=F(a)?a:[a],u=i.coordinateSystem.getRect(),h=r.isHorizontal(),c=0,p=r.getTicksCoords({tickModel:n}),d=[],f=[],g=o.getLineStyle(),y=0;y<p.length;y++){var m,v=r.toGlobalCoord(p[y].coord);0===y&&!s||y===p.length-1&&!l||(m=p[y].tickValue,h?(d[0]=v,d[1]=u.y,f[0]=v,f[1]=u.y+u.height):(d[0]=u.x,d[1]=v,f[0]=u.x+u.width,f[1]=v),v=c++%a.length,Yh((m=new ju({anid:null!=m?"line_"+m:null,autoBatch:!0,shape:{x1:d[0],y1:d[1],x2:f[0],y2:f[1]},style:z({stroke:a[v]},g),silent:!0})).shape,g.lineWidth),e.add(m))}},minorSplitLine:function(t,e,n,i){var r=n.axis,n=n.getModel("minorSplitLine").getModel("lineStyle"),o=i.coordinateSystem.getRect(),a=r.isHorizontal(),s=r.getMinorTicksCoords();if(s.length)for(var l=[],u=[],h=n.getLineStyle(),c=0;c<s.length;c++)for(var p=0;p<s[c].length;p++){var d=r.toGlobalCoord(s[c][p].coord),d=(a?(l[0]=d,l[1]=o.y,u[0]=d,u[1]=o.y+o.height):(l[0]=o.x,l[1]=d,u[0]=o.x+o.width,u[1]=d),new ju({anid:"minor_line_"+s[c][p].tickValue,autoBatch:!0,shape:{x1:l[0],y1:l[1],x2:u[0],y2:u[1]},style:h,silent:!0}));Yh(d.shape,h.lineWidth),e.add(d)}},splitArea:function(t,e,n,i){var r=e,e=i,o=(i=n).axis;if(!o.scale.isBlank()){var i=i.getModel("splitArea"),n=i.getModel("areaStyle"),a=n.get("color"),s=e.coordinateSystem.getRect(),l=o.getTicksCoords({tickModel:i,clamp:!0});if(l.length){var u=a.length,h=Rw(t).splitAreaColors,c=E(),p=0;if(h)for(var d=0;d<l.length;d++){var f=h.get(l[d].tickValue);if(null!=f){p=(f+(u-1)*d)%u;break}}for(var g=o.toGlobalCoord(l[0].coord),y=n.getAreaStyle(),a=F(a)?a:[a],d=1;d<l.length;d++){var m=o.toGlobalCoord(l[d].coord),v=void 0,_=void 0,x=void 0,w=void 0,g=o.isHorizontal()?(v=g,_=s.y,w=s.height,v+(x=m-v)):(v=s.x,_=g,x=s.width,_+(w=m-_)),m=l[d-1].tickValue;null!=m&&c.set(m,p),r.add(new As({anid:null!=m?"area_"+m:null,shape:{x:v,y:_,width:x,height:w},style:z({fill:a[p]},y),autoBatch:!0,silent:!0})),p=(p+1)%u}Rw(t).splitAreaColors=c}}}},Hw=(u(Gw,Fw=Wy),Gw.type="xAxis",Gw);function Gw(){var t=null!==Fw&&Fw.apply(this,arguments)||this;return t.type=Gw.type,t}u(Xw,Ww=Wy),Xw.type="yAxis";var Ww,Uw=Xw;function Xw(){var t=null!==Ww&&Ww.apply(this,arguments)||this;return t.type=Hw.type,t}u(Zw,Yw=Gg),Zw.prototype.render=function(t,e){this.group.removeAll(),t.get("show")&&this.group.add(new As({shape:t.coordinateSystem.getRect(),style:z({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))},Zw.type="grid";var Yw,qw=Zw;function Zw(){var t=null!==Yw&&Yw.apply(this,arguments)||this;return t.type="grid",t}var jw={offset:0};A_(function(t){t.registerComponentView(qw),t.registerComponentModel(Ux),t.registerCoordinateSystem("cartesian2d",pw),$x(t,"x",qx,jw),$x(t,"y",qx,jw),t.registerComponentView(Hw),t.registerComponentView(Uw),t.registerPreprocessor(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})})});u(Qw,Kw=g),Qw.type="title",Qw.defaultOption={z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bold",color:"#464646"},subtextStyle:{fontSize:12,color:"#6E7079"}};var Kw,$w=Qw;function Qw(){var t=null!==Kw&&Kw.apply(this,arguments)||this;return t.type=Qw.type,t.layoutMode={type:"box",ignoreSize:!0},t}u(eb,Jw=Gg),eb.prototype.render=function(t,e,n){var i,r,o,a,s,l,u,h,c;this.group.removeAll(),t.get("show")&&(i=this.group,u=t.getModel("textStyle"),r=t.getModel("subtextStyle"),h=t.get("textAlign"),c=N(t.get("textBaseline"),t.get("textVerticalAlign")),s=(u=new Ns({style:cc(u,{text:t.get("text"),fill:u.getTextColor()},{disableBox:!0}),z2:10})).getBoundingRect(),l=t.get("subtext"),r=new Ns({style:cc(r,{text:l,fill:r.getTextColor(),y:s.height+t.get("itemGap"),verticalAlign:"top"},{disableBox:!0}),z2:10}),o=t.get("link"),a=t.get("sublink"),s=t.get("triggerEvent",!0),u.silent=!o&&!s,r.silent=!a&&!s,o&&u.on("click",function(){Tp(o,"_"+t.get("target"))}),a&&r.on("click",function(){Tp(a,"_"+t.get("subtarget"))}),D(u).eventData=D(r).eventData=s?{componentType:"title",componentIndex:t.componentIndex}:null,i.add(u),l&&i.add(r),s=i.getBoundingRect(),(l=t.getBoxLayoutParams()).width=s.width,l.height=s.height,l=Pp(l,{width:n.getWidth(),height:n.getHeight()},t.get("padding")),h||("right"===(h="middle"===(h=t.get("left")||t.get("right"))?"center":h)?l.x+=l.width:"center"===h&&(l.x+=l.width/2)),c||("bottom"===(c="center"===(c=t.get("top")||t.get("bottom"))?"middle":c)?l.y+=l.height:"middle"===c&&(l.y+=l.height/2),c=c||"top"),i.x=l.x,i.y=l.y,i.markRedraw(),u.setStyle(n={align:h,verticalAlign:c}),r.setStyle(n),s=i.getBoundingRect(),u=l.margin,(h=t.getItemStyle(["color","opacity"])).fill=t.get("backgroundColor"),c=new As({shape:{x:s.x-u[3],y:s.y-u[0],width:s.width+u[1]+u[3],height:s.height+u[0]+u[2],r:t.get("borderRadius")},style:h,subPixelOptimize:!0,silent:!0}),i.add(c))},eb.type="title";var Jw,tb=eb;function eb(){var t=null!==Jw&&Jw.apply(this,arguments)||this;return t.type=eb.type,t}A_(function(t){t.registerComponentModel($w),t.registerComponentView(tb)});u(rb,nb=g),rb.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n),t.selected=t.selected||{},this._updateSelector(t)},rb.prototype.mergeOption=function(t,e){nb.prototype.mergeOption.call(this,t,e),this._updateSelector(t)},rb.prototype._updateSelector=function(t){var n=t.selector,i=this.ecModel;F(n=!0===n?t.selector=["all","inverse"]:n)&&O(n,function(t,e){V(t)&&(t={type:t}),n[e]=d(t,(e=i,"all"===(t=t.type)?{type:"all",title:e.getLocaleModel().get(["legend","selector","all"])}:"inverse"===t?{type:"inverse",title:e.getLocaleModel().get(["legend","selector","inverse"])}:void 0))})},rb.prototype.optionUpdated=function(){this._updateData(this.ecModel);var t=this._data;if(t[0]&&"single"===this.get("selectedMode")){for(var e=!1,n=0;n<t.length;n++){var i=t[n].get("name");if(this.isSelected(i)){this.select(i),e=!0;break}}e||this.select(t[0].get("name"))}},rb.prototype._updateData=function(i){var r=[],o=[],t=(i.eachRawSeries(function(t){var e,n=t.name;o.push(n),t.legendVisualProvider&&(n=t.legendVisualProvider.getAllNames(),i.isSeriesFiltered(t)||(o=o.concat(n)),n.length)?r=r.concat(n):e=!0,e&&Io(t)&&r.push(t.name)}),this._availableNames=o,this.get("data")||r),e=E(),t=B(t,function(t){return(V(t)||H(t))&&(t={name:t}),e.get(t.name)?null:(e.set(t.name,!0),new Lc(t,this,this.ecModel))},this);this._data=ut(t,function(t){return!!t})},rb.prototype.getData=function(){return this._data},rb.prototype.select=function(t){var e=this.option.selected;"single"===this.get("selectedMode")&&O(this._data,function(t){e[t.get("name")]=!1}),e[t]=!0},rb.prototype.unSelect=function(t){"single"!==this.get("selectedMode")&&(this.option.selected[t]=!1)},rb.prototype.toggleSelected=function(t){var e=this.option.selected;e.hasOwnProperty(t)||(e[t]=!0),this[e[t]?"unSelect":"select"](t)},rb.prototype.allSelect=function(){var t=this._data,e=this.option.selected;O(t,function(t){e[t.get("name",!0)]=!0})},rb.prototype.inverseSelect=function(){var t=this._data,e=this.option.selected;O(t,function(t){t=t.get("name",!0);e.hasOwnProperty(t)||(e[t]=!0),e[t]=!e[t]})},rb.prototype.isSelected=function(t){var e=this.option.selected;return!(e.hasOwnProperty(t)&&!e[t])&&0<=I(this._availableNames,t)},rb.prototype.getOrient=function(){return"vertical"===this.get("orient")?{index:1,name:"vertical"}:{index:0,name:"horizontal"}},rb.type="legend.plain",rb.dependencies=["series"],rb.defaultOption={z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,symbolRotate:"inherit",symbolKeepAspect:!0,inactiveColor:"#ccc",inactiveBorderColor:"#ccc",inactiveBorderWidth:"auto",itemStyle:{color:"inherit",opacity:"inherit",borderColor:"inherit",borderWidth:"auto",borderCap:"inherit",borderJoin:"inherit",borderDashOffset:"inherit",borderMiterLimit:"inherit"},lineStyle:{width:"auto",color:"inherit",inactiveColor:"#ccc",inactiveWidth:2,opacity:"inherit",type:"inherit",cap:"inherit",join:"inherit",dashOffset:"inherit",miterLimit:"inherit"},textStyle:{color:"#333"},selectedMode:!0,selector:!1,selectorLabel:{show:!0,borderRadius:10,padding:[3,5,3,5],fontSize:12,fontFamily:"sans-serif",color:"#666",borderWidth:1,borderColor:"#666"},emphasis:{selectorLabel:{show:!0,color:"#eee",backgroundColor:"#666"}},selectorPosition:"auto",selectorItemGap:7,selectorButtonGap:10,tooltip:{show:!1}};var nb,ib=rb;function rb(){var t=null!==nb&&nb.apply(this,arguments)||this;return t.type=rb.type,t.layoutMode={type:"box",ignoreSize:!0},t}var ob,ab=pt,sb=O,lb=Hr,ub=(u(hb,ob=Gg),hb.prototype.init=function(){this.group.add(this._contentGroup=new lb),this.group.add(this._selectorGroup=new lb),this._isFirstRender=!0},hb.prototype.getContentGroup=function(){return this._contentGroup},hb.prototype.getSelectorGroup=function(){return this._selectorGroup},hb.prototype.render=function(t,e,n){var i,r,o,a,s,l=this._isFirstRender;this._isFirstRender=!1,this.resetInner(),t.get("show",!0)&&(r=t.get("align"),i=t.get("orient"),r&&"auto"!==r||(r="right"===t.get("left")&&"vertical"===i?"right":"left"),a=t.get("selector",!0),s=t.get("selectorPosition",!0),this.renderInner(r,t,e,n,a,i,s=!a||s&&"auto"!==s?s:"horizontal"===i?"end":"start"),o=Pp(e=t.getBoxLayoutParams(),i={width:n.getWidth(),height:n.getHeight()},n=t.get("padding")),o=Pp(z({width:(r=this.layoutInner(t,r,o,l,a,s)).width,height:r.height},e),i,n),this.group.x=o.x-r.x,this.group.y=o.y-r.y,this.group.markRedraw(),this.group.add(this._backgroundEl=(l=r,s=vp((a=t).get("padding")),(e=a.getItemStyle(["color","opacity"])).fill=a.get("backgroundColor"),l=new As({shape:{x:l.x-s[3],y:l.y-s[0],width:l.width+s[1]+s[3],height:l.height+s[0]+s[2],r:a.get("borderRadius")},style:e,silent:!0,z2:-1}))))},hb.prototype.resetInner=function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl),this.getSelectorGroup().removeAll()},hb.prototype.renderInner=function(s,l,u,h,t,e,n){var c=this.getContentGroup(),p=E(),d=l.get("selectedMode"),f=[];u.eachRawSeries(function(t){t.get("legendHoverLink")||f.push(t.id)}),sb(l.getData(),function(r,o){var e,t,n,i,a=r.get("name");this.newlineDisabled||""!==a&&"\n"!==a?(e=u.getSeriesByName(a)[0],p.get(a)||(e?(i=(n=e.getData()).getVisual("legendLineStyle")||{},t=n.getVisual("legendIcon"),n=n.getVisual("style"),(i=this._createItem(e,a,o,r,l,s,i,n,t,d,h)).on("click",ab(cb,a,null,h,f)).on("mouseover",ab(db,e.name,null,h,f)).on("mouseout",ab(fb,e.name,null,h,f)),u.ssr&&i.eachChild(function(t){t=D(t);t.seriesIndex=e.seriesIndex,t.dataIndex=o,t.ssrType="legend"}),p.set(a,!0)):u.eachRawSeries(function(e){var t,n,i;p.get(a)||e.legendVisualProvider&&(n=e.legendVisualProvider).containName(a)&&(i=n.indexOfName(a),t=n.getItemVisual(i,"style"),n=n.getItemVisual(i,"legendIcon"),(i=gi(t.fill))&&0===i[3]&&(i[3]=.2,t=L(L({},t),{fill:wi(i,"rgba")})),(i=this._createItem(e,a,o,r,l,s,{},t,n,d,h)).on("click",ab(cb,null,a,h,f)).on("mouseover",ab(db,null,a,h,f)).on("mouseout",ab(fb,null,a,h,f)),u.ssr&&i.eachChild(function(t){t=D(t);t.seriesIndex=e.seriesIndex,t.dataIndex=o,t.ssrType="legend"}),p.set(a,!0))},this))):((n=new lb).newline=!0,c.add(n))},this),t&&this._createSelector(t,l,h,e,n)},hb.prototype._createSelector=function(t,i,r,e,n){var o=this.getSelectorGroup();sb(t,function(t){var e=t.type,n=new Ns({style:{x:0,y:0,align:"center",verticalAlign:"middle"},onclick:function(){r.dispatchAction({type:"all"===e?"legendAllSelect":"legendInverseSelect",legendId:i.id})}});o.add(n),uc(n,{normal:i.getModel("selectorLabel"),emphasis:i.getModel(["emphasis","selectorLabel"])},{defaultText:t.title}),Rl(n)})},hb.prototype._createItem=function(t,e,n,i,r,o,a,s,l,u,h){var c=t.visualDrawType,p=r.get("itemWidth"),d=r.get("itemHeight"),f=r.isSelected(e),g=i.get("symbolRotate"),y=i.get("symbolKeepAspect"),m=i.get("icon"),a=function(t,e,n,i,r,o,a){function s(n,i){"auto"===n.lineWidth&&(n.lineWidth=0<i.lineWidth?2:0),sb(n,function(t,e){"inherit"===n[e]&&(n[e]=i[e])})}var l=e.getModel("itemStyle"),u=l.getItemStyle(),t=0===t.lastIndexOf("empty",0)?"fill":"stroke",l=l.getShallow("decal");u.decal=l&&"inherit"!==l?Mm(l,a):i.decal,"inherit"===u.fill&&(u.fill=i[r]);"inherit"===u.stroke&&(u.stroke=i[t]);"inherit"===u.opacity&&(u.opacity=("fill"===r?i:n).opacity);s(u,i);l=e.getModel("lineStyle"),a=l.getLineStyle();s(a,n),"auto"===u.fill&&(u.fill=i.fill),"auto"===u.stroke&&(u.stroke=i.fill),"auto"===a.stroke&&(a.stroke=i.fill),o||(r=e.get("inactiveBorderWidth"),n=u[t],u.lineWidth="auto"===r?0<i.lineWidth&&n?2:0:u.lineWidth,u.fill=e.get("inactiveColor"),u.stroke=e.get("inactiveBorderColor"),a.stroke=l.get("inactiveColor"),a.lineWidth=l.get("inactiveWidth"));return{itemStyle:u,lineStyle:a}}(l=m||l||"roundRect",i,a,s,c,f,h),s=new lb,c=i.getModel("textStyle"),m=(!k(t.getLegendIcon)||m&&"inherit"!==m?(h="inherit"===m&&t.getData().getVisual("symbol")?"inherit"===g?t.getData().getVisual("symbolRotate"):g:0,s.add(function(t){var e=t.icon||"roundRect",n=Zy(e,0,0,t.itemWidth,t.itemHeight,t.itemStyle.fill,t.symbolKeepAspect);n.setStyle(t.itemStyle),n.rotation=(t.iconRotate||0)*Math.PI/180,n.setOrigin([t.itemWidth/2,t.itemHeight/2]),-1<e.indexOf("empty")&&(n.style.stroke=n.style.fill,n.style.fill="#fff",n.style.lineWidth=2);return n}({itemWidth:p,itemHeight:d,icon:l,iconRotate:h,itemStyle:a.itemStyle,lineStyle:a.lineStyle,symbolKeepAspect:y}))):s.add(t.getLegendIcon({itemWidth:p,itemHeight:d,icon:l,iconRotate:g,itemStyle:a.itemStyle,lineStyle:a.lineStyle,symbolKeepAspect:y})),"left"===o?p+5:-5),h=o,t=r.get("formatter"),l=e,g=(V(t)&&t?l=t.replace("{name}",null!=e?e:""):k(t)&&(l=t(e)),f?c.getTextColor():i.get("inactiveColor")),a=(s.add(new Ns({style:cc(c,{text:l,x:m,y:d/2,fill:g,align:h,verticalAlign:"middle"},{inheritColor:g})})),new As({shape:s.getBoundingRect(),style:{fill:"transparent"}})),y=i.getModel("tooltip");return y.get("show")&&nc({el:a,componentModel:r,itemName:e,itemTooltipOption:y.option}),s.add(a),s.eachChild(function(t){t.silent=!0}),a.silent=!u,this.getContentGroup().add(s),Rl(s),s.__legendDataIndex=n,s},hb.prototype.layoutInner=function(t,e,n,i,r,o){var a,s,l,u,h,c=this.getContentGroup(),p=this.getSelectorGroup(),n=(Ap(t.get("orient"),c,t.get("itemGap"),n.width,n.height),c.getBoundingRect()),d=[-n.x,-n.y];return p.markRedraw(),c.markRedraw(),r?(Ap("horizontal",p,t.get("selectorItemGap",!0)),a=[-(r=p.getBoundingRect()).x,-r.y],s=t.get("selectorButtonGap",!0),l=0===(t=t.getOrient().index)?"width":"height",u=0===t?"height":"width",h=0===t?"y":"x","end"===o?a[t]+=n[l]+s:d[t]+=r[l]+s,a[1-t]+=n[u]/2-r[u]/2,p.x=a[0],p.y=a[1],c.x=d[0],c.y=d[1],(o={x:0,y:0})[l]=n[l]+s+r[l],o[u]=Math.max(n[u],r[u]),o[h]=Math.min(0,r[h]+a[1-t]),o):(c.x=d[0],c.y=d[1],this.group.getBoundingRect())},hb.prototype.remove=function(){this.getContentGroup().removeAll(),this._isFirstRender=!0},hb.type="legend.plain",hb);function hb(){var t=null!==ob&&ob.apply(this,arguments)||this;return t.type=hb.type,t.newlineDisabled=!1,t}function cb(t,e,n,i){fb(t,e,n,i),n.dispatchAction({type:"legendToggleSelect",name:null!=t?t:e}),db(t,e,n,i)}function pb(t){for(var e,n=t.getZr().storage.getDisplayList(),i=0,r=n.length;i<r&&!(e=n[i].states.emphasis);)i++;return e&&e.hoverLayer}function db(t,e,n,i){pb(n)||n.dispatchAction({type:"highlight",seriesName:t,name:e,excludeSeriesId:i})}function fb(t,e,n,i){pb(n)||n.dispatchAction({type:"downplay",seriesName:t,name:e,excludeSeriesId:i})}function gb(t){var n=t.findComponents({mainType:"legend"});n&&n.length&&t.filterSeries(function(t){for(var e=0;e<n.length;e++)if(!n[e].isSelected(t.name))return!1;return!0})}function yb(e,n,t){var i="allSelect"===e||"inverseSelect"===e,r={},o=[],a=(t.eachComponent({mainType:"legend",query:n},function(t){i?t[e]():t[e](n.name),mb(t,r),o.push(t.componentIndex)}),{});return t.eachComponent("legend",function(n){O(r,function(t,e){n[t?"select":"unSelect"](e)}),mb(n,a)}),i?{selected:a,legendIndex:o}:{name:n.name,selected:a}}function mb(n,t){var i=t||{};O(n.getData(),function(t){var e,t=t.get("name");"\n"!==t&&""!==t&&(e=n.isSelected(t),Bt(i,t)?i[t]=i[t]&&e:i[t]=e)})}function vb(t){t.registerComponentModel(ib),t.registerComponentView(ub),t.registerProcessor(t.PRIORITY.PROCESSOR.SERIES_FILTER,gb),t.registerSubTypeDefaulter("legend",function(){return"plain"}),(t=t).registerAction("legendToggleSelect","legendselectchanged",pt(yb,"toggleSelected")),t.registerAction("legendAllSelect","legendselectall",pt(yb,"allSelect")),t.registerAction("legendInverseSelect","legendinverseselect",pt(yb,"inverseSelect")),t.registerAction("legendSelect","legendselected",pt(yb,"select")),t.registerAction("legendUnSelect","legendunselected",pt(yb,"unSelect"))}u(wb,_b=ib),wb.prototype.setScrollDataIndex=function(t){this.option.scrollDataIndex=t},wb.prototype.init=function(t,e,n){var i=Rp(t);_b.prototype.init.call(this,t,e,n),bb(this,t,i)},wb.prototype.mergeOption=function(t,e){_b.prototype.mergeOption.call(this,t,e),bb(this,this.option,t)},wb.type="legend.scroll",wb.defaultOption=(eh=ib.defaultOption,hu={scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800},d(d({},eh,!0),hu,!0));var _b,xb=wb;function wb(){var t=null!==_b&&_b.apply(this,arguments)||this;return t.type=wb.type,t}function bb(t,e,n){var i=[1,1];i[t.getOrient().index]=0,Op(e,n,{type:"box",ignoreSize:!!i})}var Sb,Mb=Hr,Tb=["width","height"],Cb=["x","y"],Ib=(u(kb,Sb=ub),kb.prototype.init=function(){Sb.prototype.init.call(this),this.group.add(this._containerGroup=new Mb),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new Mb)},kb.prototype.resetInner=function(){Sb.prototype.resetInner.call(this),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},kb.prototype.renderInner=function(t,i,e,r,n,o,a){var s=this,l=(Sb.prototype.renderInner.call(this,t,i,e,r,n,o,a),this._controllerGroup),t=i.get("pageIconSize",!0),u=F(t)?t:[t,t],e=(h("pagePrev",0),i.getModel("pageTextStyle"));function h(t,e){var n=t+"DataIndex",e=tc(i.get("pageIcons",!0)[i.getOrient().name][e],{onclick:ct(s._pageGo,s,n,i,r)},{x:-u[0]/2,y:-u[1]/2,width:u[0],height:u[1]});e.name=t,l.add(e)}l.add(new Ns({name:"pageText",style:{text:"xx/xx",fill:e.getTextColor(),font:e.getFont(),verticalAlign:"middle",align:"center"},silent:!0})),h("pageNext",1)},kb.prototype.layoutInner=function(t,e,n,i,r,o){var a=this.getSelectorGroup(),s=t.getOrient().index,l=Tb[s],u=Cb[s],h=Tb[1-s],c=Cb[1-s],p=(r&&Ap("horizontal",a,t.get("selectorItemGap",!0)),t.get("selectorButtonGap",!0)),d=a.getBoundingRect(),f=[-d.x,-d.y],g=y(n),n=(r&&(g[l]=n[l]-d[l]-p),this._layoutContentAndController(t,i,g,s,l,h,c,u));return r&&("end"===o?f[s]+=n[l]+p:(t=d[l]+p,f[s]-=t,n[u]-=t),n[l]+=d[l]+p,f[1-s]+=n[c]+n[h]/2-d[h]/2,n[h]=Math.max(n[h],d[h]),n[c]=Math.min(n[c],d[c]+f[1-s]),a.x=f[0],a.y=f[1],a.markRedraw()),n},kb.prototype._layoutContentAndController=function(t,e,n,i,r,o,a,s){var l=this.getContentGroup(),u=this._containerGroup,h=this._controllerGroup,c=(Ap(t.get("orient"),l,t.get("itemGap"),i?n.width:null,i?null:n.height),Ap("horizontal",h,t.get("pageButtonItemGap",!0)),l.getBoundingRect()),p=h.getBoundingRect(),d=this._showController=c[r]>n[r],f=[-c.x,-c.y],e=(e||(f[i]=l[s]),[0,0]),s=[-p.x,-p.y],g=N(t.get("pageButtonGap",!0),t.get("itemGap",!0)),f=(d&&("end"===t.get("pageButtonPosition",!0)?s[i]+=n[r]-p[r]:e[i]+=p[r]+g),s[1-i]+=c[o]/2-p[o]/2,l.setPosition(f),u.setPosition(e),h.setPosition(s),{x:0,y:0}),c=(f[r]=(d?n:c)[r],f[o]=Math.max(c[o],p[o]),f[a]=Math.min(0,p[a]+s[1-i]),u.__rectSize=n[r],d?((e={x:0,y:0})[r]=Math.max(n[r]-p[r]-g,0),e[o]=f[o],u.setClipPath(new As({shape:e})),u.__rectSize=e[r]):h.eachChild(function(t){t.attr({invisible:!0,silent:!0})}),this._getPageInfo(t));return null!=c.pageIndex&&kh(l,{x:c.contentPosition[0],y:c.contentPosition[1]},d?t:null),this._updatePageInfoView(t,c),f},kb.prototype._pageGo=function(t,e,n){t=this._getPageInfo(e)[t];null!=t&&n.dispatchAction({type:"legendScroll",scrollDataIndex:t,legendId:e.id})},kb.prototype._updatePageInfoView=function(n,i){var r=this._controllerGroup,t=(O(["pagePrev","pageNext"],function(t){var e=null!=i[t+"DataIndex"],t=r.childOfName(t);t&&(t.setStyle("fill",e?n.get("pageIconColor",!0):n.get("pageIconInactiveColor",!0)),t.cursor=e?"pointer":"default")}),r.childOfName("pageText")),e=n.get("pageFormatter"),o=i.pageIndex,o=null!=o?o+1:0,a=i.pageCount;t&&e&&t.setStyle("text",V(e)?e.replace("{current}",null==o?"":o+"").replace("{total}",null==a?"":a+""):e({current:o,total:a}))},kb.prototype._getPageInfo=function(t){var e=t.get("scrollDataIndex",!0),n=this.getContentGroup(),i=this._containerGroup.__rectSize,t=t.getOrient().index,r=Tb[t],o=Cb[t],e=this._findTargetItemIndex(e),a=n.children(),s=a[e],l=a.length,u=l?1:0,h={contentPosition:[n.x,n.y],pageCount:u,pageIndex:u-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(s){n=g(s);h.contentPosition[t]=-n.s;for(var c=e+1,p=n,d=n,f=null;c<=l;++c)(!(f=g(a[c]))&&d.e>p.s+i||f&&!y(f,p.s))&&(p=d.i>p.i?d:f)&&(null==h.pageNextDataIndex&&(h.pageNextDataIndex=p.i),++h.pageCount),d=f;for(c=e-1,p=n,d=n,f=null;-1<=c;--c)(f=g(a[c]))&&y(d,f.s)||!(p.i<d.i)||(d=p,null==h.pagePrevDataIndex&&(h.pagePrevDataIndex=p.i),++h.pageCount,++h.pageIndex),p=f}return h;function g(t){var e,n;if(t)return{s:n=(e=t.getBoundingRect())[o]+t[o],e:n+e[r],i:t.__legendDataIndex}}function y(t,e){return t.e>=e&&t.s<=e+i}},kb.prototype._findTargetItemIndex=function(n){var i,r;return this._showController?(this.getContentGroup().eachChild(function(t,e){t=t.__legendDataIndex;null==r&&null!=t&&(r=e),t===n&&(i=e)}),null!=i?i:r):0},kb.type="legend.scroll",kb);function kb(){var t=null!==Sb&&Sb.apply(this,arguments)||this;return t.type=kb.type,t.newlineDisabled=!0,t._currentIndex=0,t}A_(function(t){A_(vb),t.registerComponentModel(xb),t.registerComponentView(Ib),t.registerAction("legendScroll","legendscroll",function(t,e){var n=t.scrollDataIndex;null!=n&&e.eachComponent({mainType:"legend",subType:"scroll",query:t},function(t){t.setScrollDataIndex(n)})})});var Db=Po(),Ab=y,Pb=ct;function Lb(){this._dragging=!1,this.animationThreshold=15}function Ob(t,e,n,i){!function n(i,t){{var r;return R(i)&&R(t)?(r=!0,O(t,function(t,e){r=r&&n(i[e],t)}),!!r):i===t}}(Db(n).lastProp,i)&&(Db(n).lastProp=i,e?kh(n,i,t):(n.stopAnimation(),n.attr(i)))}function Rb(t,e){t[e.get(["label","show"])?"show":"hide"]()}function Nb(t){return{x:t.x||0,y:t.y||0,rotation:t.rotation||0}}function Eb(t,e,n){var i=e.get("z"),r=e.get("zlevel");t&&t.traverse(function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=r&&(t.zlevel=r),t.silent=n)})}function zb(t,e,n,i,r){var o=Bb(n.get("value"),e.axis,e.ecModel,n.get("seriesDataIndices"),{precision:n.get(["label","precision"]),formatter:n.get(["label","formatter"])}),n=n.getModel("label"),a=vp(n.get("padding")||0),s=n.getFont(),l=Sr(o,s),u=r.position,h=l.width+a[1]+a[3],l=l.height+a[0]+a[2],c=r.align,c=("right"===c&&(u[0]-=h),"center"===c&&(u[0]-=h/2),r.verticalAlign),i=("bottom"===c&&(u[1]-=l),"middle"===c&&(u[1]-=l/2),r=u,c=h,h=l,i=(l=i).getWidth(),l=l.getHeight(),r[0]=Math.min(r[0]+c,i)-c,r[1]=Math.min(r[1]+h,l)-h,r[0]=Math.max(r[0],0),r[1]=Math.max(r[1],0),n.get("backgroundColor"));i&&"auto"!==i||(i=e.get(["axisLine","lineStyle","color"])),t.label={x:u[0],y:u[1],style:cc(n,{text:o,font:s,fill:n.getTextColor(),padding:a,backgroundColor:i}),z2:10}}function Bb(t,e,n,i,r){t=e.scale.parse(t);var o,a=e.scale.getLabel({value:t},{precision:r.precision}),r=r.formatter;return r&&(o={value:b_(e,{value:t}),axisDimension:e.dim,axisIndex:e.index,seriesData:[]},O(i,function(t){var e=n.getSeriesByIndex(t.seriesIndex),t=t.dataIndexInside,e=e&&e.getDataParams(t);e&&o.seriesData.push(e)}),V(r)?a=r.replace("{value}",a):k(r)&&(a=r(o))),a}function Fb(t,e,n){var i=Le();return ze(i,i,n.rotation),Ee(i,i,n.position),jh([t.dataToCoord(e),(n.labelOffset||0)+(n.labelDirection||1)*(n.labelMargin||0)],i)}Lb.prototype.render=function(t,e,n,i){var r,o,a=e.get("value"),s=e.get("status");this._axisModel=t,this._axisPointerModel=e,this._api=n,!i&&this._lastValue===a&&this._lastStatus===s||(this._lastValue=a,this._lastStatus=s,i=this._group,r=this._handle,s&&"hide"!==s?(i&&i.show(),r&&r.show(),this.makeElOption(s={},a,t,e,n),(o=s.graphicKey)!==this._lastGraphicKey&&this.clear(n),this._lastGraphicKey=o,o=this._moveAnimation=this.determineAnimation(t,e),i?(o=pt(Ob,e,o),this.updatePointerEl(i,s,o),this.updateLabelEl(i,s,o,e)):(i=this._group=new Hr,this.createPointerEl(i,s,t,e),this.createLabelEl(i,s,t,e),n.getZr().add(i)),Eb(i,e,!0),this._renderHandle(a)):(i&&i.hide(),r&&r.hide()))},Lb.prototype.remove=function(t){this.clear(t)},Lb.prototype.dispose=function(t){this.clear(t)},Lb.prototype.determineAnimation=function(t,e){var n,i=e.get("animation"),r=t.axis,o="category"===r.type,e=e.get("snap");return!(!e&&!o)&&("auto"===i||null==i?(n=this.animationThreshold,o&&r.getBandWidth()>n||!!e&&(o=Iw(t).seriesDataCount,e=r.getExtent(),Math.abs(e[0]-e[1])/o>n)):!0===i)},Lb.prototype.makeElOption=function(t,e,n,i,r){},Lb.prototype.createPointerEl=function(t,e,n,i){var r=e.pointer;r&&(r=Db(t).pointerEl=new oc[r.type](Ab(e.pointer)),t.add(r))},Lb.prototype.createLabelEl=function(t,e,n,i){e.label&&(e=Db(t).labelEl=new Ns(Ab(e.label)),t.add(e),Rb(e,i))},Lb.prototype.updatePointerEl=function(t,e,n){t=Db(t).pointerEl;t&&e.pointer&&(t.setStyle(e.pointer.style),n(t,{shape:e.pointer.shape}))},Lb.prototype.updateLabelEl=function(t,e,n,i){t=Db(t).labelEl;t&&(t.setStyle(e.label.style),n(t,{x:e.label.x,y:e.label.y}),Rb(t,i))},Lb.prototype._renderHandle=function(t){var e,n,i,r,o,a;!this._dragging&&this.updateHandleTransform&&(e=this._axisPointerModel,n=this._api.getZr(),i=this._handle,r=e.getModel("handle"),a=e.get("status"),r.get("show")&&a&&"hide"!==a?(this._handle||(o=!0,i=this._handle=tc(r.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(t){Ie(t.event)},onmousedown:Pb(this._onHandleDragMove,this,0,0),drift:Pb(this._onHandleDragMove,this),ondragend:Pb(this._onHandleDragEnd,this)}),n.add(i)),Eb(i,e,!1),i.setStyle(r.getItemStyle(null,["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"])),F(a=r.get("size"))||(a=[a,a]),i.scaleX=a[0]/2,i.scaleY=a[1]/2,ry(this,"_doDispatchAxisPointer",r.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,o)):(i&&n.remove(i),this._handle=null))},Lb.prototype._moveHandleToValue=function(t,e){Ob(this._axisPointerModel,!e&&this._moveAnimation,this._handle,Nb(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},Lb.prototype._onHandleDragMove=function(t,e){var n=this._handle;n&&(this._dragging=!0,t=this.updateHandleTransform(Nb(n),[t,e],this._axisModel,this._axisPointerModel),this._payloadInfo=t,n.stopAnimation(),n.attr(Nb(t)),Db(n).lastProp=null,this._doDispatchAxisPointer())},Lb.prototype._doDispatchAxisPointer=function(){var t,e;this._handle&&(t=this._payloadInfo,e=this._axisModel,this._api.dispatchAction({type:"updateAxisPointer",x:t.cursorPoint[0],y:t.cursorPoint[1],tooltipOption:t.tooltipOption,axesInfo:[{axisDim:e.axis.dim,axisIndex:e.componentIndex}]}))},Lb.prototype._onHandleDragEnd=function(){var t;this._dragging=!1,this._handle&&(t=this._axisPointerModel.get("value"),this._moveHandleToValue(t),this._api.dispatchAction({type:"hideTip"}))},Lb.prototype.clear=function(t){this._lastValue=null,this._lastStatus=null;var t=t.getZr(),e=this._group,n=this._handle;t&&e&&(this._lastGraphicKey=null,e&&t.remove(e),n&&t.remove(n),this._group=null,this._handle=null,this._payloadInfo=null),oy(this,"_doDispatchAxisPointer")},Lb.prototype.doClear=function(){},Lb.prototype.buildLabel=function(t,e,n){return{x:t[n=n||0],y:t[1-n],width:e[n],height:e[1-n]}};u(Gb,Vb=Lb),Gb.prototype.makeElOption=function(t,e,n,i,r){var o,a,s=n.axis,l=s.grid,u=i.get("type"),h=Wb(l,s).getOtherAxis(s).getGlobalExtent(),c=s.toGlobalCoord(s.dataToCoord(e,!0)),p=(u&&"none"!==u&&(o=(a=i).get("type"),a=a.getModel(o+"Style"),"line"===o?(p=a.getLineStyle()).fill=null:"shadow"===o&&((p=a.getAreaStyle()).stroke=null),o=p,(a=Ub[u](s,c,h)).style=o,t.graphicKey=a.type,t.pointer=a),lw(l.model,n));u=e,s=t,c=p,h=n,o=i,a=r,l=vw.innerTextLayout(c.rotation,0,c.labelDirection),c.labelMargin=o.get(["label","margin"]),zb(s,h,o,a,{position:Fb(h.axis,u,c),align:l.textAlign,verticalAlign:l.textVerticalAlign})},Gb.prototype.getHandleTransform=function(t,e,n){var i=lw(e.axis.grid.model,e,{labelInside:!1}),n=(i.labelMargin=n.get(["handle","margin"]),Fb(e.axis,t,i));return{x:n[0],y:n[1],rotation:i.rotation+(i.labelDirection<0?Math.PI:0)}},Gb.prototype.updateHandleTransform=function(t,e,n,i){var n=n.axis,r=n.grid,o=n.getGlobalExtent(!0),r=Wb(r,n).getOtherAxis(n).getGlobalExtent(),n="x"===n.dim?0:1,a=[t.x,t.y],e=(a[n]+=e[n],a[n]=Math.min(o[1],a[n]),a[n]=Math.max(o[0],a[n]),(r[1]+r[0])/2),o=[e,e];o[n]=a[n];return{x:a[0],y:a[1],rotation:t.rotation,cursorPoint:o,tooltipOption:[{verticalAlign:"middle"},{align:"center"}][n]}};var Vb,Hb=Gb;function Gb(){return null!==Vb&&Vb.apply(this,arguments)||this}function Wb(t,e){var n={};return n[e.dim+"AxisIndex"]=e.index,t.getCartesian(n)}var Ub={line:function(t,e,n){var i;return i=[e,n[0]],e=[e,n[1]],n=Xb(t),{type:"Line",subPixelOptimize:!0,shape:{x1:i[n=n||0],y1:i[1-n],x2:e[n],y2:e[1-n]}}},shadow:function(t,e,n){var i=Math.max(1,t.getBandWidth()),r=n[1]-n[0];return{type:"Rect",shape:(e=[e-i/2,n[0]],n=[i,r],i=Xb(t),{x:e[i=i||0],y:e[1-i],width:n[i],height:n[1-i]})}}};function Xb(t){return"x"===t.dim?0:1}u(Zb,Yb=g),Zb.type="axisPointer",Zb.defaultOption={show:"auto",z:50,type:"line",snap:!1,triggerTooltip:!0,triggerEmphasis:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#B9BEC9",width:1,type:"dashed"},shadowStyle:{color:"rgba(210,219,238,0.2)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,borderRadius:3},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}};var Yb,qb=Zb;function Zb(){var t=null!==Yb&&Yb.apply(this,arguments)||this;return t.type=Zb.type,t}var jb=Po(),Kb=O;function $b(t,e,n){var i,c,p;function r(t,h){c.on(t,function(e){n=p;var n,i,r={dispatchAction:o,pendings:i={showTip:[],hideTip:[]}};function o(t){var e=i[t.type];e?e.push(t):(t.dispatchAction=o,n.dispatchAction(t))}Kb(jb(c).records,function(t){t&&h(t,e,r.dispatchAction)});var t,a=r.pendings,s=p,l=a.showTip.length,u=a.hideTip.length;l?t=a.showTip[l-1]:u&&(t=a.hideTip[u-1]),t&&(t.dispatchAction=null,s.dispatchAction(t))})}b.node||(i=e.getZr(),jb(i).records||(jb(i).records={}),p=e,jb(c=i).initialized||(jb(c).initialized=!0,r("click",pt(Jb,"click")),r("mousemove",pt(Jb,"mousemove")),r("globalout",Qb)),(jb(i).records[t]||(jb(i).records[t]={})).handler=n)}function Qb(t,e,n){t.handler("leave",null,n)}function Jb(t,e,n,i){e.handler(t,n,i)}function tS(t,e){b.node||(e=e.getZr(),(jb(e).records||{})[t]&&(jb(e).records[t]=null))}u(iS,eS=Gg),iS.prototype.render=function(t,e,n){var e=e.getComponent("tooltip"),i=t.get("triggerOn")||e&&e.get("triggerOn")||"mousemove|click";$b("axisPointer",n,function(t,e,n){"none"!==i&&("leave"===t||0<=i.indexOf(t))&&n({type:"updateAxisPointer",currTrigger:t,x:e&&e.offsetX,y:e&&e.offsetY})})},iS.prototype.remove=function(t,e){tS("axisPointer",e)},iS.prototype.dispose=function(t,e){tS("axisPointer",e)},iS.type="axisPointer";var eS,nS=iS;function iS(){var t=null!==eS&&eS.apply(this,arguments)||this;return t.type=iS.type,t}function rS(t,e){var n,i,r,o,a=[],s=t.seriesIndex;return null==s||!(e=e.getSeriesByIndex(s))||null==(s=Ao(n=e.getData(),t))||s<0||F(s)?{point:[]}:(i=n.getItemGraphicEl(s),r=e.coordinateSystem,e.getTooltipPosition?a=e.getTooltipPosition(s)||[]:r&&r.dataToPoint?a=t.isStacked?(e=r.getBaseAxis(),t=r.getOtherAxis(e).dim,e=e.dim,t="x"===t||"radius"===t?1:0,e=n.mapDimension(e),(o=[])[t]=n.get(e,s),o[1-t]=n.get(n.getCalculationInfo("stackResultDimension"),s),r.dataToPoint(o)||[]):r.dataToPoint(n.getValues(B(r.dimensions,function(t){return n.mapDimension(t)}),s))||[]:i&&((e=i.getBoundingRect().clone()).applyTransform(i.transform),a=[e.x+e.width/2,e.y+e.height/2]),{point:a,el:i})}var oS=Po();function aS(t,e,n){var o,a,i,s,l,r,u,h,c,p,d,f,g,y,m=t.currTrigger,v=[t.x,t.y],_=t,x=t.dispatchAction||ct(n.dispatchAction,n),w=e.getComponent("axisPointer").coordSysAxesInfo;if(w)return cS(v)&&(v=rS({seriesIndex:_.seriesIndex,dataIndex:_.dataIndex},e).point),o=cS(v),a=_.axesInfo,i=w.axesInfo,s="leave"===m||cS(v),l={},e={list:[],map:{}},u={showPointer:pt(lS,r={}),showTooltip:pt(uS,e)},O(w.coordSysMap,function(t,e){var r=o||t.containPoint(v);O(w.coordSysAxesInfo[e],function(t,e){var n=t.axis,i=function(t,e){for(var n=0;n<(t||[]).length;n++){var i=t[n];if(e.axis.dim===i.axisDim&&e.axis.model.componentIndex===i.axisIndex)return i}}(a,t);s||!r||a&&!i||null!=(i=null!=(i=i&&i.value)||o?i:n.pointToData(v))&&sS(t,i,u,!1,l)})}),h={},O(i,function(n,t){var i=n.linkGroup;i&&!r[t]&&O(i.axesInfo,function(t,e){var e=r[e];t!==n&&e&&(e=e.value,i.mapper&&(e=n.axis.scale.parse(i.mapper(e,hS(t),hS(n)))),h[n.key]=e)})}),O(h,function(t,e){sS(i[e],t,u,!0,l)}),c=r,_=i,p=l.axesInfo=[],O(_,function(t,e){var n=t.axisPointerModel.option,e=c[e];e?(t.useHandle||(n.status="show"),n.value=e.value,n.seriesDataIndices=(e.payloadBatch||[]).slice()):t.useHandle||(n.status="hide"),"show"===n.status&&p.push({axisDim:t.axis.dim,axisIndex:t.axis.model.componentIndex,value:n.value})}),m=e,_=t,e=x,cS(t=v)||!m.list.length?e({type:"hideTip"}):(x=((m.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{},e({type:"showTip",escapeConnect:!0,x:t[0],y:t[1],tooltipOption:_.tooltipOption,position:_.position,dataIndexInside:x.dataIndexInside,dataIndex:x.dataIndex,seriesIndex:x.seriesIndex,dataByCoordSys:m.list})),e=i,_=(t=n).getZr(),x="axisPointerLastHighlights",d=oS(_)[x]||{},f=oS(_)[x]={},O(e,function(t,e){var n=t.axisPointerModel.option;"show"===n.status&&t.triggerEmphasis&&O(n.seriesDataIndices,function(t){var e=t.seriesIndex+" | "+t.dataIndex;f[e]=t})}),g=[],y=[],O(d,function(t,e){f[e]||y.push(t)}),O(f,function(t,e){d[e]||g.push(t)}),y.length&&t.dispatchAction({type:"downplay",escapeConnect:!0,notBlur:!0,batch:y}),g.length&&t.dispatchAction({type:"highlight",escapeConnect:!0,notBlur:!0,batch:g}),l}function sS(t,e,n,i,r){var o,a,s,l,u,h,c,p,d,f,g=t.axis;!g.scale.isBlank()&&g.containData(e)&&(t.involveSeries?(a=e,s=t.axis,l=s.dim,u=a,h=[],c=Number.MAX_VALUE,p=-1,O(t.seriesModels,function(e,t){var n,i=e.getData().mapDimensionsAll(l);if(e.getAxisTooltipData)var r=e.getAxisTooltipData(i,a,s),o=r.dataIndices,r=r.nestestValue;else{if(!(o=e.getData().indicesOfNearest(i[0],a,"category"===s.type?.5:null)).length)return;r=e.getData().get(i[0],o[0])}null!=r&&isFinite(r)&&(i=a-r,(n=Math.abs(i))<=c)&&((n<c||0<=i&&p<0)&&(c=n,p=i,u=r,h.length=0),O(o,function(t){h.push({seriesIndex:e.seriesIndex,dataIndexInside:t,dataIndex:e.getData().getRawIndex(t)})}))}),f=(o={payloadBatch:h,snapToValue:u}).snapToValue,(d=o.payloadBatch)[0]&&null==r.seriesIndex&&L(r,d[0]),!i&&t.snap&&g.containData(f)&&null!=f&&(e=f),n.showPointer(t,e,d),n.showTooltip(t,o,f)):n.showPointer(t,e))}function lS(t,e,n,i){t[e.key]={value:n,payloadBatch:i}}function uS(t,e,n,i){var r,o,n=n.payloadBatch,a=e.axis,s=a.model,l=e.axisPointerModel;e.triggerTooltip&&n.length&&(r=Dw(e=e.coordSys.model),(o=t.map[r])||(o=t.map[r]={coordSysId:e.id,coordSysIndex:e.componentIndex,coordSysType:e.type,coordSysMainType:e.mainType,dataByAxis:[]},t.list.push(o)),o.dataByAxis.push({axisDim:a.dim,axisIndex:s.componentIndex,axisType:s.type,axisId:s.id,value:i,valueLabelOpt:{precision:l.get(["label","precision"]),formatter:l.get(["label","formatter"])},seriesDataIndices:n.slice()}))}function hS(t){var e=t.axis.model,n={},t=n.axisDim=t.axis.dim;return n.axisIndex=n[t+"AxisIndex"]=e.componentIndex,n.axisName=n[t+"AxisName"]=e.name,n.axisId=n[t+"AxisId"]=e.id,n}function cS(t){return!t||null==t[0]||isNaN(t[0])||null==t[1]||isNaN(t[1])}function pS(t){Lw.registerAxisPointerClass("CartesianAxisPointer",Hb),t.registerComponentModel(qb),t.registerComponentView(nS),t.registerPreprocessor(function(t){var e;t&&(t.axisPointer&&0!==t.axisPointer.length||(t.axisPointer={}),e=t.axisPointer.link)&&!F(e)&&(t.axisPointer.link=[e])}),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=Tw(t,e)}),t.registerAction({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},aS)}u(gS,dS=g),gS.type="tooltip",gS.dependencies=["axisPointer"],gS.defaultOption={z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:null,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"#fff",shadowBlur:10,shadowColor:"rgba(0, 0, 0, .2)",shadowOffsetX:1,shadowOffsetY:2,borderRadius:4,borderWidth:1,padding:null,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#666",fontSize:14}};var dS,fS=gS;function gS(){var t=null!==dS&&dS.apply(this,arguments)||this;return t.type=gS.type,t}function yS(t){var e=t.get("confine");return null!=e?e:"richText"===t.get("renderMode")}function mS(t){if(b.domSupported)for(var e=document.documentElement.style,n=0,i=t.length;n<i;n++)if(t[n]in e)return t[n]}var vS=mS(["transform","webkitTransform","OTransform","MozTransform","msTransform"]);function _S(t,e){if(!t)return e;e=mp(e,!0);var n=t.indexOf(e);return(t=-1===n?e:"-"+t.slice(0,n)+"-"+e).toLowerCase()}var xS=_S(mS(["webkitTransition","transition","OTransition","MozTransition","msTransition"]),"transition"),wS=_S(vS,"transform"),bS="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;"+(b.transform3dSupported?"will-change:transform;":"");function SS(t,e,n){var i,t=t.toFixed(0)+"px",e=e.toFixed(0)+"px";return b.transformSupported?(i="translate"+((i=b.transform3dSupported)?"3d":"")+"("+t+","+e+(i?",0":"")+")",n?"top:0;left:0;"+wS+":"+i+";":[["top",0],["left",0],[vS,i]]):n?"top:"+e+";left:"+t+";":[["top",e],["left",t]]}function MS(i,t,e){var n,r,o=[],a=i.get("transitionDuration"),s=i.get("backgroundColor"),l=i.get("shadowBlur"),u=i.get("shadowColor"),h=i.get("shadowOffsetX"),c=i.get("shadowOffsetY"),p=i.getModel("textStyle"),d=Ig(i,"html");return o.push("box-shadow:"+(h+"px "+c+"px "+l+"px "+u)),t&&a&&o.push((u="opacity"+(l=" "+(h=a)/2+"s "+(c="cubic-bezier(0.23,1,0.32,1)"))+",visibility"+l,e||(l=" "+h+"s "+c,u+=b.transformSupported?","+wS+l:",left"+l+",top"+l),xS+":"+u)),s&&o.push("background-color:"+s),O(["width","color","radius"],function(t){var e="border-"+t,n=mp(e),n=i.get(n);null!=n&&o.push(e+":"+n+("color"===t?"":"px"))}),o.push((r=[],t=(n=p).get("fontSize"),(a=n.getTextColor())&&r.push("color:"+a),r.push("font:"+n.getFont()),a=N(n.get("lineHeight"),Math.round(3*t/2)),t&&r.push("line-height:"+a+"px"),t=n.get("textShadowColor"),a=n.get("textShadowBlur")||0,e=n.get("textShadowOffsetX")||0,h=n.get("textShadowOffsetY")||0,t&&a&&r.push("text-shadow:"+e+"px "+h+"px "+a+"px "+t),O(["decoration","align"],function(t){var e=n.get(t);e&&r.push("text-"+t+":"+e)}),r.join(";"))),null!=d&&o.push("padding:"+vp(d).join("px ")+"px"),o.join(";")+";"}function TS(t,e,n,i,r){var o,a,s=e&&e.painter;n?(o=s&&s.getViewportRoot())&&(a=t,n=n,ge(fe,o,i,r,!0))&&ge(a,n,fe[0],fe[1]):(t[0]=i,t[1]=r,(o=s&&s.getViewportRootOffset())&&(t[0]+=o.offsetLeft,t[1]+=o.offsetTop)),t[2]=t[0]/e.getWidth(),t[3]=t[1]/e.getHeight()}IS.prototype.update=function(t){this._container||(i=this._api.getDom(),n="position",n=(e=(e=i).currentStyle||document.defaultView&&document.defaultView.getComputedStyle(e))?n?e[n]:e:null,"absolute"!==(e=i.style).position&&"absolute"!==n&&(e.position="relative"));var e,n,i=t.get("alwaysShowContent");i&&this._moveIfResized(),this._alwaysShowContent=i,this.el.className=t.get("className")||""},IS.prototype.show=function(t,e){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var n=this.el,i=n.style,r=this._styleCoord;n.innerHTML?i.cssText=bS+MS(t,!this._firstShow,this._longHide)+SS(r[0],r[1],!0)+"border-color:"+Mp(e)+";"+(t.get("extraCssText")||"")+";pointer-events:"+(this._enterable?"auto":"none"):i.display="none",this._show=!0,this._firstShow=!1,this._longHide=!1},IS.prototype.setContent=function(t,e,n,i,r){var o=this.el;if(null==t)o.innerHTML="";else{var a,s,l,u,h,c="";if(V(r)&&"item"===n.get("trigger")&&!yS(n)&&(n=n,i=i,c=V(r=r)&&"inside"!==r?(a=n.get("backgroundColor"),n=n.get("borderWidth"),i=Mp(i),r=r="left"===(r=r)?"right":"right"===r?"left":"top"===r?"bottom":"top",p=Math.max(1.5*Math.round(n),6),s="",l=wS+":",-1<I(["left","right"],r)?(s+="top:50%",l+="translateY(-50%) rotate("+(h="left"==r?-225:-45)+"deg)"):(s+="left:50%",l+="translateX(-50%) rotate("+(h="top"==r?225:45)+"deg)"),h=h*Math.PI/180,h=(u=p+n)*Math.abs(Math.cos(h))+u*Math.abs(Math.sin(h)),i=i+" solid "+n+"px;",'<div style="'+["position:absolute;width:"+p+"px;height:"+p+"px;z-index:-1;",(s+=";"+r+":-"+Math.round(100*((h-Math.SQRT2*n)/2+Math.SQRT2*n-(h-u)/2))/100+"px")+";"+l+";","border-bottom:"+i,"border-right:"+i,"background-color:"+a+";"].join("")+'"></div>'):""),V(t))o.innerHTML=t+c;else if(t){o.innerHTML="",F(t)||(t=[t]);for(var p,d=0;d<t.length;d++)yt(t[d])&&t[d].parentNode!==o&&o.appendChild(t[d]);c&&o.childNodes.length&&((p=document.createElement("div")).innerHTML=c,o.appendChild(p))}}},IS.prototype.setEnterable=function(t){this._enterable=t},IS.prototype.getSize=function(){var t=this.el;return t?[t.offsetWidth,t.offsetHeight]:[0,0]},IS.prototype.moveTo=function(t,e){var n,i;this.el&&(TS(n=this._styleCoord,this._zr,this._container,t,e),null!=n[0])&&null!=n[1]&&(i=this.el.style,O(SS(n[0],n[1]),function(t){i[t[0]]=t[1]}))},IS.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},IS.prototype.hide=function(){var t=this,e=this.el.style;e.visibility="hidden",e.opacity="0",b.transform3dSupported&&(e.willChange=""),this._show=!1,this._longHideTimeout=setTimeout(function(){return t._longHide=!0},500)},IS.prototype.hideLater=function(t){!this._show||this._inContent&&this._enterable||this._alwaysShowContent||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(ct(this.hide,this),t)):this.hide())},IS.prototype.isShow=function(){return this._show},IS.prototype.dispose=function(){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var t=this.el.parentNode;t&&t.removeChild(this.el),this.el=this._container=null};var CS=IS;function IS(t,e){if(this._show=!1,this._styleCoord=[0,0,0,0],this._enterable=!0,this._alwaysShowContent=!1,this._firstShow=!0,this._longHide=!0,b.wxa)return null;var n=document.createElement("div"),i=(n.domBelongToZr=!0,this.el=n,this._zr=t.getZr()),e=e.appendTo,e=e&&(V(e)?document.querySelector(e):yt(e)?e:k(e)&&e(t.getDom())),r=(TS(this._styleCoord,i,e,t.getWidth()/2,t.getHeight()/2),(e||t.getDom()).appendChild(n),this._api=t,this._container=e,this);n.onmouseenter=function(){r._enterable&&(clearTimeout(r._hideTimeout),r._show=!0),r._inContent=!0},n.onmousemove=function(t){var e;t=t||window.event,r._enterable||(e=i.handler,Ce(i.painter.getViewportRoot(),t,!0),e.dispatch("mousemove",t))},n.onmouseleave=function(){r._inContent=!1,r._enterable&&r._show&&r.hideLater(r._hideDelay)}}DS.prototype.update=function(t){t=t.get("alwaysShowContent");t&&this._moveIfResized(),this._alwaysShowContent=t},DS.prototype.show=function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.show(),this._show=!0},DS.prototype.setContent=function(t,e,n,i,r){var o=this,a=(R(t)&&f(""),this.el&&this._zr.remove(this.el),n.getModel("textStyle")),s=(this.el=new Ns({style:{rich:e.richTextStyles,text:t,lineHeight:22,borderWidth:1,borderColor:i,textShadowColor:a.get("textShadowColor"),fill:n.get(["textStyle","color"]),padding:Ig(n,"richText"),verticalAlign:"top",align:"left"},z:n.get("z")}),O(["backgroundColor","borderRadius","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"],function(t){o.el.style[t]=n.get(t)}),O(["textShadowBlur","textShadowOffsetX","textShadowOffsetY"],function(t){o.el.style[t]=a.get(t)||0}),this._zr.add(this.el),this);this.el.on("mouseover",function(){s._enterable&&(clearTimeout(s._hideTimeout),s._show=!0),s._inContent=!0}),this.el.on("mouseout",function(){s._enterable&&s._show&&s.hideLater(s._hideDelay),s._inContent=!1})},DS.prototype.setEnterable=function(t){this._enterable=t},DS.prototype.getSize=function(){var t=this.el,e=this.el.getBoundingRect(),t=PS(t.style);return[e.width+t.left+t.right,e.height+t.top+t.bottom]},DS.prototype.moveTo=function(t,e){var n,i,r=this.el;r&&(LS(i=this._styleCoord,this._zr,t,e),t=i[0],e=i[1],n=AS((i=r.style).borderWidth||0),i=PS(i),r.x=t+n+i.left,r.y=e+n+i.top,r.markRedraw())},DS.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},DS.prototype.hide=function(){this.el&&this.el.hide(),this._show=!1},DS.prototype.hideLater=function(t){!this._show||this._inContent&&this._enterable||this._alwaysShowContent||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(ct(this.hide,this),t)):this.hide())},DS.prototype.isShow=function(){return this._show},DS.prototype.dispose=function(){this._zr.remove(this.el)};var kS=DS;function DS(t){this._show=!1,this._styleCoord=[0,0,0,0],this._alwaysShowContent=!1,this._enterable=!0,this._zr=t.getZr(),LS(this._styleCoord,this._zr,t.getWidth()/2,t.getHeight()/2)}function AS(t){return Math.max(0,t)}function PS(t){var e=AS(t.shadowBlur||0),n=AS(t.shadowOffsetX||0),t=AS(t.shadowOffsetY||0);return{left:AS(e-n),right:AS(e+n),top:AS(e-t),bottom:AS(e+t)}}function LS(t,e,n,i){t[0]=n,t[1]=i,t[2]=t[0]/e.getWidth(),t[3]=t[1]/e.getHeight()}var OS,RS=new As({shape:{x:-1,y:-1,width:2,height:2}}),NS=(u(ES,OS=Gg),ES.prototype.init=function(t,e){var n;!b.node&&e.getDom()&&(t=t.getComponent("tooltip"),n=this._renderMode="auto"===(n=t.get("renderMode"))?b.domSupported?"html":"richText":n||"html",this._tooltipContent="richText"===n?new kS(e):new CS(e,{appendTo:t.get("appendToBody",!0)?"body":t.get("appendTo",!0)}))},ES.prototype.render=function(t,e,n){!b.node&&n.getDom()&&(this.group.removeAll(),this._tooltipModel=t,this._ecModel=e,this._api=n,(e=this._tooltipContent).update(t),e.setEnterable(t.get("enterable")),this._initGlobalListener(),this._keepShow(),"richText"!==this._renderMode&&t.get("transitionDuration")?ry(this,"_updatePosition",50,"fixRate"):oy(this,"_updatePosition"))},ES.prototype._initGlobalListener=function(){var i=this._tooltipModel.get("triggerOn");$b("itemTooltip",this._api,ct(function(t,e,n){"none"!==i&&(0<=i.indexOf(t)?this._tryShow(e,n):"leave"===t&&this._hide(n))},this))},ES.prototype._keepShow=function(){var t,e=this._tooltipModel,n=this._ecModel,i=this._api,r=e.get("triggerOn");null!=this._lastX&&null!=this._lastY&&"none"!==r&&"click"!==r&&(t=this,clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){i.isDisposed()||t.manuallyShowTip(e,n,i,{x:t._lastX,y:t._lastY,dataByCoordSys:t._lastDataByCoordSys})}))},ES.prototype.manuallyShowTip=function(t,e,n,i){var r,o,a,s;i.from!==this.uid&&!b.node&&n.getDom()&&(r=BS(i,n),this._ticket="",s=i.dataByCoordSys,(o=function(n,t,e){var i=Ro(n).queryOptionMap,r=i.keys()[0];if(r&&"series"!==r){var o,t=Eo(t,r,i.get(r),{useDefault:!1,enableAll:!1,enableNone:!1}).models[0];if(t)if(e.getViewOfComponentModel(t).group.traverse(function(t){var e=D(t).tooltipConfig;if(e&&e.name===n.name)return o=t,!0}),o)return{componentMainType:r,componentIndex:t.componentIndex,el:o}}}(i,e,n))?((a=o.el.getBoundingRect().clone()).applyTransform(o.el.transform),this._tryShow({offsetX:a.x+a.width/2,offsetY:a.y+a.height/2,target:o.el,position:i.position,positionDefault:"bottom"},r)):i.tooltip&&null!=i.x&&null!=i.y?((a=RS).x=i.x,a.y=i.y,a.update(),D(a).tooltipConfig={name:null,option:i.tooltip},this._tryShow({offsetX:i.x,offsetY:i.y,target:a},r)):s?this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,dataByCoordSys:s,tooltipOption:i.tooltipOption},r):null!=i.seriesIndex?this._manuallyAxisShowTip(t,e,n,i)||(a=(o=rS(i,e)).point[0],s=o.point[1],null!=a&&null!=s&&this._tryShow({offsetX:a,offsetY:s,target:o.el,position:i.position,positionDefault:"bottom"},r)):null!=i.x&&null!=i.y&&(n.dispatchAction({type:"updateAxisPointer",x:i.x,y:i.y}),this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,target:n.getZr().findHover(i.x,i.y).target},r)))},ES.prototype.manuallyHideTip=function(t,e,n,i){var r=this._tooltipContent;this._tooltipModel&&r.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=this._lastDataByCoordSys=null,i.from!==this.uid&&this._hide(BS(i,n))},ES.prototype._manuallyAxisShowTip=function(t,e,n,i){var r=i.seriesIndex,o=i.dataIndex,a=e.getComponent("axisPointer").coordSysAxesInfo;if(null!=r&&null!=o&&null!=a){a=e.getSeriesByIndex(r);if(a){e=zS([a.getData().getItemModel(o),a,(a.coordinateSystem||{}).model],this._tooltipModel);if("axis"===e.get("trigger"))return n.dispatchAction({type:"updateAxisPointer",seriesIndex:r,dataIndex:o,position:i.position}),!0}}},ES.prototype._tryShow=function(t,e){var n,i,r,o=t.target;this._tooltipModel&&(this._lastX=t.offsetX,this._lastY=t.offsetY,(n=t.dataByCoordSys)&&n.length?this._showAxisTooltip(n,t):o?"legend"!==D(o).ssrType&&(Ny(o,function(t){return null!=D(t).dataIndex?(i=t,1):null!=D(t).tooltipConfig&&(r=t,1)},!(this._lastDataByCoordSys=null)),i?this._showSeriesItemTooltip(t,i,e):r?this._showComponentItemTooltip(t,r,e):this._hide(e)):(this._lastDataByCoordSys=null,this._hide(e)))},ES.prototype._showOrMove=function(t,e){t=t.get("showDelay");e=ct(e,this),clearTimeout(this._showTimout),0<t?this._showTimout=setTimeout(e,t):e()},ES.prototype._showAxisTooltip=function(t,e){var u=this._ecModel,h=this._tooltipModel,n=[e.offsetX,e.offsetY],i=zS([e.tooltipOption],h),c=this._renderMode,p=[],d=_g("section",{blocks:[],noHeader:!0}),f=[],g=new kg,r=(O(t,function(t){O(t.dataByAxis,function(r){var o,a,s=u.getComponent(r.axisDim+"Axis",r.axisIndex),l=r.value;s&&null!=l&&(o=Bb(l,s.axis,u,r.seriesDataIndices,r.valueLabelOpt),a=_g("section",{header:o,noHeader:!Ct(o),sortBlocks:!0,blocks:[]}),d.blocks.push(a),O(r.seriesDataIndices,function(t){var e,n=u.getSeriesByIndex(t.seriesIndex),t=t.dataIndexInside,i=n.getDataParams(t);i.dataIndex<0||(i.axisDim=r.axisDim,i.axisIndex=r.axisIndex,i.axisType=r.axisType,i.axisId=r.axisId,i.axisValue=b_(s.axis,{value:l}),i.axisValueLabel=o,i.marker=g.makeTooltipMarker("item",Mp(i.color),c),(e=(t=wf(n.formatTooltip(t,!0,null))).frag)&&(n=zS([n],h).get("valueFormatter"),a.blocks.push(n?L({valueFormatter:n},e):e)),t.text&&f.push(t.text),p.push(i))}))})}),d.blocks.reverse(),f.reverse(),e.position),e=i.get("order"),e=Mg(d,g,c,e,u.get("useUTC"),i.get("textStyle")),e=(e&&f.unshift(e),"richText"===c?"\n\n":"<br/>"),o=f.join(e);this._showOrMove(i,function(){this._updateContentNotChangedOnAxis(t,p)?this._updatePosition(i,r,n[0],n[1],this._tooltipContent,p):this._showTooltipContent(i,o,p,Math.random()+"",n[0],n[1],r,null,g)})},ES.prototype._showSeriesItemTooltip=function(t,e,n){var i,r,o,a,s,l=this._ecModel,e=D(e),u=e.seriesIndex,h=l.getSeriesByIndex(u),c=e.dataModel||h,p=e.dataIndex,e=e.dataType,d=c.getData(e),f=this._renderMode,g=t.positionDefault,y=zS([d.getItemModel(p),c,h&&(h.coordinateSystem||{}).model],this._tooltipModel,g?{position:g}:null),h=y.get("trigger");null!=h&&"item"!==h||(i=c.getDataParams(p,e),r=new kg,i.marker=r.makeTooltipMarker("item",Mp(i.color),f),g=wf(c.formatTooltip(p,!1,e)),h=y.get("order"),e=y.get("valueFormatter"),o=g.frag,a=o?Mg(e?L({valueFormatter:e},o):o,r,f,h,l.get("useUTC"),y.get("textStyle")):g.text,s="item_"+c.name+"_"+p,this._showOrMove(y,function(){this._showTooltipContent(y,a,i,s,t.offsetX,t.offsetY,t.position,t.target,r)}),n({type:"showTip",dataIndexInside:p,dataIndex:d.getRawIndex(p),seriesIndex:u,from:this.uid}))},ES.prototype._showComponentItemTooltip=function(e,n,t){var i="html"===this._renderMode,r=D(n),o=r.tooltipConfig.option||{},a=o.encodeHTMLContent,a=(V(o)&&(o={content:o,formatter:o},a=!0),a&&i&&o.content&&((o=y(o)).content=_e(o.content)),[o]),i=this._ecModel.getComponent(r.componentMainType,r.componentIndex),r=(i&&a.push(i),a.push({formatter:o.content}),e.positionDefault),s=zS(a,this._tooltipModel,r?{position:r}:null),l=s.get("content"),u=Math.random()+"",h=new kg;this._showOrMove(s,function(){var t=y(s.get("formatterParams")||{});this._showTooltipContent(s,l,t,u,e.offsetX,e.offsetY,e.position,n,h)}),t({type:"showTip",from:this.uid})},ES.prototype._showTooltipContent=function(n,t,i,e,r,o,a,s,l){var u,h,c,p,d;this._ticket="",n.get("showContent")&&n.get("show")&&((u=this._tooltipContent).setEnterable(n.get("enterable")),h=n.get("formatter"),a=a||n.get("position"),t=t,c=this._getNearestPoint([r,o],i,n.get("trigger"),n.get("borderColor")).color,h&&(t=V(h)?(p=n.ecModel.get("useUTC"),t=h,bp(t=(d=F(i)?i[0]:i)&&d.axisType&&0<=d.axisType.indexOf("time")?tp(d.axisValue,t,p):t,i,!0)):k(h)?(d=ct(function(t,e){t===this._ticket&&(u.setContent(e,l,n,c,a),this._updatePosition(n,a,r,o,u,i,s))},this),this._ticket=e,h(i,e,d)):h),u.setContent(t,l,n,c,a),u.show(n,c),this._updatePosition(n,a,r,o,u,i,s))},ES.prototype._getNearestPoint=function(t,e,n,i){return"axis"===n||F(e)?{color:i||("html"===this._renderMode?"#fff":"none")}:F(e)?void 0:{color:i||e.color||e.borderColor}},ES.prototype._updatePosition=function(t,e,n,i,r,o,a){var s,l=this._api.getWidth(),u=this._api.getHeight(),h=(e=e||t.get("position"),r.getSize()),c=t.get("align"),p=t.get("verticalAlign"),d=a&&a.getBoundingRect().clone();a&&d.applyTransform(a.transform),F(e=k(e)?e([n,i],o,r.el,d,{viewSize:[l,u],contentSize:h.slice()}):e)?(n=to(e[0],l),i=to(e[1],u)):R(e)?((o=e).width=h[0],o.height=h[1],n=(o=Pp(o,{width:l,height:u})).x,i=o.y,p=c=null):i=(n=(s=V(e)&&a?function(t,e,n,i){var r=n[0],o=n[1],a=Math.ceil(Math.SQRT2*i)+8,s=0,l=0,u=e.width,h=e.height;switch(t){case"inside":s=e.x+u/2-r/2,l=e.y+h/2-o/2;break;case"top":s=e.x+u/2-r/2,l=e.y-o-a;break;case"bottom":s=e.x+u/2-r/2,l=e.y+h+a;break;case"left":s=e.x-r-a,l=e.y+h/2-o/2;break;case"right":s=e.x+u+a,l=e.y+h/2-o/2}return[s,l]}(e,d,h,t.get("borderWidth")):function(t,e,n,i,r,o,a){var n=n.getSize(),s=n[0],n=n[1];null!=o&&(i<t+s+o+2?t-=s+o:t+=o);null!=a&&(r<e+n+a?e-=n+a:e+=a);return[t,e]}(n,i,r,l,u,c?null:20,p?null:20))[0],s[1]),c&&(n-=FS(c)?h[0]/2:"right"===c?h[0]:0),p&&(i-=FS(p)?h[1]/2:"bottom"===p?h[1]:0),yS(t)&&(o=n,a=i,e=l,d=u,c=(c=r).getSize(),p=c[0],c=c[1],o=Math.min(o+p,e)-p,a=Math.min(a+c,d)-c,o=Math.max(o,0),a=Math.max(a,0),n=(s=[o,a])[0],i=s[1]),r.moveTo(n,i)},ES.prototype._updateContentNotChangedOnAxis=function(n,o){var t=this._lastDataByCoordSys,a=this._cbParamsList,s=!!t&&t.length===n.length;return s&&O(t,function(t,e){var t=t.dataByAxis||[],r=(n[e]||{}).dataByAxis||[];(s=s&&t.length===r.length)&&O(t,function(t,e){var e=r[e]||{},n=t.seriesDataIndices||[],i=e.seriesDataIndices||[];(s=s&&t.value===e.value&&t.axisType===e.axisType&&t.axisId===e.axisId&&n.length===i.length)&&O(n,function(t,e){e=i[e];s=s&&t.seriesIndex===e.seriesIndex&&t.dataIndex===e.dataIndex}),a&&O(t.seriesDataIndices,function(t){var t=t.seriesIndex,e=o[t],t=a[t];e&&t&&t.data!==e.data&&(s=!1)})})}),this._lastDataByCoordSys=n,this._cbParamsList=o,!!s},ES.prototype._hide=function(t){this._lastDataByCoordSys=null,t({type:"hideTip",from:this.uid})},ES.prototype.dispose=function(t,e){!b.node&&e.getDom()&&(oy(this,"_updatePosition"),this._tooltipContent.dispose(),tS("itemTooltip",e))},ES.type="tooltip",ES);function ES(){var t=null!==OS&&OS.apply(this,arguments)||this;return t.type=ES.type,t}function zS(t,e,n){for(var i=e.ecModel,r=n?(r=new Lc(n,i,i),new Lc(e.option,r,i)):e,o=t.length-1;0<=o;o--){var a=t[o];(a=a&&(V(a=a instanceof Lc?a.get("tooltip",!0):a)?{formatter:a}:a))&&(r=new Lc(a,r,i))}return r}function BS(t,e){return t.dispatchAction||ct(e.dispatchAction,e)}function FS(t){return"center"===t||"middle"===t}A_(function(t){A_(pS),t.registerComponentModel(fS),t.registerComponentView(NS),t.registerAction({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},Ft),t.registerAction({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},Ft)}),A_(K1);var VS={value:"eq","<":"lt","<=":"lte",">":"gt",">=":"gte","=":"eq","!=":"ne","<>":"ne"},HS=(GS.prototype.evaluate=function(t){var e=typeof t;return V(e)?this._condVal.test(t):!!H(e)&&this._condVal.test(t+"")},GS);function GS(t){null==(this._condVal=V(t)?new RegExp(t):_t(t)?t:null)&&f("")}US.prototype.evaluate=function(){return this.value};var WS=US;function US(){}YS.prototype.evaluate=function(){for(var t=this.children,e=0;e<t.length;e++)if(!t[e].evaluate())return!1;return!0};var XS=YS;function YS(){}ZS.prototype.evaluate=function(){for(var t=this.children,e=0;e<t.length;e++)if(t[e].evaluate())return!0;return!1};var qS=ZS;function ZS(){}KS.prototype.evaluate=function(){return!this.child.evaluate()};var jS=KS;function KS(){}QS.prototype.evaluate=function(){for(var t=!!this.valueParser,e=(0,this.getValue)(this.valueGetterParam),n=t?this.valueParser(e):null,i=0;i<this.subCondList.length;i++)if(!this.subCondList[i].evaluate(t?n:e))return!1;return!0};var $S=QS;function QS(){}function JS(t,e){if(!0===t||!1===t)return(n=new WS).value=t,n;var n;if(eM(t)||f(""),t.and)return tM("and",t,e);if(t.or)return tM("or",t,e);if(t.not)return n=e,eM(o=(o=t).not)||f(""),(l=new jS).child=JS(o,n),l.child||f(""),l;for(var i=t,r=e,o=r.prepareGetValue(i),a=[],s=ht(i),l=i.parser,u=l?Ef(l):null,h=0;h<s.length;h++){var c,p=s[h];"parser"===p||r.valueGetterAttrMap.get(p)||(c=Bt(VS,p)?VS[p]:p,p=i[p],p=u?u(p):p,(c=function(t,e){return"eq"===t||"ne"===t?new Gf("eq"===t,e):Bt(zf,t)?new Bf(t,e):null}(c,p)||"reg"===c&&new HS(p))||f(""),a.push(c))}return a.length||f(""),(l=new $S).valueGetterParam=o,l.valueParser=u,l.getValue=r.getValue,l.subCondList=a,l}function tM(t,e,n){e=e[t],F(e)||f(""),e.length||f(""),t=new("and"===t?XS:qS);return t.children=B(e,function(t){return JS(t,n)}),t.children.length||f(""),t}function eM(t){return R(t)&&!st(t)}iM.prototype.evaluate=function(){return this._cond.evaluate()};var nM=iM;function iM(t,e){this._cond=JS(t,e)}var rM={type:"echarts:filter",transform:function(t){for(var e,n,i=t.upstream,r=(t=t.config,n={valueGetterAttrMap:E({dimension:!0}),prepareGetValue:function(t){var e=t.dimension,t=(Bt(t,"dimension")||f(""),i.getDimensionInfo(e));return t||f(""),{dimIdx:t.index}},getValue:function(t){return i.retrieveValueFromItem(e,t.dimIdx)}},new nM(t,n)),o=[],a=0,s=i.count();a<s;a++)e=i.getRawDataItem(a),r.evaluate()&&o.push(e);return{data:o}}},oM={type:"echarts:sort",transform:function(t){for(var a=t.upstream,t=t.config,t=_o(t),s=(t.length||f(""),[]),t=(O(t,function(t){var e=t.dimension,n=t.order,i=t.parser,t=t.incomparable,e=(null==e&&f(""),"asc"!==n&&"desc"!==n&&f(""),t&&"min"!==t&&"max"!==t&&f(""),"asc"!==n&&"desc"!==n&&f(""),a.getDimensionInfo(e)),r=(e||f(""),i?Ef(i):null);i&&!r&&f(""),s.push({dimIdx:e.index,parser:r,comparator:new Vf(n,t)})}),a.sourceFormat),e=(t!==Xp&&t!==Yp&&f(""),[]),n=0,i=a.count();n<i;n++)e.push(a.getRawDataItem(n));return e.sort(function(t,e){for(var n=0;n<s.length;n++){var i=s[n],r=a.retrieveValueFromItem(t,i.dimIdx),o=a.retrieveValueFromItem(e,i.dimIdx),i=(i.parser&&(r=i.parser(r),o=i.parser(o)),i.comparator.evaluate(r,o));if(0!==i)return i}return 0}),{data:e}}};A_(function(t){t.registerTransform(rM),t.registerTransform(oM)}),t.Axis=Dc,t.ChartView=qg,t.ComponentModel=g,t.ComponentView=Gg,t.List=fv,t.Model=Lc,t.PRIORITY=Hy,t.SeriesModel=Rg,t.color=_i,t.connect=function(e){var t;return F(e)&&(t=e,e=null,O(t,function(t){null!=t.group&&(e=t.group)}),e=e||"g_"+w0++,O(t,function(t){t.group=e})),_0[e]=!0,e},t.dataTool={},t.dependencies={zrender:"5.6.1"},t.disConnect=Vy,t.disconnect=S0,t.dispose=function(t){V(t)?t=v0[t]:t instanceof r0||(t=M0(t)),t instanceof r0&&!t.isDisposed()&&t.dispose()},t.env=b,t.extendChartView=function(t){return t=qg.extend(t),qg.registerClass(t),t},t.extendComponentModel=function(t){return t=g.extend(t),g.registerClass(t),t},t.extendComponentView=function(t){return t=Gg.extend(t),Gg.registerClass(t),t},t.extendSeriesModel=function(t){return t=Rg.extend(t),Rg.registerClass(t),t},t.format=Ic,t.getCoordinateSystemDimensions=function(t){if(t=xd.get(t))return t.getDimensionsInfo?t.getDimensionsInfo():t.dimensions.slice()},t.getInstanceByDom=M0,t.getInstanceById=function(t){return v0[t]},t.getMap=function(t){var e=Im.getMap;return e&&e(t)},t.graphic=Zc,t.helper=Gy,t.init=function(t,e,n){var i=!(n&&n.ssr);if(i){var r=M0(t);if(r)return r}return(r=new r0(t,e,n)).id="ec_"+x0++,v0[r.id]=r,i&&zo(t,b0,r.id),t0(r),Cm.trigger("afterinit",r),r},t.innerDrawElementOnCanvas=_m,t.matrix=Ve,t.number=$o,t.parseGeoJSON=q_,t.parseGeoJson=q_,t.registerAction=P0,t.registerCoordinateSystem=L0,t.registerLayout=O0,t.registerLoading=z0,t.registerLocale=Gc,t.registerMap=B0,t.registerPostInit=k0,t.registerPostUpdate=D0,t.registerPreprocessor=C0,t.registerProcessor=I0,t.registerTheme=T0,t.registerTransform=F0,t.registerUpdateLifecycle=A0,t.registerVisual=R0,t.setCanvasCreator=function(t){C({createCanvas:t})},t.setPlatformAPI=C,t.throttle=iy,t.time=qh,t.use=A_,t.util=Sc,t.vector=re,t.version="5.6.0",t.zrUtil=Ht,t.zrender=$r});