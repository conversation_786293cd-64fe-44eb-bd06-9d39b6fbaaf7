<template>
	<div class="w-100% h-100%">
		<div class="w-100% h-300rpx">
			<BaseECharts :options="options" />
		</div>
	</div>
</template>

<script setup>
import { ref } from "vue";
import BaseECharts from "./BaseEcharts.vue";

const options = ref({
	grid: {
		left: "3%",
		right: "6%",
		bottom: "3%",
		top: "8%",
		containLabel: true,
	},
	tooltip: {
		trigger: "axis",
		confine: true,
	},
	xAxis: {
		boundaryGap: false,
		type: "category",
		axisLabel: {
			color: "#999999",
		},
		axisTick: {
			show: false,
		},
		axisLine: {
			show: false,
		},
		data: ["03:00", "06:00", "07:00", "12:00", "15:00", "18:00", "21:00"],
	},
	yAxis: {
		type: "value",
		axisLabel: {
			color: "#999999",
		},
	},
	series: [
		{
			smooth: true,
			symbolSize: 0,
			lineStyle: {
				normal: {
					color: "#8dadfa", // 线条颜色
				},
			},
			data: [150, 224, 218, 135, 147, 113, 211],
			type: "line",
		},
	],
});

setTimeout(() => {
	options.value.series[0].data = [15, 24, 18, 13, 47, 13, 21];
}, 3000);
</script>

<style lang="scss" scoped></style>
