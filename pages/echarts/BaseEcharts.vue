<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick, watch } from "vue";
import lEchart from "./l-echart/l-echart.vue";
import { debounce } from "@/utils";
// #ifdef MP-WEIXIN
const echarts = require("./static/echarts.min");
// #endif
// #ifndef MP-WEIXIN
import * as echarts from "echarts";
// #endif

const props = defineProps({
	options: { type: Object, default: () => ({}) },
	width: { type: [String, Number], default: "100%" },
	height: { type: [String, Number], default: "500rpx" },
	empty: { type: Boolean, default: false },
	emptyText: { type: String, default: "暂无数据" },
});

const chartRef = ref(null);
let myChart = null;
const loading = ref(false);
const empty = ref(false);
let pendingOption = null; // 缓存option

const showLoading = () => {
	loading.value = true;
	if (myChart && myChart.showLoading) myChart.showLoading();
};
const hideLoading = () => {
	loading.value = false;
	if (myChart && myChart.hideLoading) myChart.hideLoading();
};

const setOption = (option) => {
	showLoading();
	empty.value = false;
	if (!myChart) {
		pendingOption = option;
		return;
	}

	try {
		myChart.setOption(option, true);
		// 判断option是否有有效数据
		const hasData = option && option.series && Array.isArray(option.series) && option.series.some((s) => Array.isArray(s.data) && s.data.length > 0);
		if (!hasData) {
			empty.value = true;
		}
	} catch (e) {
		empty.value = true;
	} finally {
		hideLoading();
	}
};
const clear = () => {
	if (myChart && myChart.clear) myChart.clear();
};
const resizeChart = debounce(() => {
	if (myChart && myChart.resize) myChart.resize();
}, 200);
const dispose = () => {
	if (myChart && myChart.dispose) myChart.dispose();
	myChart = null;
};

const init = async (echartsInstance = echarts) => {
	if (!chartRef.value) return;
	showLoading();
	myChart = await chartRef.value.init(echartsInstance);
	if (pendingOption) {
		setOption(pendingOption);
		pendingOption = null;
	}
	hideLoading();
};

watch(
	() => props.options,
	(newVal) => {
		setOption(newVal);
	},
	{ immediate: true }
);

onMounted(() => {
	nextTick(() => {
		setTimeout(() => init(), 300);
		if (typeof window !== "undefined") {
			window.addEventListener("resize", resizeChart);
		}
	});
});

onBeforeUnmount(() => {
	dispose();
	if (typeof window !== "undefined") {
		window.removeEventListener("resize", resizeChart);
	}
});

defineExpose({ setOption, clear });
</script>

<template>
	<view :style="{ width: width, height: height, position: 'relative' }">
		<l-echart ref="chartRef" />
		<!-- <view v-if="empty" style="position: absolute; left: 0; top: 0; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; background: rgba(255, 255, 255, 0.8); z-index: 10">
			<text>{{ emptyText }}</text>
		</view> -->
	</view>
</template>
