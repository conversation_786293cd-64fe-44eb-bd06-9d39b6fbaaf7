<!-- z-paging聊天输入框 -->

<template>
	<view class="chat-input-bar-container">
		<view class="chat-input-bar w-full">
			<view class="chat-input-container transition-300" :style="{ width: sendEnabled ? `${750 - 20 - 20 - 54 - 20 - 110 - 20}rpx` : `${750 - 20 - 20 - 54 - 20 - 54 - 20}rpx` }">
				<textarea
					:focus="focus"
					class="chat-input w-full"
					v-model="msg"
					:adjust-position="false"
					confirm-type="send"
					type="text"
					placeholder="请输入内容"
					@confirm="sendClick"
					@focus="inputFocus"
					cols="1"
					rows="1"
					auto-height
					:show-confirm-bar="false"
				></textarea>
			</view>
			<view class="emoji-container">
				<app-image src="@/pages/message/static/icon_emoji_hei.png" size="48" @click="emojiChange"></app-image>
			</view>
			<view class="transition-300 overflow-hidden" :style="{ width: sendEnabled ? `${110 + 20}rpx` : `${54 + 20}rpx` }">
				<template v-if="sendEnabled">
					<view class="w-110 h-54 ml-20 my-10">
						<uv-button type="primary" :customStyle="{ height: '54rpx', borderRadius: '15rpx' }" :disabled="!sendEnabled" @click="sendClick">
							<view class="text-24">发送</view>
						</uv-button>
					</view>
				</template>
				<template v-else>
					<view class="emoji-container">
						<app-image src="@/pages/message/static/icon_gengduo_hei.png" size="48" @click="menuChange"></app-image>
					</view>
				</template>
			</view>
		</view>
		<view class="emoji-panel-container" :style="[{ height: keyType === 'emoji' ? '400rpx' : '0px', borderTopWidth: keyType === 'emoji' ? '1rpx' : '0px' }]">
			<scroll-view scroll-y style="height: 100%; flex: 1">
				<view class="emoji-panel">
					<text class="emoji-panel-text" v-for="(item, index) in emojisArr" :key="index" @click="emojiClick(item)">
						{{ item }}
					</text>
				</view>
			</scroll-view>
		</view>
		<view class="menu-panel-container" :style="[{ height: keyType === 'menu' ? '400rpx' : '0px', borderTopWidth: keyType === 'menu' ? '1rpx' : '0px' }]">
			<view class="w-full pt-30 px-6">
				<view class="flex flex-wrap">
					<view class="mx-40 my-20">
						<view class="w-104 flex flex-col">
							<app-image src="@/pages/message/static/icon_xiangce.png" size="104" class="ac-op" mode="" @click="chooseMediaAlbum"></app-image>
							<view class="mt-16 text-24 font-500 text-center">相册</view>
						</view>
					</view>

					<view class="mx-40 my-20">
						<view class="w-104 flex flex-col">
							<app-image src="@/pages/message/static/icon_zhaoxiang.png" size="104" class="ac-op" mode="" @click="chooseMediaCamera"></app-image>
							<view class="mt-16 text-24 font-500 text-center">拍照</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import emojiCodes from './emojiCodes';
export default {
	name: 'chat-input-bar',
	props: {
		disabled: {
			type: Boolean,
			default: false
		}
	},
	emit: ['send', 'chooseMediaType'],
	data() {
		return {
			msg: '',

			// 表情数组
			emojisArr: [],
			// 当前input focus
			focus: false,
			// 当前表情/键盘点击后的切换类型，为空字符串代表展示表情logo但是不展示表情面板
			keyType: ''
		};
	},
	computed: {
		sendEnabled() {
			return !this.disabled && this.msg.length;
		}
	},
	mounted() {
		this.emojisArr = emojiCodes;
	},
	methods: {
		// 更新了键盘高度
		updateKeyboardHeightChange(res) {
			if (res.height > 0) {
				this.keyType = '';
			}
		},
		// 用户尝试隐藏键盘
		hidedKeyboard() {
			if (this.keyType) {
				this.keyType = '';
			}
		},
		// 点击了切换表情面板/键盘
		emojiChange() {
			this.$emit('keyTypeChange', this.keyType);
			if (this.keyType === 'emoji') {
				// 点击了键盘，展示键盘
				this.focus = true;
			} else {
				// 点击了切换表情面板
				this.focus = false;
				// 隐藏键盘
				uni.hideKeyboard();
			}
			this.keyType = this.keyType === 'emoji' ? '' : 'emoji';
		},
		// 点击更多
		menuChange() {
			this.$emit('keyTypeChange', this.keyType);
			if (this.keyType === 'menu') {
				// 点击了键盘，展示键盘
				// this.focus = true;
			} else {
				// 点击了切换表情面板
				this.focus = false;
				// 隐藏键盘
				uni.hideKeyboard();
			}
			this.keyType = this.keyType === 'menu' ? '' : 'menu';
		},

		// 点击了某个表情，将其插入输入内容中
		emojiClick(text) {
			this.msg += text;
		},
		inputFocus() {
			if (this.keyType === 'menu') {
				this.menuChange();
			}
		},

		chooseMediaAlbum() {
			this.$emit('chooseMediaType', 'album');
		},

		chooseMediaCamera() {
			this.$emit('chooseMediaType', 'camera');
		},

		// 点击了发送按钮
		sendClick() {
			if (!this.sendEnabled) return;
			this.$emit('send', this.msg);
			this.msg = '';
		}
	}
};
</script>

<style scoped>
.transition-300 {
	transition: all 300ms;
}
.chat-input-bar {
	display: flex;
	flex-direction: row;
	align-items: center;
	border-top: solid 1px #f5f5f5;
	background-color: #ffffff;

	padding: 10rpx 20rpx;
}
.chat-input-container {
	padding: 15rpx 20rpx;
	background-color: white;
	min-height: 68rpx;
	border-radius: 34rpx;
	background: #f2f6fb;
}
.chat-input {
	font-size: 28rpx;
}
.emoji-container {
	width: 54rpx;
	height: 54rpx;
	margin: 10rpx 0rpx 10rpx 20rpx;
	display: flex;
	align-items: center;
}
.emoji-img {
	width: 54rpx;
	height: 54rpx;
}
.chat-input-send {
	background-color: #007aff;
	margin: 10rpx 10rpx 10rpx 20rpx;
	border-radius: 10rpx;
	width: 110rpx;
	height: 60rpx;
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	justify-content: center;
	align-items: center;
}
.chat-input-send-disabled {
	background-color: #bbbbbb;
}
.chat-input-send-text {
	color: white;
	font-size: 26rpx;
}
.emoji-panel-container {
	background-color: #ffffff;
	overflow: hidden;
	transition-property: height;
	transition-duration: 0.15s;
	/* #ifndef APP-NVUE */
	will-change: height;
	/* #endif */
	border-top: solid 1rpx #e9edf1;
	border-top-width: 0rpx;
}
.menu-panel-container {
	background-color: #ffffff;
	overflow: hidden;
	transition-property: height;
	transition-duration: 0.15s;
	/* #ifndef APP-NVUE */
	will-change: height;
	/* #endif */
	border-top: solid 1rpx #e9edf1;
	border-top-width: 0rpx;
}
.emoji-panel {
	font-size: 30rpx;
	/* #ifndef APP-NVUE */
	display: flex;
	/* #endif */
	flex-direction: row;
	flex-wrap: wrap;
	padding-right: 10rpx;
	padding-left: 15rpx;
	padding-bottom: 10rpx;
}
.emoji-panel-text {
	font-size: 50rpx;
	margin-left: 15rpx;
	margin-top: 20rpx;
}
</style>
