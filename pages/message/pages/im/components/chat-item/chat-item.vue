<script setup>
import { ref, reactive } from "vue";

import { useUserStore } from "@/store";

import { canENV, route } from "@/common/utils";

import useConvert from "@/common/useConvert";

import chatImage from "../chat-image/chat-image.vue";

import dayjs from "dayjs";
import isToday from "dayjs/plugin/isToday";
import { logo } from "@/common/images";

dayjs.extend(isToday);

const { divTenThousand } = useConvert();

const userStore = useUserStore();

const _data = reactive({});

const _props = defineProps({
	item: {
		type: Object,
		default: () => ({}),
	},
	index: {
		type: Number,
		default: -1,
	},
	shopLogo: {
		type: String,
		default: "",
	},
	shopId: {
		type: String,
		default: "",
	},
});

const isNotMine = (message) => {
	return message.sender.senderType === "SHOP_ADMIN" || message.sender.senderType === "PLATFORM_ADMIN";
};

function getMsgTime(itme) {
	if (!itme) return "";

	const dateDay = dayjs(itme);

	return dateDay.isToday() ? dateDay.format("HH:mm") : dateDay.format("YYYY/MM/DD HH:mm");
}

function getProductInfo(item) {
	const productMsg = item.message;
	if (!productMsg) {
		return { id: "", name: "未正确获取信息", salePrices: [], pic: "", price: { estimate: 0 } };
	}
	return JSON.parse(productMsg);
}

function goOrder(item) {
	const order = getProductInfo(item);
	if (!order.no) return;

	route("/pages/order/pages/orderDetailIndex/orderDetailIndex", {
		orderNo: order.no,
	});
}

function goGoods(item) {
	const product = getProductInfo(item);

	if (!product.id) return;

	const productIdArr = product.id.split(":");

	let shopId = _data.shopId;
	let productId = "";

	if (productIdArr.length > 1) {
		shopId = productIdArr[0];
		productId = productIdArr[1];
	} else {
		productId = productIdArr[0];
	}

	if (shopId && productId && shopId !== "0") {
		if (Number(product.integralGoods) === 1) {
			route("/pages/goods/pages/productDetailsIntegral/productDetailsIntegral", {
				productId,
			});
		} else {
			route("/pages/goods/pages/productDetails/productDetails", {
				productId,
				shopId,
			});
		}
	} else {
		uni.showToast({
			title: "商品信息异常",
			icon: "none",
		});
	}
}
</script>

<template>
	<view class="chat-item w-full px-30" :class="isNotMine(item) ? '' : 'is-mine'">
		<view class="w-full h-full text-#989898 py-10 flex flex-center text-22 font-500">{{ getMsgTime(item.sendTime) }}</view>
		<view class="chat-item-body flex py-20">
			<template v-if="item.sender.senderUserInfo">
				<view class="flex-shrink-0">
					<template v-if="item.sender.senderUserInfo.avatar">
						<app-image :src="item.sender.senderUserInfo.avatar" size="84" rd="100%"></app-image>
					</template>
					<template v-else>
						<app-image :src="logo" size="84" rd="100%"></app-image>
					</template>
				</view>
			</template>
			<template v-if="item.sender.senderShopInfo">
				<view class="flex-shrink-0">
					<template v-if="item.sender.senderShopInfo.avatar">
						<app-image :src="item.sender.senderShopInfo.avatar" size="84" rd="100%"></app-image>
					</template>
					<template v-else>
						<app-image :src="logo" size="84" rd="100%"></app-image>
					</template>
				</view>
			</template>
			<view class="flex-1 mx-16 chat-item-content">
				<template v-if="item.messageType === 'TEXT'">
					<view class="text-content">{{ item.message }}</view>
				</template>
				<template v-if="item.messageType === 'IMAGE'">
					<view class="image-content">
						<chatImage :srcUrl="item.message"></chatImage>
					</view>
				</template>
				<template v-if="item.messageType === 'PRODUCT'">
					<view class="product-content" @click="goGoods(item)">
						<app-image :src="getProductInfo(item).pic" width="400" rd="20" mode="widthFix"></app-image>
						<view class="w-full items-center pt-10">
							<view class="text-26 font-500">{{ getProductInfo(item).name || "暂无商品信息" }}</view>
							<template v-if="getProductInfo(item).h5">
								<view class="text-28 text-#FC3F33 text-right" v-if="getProductInfo(item).price">￥{{ divTenThousand(getProductInfo(item).price.estimate).toFixed(2) }}起</view>
							</template>
							<template v-else>
								<view class="text-28 text-#FC3F33 text-right">￥{{ String(getProductInfo(item).price.estimate).split("~")[0] }}起</view>
							</template>
						</view>
					</view>
				</template>
				<template v-if="item.messageType === 'ORDER'">
					<view class="order-content" @click="goOrder(item)">
						<app-image :src="getProductInfo(item).pic" width="400" rd="20" mode="widthFix"></app-image>
						<view class="w-full items-center mt-10">
							<view class="text-24">订单号：{{ getProductInfo(item).no || "" }}</view>
							<view class="text-26 mt-10 font-500">{{ getProductInfo(item).name || "暂无商品信息" }}</view>
							<view class="text-28 text-#FC3F33 text-right">实付：￥{{ getProductInfo(item).amountRealPay }}</view>
						</view>
					</view>
				</template>
			</view>
		</view>
	</view>
</template>

<style lang="scss" scoped>
.chat-item {
	.chat-item-content {
		display: flex;
	}

	.text-content {
		border-radius: 30rpx;
		background: #fff;
		width: fit-content;
		max-width: 490rpx;
		padding: 30rpx 20rpx;
		color: #18181b;
		font-weight: 500;
		font-size: 30rpx;
		line-height: 40rpx;
	}

	.product-content {
		border-radius: 30rpx;
		background: #fff;
		width: 460rpx;
		padding: 20rpx 30rpx;
		color: #18181b;
	}

	.order-content {
		border-radius: 30rpx;
		background: #fff;
		width: 460rpx;
		padding: 20rpx 30rpx;
		color: #18181b;
	}

	&.is-mine {
		.chat-item-body {
			flex-direction: row-reverse;
		}

		.chat-item-content {
			justify-content: flex-end;
		}

		.text-content {
			background: #e8faf3;
		}

		.product-content {
			// background: #e8faf3;
		}

		.order-content {
			// background: #e8faf3;
		}
	}
}
</style>
