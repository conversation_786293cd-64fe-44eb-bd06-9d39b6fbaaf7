<script setup>
import { ref, reactive } from 'vue';

import { canENV, getUploadSrc, openVideo } from '@/common/utils';

const _data = reactive({});

const _props = defineProps({
	srcUrl: {
		type: String,
		default: ''
	}
});

function isImage(str) {
	// return /.(gif|jpg|jpeg|png|gif|jpg|png|webp|avif|jfif)$/i.test(str);
	return true;
}

function previewImage(item) {
	uni.previewImage({
		urls: [item],
		count: item
	});
}

function previewVideo(item) {
	openVideo(item);
}
</script>

<template>
	<view class="image-box">
		<template v-if="isImage(srcUrl)">
			<app-image :src="srcUrl" width="300" mode="widthFix" rd="20" @click="previewImage(srcUrl)"></app-image>
		</template>
		<template v-else>
			<view class="video-cover-box">
				<view class="w-300 h-300 bg-#000 b-rd-20 relative" @click="previewVideo(srcUrl)">
					<app-image :src="getUploadSrc(srcUrl, 'videoCover')" size="300" rd="20" mode="aspectFit" @click=""></app-image>
					<view class="w-300 h-300 flex flex-center absolute top-0 left-0">
						<uv-icon name="play-circle" color="#fff" size="80rpx"></uv-icon>
					</view>
				</view>
			</view>
		</template>
	</view>
</template>

<style lang="scss" scoped></style>
