<script setup>
import { onLoad, onUnload } from "@dcloudio/uni-app";

import { ref, reactive, onUnmounted } from "vue";

import { putPigeonMessageShopRead, sendMessagePlatform, getPlatformChatRoom, getMessagesChatRoom, upload } from "@/server/api";

import { useUserStore, useMessageStore } from "@/store";

import useConvert from "@/common/useConvert";

import { canENV } from "@/common/utils";

import chatItem from "./components/chat-item/chat-item.vue";

import chatInputBar from "./components/chat-input-bar/chat-input-bar.vue";
import route from "../../../../uni_modules/uv-ui-tools/libs/util/route";
import { common } from "@/common/images";

const { divTenThousand } = useConvert();

const userStore = useUserStore();
const messageStore = useMessageStore();

const props = defineProps({
	// shopId: {
	// 	type: String,
	// 	required: true
	// }
});

const navBarTitle = ref("消息");

const _data = reactive({
	shopId: "0",
	shopLogo: "",
	shopName: "",

	showGoods: false,
	showGoodsInfo: null,

	showOrder: false,
	showOrderInfo: null,
});

const pagingRef = ref();
const inputBar = ref();

const dataList = ref([]);

function queryList(pageNo, pageSize) {
	const params = {
		pageNo: pageNo,
		current: pageNo,
		pageSize: pageSize,
		shopId: _data.shopId,
		userId: userStore.userData.userId,
	};

	getPlatformChatRoom(params)
		.then((res) => {
			if (res.apiStatus) {
				if (pagingRef.value) {
					const list = res.data.records;
					// const newList = list.reverse();
					const newList = list;
					pagingRef.value.complete(newList);
				}
			} else {
				if (pagingRef.value) pagingRef.value.complete(false);
			}
		})
		.catch((res) => {
			if (pagingRef.value) pagingRef.value.complete(false);
		});
}

async function initPigeonMessageShopRead() {
	if (_data.shopId) {
		try {
			const res = await putPigeonMessageShopRead({
				shopId: _data.shopId,
			});
			if (res.apiStatus) {
				messageStore.loadMessageCount();
			}
		} catch (error) {
			//TODO handle the exception
		}
	}
}

function receivedIMMessage(e) {
	// if (connectType === 'SUCCESS' || connectType === 'SUBSCRIBE') {
	// 	// if (pagingRef.value) pagingRef.value.reload();
	// }
	if (e.connectType === "SUBSCRIBE") {
		if (e.msg && e.msg.sender.senderShopInfo.shopId === _data.shopId && e.msg.receiver.receiverUserInfo.userId === userStore.userData.userId) {
			if (pagingRef.value) {
				const newMsg = {
					handled: false,
					show: true,
					...e.msg,
				};

				newMsg.sendTime = Number(newMsg.sendTime || Date.now());
				if (isNaN(newMsg.sendTime)) newMsg.sendTime = Date.now();

				pagingRef.value.addChatRecordData(newMsg);
			}
		}
	}
}

function chooseMediaType(sourceType) {
	// #ifndef H5
	uni.chooseMedia({
		count: 1,
		// mediaType: ['image', 'video'],
		mediaType: ["image"],
		// sourceType: ['album', 'camera'],
		sourceType: sourceType ? [sourceType] : ["album", "camera"],
		sizeType: ["original", "compressed"],
		success(res) {
			const tempFilePaths = res.tempFiles.map((i) => i.tempFilePath);

			uni.showLoading({
				title: "加载中...",
			});
			upload(tempFilePaths[0], {
				formData: tempFilePaths,
			})
				.then((res) => {
					uni.hideLoading();
					if (res.apiStatus) {
						sendMsg({
							message: res.data,
							messageType: "IMAGE",
						});
					}
				})
				.catch((err) => {
					uni.hideLoading();
				});
		},
	});
	// #endif
	// #ifdef H5
	uni.chooseImage({
		sourceType: sourceType ? [sourceType] : ["album", "camera"],
		success(res) {
			const tempFilePaths = res.tempFilePaths;

			uni.showLoading({
				title: "加载中...",
			});
			upload(tempFilePaths[0], {
				formData: tempFilePaths,
			})
				.then((res) => {
					uni.hideLoading();
					if (res.apiStatus) {
						sendMsg({
							message: res.data,
							messageType: "IMAGE",
						});
						if (inputBar.value) inputBar.value.hidedKeyboard();
					}
				})
				.catch((err) => {
					uni.hideLoading();
				});
		},
	});
	// #endif
}

function sendText(text) {
	sendMsg({
		message: text,
		messageType: "TEXT",
	});
}

function sendGoods() {
	sendMsg({
		message: JSON.stringify(_data.showGoodsInfo),
		messageType: "PRODUCT",
	});
	_data.showGoods = false;
}

function sendOrder() {
	sendMsg({
		message: JSON.stringify(_data.showOrderInfo),
		messageType: "ORDER",
	});
	_data.showOrder = false;
}

async function sendMsg(data) {
	const { message, messageType } = data;
	const query = {
		content: message,
		messageType,
		receiverId: _data.shopId,
		senderId: userStore.userData.userId || "",
	};

	try {
		const res = await sendMessagePlatform(query);
		if (res.apiStatus) {
			if (pagingRef.value) {
				pagingRef.value.addChatRecordData({
					handled: false,
					message: query.content,
					messageType: query.messageType,
					read: false,
					receiver: {
						receiverShopInfo: {
							shopId: _data.shopId,
							shopLogo: _data.shopLogo,
							shopName: _data.shopName,
						},
						receiverType: _data.shopId === "0" ? "PLATFORM_ADMIN" : "SHOP_ADMIN",
					},
					sendTime: Date.now(),
					sender: {
						senderType: "CONSUMER",
						senderUserInfo: {
							...userStore.userData,
							userKey: userStore.userData.userId,
						},
					},
					show: true,
				});
			}
		}
	} catch (error) {
		//TODO handle the exception
	}
}

async function loadData() {
	const info = messageStore.imUserInfo;
	if (info) {
		_data.shopId = info.shopId || "0";
		_data.shopLogo = info.shopLogo || "";
		_data.shopName = info.shopName || "";
	}

	if (_data.shopName) {
		// uni.setNavigationBarTitle({
		// 	title: _data.shopName
		// });
		navBarTitle.value = _data.shopName;
	}
	if (userStore.checkLogin) {
		try {
			await getMessagesChatRoom({
				shopId: _data.shopId,
				userId: userStore.userData.userId,
			});
		} catch (error) {
			console.log(error);
			//TODO handle the exception
		}
	}

	initPigeonMessageShopRead();

	if (info.showGoods && info.showGoodsInfo) {
		_data.showGoodsInfo = info.showGoodsInfo;
		_data.showGoods = true;
	}

	if (info.showOrder && info.showOrderInfo) {
		_data.showOrderInfo = info.showOrderInfo;
		_data.showOrder = true;
	}

	uni.$on("receivedIMMessage", receivedIMMessage);
}

function report() {
	route("/pages/settings/pages/report/report", {
		opId: _data.shopId,
	});
}

onLoad(() => {
	loadData();
});

onUnload(() => {
	uni.$off("receivedIMMessage", receivedIMMessage);
});

function keyboardHeightChange(res) {
	if (inputBar.value) inputBar.value.updateKeyboardHeightChange(res);
}

function hidedKeyboard() {
	if (inputBar.value) inputBar.value.hidedKeyboard();
}
</script>

<template>
	<view class="im">
		<app-layout>
			<z-paging ref="pagingRef" v-model="dataList" bgColor="#F5F8FE" use-chat-record-mode safe-area-inset-bottom bottom-bg-color="#f8f8f8" @query="queryList" @keyboardHeightChange="keyboardHeightChange" @hidedKeyboard="hidedKeyboard">
				<template #top>
					<app-navBar bgColor="#fff" back rightPlaceholderSize="140rpx" leftPlaceholderSize="140rpx">
						<template #content>
							<view class="w-full h-full flex justify-between items-center px-0">
								<view class="flex-1 nav-center">
									<text class="text-32 text-#000 font-bold">{{ navBarTitle }}</text>
								</view>
							</view>
						</template>
						<template #right>
							<view class="flex items-center justify-end">
								<text class="text-30 font-500" @click="report()">举报</text>
							</view>
						</template>
					</app-navBar>
				</template>

				<view v-for="(item, index) in dataList" :key="item.sendTime" style="position: relative">
					<view style="transform: scaleY(-1)">
						<!-- <chat-item :item="item"></chat-item> -->
						<chatItem :item="item" :index="index" :shopLogo="_data.shopLogo"></chatItem>
					</view>
				</view>

				<template #empty>
					<view></view>
				</template>

				<template #bottom>
					<template v-if="_data.showGoods">
						<view class="w-full h-0 relative">
							<view class="w-full p-20 absolute bottom-0">
								<view class="w-full p-20 bg-#fff border-rd-20">
									<view class="flex justify-between">
										<view class="w-160 h-160">
											<app-image :src="_data.showGoodsInfo.pic" mode="" size="160" rd="20"></app-image>
										</view>
										<view class="flex-1 flex flex-col ml-20">
											<view class="flex items-center">
												<view class="text-26 uv-line-1 flex-1"></view>

												<view class="ml-20 w-25">
													<app-image :src="common.iconClearHui" size="25" @click="_data.showGoods = false" mode=""></app-image>
												</view>
											</view>
											<view class="flex items-center">
												<view class="text-26 uv-line-1 flex-1">{{ _data.showGoodsInfo.name }}</view>
											</view>
											<view class="flex items-center mt-20">
												<view class="text-24 uv-line-1 flex-1 font-500 text-#FC3F33"> ￥{{ _data.showGoodsInfo.price.estimate && divTenThousand(_data.showGoodsInfo.price.estimate) }} 预估价 </view>
											</view>
											<view class="flex items-center">
												<view class="text-26 uv-line-1 flex-1"></view>

												<view class="w-100">
													<uv-button type="primary" :customStyle="{ height: '40rpx', borderRadius: '20rpx' }" @click="sendGoods">
														<view class="text-22">发送</view>
													</uv-button>
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</template>

					<template v-if="_data.showOrder">
						<view class="w-full h-0 relative">
							<view class="w-full p-20 absolute bottom-0">
								<view class="w-full p-20 bg-#fff border-rd-20">
									<view class="flex justify-between">
										<view class="w-160 h-160">
											<app-image :src="_data.showOrderInfo.pic" mode="" size="160" rd="20"></app-image>
										</view>
										<view class="flex-1 flex flex-col ml-20">
											<view class="flex items-center">
												<view class="text-26 uv-line-1 flex-1"></view>

												<view class="ml-20 w-25">
													<app-image :src="common.iconClearHui" size="25" @click="_data.showOrder = false" mode=""></app-image>
												</view>
											</view>
											<view class="flex items-center">
												<view class="text-26 uv-line-1 flex-1">{{ _data.showOrderInfo.no }}</view>
											</view>
											<view class="flex items-center mt-20">
												<view class="text-24 uv-line-1 flex-1 font-500 text-#FC3F33">实付 {{ _data.showOrderInfo.amountRealPay }}</view>
											</view>
											<view class="flex items-center">
												<view class="text-26 uv-line-1 flex-1"></view>

												<view class="w-100">
													<uv-button type="primary" :customStyle="{ height: '40rpx', borderRadius: '20rpx' }" @click="sendOrder">
														<view class="text-22">发送</view>
													</uv-button>
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</template>
					<chatInputBar ref="inputBar" @send="sendText" @chooseMediaType="chooseMediaType" />
				</template>
			</z-paging>
		</app-layout>
	</view>
</template>

<style lang="scss" scoped>
.im {
}
</style>
