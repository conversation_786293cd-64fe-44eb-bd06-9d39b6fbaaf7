<script setup>
import { ref, watch } from 'vue';

// import { getMsgCategory, getCategoryMsgList, setMsgItemRead } from '@/server/api';

import { goArticlePage } from '@/common/utils';

const paging = ref(null);

// 当前组件是否已经加载过了
const firstLoaded = ref(false);

const dataList = ref([]);

const queryList = (pageNo, pageSize) => {
	const params = {
		pageNo: pageNo,
		pageSize: pageSize
	};

	paging.value.complete([]);

	return;

	uni.showLoading({
		title: '加载中...'
	});

	getCategoryMsgList(params).then((res) => {
		setMsgItemRead({
			read_category_id: props.item.id
		});

		const list = res.rows;

		paging.value.complete(list);
		firstLoaded.value = true;
		uni.hideLoading();
	});
};

function toNoticeDetails(item) {
	goArticlePage('notice', item.relation_id);
}
</script>
<template>
	<view class="system">
		<z-paging ref="paging" v-model="dataList" @query="queryList">
			<view class="item" v-for="(item, index) in dataList" :key="index">
				<!-- 动态消息 -->
				<view v-if="item.relation === 'trends'" class="w-full bg-#fff border-rd-20 p20 mb-20">
					<view class="flex justify-between items-center border-b-1 border-b-solid border-b-EDEEF0 pb-20">
						<view class="flex items-center">
							<image src="/static/logo.png" class="w-44 h-44 border-rd-22 mr-16" mode=""></image>
							<view class="text-28 text-[#25292D]">
								{{ item.title }}
							</view>
						</view>
						<view class="text-24 text-[#93939C]">
							{{ item.createtime_text }}
						</view>
					</view>
					<view class="mt-30 flex justify-between items-center">
						<view class="text-28 text-[#384048]">
							{{ item.content }}
						</view>
						<uv-button
							color="#1F93FF"
							text="去查看"
							:plain="true"
							class="w-128 h-57"
							custom-style="border-radius: 29rpx;border-color: #8558DF"
							customTextStyle="font-size: 26rpx; color: #632EED;"
						></uv-button>
					</view>
				</view>
				<!-- 公告 -->
				<view v-else-if="item.relation === 'notice'" class="w-full bg-#fff border-rd-20 p20 mb-20">
					<view class="flex justify-between items-center border-b-1 border-b-solid border-b-EDEEF0 pb-20">
						<view class="flex items-center">
							<image src="/static/logo.png" class="w-44 h-44 border-rd-22 mr-16" mode=""></image>
							<view class="text-28 text-[#632EED] font-bold">
								{{ item.title }}
							</view>
						</view>
						<view class="text-24 text-[#93939C]">
							{{ item.createtime_text }}
						</view>
					</view>
					<view class="mt-30 text-28">
						<uv-parse :content="item.content" :selectable="true"></uv-parse>
					</view>
					<view class="mt-30 flex justify-between items-center">
						<view class="text-28 text-[#384048]"></view>
						<uv-button
							color="#1F93FF"
							text="查看详情"
							:plain="true"
							@click="toNoticeDetails(item)"
							class="w-128 h-57"
							custom-style="border-radius: 29rpx;border-color: #8558DF"
							customTextStyle="font-size: 26rpx; color: #632EED;"
						></uv-button>
					</view>
				</view>
				<!-- 系统消息 -->
				<view v-else class="w-full bg-#fff border-rd-20 p20 mb-20">
					<view class="flex justify-between items-center border-b-1 border-b-solid border-b-EDEEF0 pb-20">
						<view class="flex items-center">
							<image src="/static/logo.png" class="w-44 h-44 border-rd-22 mr-16" mode=""></image>
							<view class="text-28 text-[#25292D]">
								{{ item.title }}
							</view>
						</view>
						<view class="text-24 text-[#93939C]">
							{{ item.createtime_text }}
						</view>
					</view>
					<view class="mt-30 text-28">
						<uv-parse :content="item.content" :selectable="true"></uv-parse>
					</view>
				</view>
			</view>
		</z-paging>
	</view>
</template>

<style lang="scss" scoped></style>
