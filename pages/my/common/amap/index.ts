import { LocationInfo, GeometryOptionalCoordinates, GeometryType, Geometry, AddressInfo } from './type/index.d';

const amapWebServiceKey = import.meta.env.VITE_GD_WEB_MAP_KEY as string;

/**
 * 逆地址解析
 * @param {Array} data.location location[0]:经度,[-180,180],负数表示西经。 location[1]:纬度,[-90,90],负数表示南纬
 * 文档地址: https://lbs.amap.com/api/webservice/guide/api/georegeo
 */
export const locToAddress = (data: LocationInfo) => {
	return new Promise<GeometryOptionalCoordinates>((resolve, reject) => {
		if (data?.area?.length) {
			return resolve({
				location: {
					type: GeometryType.Point,
					coordinates: [Number(data.longitude), Number(data.latitude)]
				},
				address:
					data.address
						?.replace(data.area[0], '')
						?.replace(data.area[1], '')
						?.replace(data.area?.[2] || '', '') || '',
				area: data.area,
				name: data.name || ''
			});
		}

		const ampUrl = `https://restapi.amap.com/v3/geocode/regeo?key=${amapWebServiceKey}&location=${data.longitude},${data.latitude}`;

		uni.request({
			// 逆地理编码 需要web服务高德key
			url: ampUrl,
			success: (res: any) => {
				const result = res.data;
				if (result.status === '1' && result.infocode === '10000') {
					let tempObj = result.regeocode;
					const area = [] as unknown as [string, string, string?];
					if (tempObj.addressComponent.province.length) {
						area.push(tempObj.addressComponent.province);
					}
					if (tempObj.addressComponent.city.length) {
						area.push(tempObj.addressComponent.city);
					}
					if (tempObj.addressComponent.district.length) {
						area.push(tempObj.addressComponent.district);
					}
					if (area.length < 2) {
						area.push('');
					}
					const originalAddress = data.address
						?.replace(tempObj.addressComponent.province, '')
						?.replace(tempObj.addressComponent.city, '')
						?.replace(tempObj.addressComponent.district, '');
					const resp = {
						location: {
							type: GeometryType.Point,
							coordinates: [data.longitude, data.latitude]
						},
						address:
							originalAddress ||
							tempObj.formatted_address.replace(tempObj.addressComponent.province, '').replace(tempObj.addressComponent.city, '').replace(tempObj.addressComponent.district, ''),
						area: data.area || area,
						name: data.name
					} as GeometryOptionalCoordinates;
					resolve(resp);
				} else {
					reject(res);
				}
			},
			fail: (err) => {
				console.error('获取位置信息失败', err);
				reject(err);
			}
		});
	});
};

/**
 * 地址解析
 * 文档地址: https://lbs.amap.com/api/webservice/guide/api/georegeo
 */
export const addressToLocItem = (data: AddressInfo) => {
	return new Promise<GeometryOptionalCoordinates>((resolve, reject) => {
		const ampUrl = `https://restapi.amap.com/v3/geocode/geo?key=${amapWebServiceKey}&address=${data.address || ''}&city=${data.city || ''}`;

		uni.request({
			// 逆地理编码 需要web服务高德key
			url: ampUrl,
			success: (res: any) => {
				const result = res.data;
				if (result.status === '1' && result.infocode === '10000' && result.geocodes.length > 0) {
					const resData = result.geocodes[0];

					const area = ['', '', ''] as unknown as [string, string, string?];

					if (resData.province) {
						area[0] = resData.province;
					}

					if (resData.city) {
						area[1] = resData.city;
					}

					if (resData.district) {
						area[2] = resData.district;
					}

					const locationArr = resData.location.split(',');

					const lat = locationArr[1];
					const lng = locationArr[0];

					const resp = {
						location: {
							type: GeometryType.Point,
							coordinates: [lng, lat]
						},
						address: resData.formatted_address.replace(resData.province, '').replace(resData.city, '').replace(resData.district, ''),
						area: area,
						name: resData.formatted_address || data.address || ''
					} as GeometryOptionalCoordinates;

					resolve(resp);
				} else {
					reject(res);
				}
			},
			fail: (err) => {
				console.error('获取位置信息失败', err);
				reject(err);
			}
		});
	});
};

/**
 * 地址解析 - 列表
 * @param {Array} data.location location[0]:经度,[-180,180],负数表示西经。 location[1]:纬度,[-90,90],负数表示南纬
 * 文档地址: https://lbs.amap.com/api/webservice/guide/api/georegeo
 */
export const addressToLoc = (data: AddressInfo) => {
	return new Promise<GeometryOptionalCoordinates[]>((resolve, reject) => {
		const ampUrl = `https://restapi.amap.com/v3/geocode/geo?key=${amapWebServiceKey}&address=${data.address || ''}&city=${data.city || ''}`;

		uni.request({
			// 逆地理编码 需要web服务高德key
			url: ampUrl,
			success: (res: any) => {
				const result = res.data;
				if (result.status === '1' && result.infocode === '10000') {
					const respArr: GeometryOptionalCoordinates[] = [];

					for (let i = 0; i < result.geocodes.length; i++) {
						const resData = result.geocodes[i];

						const area = ['', '', ''] as unknown as [string, string, string?];

						if (resData.province) {
							area[0] = resData.province;
						}

						if (resData.city) {
							area[1] = resData.city;
						}

						if (resData.district) {
							area[2] = resData.district;
						}

						const locationArr = resData.location.split(',');

						const lat = locationArr[1];
						const lng = locationArr[0];

						const resp = {
							location: {
								type: GeometryType.Point,
								coordinates: [lng, lat]
							},
							address: resData.formatted_address.replace(resData.province, '').replace(resData.city, '').replace(resData.district, ''),
							area: area,
							name: resData.formatted_address || data.address || ''
						} as GeometryOptionalCoordinates;

						respArr.push(resp);
					}

					resolve(respArr);
				} else {
					reject(res);
				}
			},
			fail: (err) => {
				console.error('获取位置信息失败', err);
				reject(err);
			}
		});
	});
};

/**
 * 高德地图 IP 定位
 * 文档地址: https://lbs.amap.com/api/webservice/guide/api/ipconfig
 */
export const ipToAddress = (ip?: string) => {
	return new Promise<Geometry>((resolve, reject) => {
		const ampUrl = `https://restapi.amap.com/v3/ip?key=${amapWebServiceKey}${ip ? `&ip=${ip}` : ''}`;

		uni.request({
			url: ampUrl,
			success: (res: any) => {
				const result = res.data;
				if (result.status === '1' && result.infocode === '10000') {
					// 左下坐标
					const locationLeftBottom: string[] = result.rectangle?.split(';')?.[0]?.split(',') || [];
					// 右上坐标
					const locationRightTop: string[] = result.rectangle?.split(';')?.[1]?.split(',') || [];
					let locationCenter = [] as unknown as [number, number];
					if (locationLeftBottom.length && locationRightTop.length) {
						// 保留八位小数
						locationCenter = [
							Number(((Number(locationLeftBottom[0]) + Number(locationRightTop[0])) / 2).toFixed(8)),
							Number(((Number(locationLeftBottom[1]) + Number(locationRightTop[1])) / 2).toFixed(8))
						];
					}
					console.error('高德ip获取位置信息成功,城市中心坐标:', locationCenter);
					resolve({
						type: GeometryType.Point,
						// 坐标只能定位到城市,所以选取城市中心点
						coordinates: locationCenter
					});
				} else {
					console.error('高德ip获取位置信息失败', res);
					reject(res);
				}
			},
			fail: (err) => {
				console.error('高德ip获取位置信息请求失败', err);
				reject(err);
			}
		});
	});
};

/**
 * 坐标系类型
 */
enum CoordinateSystemType {
	gps = 'gps', // GPS(WGS84) 坐标系
	mapbar = 'mapbar', // Mapbar 坐标系
	baidu = 'baidu', // 百度坐标系(BD09) 坐标系
	autonavi = 'autonavi' // 高德坐标系(AMAP) 坐标系
}
/**
 * 坐标转换
 * 非高德坐标（GPS 坐标、mapbar 坐标、baidu 坐标）转换成高德坐标
 * 文档地址: https://lbs.amap.com/api/webservice/guide/api/convert
 * @param {Array} coords 经纬度坐标[经度,纬度], 经纬度小数点后不得超过6位。
 * @param {string} type 坐标的类型,可选值：gps;mapbar;baidu;autonavi(不进行转换)
 */
const locConvert = (coords: [number, number], type: CoordinateSystemType = CoordinateSystemType.gps) => {
	return new Promise<Geometry>((resolve) => {
		const ampUrl = `https://restapi.amap.com/v3/assistant/coordinate/convert?key=${amapWebServiceKey}&locations=${coords}&coordsys=${type}`;

		uni.request({
			url: ampUrl,
			success: (res: any) => {
				const result = res.data;
				if (result.status === '1' && result.infocode === '10000') {
					const location = result.locations.split(',');
					resolve({
						type: GeometryType.Point,
						coordinates: [Number(location[0]), Number(location[1])]
					});
				} else {
					resolve({
						type: GeometryType.Point,
						coordinates: coords
					});
				}
			},
			fail: (err: any) => {
				resolve({
					type: GeometryType.Point,
					coordinates: coords
				});
			}
		});
	});
};

/**
 * 获取当前定位
 */
export const getLocation = () => {
	return new Promise<{ longitude: number; latitude: number; address?: string }>((resolve, reject) => {
		if (window?.navigator?.geolocation) {
			window.navigator.geolocation.getCurrentPosition(
				async (position) => {
					console.log('浏览器高精度定位成功:', position.coords);
					try {
						console.log('开始坐标转换:', [position.coords.longitude, position.coords.latitude]);
						const { coordinates } = await locConvert([position.coords.longitude, position.coords.latitude]);
						resolve({
							latitude: coordinates[1],
							longitude: coordinates[0]
						});
					} catch (error) {
						console.log('坐标转换失败,使用浏览器定位', error);
						resolve({
							latitude: position.coords.latitude,
							longitude: position.coords.longitude
						});
					}
				},
				(error) => {
					console.log('浏览器获取定位失败,尝试使用ip定位', error);
					// 尝试使用高德地图ip定位
					ipToAddress()
						.then((res) => {
							resolve({
								latitude: res.coordinates[1],
								longitude: res.coordinates[0]
							});
						})
						.catch((err) => {
							console.log('ip定位失败,彻底定位失败', err);
							reject(err);
						});
				},
				{
					enableHighAccuracy: true, //是否要求高精度的地理位置信息
					timeout: 3001 //对地理位置信息的获取操作做超时限制，如果再该事件内未获取到地理位置信息，将返回错误
				}
			);
		} else {
			// 先尝试原有的获取位置信息方式
			uni.getLocation({
				type: 'gcj02',
				isHighAccuracy: true,
				highAccuracyExpireTime: 3001,
				success: (lb) => {
					console.log('uni.getLocation()获取定位成功', lb);
					resolve({
						latitude: lb.latitude,
						longitude: lb.longitude
					});
				},
				fail: (error) => {
					console.log('uni.getLocation()获取定位失败,尝试使用ip定位', error);
					// 尝试使用高德地图ip定位
					ipToAddress()
						.then((res) => {
							resolve({
								latitude: res.coordinates[1],
								longitude: res.coordinates[0]
							});
						})
						.catch((err) => {
							console.log('ip定位失败,彻底定位失败', err);
							reject(err);
						});
				}
			});
		}
	});
};
