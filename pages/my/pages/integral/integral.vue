<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';

import { onLoad } from '@dcloudio/uni-app';

import { getUserIntegralSystemtotal, getUserIntegralDetailInfo } from '@/server/api';

import { route, salesVolumeToStr } from "@/common/utils";
import { common } from "@/common/images";

const paging = ref(null);

const pageData = reactive({
	typeList: [
		{
			name: '全部',
			value: ''
		},
		{
			name: '收入',
			value: 'INCREASE'
		},
		{
			name: '支出',
			value: 'REDUCE'
		}
	],
	typeIndex: 0,

	dataList: [],

	totalIntegral: 0
});

const gainIntegralCn = {
	DAY_LOGIN: '每日登入',
	INTEGRAL_PRODUCT_EXCHANGE: '积分商品兑换',
	DAY_SHARE: '每日分享',
	INTEGRAL_CLEAR: '积分清空',
	DAY_SIGN_IN: '每日签到',
	SYSTEM_RECHARGE: '系统充值',
	SYSTEM_DEDUCT: '系统扣除',
	ORDER_CONSUMPTION: '订单消费',
	ORDER_CANCEL: '订单取消',
	INTEGRAL_CONSUME: '消费所得',
	INVITE_RECHARGE: '邀请奖励'
};

const checkProductValue = ref([]);

function changeType(e) {
	pageData.typeIndex = e.index;
	if (paging.value) paging.value.reload();
}

function changeSort(e) {
	pageData.sort = e;
}

function loadData() {
	getUserIntegralSystemtotal().then((res) => {
		if (res.apiStatus) {
			pageData.totalIntegral = res.data;
		}
	});
}

function queryList(pageNo, pageSize) {
	const params = {
		pageNo: pageNo,
		current: pageNo,
		pageSize: pageSize,
		changeType: pageData.typeList[pageData.typeIndex].value
	};

	getUserIntegralDetailInfo({ ...params }).then((res) => {
		if (res.apiStatus) {
			if (paging.value) paging.value.complete(res.data.records);
		}
	});
}

onLoad(() => {
	loadData();
});
</script>
<template>
	<app-layout>
		<z-paging ref="paging" bg-color="rgba(255,255,255,0)" v-model="pageData.dataList" @query="queryList">
			<template #top>
				<app-navBar bg-color="rgba(255,255,255,0)" back rightPlaceholderSize="70rpx" :backIcon="common.iconLeftBai" :fixed="true" :fixed-placeholder="false">
					<template #content>
						<view class="w-full h-full flex justify-between items-center px-0">
							<view class="flex-1 nav-center">
								<text class="text-32 text-#fff font-bold">我的积分</text>
							</view>
						</view>
					</template>
				</app-navBar>
				<view class="w-full h-500">
					<app-image src="@/pages/my/static/bg_jifenzhongxin_header.png" width="750" height="500" class="absolute" mode=""></app-image>
					<view class="w-full relative w-full h-full pt-230 pl-60">
						<view class="text-32 text-#fff op-78 font-500">当前积分</view>
						<!-- <view class="font-bold text-76 text-#fff">{{ salesVolumeToStr(pageData.totalIntegral) }}</view> -->
						<view class="font-bold text-76 text-#fff">{{ pageData.totalIntegral }}</view>
					</view>
				</view>
				<view class="w-full pt-38 pb-20 bg-#fff relative" style="border-radius: 20rpx 20rpx 0rpx 0rpx; margin-top: -42rpx">
					<view class="text-34 font-bold px-40 flex items-center">
						<app-image src="@/pages/my/static/icon_jifen.png" size="30" mr="15" mode=""></app-image>
						<view class="">积分明细</view>
					</view>
					<view class="px-0 pt-20 justify-between w-full border-solid border-0 border-#F2F2F2 border-bottom-1">
						<uv-tabs
							:list="pageData.typeList"
							:current="pageData.typeIndex"
							:scrollable="false"
							lineColor="#FB6931"
							:activeStyle="{
								fontWeight: 'bold',
								fontSize: '30rpx',
								color: '#222222'
							}"
							:inactiveStyle="{
								fontWeight: 'bold',
								fontSize: '30rpx',
								color: '#666666'
							}"
							lineWidth="50rpx"
							lineHeight="4rpx"
							@change="changeType"></uv-tabs>
					</view>
				</view>
			</template>
			<view class="evaluation py-0">
				<template v-for="(item, index) in pageData.dataList" :key="index">
					<view class="px-40">
						<view class="w-full pb-30 pt-35 border-solid border-0 border-#E9EDF1 border-bottom-1">
							<view class="flex justify-between">
								<view class="flex-1 flex flex-col">
									<view class="text-30 font-500 flex items-center">
										<view class="">
											<template v-if="item.changeType === 'INCREASE'">
												<text>收入-</text>
											</template>
											<template v-else>
												<text>支出-</text>
											</template>
											<text>{{ gainIntegralCn[item.gainIntegralType] }}</text>
										</view>
									</view>
									<view class="text-24 text-#222 font-500 mt-20" v-if="item.particulars">（{{ item.particulars }}）</view>
									<view class="text-24 text-#888 font-500 mt-20">{{ item.createTime }}</view>
								</view>

								<view class="">
									<template v-if="item.changeType === 'INCREASE'">
										<view class="flex items-end text-30 font-bold text-#222222 text-#F5602B">
											<view class="">+</view>
											<view class="">{{ item.variationIntegral }}</view>
										</view>
									</template>
									<template v-else>
										<view class="flex items-end text-30 font-bold text-#222222">
											<view class="">-</view>
											<view class="">{{ item.variationIntegral }}</view>
										</view>
									</template>
								</view>
							</view>
						</view>
					</view>
				</template>
			</view>
		</z-paging>
	</app-layout>
</template>
<style lang="scss" scoped>
.evaluation {
}
</style>
