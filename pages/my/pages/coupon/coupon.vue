<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';

import { onLoad } from '@dcloudio/uni-app';

import { getCouponList, consumerCollectCoupon, getCouponNumber } from '@/server/api';

const paging = ref(null);

const pageData = reactive({
	typeList: [
		{
			name: `可用优惠券`,
			value: 'UNUSED'
		},
		{
			name: `不可用优惠券`,
			value: 'NOUSE'
		}
	],
	typeIndex: 0,

	dataList: [],

	value: 1
});

const checkProductValue = ref([]);

function changeType(e) {
	pageData.typeIndex = e.index;
	if (paging.value) paging.value.reload();
}

function changeSort(e) {
	pageData.sort = e;
}

async function loadNumber() {
	try {
		const couponNumberRes = await getCouponNumber();

		if (couponNumberRes && couponNumberRes.apiStatus && couponNumberRes.data) {
			pageData.typeList[0].name = `可用优惠券(${couponNumberRes.data.unuseNum})`;
			pageData.typeList[1].name = `不可用优惠券(${couponNumberRes.data.noUnseNum})`;
		}
	} catch (error) {
		//TODO handle the exception
	}
}

function queryList(pageNo, pageSize) {
	const params = {
		pageNo: pageNo,
		current: pageNo,
		pageSize: pageSize,
		isPlatform: true,
		status: pageData.typeList[pageData.typeIndex].value
	};

	getCouponList({ ...params }).then((res) => {
		if (res.apiStatus) {
			const list = res.data.records.map((i) => ({ status: pageData.typeList[pageData.typeIndex].value, ...i }));
			if (paging.value) paging.value.complete(list);
		}
	});
}

onLoad(() => {
	loadNumber();
});
</script>
<template>
	<app-layout>
		<z-paging ref="paging" bg-color="rgba(255,255,255,0)" v-model="pageData.dataList" @query="queryList">
			<template #top>
				<view class="w-full pt-0 pb-20 bg-#fff relative">
					<view class="px-0 pt-0 justify-between w-full border-solid border-0 border-#F2F2F2 border-bottom-1">
						<uv-tabs
							:list="pageData.typeList"
							:current="pageData.typeIndex"
							:scrollable="false"
							lineColor="#323232"
							:activeStyle="{
								fontWeight: 'bold',
								fontSize: '28rpx',
								color: '#222222'
							}"
							:inactiveStyle="{
								fontSize: '28rpx',
								color: '#767676'
							}"
							lineWidth="50rpx"
							lineHeight="4rpx"
							@change="changeType"
						></uv-tabs>
					</view>
				</view>
			</template>
			<view class="evaluation py-20 pt-30">
				<template v-for="(item, index) in pageData.dataList" :key="index">
					<view class="px-40 mb-20">
						<page-coupon-card-item :item="item" :index="index" :type="3"></page-coupon-card-item>
					</view>
				</template>
			</view>
		</z-paging>
	</app-layout>
</template>
<style lang="scss" scoped>
.evaluation {
}
</style>
