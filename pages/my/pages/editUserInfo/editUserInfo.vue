<script setup>
import { onLoad } from "@dcloudio/uni-app";

import { ref, reactive } from "vue";

import { useUserStore } from "@/store";

import { canENV, route, chooseMediaFile } from "@/common/utils";

import { upload, changeUserData } from "@/server/api";
import { sleep } from "@/uni_modules/uv-ui-tools/libs/function";
import { logo } from "@/common/images";

const userStore = useUserStore();

const userInfo = reactive({
	avatar: "",
	nickname: "",
	genderText: "未知",
	gender: "UNKNOWN",
	birthday: "",
	birthdayValue: Date.now() - 1000 * 60 * 60 * 24 * 365 * 18,
	area: [],
	areaVlaue: "",
});
const formRules = {
	nickname: {
		required: true,
		message: "请设置昵称",
		trigger: "blur",
	},
	// area: [
	// 	{
	// 		required: true,
	// 		message: '请选择地址',
	// 		trigger: 'blur'
	// 	}
	// ],
	areaVlaue: [
		{
			required: true,
			message: "请选择地址",
			trigger: "blur",
		},
	],
};

const pageData = reactive({
	genderList: [
		[
			{
				text: "男",
				value: "MALE",
			},
			{
				text: "女",
				value: "FEMALE",
			},
			{
				text: "未知",
				value: "UNKNOWN",
			},
		],
	],
	genderIndex: [[]],
	genderValue: [[]],

	localdata: [],
	localdataValue: "",

	pageLoading: false,
});

const userInfoForm = ref();

const actionSheet = ref();

function uploadImage() {
	// if (actionSheet.value) actionSheet.value.open();
	// #ifndef MP-WEIXIN

	// #ifndef H5
	uni.chooseMedia({
		count: 1,
		mediaType: ["image"],
		sourceType: ["album", "camera"],
		sizeType: ["original", "compressed"],
		success(res) {
			const tempFilePaths = res.tempFiles.map((i) => i.tempFilePath);

			uni.showLoading({
				title: "加载中...",
			});
			upload(tempFilePaths[0], {
				formData: tempFilePaths,
			})
				.then((res) => {
					uni.hideLoading();
					if (res.apiStatus) {
						userInfo.avatar = res.data;
					}
				})
				.catch((err) => {
					uni.hideLoading();
				});
		},
	});
	// #endif
	// #ifdef H5
	uni.chooseImage({
		success(res) {
			const tempFilePaths = res.tempFilePaths;

			uni.showLoading({
				title: "加载中...",
			});
			upload(tempFilePaths[0], {
				formData: tempFilePaths,
			})
				.then((res) => {
					uni.hideLoading();
					if (res.apiStatus) {
						userInfo.avatar = res.data;
					}
				})
				.catch((err) => {
					uni.hideLoading();
				});
		},
	});
	// #endif

	// uni.chooseImage({
	// 	success(res) {
	// 		console.log(res);

	// 		uni.showLoading({
	// 			title: '加载中...'
	// 		});
	// 		upload(res.tempFilePaths[0], {
	// 			formData: res.tempFilePaths
	// 		})
	// 			.then((res) => {
	// 				uni.hideLoading();
	// 				console.log(res);
	// 			})
	// 			.catch((err) => {
	// 				uni.hideLoading();
	// 				console.log(err);
	// 			});
	// 	}
	// });
	// #endif
}

function onChooseAvatar(e) {
	const { avatarUrl } = e.detail;

	const tempFilePaths = [avatarUrl];

	uni.showLoading({
		title: "加载中...",
	});
	upload(tempFilePaths[0], {
		formData: tempFilePaths,
	})
		.then((res) => {
			uni.hideLoading();
			if (res.apiStatus) {
				userInfo.avatar = res.data;
			}
		})
		.catch((err) => {
			uni.hideLoading();
		});
}

function selectActionSheet(e) {
	// #ifndef MP-WEIXIN
	chooseMediaFile({
		mediaType: ["image"],
		sourceType: e,
	});
	// #endif
}

const birthdayDatePicker = ref();

function openBirthdayDatePicker() {
	if (birthdayDatePicker.value) {
		birthdayDatePicker.value.open();
	}
}

function birthdayDatePickerConfirm(e) {
	userInfo.birthday = uni.$uv.timeFormat(e.value, "yyyy-mm-dd");
}

function changeAddress() {
	route({
		url: "/pages/common/pages/selectCity/selectCity",
		events: {
			selectCity(city) {
				console.log(city);
				// userInfo.area = city.name;
			},
		},
	});
}

function changeAddressPicker(e) {
	const area = e.detail.value.map((i) => i.text);

	changeAddressData(area);
}

function changeAddressPickerWX(e) {
	changeAddressData(e.detail.value);
}

async function changeAddressData(area) {
	userInfo.area = area;
	userInfo.areaVlaue = JSON.stringify(area);
}

function initAddressPickValue() {
	pageData.localdata = regionData;
}

function submit() {
	userInfoForm.value.validate().then((res) => {
		pageData.pageLoading = true;
		changeUserData({
			nickname: userInfo.nickname,
			gender: userInfo.gender,
			avatar: userInfo.avatar,
			birthday: userInfo.birthday,
			area: JSON.stringify(userInfo.area),
		})
			.then(async (res) => {
				if (res.apiStatus) {
					userStore
						.getAllUserInfo()
						.then((res) => {
							pageData.pageLoading = false;
							if (res.apiStatus) {
								loadData();
							}
						})
						.catch((err) => {
							pageData.pageLoading = false;
						});
				}
			})
			.catch((err) => {
				pageData.pageLoading = false;
			});
	});
}

function loadData() {
	// initAddressPickValue();

	if (userStore.token) {
		userInfo.avatar = userStore.userData.avatar;
		userInfo.nickname = userStore.userData.nickname;
		userInfo.gender = userStore.userData.gender;
		const findIndex = pageData.genderList[0].findIndex((i) => i.value === userInfo.gender);
		if (findIndex >= 0) {
			userInfo.genderText = pageData.genderList[0][findIndex].text;
		}
		userInfo.birthday = userStore.userData.birthday || "";

		userInfo.area = userStore.userData.area || [];
		userInfo.areaVlaue = JSON.stringify(userStore.userData.area || []);
	}
}

onLoad(() => {
	loadData();
});

const formStyle = ref({
	labelStyle: {
		fontSize: "30rpx",
		fontWeight: "bold",
	},
	inputStyle: {
		height: "98rpx",
		padding: "0rpx 0rpx",
		borderBottom: `solid 1rpx #E9EDF1`,
	},
	labelStyle2: {
		fontSize: "26rpx",
		fontWeight: "bold",
		color: "#1152D7",
	},
	inputStyle2: {
		height: "88rpx",
		borderRadius: "10rpx",
		background: "#fff",
		padding: "0rpx 30rpx",
	},
	inputStyle3: {
		borderRadius: "16rpx",
		background: "#F2F6FB",
		padding: "25rpx 30rpx",
	},
	inputStyle4: {
		borderRadius: "16rpx",
		background: "#fff",
		padding: "25rpx 30rpx",
	},
	inputStyle5: {
		height: "105rpx",
		borderRadius: "16rpx",
		background: "#F2F6FB",
		padding: "0rpx 30rpx",
	},
	labelStyle6: {
		fontSize: "28rpx",
		fontWeight: "500",
		color: "#1A1A1B",
	},
	inputStyle6: {
		fontWeight: "bold",
		fontSize: "28rpx",
	},
	labelStyle7: {
		fontSize: "28rpx",
		fontWeight: "500",
		color: "#1A1A1B",
	},
	inputStyle7: {
		fontWeight: "500",
		fontSize: "30rpx",
		padding: 0,
	},
	inputStyle8: {
		// borderRadius: '16rpx',
		background: "#F3F4F7",
		padding: "25rpx 30rpx",
		borderRadius: "53rpx",
	},
	inputPlaceholderStyle: `font-size: 30rpx; font-weight: 500; color: #AEAEB4;`,
	inputPlaceholderStyle3: `font-size: 26rpx; font-weight: 500; color: #B1B6BC;`,
	inputPlaceholderStyle4: `font-size: 30rpx; font-weight: 500; color: #B1B6BC;`,
	inputPlaceholderStyle6: `font-size: 28rpx; font-weight: 500; color: #939398;`,
	inputPlaceholderStyle7: `font-size: 30rpx; font-weight: 500; color: #93939C;`,
});
</script>

<template>
	<view class="edit-user-info w-full">
		<view class="complete-user-info-content px-60 pt-120 pb-50 w-full">
			<view class="w-full flex flex-col items-center">
				<view class="w-168 h-168 relative">
					<button class="w-168 h-168 relative border-rd-168 uv-reset-button" open-type="chooseAvatar" @chooseavatar="onChooseAvatar" @click="uploadImage">
						<template v-if="userInfo.avatar">
							<app-image :src="userInfo.avatar" size="168rpx" rd="50%"></app-image>
						</template>
						<template v-else>
							<app-image :src="logo" size="168rpx" rd="50%"></app-image>
						</template>
					</button>

					<app-image class="absolute bottom-0 right-0" src="@/pages/my/static/icon_touxiang_xiangji.png" mode="widthFix" size="46rpx"></app-image>
				</view>
				<!-- <view class="text-#7A7A8A text-24 mt-22">
					请上传本人照片
				</view> -->

				<view class="w-full mt-65">
					<view class="text-32 font-bold">个人资料</view>

					<view class="">
						<uv-form labelPosition="left" :model="userInfo" :rules="formRules" :labelStyle="formStyle.labelStyle6" label-width="200rpx" ref="userInfoForm" class="w-full mt-15" errorType="toast">
							<view class="w-full px-0">
								<uv-form-item label="昵称" prop="nickname" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
									<uv-input v-model="userInfo.nickname" placeholder="请输入昵称" :placeholderStyle="formStyle.inputPlaceholderStyle6" :customStyle="{ ...formStyle.inputStyle6 }" fontSize="28rpx" border="none" type="nickname" inputAlign="right"></uv-input>
								</uv-form-item>

								<uv-form-item label="性别" prop="gender" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
									<page-uv-picker
										keyName="text"
										:columns="pageData.genderList"
										v-model:indexs="pageData.genderIndex"
										v-model:values="pageData.genderValue"
										@confirm="
											({ value }) => {
												userInfo.genderText = value[0].text;
												userInfo.gender = value[0].value;
											}
										">
										<uv-input v-model="userInfo.genderText" placeholder="请选择" :placeholderStyle="formStyle.inputPlaceholderStyle6" :customStyle="formStyle.inputStyle6" fontSize="28rpx" border="none" type="text" inputAlign="right" readonly></uv-input>
									</page-uv-picker>

									<template #right>
										<uv-icon name="arrow-right"></uv-icon>
									</template>
								</uv-form-item>

								<uv-form-item label="生日" prop="birthday" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom v-if="false">
									<view class="" @click="openBirthdayDatePicker">
										<uv-input v-model="userInfo.birthday" placeholder="请选择" :placeholderStyle="formStyle.inputPlaceholderStyle6" :customStyle="formStyle.inputStyle6" fontSize="28rpx" border="none" type="text" inputAlign="right" readonly></uv-input>
									</view>

									<template #right>
										<uv-icon name="arrow-right"></uv-icon>
									</template>
								</uv-form-item>

								<!-- <uv-form-item label="地区" prop="city" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
									<view class="" @click="changeAddress">
										<uv-input
											v-model="userInfo.city"
											placeholder="请选择"
											:placeholderStyle="formStyle.inputPlaceholderStyle6"
											:customStyle="formStyle.inputStyle6"
											fontSize="28rpx"
											border="none"
											type="text"
											inputAlign="right"
											readonly
										></uv-input>
									</view>

									<template #right>
										<uv-icon name="arrow-right"></uv-icon>
									</template>
								</uv-form-item> -->
								<uv-form-item label="地址" prop="areaVlaue" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
									<!-- <uni-data-picker
										v-model="pageData.localdataValue"
										:localdata="pageData.localdata"
										popup-title="请选择省市区"
										:map="{ text: 'address', value: 'code' }"
										@change="changeAddressPicker"
									>
										<view class="text-28 font-bold text-right pr-5">
											{{ userInfo.area.join(' ') }}
										</view>
										<uv-input
											:value="userInfo.area.join(' ')"
											placeholder="请选择地址"
											:placeholderStyle="formStyle.inputPlaceholderStyle6"
											:customStyle="formStyle.inputStyle6"
											fontSize="28rpx"
											border="none"
											type="text"
											inputAlign="right"
											readonly
										></uv-input>
									</uni-data-picker> -->

									<!-- #ifndef MP-WEIXIN -->
									<uni-data-picker collection="opendb-city-china" field="code as value, name as text" orderby="value asc" self-field="code" parent-field="parent_code" :preload="true" :step-searh="true" popup-title="请选择省市区" @change="changeAddressPicker">
										<view class="text-28 font-bold text-right pr-5">
											{{ userInfo.area.join(" ") }}
										</view>
									</uni-data-picker>
									<!-- #endif -->
									<!-- #ifdef MP-WEIXIN -->
									<picker mode="region" @change="changeAddressPickerWX">
										<view class="text-28 font-bold text-right pr-5">
											{{ userInfo.area.join(" ") }}
										</view>
									</picker>
									<!-- #endif -->

									<template #right>
										<uv-icon name="arrow-right"></uv-icon>
									</template>
								</uv-form-item>
							</view>
						</uv-form>
					</view>
				</view>
			</view>

			<view class="mt-120">
				<uv-button :loading="pageData.pageLoading" type="primary" text="保存修改" class="w-full flex-center font-bold" custom-style="height: 88rpx; border-radius: 44rpx;" customTextStyle="font-size: 30rpx;" @click="submit()"></uv-button>
			</view>
		</view>

		<uv-datetime-picker ref="birthdayDatePicker" v-model="userInfo.birthdayValue" mode="date" :maxDate="Date.now() - 1000 * 60 * 60 * 24" :minDate="Date.now() - 1000 * 60 * 60 * 24 * 365 * 120" @confirm="birthdayDatePickerConfirm" confirmColor="#38B597"></uv-datetime-picker>

		<uv-action-sheet
			ref="actionSheet"
			:actions="[
				{ name: '拍照', type: 'camera' },
				{ name: '从相册中选取', type: 'album' },
			]"
			round="20rpx"
			title=""
			@select="selectActionSheet"></uv-action-sheet>
	</view>
</template>

<style lang="scss" scoped>
// .edit-user-info {}
</style>
