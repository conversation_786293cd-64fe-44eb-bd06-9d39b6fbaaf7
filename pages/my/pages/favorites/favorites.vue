<script setup>
import { ref, reactive, getCurrentInstance } from "vue";

import { onLoad, onReady, onShow, onHide } from "@dcloudio/uni-app";

import { getAssessOrderList, goodsAddAssess } from "@/server/api";

import { usePublicStore, useAppStore } from "@/store";

import { getPriceInfo, route } from "@/common/utils";
import { common } from "@/common/images";

const paging = ref(null);

const pageData = reactive({
	typeList: [
		{
			name: "商品",
			type: "shop",
			id: "0",
			value: 0,
		},
		{
			name: "护理",
			type: "nurse",
			id: "1904049800822345728",
			value: 3,
		},
		{
			name: "陪诊",
			type: "attend",
			id: "1904111595356377088",
			value: 2,
		},
		{
			name: "家政",
			type: "homemaking",
			id: "1904111619062583296",
			value: 1,
		},
	],
	typeCurrent: 0,

	isEditor: false,

	sort: 1,
	dataList: [],

	value: 1,
});

pageData.typeList.map((item, index) => {
	pageData.typeList[index].value = Number(useAppStore().goodsServerTypeList[item.type].id || 0);
	pageData.typeList[index].id = useAppStore().goodsServerTypeList[item.type].trueId;
});

const checkProductValue = ref([]);

function changeType(e) {
	pageData.typeCurrent = e;
	if (paging.value) paging.value.reload();
}

function changeSort(e) {
	pageData.sort = e;
}

async function delCollection() {
	if (checkProductValue.value.length > 0) {
		uni.showModal({
			title: "提醒",
			content: "确定要取消收藏吗？",
			success(res) {
				if (res.confirm) {
					const items = checkProductValue.value.map((i) => {
						return pageData.dataList[i];
					});
					const itemIds = items.map((i) => i.id);

					goodsAddAssess({
						userCollectDTOList: items,
						whetherCollect: false,
						serveTypeId: pageData.typeList[pageData.typeCurrent].id,
					}).then((res) => {
						if (res.apiStatus) {
							checkProductValue.value = [];
							if (paging.value) paging.value.reload();
						}
					});
				}
			},
		});
	} else {
		openEditor(false);
	}
}

function openEditor(state = true) {
	checkProductValue.value = [];
	pageData.isEditor = state;
}

// 商品详情
function goGoodsDetails(goods) {
	route("/pages/goods/pages/productDetails/productDetails", {
		productId: goods.productId,
		shopId: goods.shopId,
	});
}

function queryList(pageNo, pageSize) {
	const params = {
		pageNo: pageNo,
		current: pageNo,
		pageSize: pageSize,
		serveTypeId: pageData.typeList[pageData.typeCurrent].id,
	};

	getAssessOrderList({ ...params })
		.then((res) => {
			if (res.apiStatus) {
				paging.value.complete(res.data.records);
			} else {
				if (paging.value) paging.value.complete(false);
			}
		})
		.catch((err) => {
			if (paging.value) paging.value.complete(false);
		});
}
</script>
<template>
	<app-layout>
		<z-paging ref="paging" bg-color="#F0F3F7" v-model="pageData.dataList" @query="queryList">
			<template #top>
				<app-navBar bgColor="#fff" back rightPlaceholderSize="140rpx" leftPlaceholderSize="140rpx">
					<template #content>
						<view class="w-full h-full flex justify-between items-center px-0">
							<view class="flex-1 nav-center">
								<text class="text-32 text-#000 font-bold">收藏夹</text>
							</view>
						</view>
					</template>
					<template #right>
						<template v-if="pageData.isEditor">
							<view class="flex items-center justify-end">
								<text class="text-30 font-500 flex-shrink-0" @click="openEditor(false)">完成</text>
								<text class="text-30 font-500 flex-shrink-0 ml-20 text-#FC3F33" @click="delCollection">删除</text>
							</view>
						</template>
						<template v-if="!pageData.isEditor">
							<view class="flex items-center justify-end">
								<text class="text-30 font-500" @click="openEditor(true)">管理</text>
							</view>
						</template>
					</template>
				</app-navBar>
				<view class="w-full pb-20 bg-#fff">
					<view class="flex px-10 pt-20 justify-between">
						<template v-for="(type, index) in pageData.typeList" :key="index">
							<view @click="changeType(index)" class="bg-#F2F4F8 border-rd-8 flex-1 h-60 text-center flex flex-center mx-10 border-solid border-1 border-#F2F4F8" :style="[pageData.typeCurrent === index && 'background-color: #EFFAF7; border-color: #09C1B1;']">
								<text class="text-26 text-#323232 font-500" :style="[pageData.typeCurrent === index && 'color: #09C1B1; font-weight: bold']">{{ type.name }}</text>
							</view>
						</template>
					</view>
				</view>
			</template>
			<view class="evaluation py-20">
				<uv-checkbox-group v-model="checkProductValue" placement="column" shape="circle" activeColor="#09C1B1">
					<template v-for="(item, index) in pageData.dataList" :key="index">
						<view class="px-20">
							<view class="w-full bg-#fff border-rd-20 py-35-n py-26 px-26 mb-20">
								<!-- <view class="w-full flex items-center">
									<view class="mr-12">
										<uv-checkbox :name="index"></uv-checkbox>
									</view>
									<view class="text-32 font-bold">XX官方旗舰店</view>
									<view class="">
										<app-image :src="common.iconRightHui" size="20" ml="5" mode=""></app-image>
									</view>
								</view> -->

								<view class="w-full">
									<view class="w-full flex items-center mt-30-n">
										<view class="mr-20">
											<template v-if="pageData.isEditor">
												<view class="mr-20">
													<uv-checkbox :name="index"></uv-checkbox>
												</view>
											</template>
										</view>
										<view class="">
											<view class="w-180 h-180 border-1 border-solid border-#F5F5F5 border-rd-20" @click="goGoodsDetails(item)">
												<app-image :src="item.productPic" size="178" rd="20" mode=""></app-image>
											</view>
										</view>
										<view class="flex-1 ml-20 min-h-180" @click="goGoodsDetails(item)">
											<view class="w-full flex flex-col">
												<view class="text-30 font-bold uv-line-1">{{ item.productName }}</view>
												<view class="w-full mt-18">
													<view class="h-46"></view>
													<!-- <view class="w-full bg-#F8F8F8 border-rd-6 h-46 flex items-center px-10">
														<view class="flex-1 text-#555555 text-24 font-500 uv-line-1">米色/三人+弧形转角位/280cm</view>

														<view class="pl-20">
															<app-image :src="common.iconJiantouBottomHui" size="20" mode=""></app-image>
														</view>
													</view> -->
												</view>
												<view class="w-full flex items-center justify-between mt-30">
													<view class="flex items-end text-#FC3F33 font-bold">
														<view class="text-30">￥</view>
														<view class="text-34">{{ getPriceInfo(item.productPrice).integer }}.{{ getPriceInfo(item.productPrice).decimalText }}</view>
													</view>

													<!-- <view class="">
														<uv-number-box v-model="pageData.value" min="1" :integer="true" inputWidth="60rpx" buttonSize="50rpx" bgColor="#F3F4F6"></uv-number-box>
													</view> -->
												</view>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</template>
				</uv-checkbox-group>
			</view>
		</z-paging>
	</app-layout>
</template>
<style lang="scss" scoped>
.evaluation {
}
</style>
