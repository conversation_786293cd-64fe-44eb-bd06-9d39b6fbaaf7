<script setup>
import { onLoad } from '@dcloudio/uni-app';

import { ref, reactive } from 'vue';

import { useUserStore } from '@/store';

import { canENV, route } from '@/common/utils';

import { addCaregiver, editCaregiver, getCaregiverData, getAccounts, setAccounts } from '@/server/api';

import { REGEX_CITIZEN_ID } from '@/common/test';

const userStore = useUserStore();

const props = defineProps({
	card: {
		type: [String, Number],
		default: ''
	},
	isEdit: {
		type: [String, Number],
		default: 0
	},
	showCardInput: {
		type: [String, Number],
		default: 1
	}
});

const pageData = reactive({
	bankNameList: [
		[
			{
				text: '建设银行',
				value: '建设银行'
			},
			{
				text: '工商银行',
				value: '工商银行'
			},
			{
				text: '农业银行',
				value: '农业银行'
			},
			{
				text: '中国银行',
				value: '中国银行'
			},
			{
				text: '交通银行',
				value: '交通银行'
			},
			{
				text: '邮储银行',
				value: '邮储银行'
			},
			{
				text: '招商银行',
				value: '招商银行'
			},
			{
				text: '浦发银行',
				value: '浦发银行'
			},
			{
				text: '中信银行',
				value: '中信银行'
			},
			{
				text: '光大银行',
				value: '光大银行'
			},
			{
				text: '民生银行',
				value: '民生银行'
			},
			{
				text: '兴业银行',
				value: '兴业银行'
			},
			{
				text: '平安银行',
				value: '平安银行'
			},
			{
				text: '华夏银行',
				value: '华夏银行'
			},
			{
				text: '广发银行',
				value: '广发银行'
			},
			{
				text: '浙商银行',
				value: '浙商银行'
			}
		]
	],
	bankNameIndex: [[]],
	bankNameValue: [[]]
});

const formData = reactive({
	cardNo: '', // 卡号

	name: '', // 姓名

	bank: '', // 开户行

	bankName: '', // 所属银行
	bankNameText: ''
});

const formRules = {
	cardNo: [
		{
			required: true,
			message: '请输入卡号',
			trigger: 'blur'
		}
	],
	name: [
		{
			required: true,
			message: '请输入名称',
			trigger: 'blur'
		}
	],
	bank: [
		{
			required: true,
			message: '请输入开户行',
			trigger: 'blur'
		}
	],
	bankName: [
		{
			required: true,
			message: '请选择所属银行',
			trigger: 'blur'
		}
	]
};

const pageFormRef = ref();

async function loadData() {
	if (props.card) {
		formData.cardNo = props.card;
	}
	if (Number(props.isEdit) !== 0) {
		loadInfo();
	}
}

function loadInfo() {
	getAccounts().then((res) => {
		if (res.apiStatus && typeof res.data === 'object' && res.data.BANK_CARD) {
			formData.cardNo = res.data.BANK_CARD.cardNo;
			formData.name = res.data.BANK_CARD.name;
			formData.bank = res.data.BANK_CARD.bank;

			for (const [index, item] of Object.entries(pageData.bankNameList[0])) {
				if (item.value === res.data.BANK_CARD.bankName) {
					// pageData.bankNameIndex = [[index]];
					// pageData.bankNameValue = [[item]];
					formData.bankName = item.value;
					formData.bankNameText = item.text;
					break;
				}
			}
		}
	});
}

function changeMedicalHistory(item, index) {
	if (formData.medicalHistory !== item.value) {
		formData.medicalHistory = item.value;
		formData.medicalHistoryRemark = '';
	}
}

function submit() {
	if (pageFormRef.value) {
		canENV(() => {
			console.log(formData);
		});
		if (userStore.checkLogin) {
			pageFormRef.value.validate().then((res) => {
				setAccounts({
					type: 'BANK_CARD',
					detail: {
						bank: formData.bank,
						cardNo: formData.cardNo,
						name: formData.name,
						bankName: formData.bankName
					}
				}).then((res) => {
					if (res.apiStatus) {
						route({
							type: 'back'
						});
					}
				});
			});
		} else {
			uni.showToast({
				icon: 'fail',
				title: '请先登录'
			});
		}
	}
}

onLoad(() => {
	loadData();
});

const formStyle = ref({
	labelStyle: {
		fontSize: '30rpx',
		fontWeight: 'bold'
	},
	inputStyle: {
		height: '98rpx',
		padding: '0rpx 0rpx',
		borderBottom: `solid 1rpx #E9EDF1`
	},
	labelStyle2: {
		fontSize: '26rpx',
		fontWeight: 'bold',
		color: '#1152D7'
	},
	inputStyle2: {
		height: '88rpx',
		borderRadius: '10rpx',
		background: '#fff',
		padding: '0rpx 30rpx'
	},
	inputStyle3: {
		borderRadius: '16rpx',
		background: '#F2F6FB',
		padding: '25rpx 30rpx'
	},
	inputStyle4: {
		borderRadius: '16rpx',
		background: '#fff',
		padding: '25rpx 30rpx'
	},
	inputStyle5: {
		height: '105rpx',
		borderRadius: '16rpx',
		background: '#F2F6FB',
		padding: '0rpx 30rpx'
	},
	labelStyle6: {
		fontSize: '28rpx',
		fontWeight: '500',
		color: '#1A1A1B'
	},
	inputStyle6: {
		fontWeight: 'bold',
		fontSize: '28rpx'
	},
	labelStyle7: {
		fontSize: '28rpx',
		fontWeight: '500',
		color: '#1A1A1B'
	},
	inputStyle7: {
		fontWeight: '500',
		fontSize: '30rpx',
		padding: 0
	},
	inputStyle8: {
		// borderRadius: '16rpx',
		background: '#F3F4F7',
		padding: '25rpx 30rpx',
		borderRadius: '53rpx'
	},
	labelStyle9: {
		fontSize: '26rpx',
		fontWeight: '500',
		color: '#555555',
		paddingLeft: '8rpx'
	},
	inputPlaceholderStyle: `font-size: 30rpx; font-weight: 500; color: #AEAEB4;`,
	inputPlaceholderStyle3: `font-size: 26rpx; font-weight: 500; color: #B1B6BC;`,
	inputPlaceholderStyle4: `font-size: 30rpx; font-weight: 500; color: #B1B6BC;`,
	inputPlaceholderStyle6: `font-size: 28rpx; font-weight: 500; color: #939398;`,
	inputPlaceholderStyle7: `font-size: 30rpx; font-weight: 500; color: #93939C;`
});
</script>

<template>
	<view class="add-caregiver pb-150">
		<view class="w-full py-30 px-50">
			<view class="w-full border-rd-20 bg-#FFFFFF px-0">
				<uv-form :model="formData" :rules="formRules" :labelStyle="formStyle.labelStyle9" label-width="200rpx" labelPosition="top" errorType="toast" ref="pageFormRef">
					<!-- 所属银行 -->
					<uv-form-item label="所属银行" prop="bankName" :customStyle="{ padding: '40rpx 0 0rpx' }" borderBottom>
						<page-uv-picker
							keyName="text"
							:columns="pageData.bankNameList"
							v-model:indexs="pageData.bankNameIndex"
							v-model:values="pageData.bankNameValue"
							@confirm="
								({ value }) => {
									formData.bankNameText = value[0].text;
									formData.bankName = value[0].value;
								}
							"
						>
							<view class="pt-20 pb-40">
								<uv-input
									v-model="formData.bankNameText"
									placeholder="请选择所属银行"
									:placeholderStyle="formStyle.inputPlaceholderStyle6"
									:customStyle="formStyle.inputStyle6"
									fontSize="28rpx"
									border="none"
									type="text"
									inputAlign="left"
									readonly
								></uv-input>
							</view>
						</page-uv-picker>

						<template #right>
							<uv-icon name="arrow-right"></uv-icon>
						</template>
					</uv-form-item>

					<!-- 卡号 -->
					<template v-if="Number(showCardInput) === 1">
						<uv-form-item label="卡号" prop="cardNo" :customStyle="{ padding: '40rpx 0 0rpx' }" borderBottom>
							<view class="pt-20 pb-40">
								<uv-input
									v-model="formData.cardNo"
									placeholder="请输入卡号"
									:placeholderStyle="formStyle.inputPlaceholderStyle6"
									:customStyle="{ ...formStyle.inputStyle6 }"
									fontSize="28rpx"
									border="none"
									type="text"
									inputAlign="left"
								></uv-input>
							</view>
						</uv-form-item>
					</template>

					<!-- 姓名 -->
					<uv-form-item label="姓名" prop="name" class="w-full" :customStyle="{ padding: '0rpx 0' }" borderBottom>
						<view class="pt-20 pb-40">
							<uv-input
								v-model="formData.name"
								placeholder="请输入你的真实姓名"
								:placeholderStyle="formStyle.inputPlaceholderStyle6"
								:customStyle="{ ...formStyle.inputStyle6 }"
								fontSize="28rpx"
								border="none"
								type="text"
								inputAlign="left"
							></uv-input>
						</view>
					</uv-form-item>

					<!-- 开户行 -->
					<uv-form-item label="开户行" prop="bank" :customStyle="{ padding: '40rpx 0 0rpx' }" borderBottom>
						<view class="pt-20 pb-40">
							<uv-input
								v-model="formData.bank"
								placeholder="请输入开户行"
								:placeholderStyle="formStyle.inputPlaceholderStyle6"
								:customStyle="{ ...formStyle.inputStyle6 }"
								fontSize="28rpx"
								border="none"
								type="text"
								inputAlign="left"
							></uv-input>
						</view>
					</uv-form-item>
				</uv-form>
			</view>
		</view>
		<view class="w-full h-142 px-30 pt-14 bg-#fff fixed left-0 bottom-0 border-t-solid border-t-#EDEEF0">
			<uv-button
				@click="submit()"
				color="#00B496"
				text="确认"
				class="w-690 h-88"
				custom-style="border-radius: 44rpx;"
				customTextStyle="font-size: 30rpx; color: #fff; font-weight: bold;"
			></uv-button>
		</view>
	</view>
</template>

<style lang="scss" scoped>
.add-caregiver {
	min-height: calc(100vh - var(--window-top) - var(--window-bottom));
	// background-color: #F1F3F7;
	background: #fff;
}
</style>
