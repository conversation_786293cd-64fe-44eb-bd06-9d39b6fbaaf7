<script setup>
import { ref, reactive, getCurrentInstance, computed } from "vue";

import { onLoad, onShow } from "@dcloudio/uni-app";

import { getAccounts, delAccounts } from "@/server/api";

import { route } from "@/common/utils";
import { logo } from "@/common/images";

const payTypeArr = [
	{
		text: "微信",
		type: "WECHAT",
	},
	// #ifdef APP || H5
	// {
	// 	text: '支付宝',
	// 	type: 'ALIPAY'
	// },
	// #endif
	{
		text: "银行卡",
		type: "BANK_CARD",
	},
];

const pageData = reactive({
	manageStatus: false,

	bankTypeData: {
		WECHAT: {
			name: "微信",
			addUrl: "/pages/my/pages/account/bankCardWeiXin/bankCardWeiXin",
			data: null,
		},

		// #ifdef APP || H5
		ALIPAY: {
			name: "支付宝",
			addUrl: "/pages/my/pages/account/bankCardAli/bankCardAli",
			data: null,
		},
		// #endif

		BANK_CARD: {
			name: "银行卡",
			addUrl: "/pages/my/pages/account/bankCard/bankCard",
			data: null,
		},
	},

	bankTypeList: [],
});

const canAdd = computed(() => {
	let status = false;
	for (const [key, value] of Object.entries(pageData.bankTypeData)) {
		if (!value.data) {
			status = true;
			break;
		}
	}

	return status;
});

const addList = computed(() => {
	let arr = [];
	for (const [key, value] of Object.entries(pageData.bankTypeData)) {
		if (!value.data) {
			arr.push(value);
		}
	}

	return arr;
});

const pagePaging = ref(null);

const actionSheet = ref();

function queryList(pageNo, pageSize) {
	const params = {
		pageNo: pageNo,
		current: pageNo,
		pageSize: pageSize,
	};

	getAccounts()
		.then((res) => {
			if (res.apiStatus) {
				// pagePaging.value.complete(res.data.records)
				if (typeof res.data === "object") {
					for (const [key, value] of Object.entries(pageData.bankTypeData)) {
						if (res.data[key]) {
							pageData.bankTypeData[key].data = res.data[key];
						} else {
							pageData.bankTypeData[key].data = null;
						}
					}
					let arr = [];
					for (const [key, value] of Object.entries(res.data)) {
						arr.push({
							...value,
							type: key,
						});
					}

					if (pagePaging.value) {
						pagePaging.value.complete(arr);
					}
				}
			} else {
				if (pagePaging.value) pagePaging.value.complete(false);
			}
		})
		.catch((err) => {
			if (pagePaging.value) pagePaging.value.complete(false);
		});
}

function delBank(item) {
	uni.showModal({
		title: "提醒",
		content: "确定要删除吗？",
		success(res) {
			if (res.confirm) {
				delAccounts(
					{},
					{
						params: {
							...item,
						},
					}
				).then((res) => {
					if (res.apiStatus) {
						if (pagePaging.value) pagePaging.value.reload();
					}
				});
			}
		},
	});
}

function addBank() {
	actionSheet.value.open();
}

function selectActionSheet(e) {
	route(e.addUrl);
}

function loadData() {}

onLoad(() => {
	loadData();
});

onShow(() => {
	if (pagePaging.value) pagePaging.value.reload();
});
</script>

<template>
	<app-layout>
		<z-paging ref="pagePaging" v-model="pageData.bankTypeList" @query="queryList" :auto="true">
			<template #top>
				<view class="flex justify-between items-center px-40 py-20">
					<text class="text-30 text-#222 font-bold">账户</text>
					<view @click="pageData.manageStatus = !pageData.manageStatus" class="text-26 font-500">
						<text v-if="!pageData.manageStatus" class="text-#8A90A0">管理</text>
						<text v-else class="text-#00B496">退出管理</text>
					</view>
				</view>
			</template>

			<view class="px-40">
				<template v-for="(bank, index) in pageData.bankTypeList" :key="index">
					<template v-if="bank.type === 'WECHAT'">
						<view class="px-36 border-rd-8 mb-20 bank-item wx">
							<view class="flex justify-between items-center py-36" @click="route('/pages/my/pages/account/bankCardWeiXin/bankCardWeiXin', { isEdit: 1 })">
								<view class="flex justify-start items-center">
									<app-image :src="logo" class="w-70 h-70" size="70" rd="50%" mode="" v-if="false"></app-image>
									<view class="h-70 ml-20">
										<view class="text-30 text-#fff font-bold">微信</view>
										<view class="text-22 text-[rgba(255,255,255,0.7)] font-500 mt-10">电子账户</view>
									</view>
								</view>
								<view class="text-36 text-#fff font-bold">{{ bank.account ? `****${bank.account.slice(bank.account.length - 4)}` : "" }}</view>
							</view>
							<view v-if="pageData.manageStatus" class="text-right h-70 line-height-70 border-t-solid border-1 border-[rgba(255,255,255,0.16)] text-22 text-#fff font-500" @click="delBank(bank)"> 解除绑定 </view>
						</view>
					</template>

					<!-- #ifdef APP || H5 -->
					<template v-if="bank.type === 'ALIPAY'">
						<!-- <view class="px-36 border-rd-8 mb-20 bank-item zfb">
							<view class="flex justify-between items-center py-36" @click="route('/pages/my/pages/account/bankCardAli/bankCardAli', { isEdit: 1 })">
								<view class="flex justify-start items-center">
									<app-image :src="logo" class="w-70 h-70" size="70" rd="50%" mode="" v-if="false"></app-image>
									<view class="h-70 ml-20">
										<view class="text-30 text-#fff font-bold">支付宝</view>
										<view class="text-22 text-[rgba(255,255,255,0.7)] font-500 mt-10">电子账户</view>
									</view>
								</view>
								<view class="text-36 text-#fff font-bold">{{ bank.account ? `****${bank.account.slice(bank.account.length - 4)}` : "" }}</view>
							</view>
						</view> -->
					</template>
					<!-- #endif -->

					<template v-if="bank.type === 'BANK_CARD'">
						<view class="px-36 border-rd-8 mb-20 bank-item yhk">
							<view class="flex justify-between items-center py-36" @click="route('/pages/my/pages/account/bankCard/bankCard', { isEdit: 1 })">
								<view class="flex justify-start items-center">
									<app-image :src="logo" class="w-70 h-70" size="70" rd="50%" mode="" v-if="false"></app-image>
									<view class="h-70 ml-20">
										<view class="text-30 text-#fff font-bold">{{ bank.bankName }}</view>
										<view class="text-22 text-[rgba(255,255,255,0.7)] font-500 mt-10">储蓄卡</view>
									</view>
								</view>
								<view class="text-36 text-#fff font-bold">{{ bank.cardNo ? `****${bank.cardNo.slice(bank.cardNo.length - 4)}` : "" }}</view>
							</view>
							<view v-if="pageData.manageStatus" class="text-right h-70 line-height-70 border-t-solid border-1 border-[rgba(255,255,255,0.16)] text-22 text-#fff font-500" @click="delBank(bank)"> 解除绑定 </view>
						</view>
					</template>
				</template>
			</view>

			<template #bottom>
				<template v-if="canAdd">
					<view class="py-14 px-40 border-t-solid border-1 border-#F2F2F2 bg-#fff">
						<uv-button @click="addBank" color="#00B496" text="添加账户" shape="circle" custom-style="height: 84rpx;" customTextStyle="font-size: 30rpx; font-weight: bold;color: #fff;"></uv-button>
						<app-safeAreaBottom></app-safeAreaBottom>
					</view>
				</template>
			</template>
		</z-paging>

		<my-uv-action-sheet ref="actionSheet" :actions="addList" title="" cancelText="取消" round="20rpx" @select="selectActionSheet"></my-uv-action-sheet>
	</app-layout>
</template>

<style lang="scss" scoped>
.bank-item {
	&.jianshe {
		background: linear-gradient(-90deg, #4068c0 0%, #498cd0 100%);
	}
	&.yhk {
		background: linear-gradient(-90deg, #dcb26a 0%, #ecc88a 100%);
	}
	&.zfb {
		background: linear-gradient(-90deg, #0a73cf 0%, #108ee9 100%);
	}
	&.wx {
		background: linear-gradient(-90deg, #2aad67 0%, #07c160 100%);
	}
}
</style>
