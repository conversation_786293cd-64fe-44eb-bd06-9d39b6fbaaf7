<script setup>
import { onShow, onLoad, onNavigationBarButtonTap } from '@dcloudio/uni-app';

import { ref, reactive, computed, watchEffect } from 'vue';

import { useUserStore } from '@/store';

import { goArticlePage, canENV, route } from '@/common/utils';

const userStore = useUserStore();

const loginForm = reactive({
	cardCode: ''
});

const loginLoading = ref(false);

const loginFormRules = {
	cardCode: [
		{
			required: true,
			message: '请输入卡号',
			trigger: 'blur'
		}
	]
};

const loginFormRef = ref();

const loginFormSend = computed(() => {
	return Boolean(loginForm.cardCode);
});

function submit() {
	loginFormRef.value.validate().then((res) => {
		route('/pages/my/pages/account/bankCard/bankCard', {
			card: loginForm.cardCode,
			showCardInput: 0
		});

		// loginLoading.value = true

		// checkSMS({
		// 		...loginForm,
		// 		event: 'mobilelogin'
		// 	}, {
		// 		custom: {
		// 			catch: true
		// 		}
		// 	})
		// 	.then((loginRes) => {
		// 		if (loginRes.apiStatus) {
		// 			route('/pages/login/pages/verifyNewMobile/verifyNewMobile')
		// 		} else {
		// 			loginLoading.value = false
		// 		}
		// 	})
		// 	.catch((err) => {
		// 		loginLoading.value = false
		// 	});
	});
}

function recognizeCard() {}

const formStyle = ref({
	labelStyle: {
		fontSize: '30rpx',
		fontWeight: 'bold'
	},
	inputStyle: {
		height: '98rpx',
		padding: '0rpx 0rpx',
		borderBottom: `solid 1rpx #E9EDF1`
	},
	labelStyle2: {
		fontSize: '26rpx',
		fontWeight: 'bold',
		color: '#1152D7'
	},
	inputStyle2: {
		height: '88rpx',
		borderRadius: '10rpx',
		background: '#fff',
		padding: '0rpx 30rpx'
	},
	inputStyle3: {
		borderRadius: '16rpx',
		background: '#F2F6FB',
		padding: '25rpx 30rpx'
	},
	inputStyle4: {
		borderRadius: '16rpx',
		background: '#fff',
		padding: '25rpx 30rpx'
	},
	inputStyle5: {
		height: '105rpx',
		borderRadius: '16rpx',
		background: '#F2F6FB',
		padding: '0rpx 30rpx'
	},
	labelStyle6: {
		fontSize: '28rpx',
		fontWeight: '500',
		color: '#1A1A1B'
	},
	inputStyle6: {
		fontWeight: 'bold',
		fontSize: '28rpx'
	},
	labelStyle7: {
		fontSize: '28rpx',
		fontWeight: '500',
		color: '#1A1A1B'
	},
	inputStyle7: {
		fontWeight: '500',
		fontSize: '30rpx',
		padding: 0
	},
	inputStyle8: {
		// borderRadius: '16rpx',
		background: '#F3F4F7',
		padding: '25rpx 30rpx',
		borderRadius: '53rpx'
	},
	inputPlaceholderStyle: `font-size: 30rpx; font-weight: 500; color: #AEAEB4;`,
	inputPlaceholderStyle3: `font-size: 26rpx; font-weight: 500; color: #B1B6BC;`,
	inputPlaceholderStyle4: `font-size: 30rpx; font-weight: 500; color: #B1B6BC;`,
	inputPlaceholderStyle6: `font-size: 28rpx; font-weight: 500; color: #939398;`,
	inputPlaceholderStyle7: `font-size: 30rpx; font-weight: 500; color: #93939C;`
});

const labelStyle = {
	fontSize: '26rpx',
	fontWeight: 'bold'
};
</script>

<template>
	<app-layout>
		<view class="login-code">
			<view class="w-full pt-40 px-50">
				<view class="mt-0">
					<uv-form labelPosition="top" labelWidth="auto" :labelStyle="formStyle.labelStyle" :model="loginForm" :rules="loginFormRules" ref="loginFormRef">
						<uv-form-item label="输入卡号添加" class="" prop="cardCode">
							<view class="w-full mt-28">
								<uv-input
									v-model="loginForm.cardCode"
									placeholder="请输入银行卡号"
									class="mt-28 text-32"
									:placeholderStyle="formStyle.inputPlaceholderStyle"
									:customStyle="formStyle.inputStyle8"
									fontSize="32rpx"
									border="none"
									clearable
									type="number"
								>
									<template #suffix>
										<!-- <view class="w-full">
											<uv-icon name="camera" color="#464447" size="48rpx" @click="recognizeCard"></uv-icon>
										</view> -->
									</template>
								</uv-input>
							</view>
						</uv-form-item>

						<view class="w-full flex flex-center mt-70">
							<view class="">
								<uv-button
									:color="'#00B496'"
									text="提交卡号"
									loadingText="加载中..."
									class="w-640 flex-center"
									custom-style="height: 90rpx;"
									customTextStyle="font-size: 30rpx; font-weight: bold;"
									shape="circle"
									loadingMode="circle"
									:loading="loginLoading"
									:disabled="!loginFormSend"
									@click="submit"
								></uv-button>
							</view>
						</view>
					</uv-form>
				</view>
			</view>
		</view>
	</app-layout>
</template>

<style lang="scss" scoped></style>
