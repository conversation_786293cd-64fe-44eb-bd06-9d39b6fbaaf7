<script setup>
import { onLoad } from '@dcloudio/uni-app';

import { ref, reactive } from 'vue';

import { useUserStore } from '@/store';

import { canENV, route } from '@/common/utils';

import { addCaregiver, editCaregiver, getCaregiverData, getAccounts, setAccounts } from '@/server/api';

import { REGEX_CITIZEN_ID } from '@/common/test';

const userStore = useUserStore();

const props = defineProps({
	card: {
		type: [String, Number],
		default: ''
	},
	isEdit: {
		type: [String, Number],
		default: 0
	},
	showCardInput: {
		type: [String, Number],
		default: 1
	}
});

const pageData = reactive({});

const formData = reactive({
	name: '', // 姓名
	account: '' // 账户
});

const formRules = {
	account: [
		{
			required: true,
			message: '请输入账号',
			trigger: 'blur'
		}
	],
	name: [
		{
			required: true,
			message: '请输入名称',
			trigger: 'blur'
		}
	]
};

const pageFormRef = ref();

async function loadData() {
	if (props.card) {
		formData.cardNo = props.card;
	}
	if (Number(props.isEdit) !== 0) {
		loadInfo();
	}
}

function loadInfo() {
	getAccounts().then((res) => {
		if (res.apiStatus && typeof res.data === 'object' && res.data.WECHAT) {
			formData.account = res.data.WECHAT.account;
			formData.name = res.data.WECHAT.name;
		}
	});
}

function changeMedicalHistory(item, index) {
	if (formData.medicalHistory !== item.value) {
		formData.medicalHistory = item.value;
		formData.medicalHistoryRemark = '';
	}
}

function submit() {
	if (pageFormRef.value) {
		canENV(() => {
			console.log(formData);
		});
		if (userStore.checkLogin) {
			pageFormRef.value.validate().then((res) => {
				setAccounts({
					type: 'WECHAT',
					detail: {
						name: formData.name,
						account: formData.account
					}
				}).then((res) => {
					if (res.apiStatus) {
						route({
							type: 'back'
						});
					}
				});
			});
		} else {
			uni.showToast({
				icon: 'fail',
				title: '请先登录'
			});
		}
	}
}

onLoad(() => {
	loadData();
});

const formStyle = ref({
	labelStyle: {
		fontSize: '30rpx',
		fontWeight: 'bold'
	},
	inputStyle: {
		height: '98rpx',
		padding: '0rpx 0rpx',
		borderBottom: `solid 1rpx #E9EDF1`
	},
	labelStyle2: {
		fontSize: '26rpx',
		fontWeight: 'bold',
		color: '#1152D7'
	},
	inputStyle2: {
		height: '88rpx',
		borderRadius: '10rpx',
		background: '#fff',
		padding: '0rpx 30rpx'
	},
	inputStyle3: {
		borderRadius: '16rpx',
		background: '#F2F6FB',
		padding: '25rpx 30rpx'
	},
	inputStyle4: {
		borderRadius: '16rpx',
		background: '#fff',
		padding: '25rpx 30rpx'
	},
	inputStyle5: {
		height: '105rpx',
		borderRadius: '16rpx',
		background: '#F2F6FB',
		padding: '0rpx 30rpx'
	},
	labelStyle6: {
		fontSize: '28rpx',
		fontWeight: '500',
		color: '#1A1A1B'
	},
	inputStyle6: {
		fontWeight: 'bold',
		fontSize: '28rpx'
	},
	labelStyle7: {
		fontSize: '28rpx',
		fontWeight: '500',
		color: '#1A1A1B'
	},
	inputStyle7: {
		fontWeight: '500',
		fontSize: '30rpx',
		padding: 0
	},
	inputStyle8: {
		// borderRadius: '16rpx',
		background: '#F3F4F7',
		padding: '25rpx 30rpx',
		borderRadius: '53rpx'
	},
	labelStyle9: {
		fontSize: '26rpx',
		fontWeight: '500',
		color: '#555555',
		paddingLeft: '8rpx'
	},
	inputPlaceholderStyle: `font-size: 30rpx; font-weight: 500; color: #AEAEB4;`,
	inputPlaceholderStyle3: `font-size: 26rpx; font-weight: 500; color: #B1B6BC;`,
	inputPlaceholderStyle4: `font-size: 30rpx; font-weight: 500; color: #B1B6BC;`,
	inputPlaceholderStyle6: `font-size: 28rpx; font-weight: 500; color: #939398;`,
	inputPlaceholderStyle7: `font-size: 30rpx; font-weight: 500; color: #93939C;`
});
</script>

<template>
	<view class="add-caregiver pb-150">
		<view class="w-full py-30 px-50">
			<view class="w-full border-rd-20 bg-#FFFFFF px-0">
				<uv-form :model="formData" :rules="formRules" :labelStyle="formStyle.labelStyle9" label-width="200rpx" labelPosition="top" errorType="toast" ref="pageFormRef">
					<!-- 姓名 -->
					<uv-form-item label="姓名" prop="name" class="w-full" :customStyle="{ padding: '0rpx 0' }" borderBottom>
						<view class="pt-20 pb-40">
							<uv-input
								v-model="formData.name"
								placeholder="请输入你的真实姓名"
								:placeholderStyle="formStyle.inputPlaceholderStyle6"
								:customStyle="{ ...formStyle.inputStyle6 }"
								fontSize="28rpx"
								border="none"
								type="text"
								inputAlign="left"
							></uv-input>
						</view>
					</uv-form-item>

					<!-- 账号 -->
					<template v-if="Number(showCardInput) === 1">
						<uv-form-item label="账号" prop="account" :customStyle="{ padding: '40rpx 0 0rpx' }" borderBottom>
							<view class="pt-20 pb-40">
								<uv-input
									v-model="formData.account"
									placeholder="请输入账号"
									:placeholderStyle="formStyle.inputPlaceholderStyle6"
									:customStyle="{ ...formStyle.inputStyle6 }"
									fontSize="28rpx"
									border="none"
									type="text"
									inputAlign="left"
								></uv-input>
							</view>
						</uv-form-item>
					</template>
				</uv-form>
			</view>
		</view>
		<view class="w-full h-142 px-30 pt-14 bg-#fff fixed left-0 bottom-0 border-t-solid border-t-#EDEEF0">
			<uv-button
				@click="submit()"
				color="#00B496"
				text="确认"
				class="w-690 h-88"
				custom-style="border-radius: 44rpx;"
				customTextStyle="font-size: 30rpx; color: #fff; font-weight: bold;"
			></uv-button>
		</view>
	</view>
</template>

<style lang="scss" scoped>
.add-caregiver {
	min-height: calc(100vh - var(--window-top) - var(--window-bottom));
	// background-color: #F1F3F7;
	background: #fff;
}
</style>
