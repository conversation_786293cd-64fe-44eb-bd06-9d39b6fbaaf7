<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';

import { onLoad } from '@dcloudio/uni-app';

import { Decimal } from 'decimal.js';

import { getDistributeCenter, getDistributeCustomer, getDistributeOrder, getDistributeTeam } from '@/server/api';

import { route, salesVolumeToStr, getPriceInfo } from '@/common/utils';

import useConvert from '@/common/useConvert';

const paging = ref(null);

const { divTenThousand } = useConvert();

const pageData = reactive({
	typeList: [
		{
			name: '待结算',
			value: 'PAID'
		},
		{
			name: '已结算',
			value: 'COMPLETED'
		}
	],
	typeIndex: 0,

	dataList: [],

	totalIntegral: 0
});

const memberInfo = ref({
	avatar: '',
	nickname: '',
	name: '',
	config: {
		id: '',
		level: 'ONE',
		protocol: '',
		condition: {
			types: ['APPLY'],
			requiredAmount: 0
		},
		purchase: false,
		poster: '',
		shareType: 'RATE',
		one: '0'
	},
	createTime: '',
	id: '',
	identity: 'UserType',
	mobile: '',
	statistics: { customer: '0', order: '0', bonus: '0' },
	total: '0',
	undrawn: '0',
	unsettled: '0',
	userId: '',
	referrer: '',
	code: ''
});

const teamNumber = ref(0);

function getcount(orderitem, level, orderStatus) {
	let count = new Decimal(0);
	if ('items' in orderitem && orderitem.items.length) {
		orderitem.items.forEach((item) => {
			if (orderStatus && item.orderStatus !== orderStatus) return;
			if (item[level.toLowerCase()].bonus) {
				// count = count.add(divTenThousand(item[level.toLowerCase()].bonus));
				count = count.add(item[level.toLowerCase()].bonus);
			}
		});
	}

	return count.toNumber();
}

function changeType(e) {
	pageData.typeIndex = e.index;
	if (paging.value) paging.value.reload();
}

function changeSort(e) {
	pageData.sort = e;
}

async function loadData() {}

function queryList(pageNo, pageSize) {
	const params = {
		pageNo: pageNo,
		current: pageNo,
		pageSize: pageSize
	};

	getDistributeTeam().then((res) => {
		if (res.apiStatus) {
			if (paging.value) paging.value.complete(res.data.records);

			teamNumber.value = res.data.total;
		}
	});
}

onLoad(() => {
	// loadData();
});
</script>
<template>
	<app-layout>
		<z-paging ref="paging" bg-color="#F1F3F7" v-model="pageData.dataList" @query="queryList">
			<template #top>
				<view class="w-full px-30 pt-35 pb-5 flex items-center">
					<view class="w-6 h-24 border-rd-2 bg-#00B496 mr-16"></view>
					<view class="text-30 font-bold">{{ teamNumber }}人</view>
				</view>
			</template>
			<view class="evaluation py-0">
				<template v-for="(item, index) in pageData.dataList" :key="index">
					<view class="px-30 pt-20">
						<view class="w-full px-30 py-30 bg-#fff border-rd-16">
							<view class="flex items-center justify-between">
								<view class="flex items-center">
									<app-image :src="item.avatar" size="40" rd="50%" mr="10"></app-image>
									<view class="text-30 font-bold">{{ item.nickname }}</view>
								</view>

								<view class="flex items-center text-28 font-500">
									<!-- <view class="">等级</view> -->
									<view class="">
										{{ { ONE: '一级', TWO: '二级', THREE: '三级' }[item.level] }}
									</view>
								</view>
							</view>
							<view class="mt-20">
								<view class="text-26 text-#666666 font-500">注册时间：{{ item.createTime }}</view>
							</view>
						</view>
					</view>
				</template>
			</view>
		</z-paging>
	</app-layout>
</template>
<style lang="scss" scoped>
.evaluation {
}
</style>
