<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';

import { onLoad } from '@dcloudio/uni-app';

import { Decimal } from 'decimal.js';

import { getDistributeCenter, getDistributeCustomer, getDistributeOrder, getDistributeTeam } from '@/server/api';

import { route, salesVolumeToStr, getPriceInfo } from '@/common/utils';

import useConvert from '@/common/useConvert';

const paging = ref(null);

const { divTenThousand } = useConvert();

const props = defineProps({
	status: {
		type: String,
		default: ''
	}
});

const pageData = reactive({
	typeList: [
		{
			name: '待结算',
			value: 'PAID'
		},
		{
			name: '已结算',
			value: 'COMPLETED'
		}
	],
	typeIndex: 0,

	dataList: [],

	totalIntegral: 0
});

const totalPrice = ref(0);

function getcount(orderitem, level, orderStatus) {
	let count = new Decimal(0);
	if ('items' in orderitem && orderitem.items.length) {
		orderitem.items.forEach((item) => {
			if (orderStatus && item.orderStatus !== orderStatus) return;
			if (item[level.toLowerCase()].bonus) {
				// count = count.add(divTenThousand(item[level.toLowerCase()].bonus));
				count = count.add(item[level.toLowerCase()].bonus);
			}
		});
	}

	return count.toNumber();
}

function changeType(e) {
	pageData.typeIndex = e.index;
	if (paging.value) paging.value.reload();
}

function changeSort(e) {
	pageData.sort = e;
}

async function loadData() {}

function queryList(pageNo, pageSize) {
	const params = {
		pageNo: pageNo,
		current: pageNo,
		pageSize: pageSize,
		status: props.status
	};

	getDistributeOrder({ ...params }).then((res) => {
		if (res.apiStatus) {
			if (paging.value) paging.value.complete(res.data.page.records);

			if (props.status === 'PAID') {
				totalPrice.value = res.data.statistic.unsettled;
			}

			if (props.status === 'COMPLETED') {
				totalPrice.value = res.data.statistic.total;
			}
		}
	});
}

onLoad(() => {
	// loadData();
	if (props.status === 'PAID') {
		uni.setNavigationBarTitle({
			title: '待结算佣金'
		});
	}

	if (props.status === 'COMPLETED') {
		uni.setNavigationBarTitle({
			title: '已获得佣金'
		});
	}
});
</script>
<template>
	<app-layout>
		<z-paging ref="paging" bg-color="#F1F3F7" v-model="pageData.dataList" @query="queryList">
			<template #top>
				<template v-if="status === 'PAID'">
					<view class="w-full px-30 pt-35 pb-5 flex items-center">
						<view class="w-6 h-24 border-rd-2 bg-#FC3F33 mr-16"></view>
						<view class="text-30 font-bold">待结算：￥{{ getPriceInfo(totalPrice, 2).integer }}.{{ getPriceInfo(totalPrice, 2).decimalText }}</view>
					</view>
				</template>
				<template v-if="status === 'COMPLETED'">
					<view class="w-full px-30 pt-35 pb-5 flex items-center">
						<view class="w-6 h-24 border-rd-2 bg-#00B496 mr-16"></view>
						<view class="text-30 font-bold">已获得：￥{{ getPriceInfo(totalPrice, 2).integer }}.{{ getPriceInfo(totalPrice, 2).decimalText }}</view>
					</view>
				</template>
			</template>
			<view class="evaluation py-0">
				<template v-for="(item, index) in pageData.dataList" :key="index">
					<view class="px-30 pt-20">
						<view class="w-full px-30 py-30 bg-#fff border-rd-16">
							<view class="flex items-center justify-between">
								<view class="flex items-center">
									<app-image :src="item.buyerAvatar" size="40" rd="50%" mr="10"></app-image>
									<view class="text-30 font-bold">{{ item.buyerNickname }}</view>
								</view>

								<template v-if="getcount(item, item.currentUserLevel ? item.currentUserLevel : item.level, 'PAID')">
									<view class="flex items-center text-28 font-500">
										<view class="">预计结算</view>
										<view class="text-#FC3F33">
											￥{{ getPriceInfo(getcount(item, item.currentUserLevel ? item.currentUserLevel : item.level, 'PAID'), 2).integer }}.{{
												getPriceInfo(getcount(item, item.currentUserLevel ? item.currentUserLevel : item.level, 'PAID'), 2).decimalText
											}}
										</view>
									</view>
								</template>

								<template v-if="getcount(item, item.currentUserLevel ? item.currentUserLevel : item.level, 'COMPLETED')">
									<view class="flex items-center text-28 font-500">
										<view class="">已结算</view>
										<view class="text-#00B496">
											￥{{ getPriceInfo(getcount(item, item.currentUserLevel ? item.currentUserLevel : item.level, 'COMPLETED'), 2).integer }}.{{
												getPriceInfo(getcount(item, item.currentUserLevel ? item.currentUserLevel : item.level, 'COMPLETED'), 2).decimalText
											}}
										</view>
									</view>
								</template>

								<template v-if="getcount(item, item.currentUserLevel ? item.currentUserLevel : item.level, 'CLOSED')">
									<view class="flex items-center text-28 font-500">
										<view class="">已失效</view>
										<view class="text-#FC3F33">
											￥{{ getPriceInfo(getcount(item, item.currentUserLevel ? item.currentUserLevel : item.level, 'CLOSED'), 2).integer }}.{{
												getPriceInfo(getcount(item, item.currentUserLevel ? item.currentUserLevel : item.level, 'CLOSED'), 2).decimalText
											}}
										</view>
									</view>
								</template>
							</view>
							<view class="mt-20">
								<view class="text-26 text-#666666 font-500">下单时间：{{ item.createTime }}</view>
							</view>
						</view>
					</view>
				</template>
			</view>
		</z-paging>
	</app-layout>
</template>
<style lang="scss" scoped>
.evaluation {
}
</style>
