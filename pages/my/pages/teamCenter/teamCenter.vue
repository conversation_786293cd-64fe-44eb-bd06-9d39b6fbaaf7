<script setup>
import { ref, reactive, getCurrentInstance } from 'vue';

import { onLoad } from '@dcloudio/uni-app';

import { Decimal } from 'decimal.js';

import { getDistributeCenter, getDistributeCustomer, getDistributeOrder, getDistributeTeam } from '@/server/api';

import { route, salesVolumeToStr, getPriceInfo } from '@/common/utils';

import useConvert from '@/common/useConvert';
import { common } from '@/common/images';

const paging = ref(null);

const { divTenThousand } = useConvert();

const pageData = reactive({
	typeList: [
		{
			name: '待结算',
			value: 'PAID'
		},
		{
			name: '已结算',
			value: 'COMPLETED'
		}
	],
	typeIndex: 0,

	dataList: [],

	totalIntegral: 0
});

const memberInfo = ref({
	avatar: '',
	nickname: '',
	name: '',
	config: {
		id: '',
		level: 'ONE',
		protocol: '',
		condition: {
			types: ['APPLY'],
			requiredAmount: 0
		},
		purchase: false,
		poster: '',
		shareType: 'RATE',
		one: '0'
	},
	createTime: '',
	id: '',
	identity: 'UserType',
	mobile: '',
	statistics: { customer: '0', order: '0', bonus: '0' },
	total: '0',
	undrawn: '0',
	unsettled: '0',
	userId: '',
	referrer: '',
	code: ''
});

const teamNumber = ref(0);

function getcount(orderitem, level, orderStatus) {
	let count = new Decimal(0);
	if ('items' in orderitem && orderitem.items.length) {
		orderitem.items.forEach((item) => {
			if (orderStatus && item.orderStatus !== orderStatus) return;
			if (item[level.toLowerCase()].bonus) {
				// count = count.add(divTenThousand(item[level.toLowerCase()].bonus));
				count = count.add(item[level.toLowerCase()].bonus);
			}
		});
	}

	return count.toNumber();
}

function changeType(e) {
	pageData.typeIndex = e.index;
	if (paging.value) paging.value.reload();
}

function changeSort(e) {
	pageData.sort = e;
}

async function loadData() {
	try {
		const resCenter = await getDistributeCenter();
		if (resCenter.apiStatus) {
			memberInfo.value = resCenter.data;
		}
	} catch (error) {
		//TODO handle the exception
	}

	try {
		const teamRes = await getDistributeTeam();
		if (teamRes.apiStatus) {
			teamNumber.value = teamRes.data.total;
		}
	} catch (error) {
		//TODO handle the exception
	}
}

function queryList(pageNo, pageSize) {
	const params = {
		pageNo: pageNo,
		current: pageNo,
		pageSize: pageSize,
		status: pageData.typeList[pageData.typeIndex].value
	};

	if (params.current === 1) {
		loadData();
	}

	getDistributeOrder({ ...params }).then((res) => {
		if (res.apiStatus) {
			if (paging.value) paging.value.complete(res.data.page.records);
		}
	});
}

onLoad(() => {
	// loadData();
});
</script>
<template>
	<app-layout>
		<z-paging ref="paging" bg-color="#F1F3F7" v-model="pageData.dataList" @query="queryList">
			<template #top>
				<app-navBar
					bgColor="linear-gradient(135deg, #01C8A7 33%, #14D1D5 100%)"
					back
					rightPlaceholderSize="70rpx"
					:backIcon="common.iconLeftBai"
					:fixed="false"
					:fixed-placeholder="false"
				>
					<template #content>
						<view class="w-full h-full flex justify-between items-center px-0">
							<view class="flex-1 nav-center">
								<text class="text-32 text-#fff font-bold">我的邀请</text>
							</view>
						</view>
					</template>
					<template #footer>
						<view class="w-full p-20">
							<view class="flex justify-between">
								<view
									class="w-345 h-136 rd-20 ac-op py-20 px-30"
									style="background: linear-gradient(180deg, #dffdf8 0%, #ffffff 37%)"
									@click="route('/pages/my/pages/teamCenter/timeUserList/timeUserList')"
								>
									<view class="flex justify-between items-center">
										<view class="text-26 font-500 text-#444">已邀请人数</view>
										<app-image :src="common.iconRightHui" mode="" size="20"></app-image>
									</view>

									<view class="text-36 font-bold text-#111010 mt-10">{{ teamNumber }}</view>
								</view>

								<view
									class="w-345 h-136 rd-20 ac-op py-20 px-30"
									style="background: linear-gradient(180deg, #dffdf8 0%, #ffffff 37%)"
									@click="route('/pages/my/pages/teamCenter/walletCommission/walletCommission')"
								>
									<view class="flex justify-between items-center">
										<view class="text-26 font-500 text-#444">佣金余额(元)</view>
										<app-image :src="common.iconRightHui" mode="" size="20"></app-image>
									</view>

									<view class="text-36 font-bold text-#111010 mt-10">{{ getPriceInfo(memberInfo.undrawn, 2).integer }}.{{ getPriceInfo(memberInfo.undrawn, 2).decimalText }}</view>
								</view>
							</view>

							<view class="flex justify-between mt-20">
								<view
									class="w-345 h-136 rd-20 ac-op py-20 px-30"
									style="background: linear-gradient(180deg, #dffdf8 0%, #ffffff 37%)"
									@click="route('/pages/my/pages/teamCenter/distributeOrderList/distributeOrderList', { status: 'COMPLETED' })"
								>
									<view class="flex justify-between items-center">
										<view class="text-26 font-500 text-#444">已获得佣金(元)</view>
										<app-image :src="common.iconRightHui" mode="" size="20"></app-image>
									</view>

									<view class="text-36 font-bold text-#111010 mt-10">{{ getPriceInfo(memberInfo.total, 2).integer }}.{{ getPriceInfo(memberInfo.total, 2).decimalText }}</view>
								</view>

								<view
									class="w-345 h-136 rd-20 ac-op py-20 px-30"
									style="background: linear-gradient(180deg, #dffdf8 0%, #ffffff 37%)"
									@click="route('/pages/my/pages/teamCenter/distributeOrderList/distributeOrderList', { status: 'PAID' })"
								>
									<view class="flex justify-between items-center">
										<view class="text-26 font-500 text-#444">待结算佣金(元)</view>
										<app-image :src="common.iconRightHui" mode="" size="20"></app-image>
									</view>

									<view class="text-36 font-bold text-#111010 mt-10">
										{{ getPriceInfo(memberInfo.unsettled, 2).integer }}.{{ getPriceInfo(memberInfo.unsettled, 2).decimalText }}
									</view>
								</view>
							</view>
						</view>
					</template>
				</app-navBar>
				<view class="w-full pt-0 pb-0 bg-#fff relative">
					<uv-tabs
						:list="pageData.typeList"
						:current="pageData.typeIndex"
						:scrollable="false"
						lineColor="#198B6B"
						:activeStyle="{
							fontWeight: 'bold',
							fontSize: '30rpx',
							color: '#222222'
						}"
						:inactiveStyle="{
							fontWeight: 'bold',
							fontSize: '30rpx',
							color: '#666666'
						}"
						lineWidth="50rpx"
						lineHeight="4rpx"
						@change="changeType"
					></uv-tabs>
				</view>
			</template>
			<view class="evaluation py-0">
				<template v-for="(item, index) in pageData.dataList" :key="index">
					<view class="px-30 pt-20">
						<view class="w-full px-30 py-30 bg-#fff border-rd-16">
							<view class="flex items-center justify-between">
								<view class="flex items-center">
									<app-image :src="item.buyerAvatar" size="40" rd="50%" mr="10"></app-image>
									<view class="text-30 font-bold">{{ item.buyerNickname }}</view>
								</view>

								<template v-if="getcount(item, item.currentUserLevel ? item.currentUserLevel : item.level, 'PAID')">
									<view class="flex items-center text-28 font-500">
										<view class="">预计结算</view>
										<view class="text-#FC3F33">
											￥{{ getPriceInfo(getcount(item, item.currentUserLevel ? item.currentUserLevel : item.level, 'PAID'), 2).integer }}.{{
												getPriceInfo(getcount(item, item.currentUserLevel ? item.currentUserLevel : item.level, 'PAID'), 2).decimalText
											}}
										</view>
									</view>
								</template>

								<template v-if="getcount(item, item.currentUserLevel ? item.currentUserLevel : item.level, 'COMPLETED')">
									<view class="flex items-center text-28 font-500">
										<view class="">已结算</view>
										<view class="text-#00B496">
											￥{{ getPriceInfo(getcount(item, item.currentUserLevel ? item.currentUserLevel : item.level, 'COMPLETED'), 2).integer }}.{{
												getPriceInfo(getcount(item, item.currentUserLevel ? item.currentUserLevel : item.level, 'COMPLETED'), 2).decimalText
											}}
										</view>
									</view>
								</template>

								<template v-if="getcount(item, item.currentUserLevel ? item.currentUserLevel : item.level, 'CLOSED')">
									<view class="flex items-center text-28 font-500">
										<view class="">已失效</view>
										<view class="text-#FC3F33">
											￥{{ getPriceInfo(getcount(item, item.currentUserLevel ? item.currentUserLevel : item.level, 'CLOSED'), 2).integer }}.{{
												getPriceInfo(getcount(item, item.currentUserLevel ? item.currentUserLevel : item.level, 'CLOSED'), 2).decimalText
											}}
										</view>
									</view>
								</template>
							</view>
							<view class="mt-20">
								<view class="text-26 text-#666666 font-500">下单时间：{{ item.createTime }}</view>
							</view>
						</view>
					</view>
				</template>
			</view>
		</z-paging>
	</app-layout>
</template>
<style lang="scss" scoped>
.evaluation {
}
</style>
