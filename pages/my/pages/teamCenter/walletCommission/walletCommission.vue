<script setup>
import { reactive, ref, getCurrentInstance } from 'vue'

import { onLoad, onReady, onShow } from '@dcloudio/uni-app'

import { getDistributeCenter, getWithdrawList } from '@/server/api'

import { route, getPriceInfo } from '@/common/utils'

import storage from '@/common/storage'

import useConvert from '@/common/useConvert'

const { divTenThousand, salesVolumeToStr } = useConvert()

const dealTypeCn = {
    SYSTEM_GIVE: '系统赠送',
    PERSONAL_CHARGING: '个人充值',
    SYSTEM_CHARGING: '系统充值',
    SHOPPING_PURCHASE: '购物消费',
    PURCHASE_MEMBER: '购买会员',
    REFUND_SUCCEED: '退款成功',
    WITHDRAW: '提现',
}

const memberInfo = ref({
    avatar: '',
    nickname: '',
    name: '',
    config: {
        id: '',
        level: 'ONE',
        protocol: '',
        condition: {
            types: ['APPLY'],
            requiredAmount: 0,
        },
        purchase: false,
        poster: '',
        shareType: 'RATE',
        one: '0',
    },
    createTime: '',
    id: '',
    identity: 'UserType',
    mobile: '',
    statistics: { customer: '0', order: '0', bonus: '0' },
    total: '0',
    undrawn: '0',
    unsettled: '0',
    userId: '',
    referrer: '',
    code: '',
})

const pageData = reactive({
    barBg: 'rgba(255,255,255,0)',
    walletDetail: {
        beans: 0,
        money: 0,
        score: 0,
        totalincome: 0,
        withdraw: 0,
    },

    userPerson: {
        balance: 0,
        collectCount: 0,
        footprint: 0,
        integral: 0,

        goodsCollectCount: 0,
        shopCollectCount: 0,

        couponCount: 0,
        bankCount: 0,
    },

    dataList: [],
    streamTotal: 0,
})
const pagePaging = ref(null)

const topUpParam = ref({
    switching: false,
    discountsState: true,
    ruleJson: [],
    id: '',
})

function convertStatus(value) {
    const statusType = {
        APPLYING: '待审核',
        PROCESSING: '提现中',
        SUCCESS: '已到账',
        CLOSED: '提现失败',
        FORBIDDEN: '已拒绝',
    }
    return statusType[value]
}

function convertSource(value) {
    return value === 'BANK_CARD' ? '银行卡' : value === 'WECHAT' ? '微信钱包' : '支付宝钱包'
}

async function queryList(pageNo, pageSize) {
    const params = {
        pageNo: pageNo,
        current: pageNo,
        pageSize: pageSize,
        withdrawSourceType: 'DISTRIBUTE',
        ownerType: 'DISTRIBUTOR',
    }

    if (pageNo === 1) {
        try {
            const resCenter = await getDistributeCenter()
            if (resCenter.apiStatus) {
                memberInfo.value = resCenter.data
            }
        } catch (error) {
            //TODO handle the exception
        }
    }

    getWithdrawList({ ...params }).then((res) => {
        if (res.apiStatus) {
            if (pagePaging.value) pagePaging.value.complete(res.data.records)
        }
    })
}

onShow(() => {
    if (pagePaging.value) pagePaging.value.reload()
})
</script>
<template>
    <app-layout>
        <z-paging
            ref="pagePaging"
            :paging-style="{
                backgroundColor: '#F0F3F7',
                backgroundImage: 'linear-gradient(180deg, #D7FFF7 80%, #F0F3F7)',
                backgroundRepeat: 'no-repeat',
                backgroundSize: '100% 446rpx',
            }"
            v-model="pageData.dataList"
            @query="queryList"
        >
            <template #top>
                <app-navBar back>
                    <template #content>
                        <view class="w-full flex items-center h-full">
                            <view class="text-32 text-#000 font-bold nav-center flex-1">我的佣金</view>
                        </view>
                    </template>
                </app-navBar>
            </template>
            <view class="px-30">
                <view class="w-full h-160 border-rd-20 mt-16" style="background: linear-gradient(87deg, #00c9a7 34%, #00c9a7)">
                    <view class="h-158 pl-31 pr-40 flex justify-between items-center">
                        <view class="">
                            <view class="text-26 text-#fff font-bold">佣金(元)</view>
                            <!-- <view class="text-48 text-#fff font-bold mt-20">{{ salesVolumeToStr(divTenThousand(pageData.userPerson.balance)) }}</view> -->
                            <view class="text-48 text-#fff font-bold mt-20"
                                >{{ getPriceInfo(memberInfo.undrawn, 2).integer }}.{{ getPriceInfo(memberInfo.undrawn, 2).decimalText }}</view
                            >
                        </view>
                        <view class="flex justify-end items-center">
                            <view
                                @click="
                                    route('/pages/my/pages/teamCenter/walletCommission/withdraw/withdraw', {
                                        balance: (Math.floor(divTenThousand(memberInfo.undrawn) * 100) / 100).toFixed(2),
                                    })
                                "
                                class="w-130 h-64 border-rd-32 flex justify-center items-center border-solid border-2 box-border border-#95F8E7 text-28 text-#fff font-bold"
                            >
                                提现
                            </view>
                        </view>
                    </view>
                </view>
                <view class="flex justify-start items-center mt-50">
                    <view class="w-6 h-24 bg-#00B698 border-rd-2 mr-14"></view>
                    <view class="text-34 text-#000 font-bold">佣金提现</view>
                </view>
                <view class="bg-#fff border-rd-16 mt-30 px-30">
                    <template v-for="(item, index) in pageData.dataList" :key="index">
                        <view class="py-30 border-b-solid border-1 border-b-#EAEDF3">
                            <view class="flex justify-between items-center">
                                <text class="text-30 text-#222 font-bold">提现 - {{ convertSource(item.drawType.type) }}</text>
                                <!-- <template v-if="item.changeType === 'INCREASE'"> -->
                                <!-- <text class="text-30 font-bold text-#00B698">+{{ getPriceInfo(item.money).integer }}.{{ getPriceInfo(item.money).decimalText }}</text> -->
                                <!-- </template> -->
                                <!-- <template v-else> -->
                                <text class="text-30 font-bold"
                                    >{{ getPriceInfo(item.drawType.amount, 2).integer }}.{{ getPriceInfo(item.drawType.amount, 2).decimalText }}</text
                                >
                                <!-- </template> -->
                            </view>
                            <!-- 手续费提示 -->
                            <template v-if="item.drawType.commission">
                                <view class="flex justify-between items-center mt-20 color-#FD9224 text-24">
                                    <text>含手续费</text>
                                    <text>￥{{ divTenThousand(item.drawType.commission) }}</text>
                                </view>
                            </template>
                            <view class="flex items-center justify-between">
                                <view class="text-24 text-#43414188 mt-20">{{ item.createTime }}</view>
                                <template v-if="item.status === 'APPLYING'">
                                    <view class="text-24 text-#005cf4 mt-20">{{ convertStatus(item.status) }}</view>
                                </template>

                                <template v-if="item.status === 'PROCESSING'">
                                    <view class="text-24 text-#FD9224 mt-20">{{ convertStatus(item.status) }}</view>
                                </template>

                                <template v-if="item.status === 'CLOSED'">
                                    <view class="text-24 text-#999999 mt-20">{{ convertStatus(item.status) }}</view>
                                </template>

                                <template v-if="item.status === 'FORBIDDEN'">
                                    <view class="text-24 text-#E60C00 mt-20">{{ convertStatus(item.status) }}</view>
                                </template>

                                <template v-if="item.status === 'SUCCESS'">
                                    <view class="text-24 text-#00B698 mt-20">{{ convertStatus(item.status) }}</view>
                                </template>
                            </view>
                        </view>
                    </template>
                </view>
            </view>
        </z-paging>
    </app-layout>
</template>
<style lang="scss" scoped></style>
