<script setup>
import 'uno.css';
import { ref, reactive, getCurrentInstance, watch } from 'vue';
import { onLoad, onUnload } from '@dcloudio/uni-app';
import { useMerchantStore } from '@/store';
import { doAddShops, doGetPlatformCategory, upload, getOrganizationListApi } from '@/server/api';
import { checkCategoryEnable } from '@/common/utils.js';
// import { locToAddress } from "@/common/amap"
import { locToAddress } from '@/pages/my/common/amap';
// import { alUploadFile } from "@/uni_modules/qying-alUpload/js_sdk/alUpload";
import { REGEX_CITIZEN_ID, REGEX_MOBILE } from '@/common/test';
import { common}  from "@/common/images";
const { ctx } = getCurrentInstance();
const merchantStore = useMerchantStore();
const pageData = reactive({
	firstCategory: '', // 一级类目id
	firstCategoryList: [], // 一级类目
	secondCategory: '', // 二级类目id
	secondCategoryList: [], // 二级类目
	threeCategoryList: [], // 三级类目
	checkCategoryList: [], // 选择的类目列表
	
	organizationId: '',
	organizationList: [],
	
	rules: {
		agencyId: { rules: [{ required: true, errorMessage: '机构码不能为空' }] },
		companyName: { rules: [{ required: true, errorMessage: '公司名称不能为空' }] },
		name: { rules: [{ required: true, errorMessage: '店铺名称不能为空' }] },
		contractNumber: {
			rules: [
				{ required: true, errorMessage: '联系方式不能为空' },
				{
					validateFunction: (rule, value, data, callback) => {
						if (!REGEX_MOBILE(value)) {
							callback('请输入正确的联系方式');
						}
						return true;
					}
				}
			]
		},
		address: { rules: [{ required: true, errorMessage: '店铺地址不能为空' }] },
		fakeAddress: { rules: [{ required: true, errorMessage: '详细地址不能为空' }] },
		briefing: { rules: [{ required: true, errorMessage: '店铺介绍不能为空' }] },
		bankName: { rules: [{ required: true, errorMessage: '银行名称不能为空' }] },
		payee: { rules: [{ required: true, errorMessage: '收款人不能为空' }] },
		bankAcc: { rules: [{ required: true, errorMessage: '账户不能为空' }] },
		openAccountBank: { rules: [{ required: true, errorMessage: '开户行不能为空' }] },
		logo: { rules: [{ required: true, errorMessage: '店铺logo不能为空' }] },
		license: { rules: [{ required: true, errorMessage: '营业执照不能为空' }] },
		legalPersonIdFront: { rules: [{ required: true, errorMessage: '法人身份证正面不能为空' }] },
		legalPersonIdBack: { rules: [{ required: true, errorMessage: '法人身份证反面不能为空' }] },
		signingCategory: { rules: [{ required: true, errorMessage: '签约类目不能为空' }] },
		legalName: { rules: [{ required: true, errorMessage: '法人姓名不能为空' }] },
		legalEmail: { rules: [{ required: true, errorMessage: '电子邮箱不能为空' }] },
		idCardNo: {
			rules: [
				{ required: true, errorMessage: '法人身份证号不能为空' },
				{
					validateFunction: (rule, value, data, callback) => {
						if (!REGEX_CITIZEN_ID(value)) {
							callback('请输入正确的身份证号');
						}
						return true;
					}
				}
			]
		},
		contractName: { rules: [{ required: true, errorMessage: '联系人不能为空' }] },
		companyCode: { rules: [{ required: true, errorMessage: '统一社会信用代码不能为空' }] }
	}
});
const formData = reactive(merchantStore.enterMerchantInfo);
const formRef = ref();
const categoryPopup = ref();
watch(formData, (newVal) => {
	merchantStore.SET_MERCHANT_INFO(newVal);
});
onLoad((options) => {
	formData.shopMode = options.mode;
	getFirstCategoryList();
	getOrganizationList()
});

function getOrganizationList() {
	getOrganizationListApi().then(res => {
		if(res.apiStatus) {
			pageData.organizationList = res.data.records.map((elem) => {
				return {
					value: elem.id,
					text: elem.name
				};
			});
		}
	})
}

function chooseImage(key) {
	// uni.chooseImage({
	// 	count: 1, //默认9
	// 	sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
	// 	sourceType: ['album'], //从相册选择
	// 	success: (res) => {
	// 		alUploadFile(res.tempFilePaths[0], true).then((res) => {
	// 			formData[key] = res.file;
	// 		});
	// 	}
	// });
// #ifndef H5
	uni.chooseMedia({
		count: 1,
		mediaType: ['image'],
		sourceType: ['album', 'camera'],
		sizeType: ['original', 'compressed'],
		success(res) {
			const tempFilePaths = res.tempFiles.map((i) => i.tempFilePath);

			uni.showLoading({
				title: '加载中...'
			});
			upload(tempFilePaths[0], {
				formData: tempFilePaths
			})
				.then((res) => {
					uni.hideLoading();
					if (res.apiStatus && key) {
						formData[key] = res.data;
					}
				})
				.catch((err) => {
					uni.hideLoading();
				});
		}
	});
// #endif
// #ifdef H5
	uni.chooseImage({
		count: 1,
		sourceType: ['album', 'camera'],
		sizeType: ['original', 'compressed'],
		success(res) {
			const tempFilePaths = res.tempFilePaths;

			uni.showLoading({
				title: '加载中...'
			});
			upload(tempFilePaths[0], {
				formData: tempFilePaths
			})
				.then((res) => {
					uni.hideLoading();
					if (res.apiStatus && key) {
						formData[key] = res.data;
					}
				})
				.catch((err) => {
					uni.hideLoading();
				});
		}
	});
// #endif

}
function chooseAddress() {
	uni.getLocation({
		type: 'gcj02',
		isHighAccuracy: true,
		success: (res) => {
			uni.chooseLocation({
				latitude: res.latitude,
				longitude: res.longitude,
				success: (res) => {
					formData.address = res.address + res.name;
					formData.location.coordinates = [res.longitude, res.latitude];
					locToAddress({
						longitude: res.longitude,
						latitude: res.latitude
					}).then((res) => {
						formData.area = res.area;
					});
				},
				fail: (err) => {
					console.log(err, 2222);
				}
			});
		},
		fail: (err) => {
			console.log(err, 1111);
		}
	});
}
function openCategoryPopup() {
	categoryPopup.value.open();
}
function closeCategoryPopup() {
	categoryPopup.value.close();
}
// 获取一级类目
function getFirstCategoryList() {
	doGetPlatformCategory('LEVEL_1', 0).then((res) => {
		if (res.code == 200) {
			pageData.firstCategoryList = res.data.records.map((elem) => {
				return {
					value: elem.id,
					text: elem.name
				};
			});
		}
	});
}
// 获取二级类目
function getSecondCategoryList() {
	pageData.secondCategory = '';
	pageData.threeCategoryList = [];
	pageData.checkCategoryList = [];
	doGetPlatformCategory('LEVEL_2', pageData.firstCategory).then((res) => {
		if (res.code == 200) {
			pageData.secondCategoryList = res.data.records.map((elem) => {
				return {
					value: elem.id,
					text: elem.name
				};
			});
		}
	});
}
// 获取三级类目
function getThreeCategoryList() {
	const currentFirstCategory = pageData.firstCategoryList.find((item) => item.value === pageData.firstCategory);
	const currentSecondCategory = pageData.secondCategoryList.find((item) => item.value === pageData.secondCategory);
	doGetPlatformCategory('LEVEL_3', pageData.secondCategory).then((res) => {
		if (res.code == 200) {
			pageData.threeCategoryList = res.data.records.map((elem) => {
				return {
					...elem,
					firstId: pageData.firstCategory,
					firstName: currentFirstCategory.text,
					secondName: currentSecondCategory.text,
					customDeductionRatio: 0
				};
			});
			const idsList = new Set(formData.signingCategory.map((item) => item.id));
			pageData.checkCategoryList = pageData.threeCategoryList.filter((item) => idsList.has(item.id));
		}
	});
}
// 单选签约类目
function checkCategory(e) {
	let index = pageData.checkCategoryList.findIndex((elem) => elem.id == e.id);
	if (index > -1) {
		pageData.checkCategoryList.splice(index, 1);
	} else {
		pageData.checkCategoryList.push(e);
	}
}
// 确认签约类目
function handleCheckCategory() {
	pageData.checkCategoryList.forEach((item) => {
		let index = formData.signingCategory.findIndex((elem) => elem.id == item.id);
		if (index > -1) {
			formData.signingCategory[index] = item;
		} else {
			formData.signingCategory.push(item);
		}
	});
	closeCategoryPopup();
}
// 删除选择过的签约类目
function removeCheckCategory(index, ids) {
	formData.signingCategory.splice(index, 1);
	let cut = pageData.checkCategoryList.findIndex((elem) => elem.id == ids);
	pageData.checkCategoryList.splice(cut, 1);
}
function handleSubmit() {
	formRef.value
		.validate()
		.then((res) => {
			const params = JSON.parse(JSON.stringify(formData));
			params.bankAccount.payee = params.payee;
			params.bankAccount.bankName = params.bankName;
			params.bankAccount.bankAccount = params.bankAcc;
			params.bankAccount.openAccountBank = params.openAccountBank;
			params.registerInfo.legalPersonIdBack = params.legalPersonIdBack;
			params.registerInfo.legalPersonIdFront = params.legalPersonIdFront;
			params.registerInfo.license = params.license;
			params.registerInfo.legalName = params.legalName;
			params.registerInfo.legalEmail = params.legalEmail;
			params.registerInfo.idCardNo = params.idCardNo;
			params.signingCategory = params.signingCategory.map((item) => ({
				id: null,
				parentId: item.firstId, // 一级
				currentCategoryId: item.parentId, // 二级
				customDeductionRatio: item.customDeductionRatio, // 费率
				threeId: item.id // 三级
			}));
			delete params.mode;
			uni.showLoading({
				title: '加载中...',
				mask: true
			});
			doAddShops(params, {
				// header: {
				// 	'agency-id': pageData.organizationId
				// }
			}).then((res) => {
				uni.hideLoading();
				if (res.code == 200) {
					merchantStore.CLEAR_MERCHANT_INFO();
					uni.showModal({
						title: '提交成功',
						content: '请耐心等待审核',
						showCancel: false,
						success: (res) => {
							if (res.confirm) {
								uni.navigateBack({
									delta: 1
								});
							} else if (res.cancel) {
								console.log('用户点击取消');
							}
						}
					});
				}
			});
			console.log(params, 123);
		})
		.catch((err) => {
			console.log('表单错误信息：', err);
		});
}

onUnload(() => {
	merchantStore.CLEAR_MERCHANT_INFO();
});
</script>

<template>
	<app-layout>
		<z-paging
			ref="pagePaging"
			:paging-style="{
				backgroundImage: 'url(https://hejiawuyou.oss-cn-shanghai.aliyuncs.com/weixin/storeInfo_top_bg.png)',
				backgroundColor: '#fff',
				backgroundRepeat: 'no-repeat',
				backgroundSize: '100% 460rpx',
				top: '0',
				bottom: '0'
			}"
			refresher-theme-style="white"
		>
			<template #top>
				<app-navBar :has-right="false">
					<template v-slot:content>
						<view class="w-full flex justify-between items-center h-full">
							<!-- #ifdef MP-WEIXIN -->
							<view class="w-36 h-36 ml-24">
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<view class="w-120 h-36 ml-24">
							<!-- #endif -->
								<image @click="ctx.$uv.route({type: 'back'})" :src="common.iconLeftBai" class="w-36 h-36" mode=""></image>
							</view>
							<view class="text-34 text-#fff font-bold">
								{{formData.shopMode == 'COMMON' ? '入驻商家申请' : '入驻服务商申请'}}
							</view>
							<!-- #ifdef MP-WEIXIN -->
							<view class="w-36 h-36 ml-24">
							<!-- #endif -->
							<!-- #ifndef MP-WEIXIN -->
							<view class="w-120 h-36 ml-24">
							<!-- #endif -->
								
							</view>
						</view>
					</template>
				</app-navBar>
			</template>
			<view class="bg-#fff border-rd-t-l-20 border-rd-t-r-20 pb-20">
				<uni-forms ref="formRef" :modelValue="formData" :rules="pageData.rules" :label-width="80">
					<view class="w-full h-44 mb-20 line-height-44 bg-#F0F3F7 px-35 text-18 text-#828282 font-500">基本信息</view>
					<view class="px-20">
						<!-- <uni-forms-item label="所属机构" name="serveTypeId" required>
							<uni-data-select
								v-model="pageData.organizationId"
								:localdata="pageData.organizationList"
								border-color="rgba(0,0,0,0)"
								text-align="right"
								style="width: 100%"
							></uni-data-select>
						</uni-forms-item> -->
						<uni-forms-item label="机构码" name="agencyId" required>
							<uni-easyinput type="text" v-model="formData.agencyId" placeholder="请输入机构码" />
							<view class="text-22 text-#B5B9BF">请联系客服获取有效机构码</view>
						</uni-forms-item>
						<uni-forms-item label="公司名称" name="companyName" required>
							<uni-easyinput type="text" v-model="formData.companyName" placeholder="请输入公司名称" />
						</uni-forms-item>
						<uni-forms-item label="统一社会信用代码" name="companyCode" required>
							<uni-easyinput type="text" v-model="formData.companyCode" placeholder="请输入统一社会信用代码" />
						</uni-forms-item>
						<uni-forms-item label="营业执照" name="license" label-position="top" required>
							<view class="pl-10">
								<view @click="chooseImage('license')">
									<image v-if="formData.license" :src="formData.license" class="w-315 h-186 border-rd-20" mode="aspectFit"></image>
									<image v-else src="@/pages/my/static/upload_yyzz.png" class="w-315 h-186" mode=""></image>
								</view>
							</view>
						</uni-forms-item>
						<uni-forms-item label="法人姓名" name="legalName" required>
							<uni-easyinput type="text" v-model="formData.legalName" placeholder="请输入法人姓名" />
						</uni-forms-item>
						<uni-forms-item label="法人身份证号" name="idCardNo" required>
							<uni-easyinput type="text" v-model="formData.idCardNo" placeholder="请输入法人身份证号" />
						</uni-forms-item>
							<uv-row customStyle="margin-bottom: 10px">
								<uv-col span="6">
									<uni-forms-item label="法人身份证" name="legalPersonIdFront" label-position="top" required>
										<view class="pl-10">
											<view @click="chooseImage('legalPersonIdFront')">
												<image v-if="formData.legalPersonIdFront" :src="formData.legalPersonIdFront" class="w-315 h-186 border-rd-20" mode="aspectFit"></image>
												<image v-else src="@/pages/my/static/upload_sfzz.png" class="w-315 h-186" mode=""></image>
											</view>
										</view>
									</uni-forms-item>
								</uv-col>
								<uv-col span="6">
									<uni-forms-item label="" name="legalPersonIdBack" label-position="top">
										<view @click="chooseImage('legalPersonIdBack')">
											<image v-if="formData.legalPersonIdBack" :src="formData.legalPersonIdBack" class="w-315 h-186 border-rd-20" mode="aspectFit"></image>
											<image v-else src="@/pages/my/static/upload_sfzf.png" class="w-315 h-186" mode=""></image>
										</view>
									</uni-forms-item>
								</uv-col>
							</uv-row>
						<uni-forms-item label="联系人" name="contractName" required>
							<uni-easyinput type="text" v-model="formData.contractName" placeholder="请输入联系人" />
						</uni-forms-item>
						<uni-forms-item label="联系方式" name="contractNumber" required>
							<uni-easyinput type="text" v-model="formData.contractNumber" placeholder="请输入联系方式" maxlength="11" />
						</uni-forms-item>
						<uni-forms-item label="联系地址" name="address" required>
							<app-easyinput :text-value="formData.address" placeholder="请选择联系地址" @click="chooseAddress()"></app-easyinput>
						</uni-forms-item>
						<uni-forms-item label="详细地址" name="fakeAddress" required>
							<uni-easyinput type="text" v-model="formData.fakeAddress" placeholder="请输入详细地址" />
						</uni-forms-item>
						<uni-forms-item label="电子邮箱" name="legalEmail" required>
							<uni-easyinput type="text" v-model="formData.legalEmail" placeholder="请输入电子邮箱" />
						</uni-forms-item>
						<!-- <uni-forms-item label="店铺介绍" name="briefing" required>
							<uni-easyinput type="textarea" v-model="formData.briefing" placeholder="请输入店铺介绍" />
						</uni-forms-item> -->
					</view>
					<view class="w-full h-44 mb-20 line-height-44 bg-#F0F3F7 px-35 text-18 text-#828282 font-500">收款账户</view>
					<view class="px-20">
						<uni-forms-item label="账户名称" name="payee" required>
							<uni-easyinput type="text" v-model="formData.payee" placeholder="请输入账户名称" />
						</uni-forms-item>
						<uni-forms-item label="银行名称" name="bankName" required>
							<uni-easyinput type="text" v-model="formData.bankName" placeholder="请输入银行名称" />
						</uni-forms-item>
						<uni-forms-item label="开户行" name="openAccountBank" required>
							<uni-easyinput type="text" v-model="formData.openAccountBank" placeholder="请输入开户行" />
						</uni-forms-item>
						<uni-forms-item label="银行账户" name="bankAcc" required>
							<uni-easyinput type="text" v-model="formData.bankAcc" placeholder="请输入银行账户" />
						</uni-forms-item>
						<uni-forms-item label="税率(%)" name="taxRate" required>
							<uni-easyinput type="text" v-model="formData.taxRate" :clearable="false" placeholder="请输入税率">
								<template #right>
									<view class="text-28 pr-20">%</view>
								</template>
							</uni-easyinput>
						</uni-forms-item>
					</view>
					<view class="w-full h-44 mb-20 line-height-44 bg-#F0F3F7 px-35 text-18 text-#828282 font-500">服务费用</view>
					<view class="px-20">
						<uni-forms-item label="保证金" name="earnestMoney" required>
							<uni-easyinput type="text" v-model="formData.earnestMoney" :clearable="false" placeholder="请输入保证金">
								<template #right>
									<view class="text-28 pr-20">元</view>
								</template>
							</uni-easyinput>
						</uni-forms-item>
						<uni-forms-item label="结算周期" name="closePeriod" required>
							<uni-easyinput type="text" v-model="formData.closePeriod" :clearable="false" placeholder="请输入结算周期">
								<template #right>
									<view class="text-28 pr-20">日</view>
								</template>
							</uni-easyinput>
						</uni-forms-item>
						<uni-forms-item name="signingCategory" label-position="top">
							<template #label>
								<view class="w-full flex justify-between items-center">
									<view class="text-28 text-#606266 h-80 flex justify-start items-center">
										<text class="text-#FC3F33">*</text>
										<text>签约类目</text>
									</view>
									<uv-button @click="openCategoryPopup()" type="primary" size="small" text="选择类目"></uv-button>
								</view>
							</template>
							<view class="mt-20">
								<uni-table style="width: 100%" ref="table" border stripe emptyText="暂无更多数据">
									<uni-tr>
										<uni-th width="100rpx" align="center" padding="20rpx 10rpx">
											<text class="text-24 line-height-24">一级类目</text>
										</uni-th>
										<uni-th width="100rpx" align="center" padding="20rpx 10rpx">
											<text class="text-24 line-height-24">二级类目</text>
										</uni-th>
										<uni-th width="160rpx" align="center" padding="20rpx 10rpx">
											<text class="text-24 line-height-24">三级类目</text>
										</uni-th>
										<uni-th width="120rpx" align="center" padding="20rpx 10rpx">
											<text class="text-24 line-height-24">佣金(%)</text>
										</uni-th>
										<uni-th width="90rpx" align="center" padding="20rpx 10rpx">
											<text class="text-24 line-height-24">操作</text>
										</uni-th>
									</uni-tr>
									<uni-tr v-for="(item, index) in formData.signingCategory" :key="index">
										<uni-td align="center" padding="20rpx 10rpx">
											<text class="text-24 line-height-24">{{ item.firstName }}</text>
										</uni-td>
										<uni-td align="center" padding="20rpx 10rpx">
											<text class="text-24 line-height-24">{{ item.secondName }}</text>
										</uni-td>
										<uni-td align="center" padding="20rpx 10rpx">
											<text class="text-24 line-height-24">{{ item.name }}</text>
										</uni-td>
										<uni-td align="center" padding="20rpx 10rpx">
											<text class="text-24 line-height-24">{{ item.customDeductionRatio }}</text>
										</uni-td>
										<uni-td align="center" padding="20rpx 10rpx">
											<view @click="removeCheckCategory(index, item.id)" class="text-24 text-#F56C6C">移除</view>
										</uni-td>
									</uni-tr>
								</uni-table>
							</view>
						</uni-forms-item>
					</view> 
					<view class="w-full h-44 mb-20 line-height-44 bg-#F0F3F7 px-35 text-18 text-#828282 font-500">店铺信息</view>
					<view class="px-20">
						<uni-forms-item label="店铺名称" name="name" required>
							<uni-easyinput type="text" v-model="formData.name" placeholder="请输入店铺名称" />
						</uni-forms-item>
						<uni-forms-item label="店铺logo" name="logo" required>
							<view @click="chooseImage('logo')" class="text-right">
								<image v-if="formData.logo" :src="formData.logo" class="w-80 h-80 border-rd-40" mode="aspectFill"></image>
								<image v-else src="@/pages/my/static/icon_upload_avatar.png" class="w-80 h-80" mode=""></image>
							</view>
						</uni-forms-item>
					</view>
				</uni-forms>
				<uni-popup ref="categoryPopup" type="bottom" border-radius="10px 10px 0 0" background-color="#fff">
					<view class="w-full h-100 px-30 flex justify-between items-center">
						<view class="w-30"></view>
						<view class="text-30 font-bold">选择签约类目</view>
						<image @click="closeCategoryPopup()" :src="common.iconCloseHui" class="w-30 h-30" mode=""></image>
					</view>
					<view class="px-20">
						<uni-forms-item name="limitType">
							<template #label>
								<view class="text-30 text-#222 font-bold h-80 flex justify-start items-center">
									<text>一级类目</text>
								</view>
							</template>
							<view class="ml-30 h-80 flex justify-end items-center">
								<uni-data-select
									v-model="pageData.firstCategory"
									:localdata="pageData.firstCategoryList"
									border-color="rgba(0,0,0,0)"
									text-align="right"
									style="width: 100%"
									@change="getSecondCategoryList()"
								></uni-data-select>
							</view>
						</uni-forms-item>
						<uni-forms-item name="limitType">
							<template #label>
								<view class="text-30 text-#222 font-bold h-80 flex justify-start items-center">
									<text>二级类目</text>
								</view>
							</template>
							<view class="ml-30 h-80 flex justify-end items-center">
								<uni-data-select
									v-model="pageData.secondCategory"
									:localdata="pageData.secondCategoryList"
									border-color="rgba(0,0,0,0)"
									text-align="right"
									style="width: 100%"
									@change="getThreeCategoryList()"
								></uni-data-select>
							</view>
						</uni-forms-item>
						<uni-table style="width: 100%; height: 40vh; overflow-y: scroll" ref="table" border stripe emptyText="暂无更多数据">
							<uni-tr>
								<uni-th width="50rpx" align="center" @click="pageData.checkCategoryList = pageData.threeCategoryList">
									<image
										v-if="pageData.checkCategoryList.length == pageData.threeCategoryList.length && pageData.threeCategoryList.length > 0"
										src="/pages/my/static/checkbox_active.png"
										class="w-40 h-40"
										mode=""
									></image>
									<image v-else src="/pages/my/static/checkbox.png" class="w-40 h-40" mode=""></image>
								</uni-th>
								<uni-th width="440rpx" align="center" style="font-size: 24rpx">三级类目</uni-th>
								<uni-th width="200rpx" align="center" style="font-size: 24rpx">佣金(%)</uni-th>
							</uni-tr>
							<uni-tr v-for="(item, index) in pageData.threeCategoryList" :key="index">
								<uni-td align="center">
									<view class="" @click="checkCategory(item)">
										<image
											v-if="pageData.checkCategoryList.findIndex((elem) => elem.id == item.id) > -1"
											src="/pages/my/static/checkbox_active.png"
											class="w-40 h-40"
											mode=""
										></image>
										<image v-else src="/pages/my/static/checkbox.png" class="w-40 h-40" mode=""></image>
									</view>
								</uni-td>
								<uni-td align="center" style="font-size: 24rpx">{{ item.name }}</uni-td>
								<uni-td align="center">
									<uni-easyinput type="number" v-model="item.customDeductionRatio" :clearable="false" :styles="{ textAlign: 'center' }"></uni-easyinput>
								</uni-td>
							</uni-tr>
						</uni-table>
					</view>
					<view class="py-14 px-40 border-t-solid border-1 border-#EFF2F9">
						<uv-button
							@click="handleCheckCategory()"
							color="#00B496"
							shape="circle"
							text="确认"
							customTextStyle="font-size: 30rpx;font-weight: bold;"
							customStyle="height: 84rpx;"
						></uv-button>
					</view>
					<app-safeAreaBottom></app-safeAreaBottom>
				</uni-popup>
			</view>
			<template #bottom>
				<view class="py-14 px-40 border-t-solid border-1 border-#EFF2F9">
					<uv-button
						@click="handleSubmit()"
						color="#00B496"
						shape="circle"
						text="提交"
						customTextStyle="font-size: 30rpx;font-weight: bold;"
						customStyle="height: 84rpx;"
					></uv-button>
				</view>
				<app-safeAreaBottom></app-safeAreaBottom>
			</template>
		</z-paging>
	</app-layout>
</template>

<style lang="scss" scoped>
.table_style {
	font-size: 24rpx !important;
	padding: 20rpx 10rpx !important;
	line-height: 28rpx !important;
}
</style>
