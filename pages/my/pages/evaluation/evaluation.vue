<script setup>
import { onLoad } from '@dcloudio/uni-app';

import { ref, reactive, getCurrentInstance } from 'vue';

import { useUserStore } from '@/store';

import { getOrderEvaluateInfo, getOrderEvaluateInfoList, likeEvaluation, getBuyersEvaluationUser, getBuyersEvaluation } from '@/server/api';

const props = defineProps({
	productId: {
		type: String,
		default: ''
	},
	shopId: {
		type: String,
		default: ''
	},
	serveGoods: {
		type: [String, Number],
		default: 0
	}
});

const paging = ref(null);

const pageData = reactive({
	activeTab: 0,
	tabList: [
		{
			name: '全部',
			type: '',
			count: 0,
			countKey: 'totalCount',
			show: true
		},
		{
			name: '有内容',
			type: 'CONTENT',
			count: 0,
			countKey: 'contentCount',
			show: true
		},
		{
			name: '有图片',
			type: 'IMAGE',
			count: 0,
			countKey: 'mediaCount',
			show: true
		},
		{
			name: '好评',
			type: 'PRAISE',
			count: 0,
			countKey: 'praiseCount',
			show: true
		},
		{
			name: '差评',
			type: 'CRITICIZE',
			count: 0,
			countKey: 'criticizeCount',
			show: true
		}
	],

	sort: 1,

	evaluateOverview: {
		contentCount: 0,
		mediaCount: 0,
		praiseCount: 0,
		criticizeCount: 0,
		praiseRatio: '0.00',
		totalCount: 0
	},

	dataList: []
});

function changeSort(e) {
	if (pageData.sort !== e) {
		pageData.sort = e;
		if (paging.value) paging.value.reload();
	}
}

function queryList(pageNo, pageSize) {
	const params = {
		userId: useUserStore().userData.userId,
		// type: pageData.tabList[pageData.activeTab].type,
		current: pageNo,
		size: pageSize
	};

	getBuyersEvaluation({
		...params
	})
		.then((res) => {
			console.log(res);
			if (res.apiStatus) {
				paging.value.complete(res.data.records);
			} else {
				paging.value.complete(false);
			}
		})
		.catch((err) => {
			paging.value.complete(false);
		});
}

function likeEvaluationItem(e) {
	const { item, index } = e;
	// likeEvaluation().then((res) => {
	// if (res.apiStatus) {
	pageData.dataList[index].userEvaluateStatus = !pageData.dataList[index].userEvaluateStatus;
	if (pageData.dataList[index].userEvaluateStatus) {
		pageData.dataList[index].likeNum += 1;
	} else {
		pageData.dataList[index].likeNum -= 1;
	}
	// }
	// });
}
onLoad(() => {});
</script>
<template>
	<app-layout>
		<z-paging ref="paging" v-model="pageData.dataList" @query="queryList">
			<template #top>
				<view class="w-full h-1 bg-#EAEDF3 mt-20"></view>
			</template>
			<view class="evaluation">
				<view v-for="(item, index) in pageData.dataList" :key="index" class="">
					<page-evaluation-item :item="item" :index="index" :serveGoods="item.serveGoods" type="user" @likeEvaluation="likeEvaluationItem"></page-evaluation-item>
				</view>
			</view>
		</z-paging>
	</app-layout>
</template>
<style lang="scss" scoped>
.evaluation {
}
</style>
