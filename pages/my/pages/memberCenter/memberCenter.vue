<script setup>
import { onLoad } from '@dcloudio/uni-app';

import { ref, reactive, computed, unref } from 'vue';

import { canENV, route, goArticlePage, getPriceInfo } from '@/common/utils';

import { getIdUserMemberData, addonMemberPaidMemberRank, createActiveMember, getMemberGrow, getUserBalance, getOrderIsPaySuccess } from '@/server/api';

import { useAppStore, useUserStore } from '@/store';

import useConvert from '@/common/useConvert';

import { Decimal } from 'decimal.js';

import { ORDERPAYMENT, PAY_TYPE } from "@/common/types/goods";
import { common, logo } from "@/common/images";

const { divTenThousand, salesVolumeToStr, mulTenThousand, divHundred } = useConvert();

const userStore = useUserStore();

const pageData = reactive({});

const memberCardInfo = ref({
	memberType: "FREE_MEMBER",
	userHeadPortrait: "",
	userNickname: "",
	growthValue: 0,
	rankCode: 1,
	currentMemberVO: {
		memberCardId: "",
		memberCardValidTime: "",
		memberName: "",
		relevancyRights: {
			memberRightsId: "",
			extendValue: "",
			rightsExplain: "",
			rightsType: "EXCLUSIVE_SERVICE",
			rightsName: "",
			rightsIcon: "",
		},
		rankCode: 0
	},
	growValue: 0
});

/**
 * 用户所使用得会员卡类型
 */
const memberTypeCn = {
	FREE_MEMBER: '免费会员',
	PAID_MEMBER: '付费会员'
};
/**
 * 会员权益扩展值
 */
const reightsTypeCn = {
	GOODS_DISCOUNT: '商品抵扣',
	INTEGRAL_MULTIPLE: '积分加倍',
	LOGISTICS_DISCOUNT: '物流优惠',
	PRIORITY_SHIPMENTS: '优先发货',
	QUICKNESS_AFS: '极速售后',
	EXCLUSIVE_SERVICE: '专属客服',
	USER_DEFINED: '自定义'
};

/**
 * 有效的时间类型
 */
const efficientTimeTypeCn = {
	ONE_MONTH: '一个月',
	THREE_MONTH: '三个月',
	TWELVE_MONTH: '12个月',
	THREE_YEAR: '三年',
	FIVE_YEAR: '五年'
};

/**
 * 有效的时间类型
 */
const efficientTimeNumber = {
	ONE_MONTH: 30,
	THREE_MONTH: 90,
	TWELVE_MONTH: 365,
	THREE_YEAR: 365 * 3,
	FIVE_YEAR: 365 * 5
};

const strategyPatternHeader = {
	ONE_MONTH: {
		everyDayPrice: (paidRule) => {
			if (!paidRule.price) return 0;
			return Number(divTenThousand(paidRule.price).div(efficientTimeNumber[paidRule.effectiveDurationType]).toFixed(2)) || '';
		}
	},
	THREE_MONTH: {
		everyDayPrice: (paidRule) => {
			if (!paidRule.price) return 0;
			return Number(divTenThousand(new Decimal(paidRule.price).div(efficientTimeNumber[paidRule.effectiveDurationType]).toString()).toFixed(2)) || '';
		}
	},
	TWELVE_MONTH: {
		everyDayPrice: (paidRule) => {
			if (!paidRule.price) return 0;
			return Number(divTenThousand(new Decimal(paidRule.price).div(efficientTimeNumber[paidRule.effectiveDurationType]).toString()).toFixed(2)) || '';
		}
	},
	THREE_YEAR: {
		everyDayPrice: (paidRule) => {
			if (!paidRule.price) return 0;
			return Number(divTenThousand(new Decimal(paidRule.price).div(efficientTimeNumber[paidRule.effectiveDurationType]).toString()).toFixed(2)) || '';
		}
	},
	FIVE_YEAR: {
		everyDayPrice: (paidRule) => {
			if (!paidRule.price) return 0;
			return Number(divTenThousand(new Decimal(paidRule.price).div(efficientTimeNumber[paidRule.effectiveDurationType]).toString()).toFixed(2)) || '';
		}
	}
};

const strategyPatternHandler = {
	GOODS_DISCOUNT: {
		msg: (memberRight) => {
			return `${memberRight.rightsExplain}全场商品${divHundred(memberRight.extendValue)}折`;
		}
	},
	INTEGRAL_MULTIPLE: {
		msg: (memberRight) => {
			return `${memberRight.rightsExplain}${divHundred(memberRight.extendValue)}倍`;
		}
	},
	PRIORITY_SHIPMENTS: {
		msg: (memberRight) => {
			return `${memberRight.rightsExplain}`;
		}
	},
	LOGISTICS_DISCOUNT: {
		msg: (memberRight) => {
			return `${memberRight.rightsExplain}`;
		}
	},
	QUICKNESS_AFS: {
		msg: (memberRight) => {
			return `${memberRight.rightsExplain}`;
		}
	},
	EXCLUSIVE_SERVICE: {
		msg: (memberRight) => {
			return `${memberRight.rightsExplain}`;
		}
	},
	USER_DEFINED: {
		msg: (memberRight) => {
			return `${memberRight.rightsExplain}`;
		}
	}
};

const memberPaidMemberRank = ref([]);

const memberPaidMemberRankIndex = ref(0);
const changeMemberPaidMemberRankRuleIndex = ref(0);

const growPercent = computed(() => {
	return Math.floor((memberCardInfo.value.growthValue / memberCardInfo.value.growValue) * 100);
});

const memberPaidMemberRankItem = computed(() => {
	return memberPaidMemberRank.value[memberPaidMemberRankIndex.value] || null;
});

const changeMemberPaidMemberRankRuleItem = computed(() => {
	if (memberPaidMemberRankItem.value && Array.isArray(memberPaidMemberRankItem.value.paidRuleJson)) {
		return memberPaidMemberRankItem.value.paidRuleJson[changeMemberPaidMemberRankRuleIndex.value] || null;
	}

	return null;
});

function changeMemberPaidMemberRank(index) {
	if (memberPaidMemberRankIndex.value === index) return;
	memberPaidMemberRankIndex.value = index;
}

function changeMemberPaidMemberRankRule(index) {
	if (changeMemberPaidMemberRankRuleIndex.value === index) return;
	changeMemberPaidMemberRankRuleIndex.value = index;
}

const relevancyRightInfoModalRef = ref();
const relevancyRightInfo = ref({
	rightsName: '',
	rightsExplain: '',
	rightsType: ''
});

function showRelevancyRightInfo(item) {
	if (!item) return;
	relevancyRightInfo.value = item;
	if (relevancyRightInfo.value.rightsName && relevancyRightInfoModalRef.value) relevancyRightInfoModalRef.value.open();
}

/**
 * 获取会员中心信息
 */
async function initGetMemberCardInfo() {
	try {
		const res = await getIdUserMemberData();
		if (res.apiStatus) {
			memberCardInfo.value = res.data;

			useUserStore().changeUserMemberData(res.data);

			if (res.data.memberType === 'FREE_MEMBER') {
				const memberGrowRes = await getMemberGrow({ rankCode: res.data.currentMemberVO.rankCode });
				if (memberGrowRes.apiStatus) {
					memberCardInfo.value.growValue = memberGrowRes.data.needValue;
					memberCardInfo.value.rankCode = memberGrowRes.data.rankCode;
				}
			}
		}
	} catch (error) {
		//TODO handle the exception
	}
}

/**
 * 付费会员级别列表
 */
async function initMemberPaidMemberRank() {
	try {
		const res = await addonMemberPaidMemberRank();

		if (res.apiStatus) {
			memberPaidMemberRankIndex.value = 0;
			changeMemberPaidMemberRankRuleIndex.value = 0;
			memberPaidMemberRank.value = res.data;
		}
	} catch (error) {
		//TODO handle the exception
	}
}

function computedGrowthValue() {
	if (memberCardInfo.value.growValue && memberCardInfo.value.growthValue) {
		const growValue = memberCardInfo.value.growValue - memberCardInfo.value.growthValue;
		return growValue <= 0 ? 0 : growValue;
	}
	return 0;
}

const paySelectRef = ref();
const showPay = ref(false);
const payLoading = ref(false);
const payInfo = reactive({
	price: 0,
	balanceTotalShow: true,
	balanceTotal: 0,
	timeout: 0
});

const orderNumber = ref('');
const payExtra = ref();
const payFrom = ref(PAY_TYPE.MEMBER);

const payParame = ref({
	memberId: '',
	ruleId: '',
	memberPrice: ''
});

async function initUserBalance() {
	const { code, msg, data, apiStatus } = await getUserBalance();
	payInfo.balanceTotalShow = apiStatus;
	payInfo.balanceTotal = apiStatus ? `${getPriceInfo(data).integer}.${getPriceInfo(data).decimalText}` : '';
}

async function paySubmit(e) {
	if (e.type === 'balance') {
		const { code, data, msg, apiStatus } = await createActiveMember({
			payType: ORDERPAYMENT.BALANCE,
			id: payParame.value.memberId,
			payAmount: payParame.value.memberPrice,
			paidRuleId: payParame.value.ruleId
		}).catch((e) => {
			payLoading.value = false;
		});

		if (!apiStatus) {
			payError(unref(orderNumber), payFrom.value);

			return;
		}

		orderNumber.value = data.outTradeNo;
		loopCheckPayStatus();
	}

	if (e.type === 'wechat') {
		const payRes = await createActiveMember({
			payType: ORDERPAYMENT.WECHAT,
			id: payParame.value.memberId,
			payAmount: payParame.value.memberPrice,
			paidRuleId: payParame.value.ruleId
		}).catch((e) => {
			payLoading.value = false;
		});

		canENV(() => {
			console.log(payRes);
		});

		if (!payRes || !payRes.apiStatus) {
			payError(unref(orderNumber), payFrom.value);
			return;
		}

		orderNumber.value = payRes.data.outTradeNo;

		requestPayment(e.payItem.payment, payRes.data.data);
	}

	if (e.type === 'alipay') {
		const payRes = await createActiveMember({
			payType: ORDERPAYMENT.ALIPAY,
			id: payParame.value.memberId,
			payAmount: payParame.value.memberPrice,
			paidRuleId: payParame.value.ruleId
		}).catch((e) => {
			payLoading.value = false;
		});

		canENV(() => {
			console.log(payRes);
		});

		if (!payRes || !payRes.apiStatus) {
			payError(unref(orderNumber), payFrom.value);
			return;
		}

		orderNumber.value = payRes.data.outTradeNo;

		requestPayment(e.payItem.payment, payRes.data.data);
	}
}

/**
 * uni requestPayment 支付
 * @param {*} data
 */
function requestPayment(provider, data) {
	uni.requestPayment({
		provider,
		orderInfo: data,
		...data,
		success: () => {
			loopCheckPayStatus();
		},
		fail: () => {
			closeLoopCheck(false, true);
		}
	});
}

let time = null;

/**
 * 循环检查支付状态
 */
function loopCheckPayStatus() {
	uni.showLoading({
		title: '支付校验中...',
		mask: true
	});
	let count = 1;
	checkPayStatus(false);
	try {
		//先清除一次定时器
		time && clearInterval(time);
		// 开启轮训
		time = setInterval(() => checkPayStatus(++count > 10), 800);
	} catch (error) {
		uni.hideLoading();
		time && clearInterval(time);
	}
	// payBtnLoading.value = false;
}

/**
 * 定时器循环体
 */
function checkPayStatus(skipLoop) {
	if (skipLoop) {
		return closeLoopCheck(false, true);
	}
	getOrderIsPaySuccess({
		outTradeNo: unref(orderNumber)
	}).then((res) => {
		if (res.apiStatus) {
			closeLoopCheck(true, true);
		}
	});
}

/**
 * 取消轮训检查
 */
function closeLoopCheck(state = false, isPayStatus = false) {
	time && clearInterval(time);
	uni.hideLoading();
	if (isPayStatus) {
		if (state) {
			paySuccess(unref(orderNumber), payFrom.value);
		} else {
			payError(unref(orderNumber), payFrom.value);
		}
	} else {
		payLoading.value = false;
		showPay.value = false;
		// reloadList();
	}
}

async function paySuccess(orderType, payFrom) {
	// uni.hideLoading();
	payLoading.value = false;
	showPay.value = false;
	uni.showToast({
		icon: 'success',
		title: '支付成功',
		duration: 1000,
		mask: true
	});

	reloadList();
}

async function payError(orderType, payFrom) {
	// uni.hideLoading();
	payLoading.value = false;
	showPay.value = false;
	uni.showToast({
		icon: 'error',
		title: '支付失败',
		duration: 1500,
		mask: true
	});

	// reloadList();
}

function paySelectChange(e) {
	if (!e.show) {
		closeLoopCheck(false, false);
	}
}

async function submit() {
	uni.showLoading({
		title: '加载中...'
	});
	if (!memberPaidMemberRankItem.value || !changeMemberPaidMemberRankRuleItem.value) return;

	const memberId = memberPaidMemberRankItem.value?.id;
	const ruleId = changeMemberPaidMemberRankRuleItem.value.id;
	const memberPrice = changeMemberPaidMemberRankRuleItem.value.price;

	if (!memberPrice) {
		return;
	}

	payParame.value = {
		memberId,
		ruleId,
		memberPrice
	};

	payInfo.price = `${getPriceInfo(memberPrice).integer}.${getPriceInfo(memberPrice).decimalText}`;

	await initUserBalance();

	showPay.value = true;
	uni.hideLoading();
}

function reloadList() {
	loadData();
}

function loadData() {
	initGetMemberCardInfo();
	initMemberPaidMemberRank();
}

onLoad(() => {
	loadData();
});
</script>

<template>
	<view class="member-center">
		<view class="header-box w-full">
			<app-navBar bg-color="#0F0D07" :backIcon="common.iconLeftBai" rightPlaceholderSize="0rpx" :showRight="false" back>
				<template #content>
					<view class="w-full h-full flex justify-between items-center px-0">
						<!-- #ifndef MP-WEIXIN -->
						<view class="flex flex-center w-70"></view>
						<!-- #endif -->

						<view class="flex-1 nav-center">
							<text class="text-32 text-#fff font-bold">会员中心</text>
						</view>

						<!-- <view class="flex flex-center pr-30 w-150"
							@click="route('/pages/my/pages/memberCenter/openRecord/openRecord')">
							<text class="text-30 font-500 text-#fff">开通记录</text>
						</view> -->

						<view class="flex flex-center pr-30 w-150"></view>
					</view>
				</template>
				<template #right>
					<view class=""></view>
				</template>
			</app-navBar>

			<view class="w-full h-325 relative">
				<app-image src="@/pages/my/static/bg_vip_card.png" width="750" height="325" class="absolute"></app-image>
				<template v-if="memberCardInfo.memberType === 'FREE_MEMBER'">
					<view class="w-full h-325 relative pt-65 pl-75">
						<view class="w-full w-360">
							<!-- <app-image src="@/pages/my/static/icon_vip_logo.png" width="190" height="44" mode=""></app-image> -->
							<view class="w-full flex items-center justify-between text-24 text-#ccc">
								<view class="">成长值</view>
								<view class="">LV{{ memberCardInfo.rankCode }}</view>
							</view>
							<view class="mt-15">
								<uv-line-progress :percentage="growPercent * 1" height="8rpx" :show-text="false" active-color="#D9C395" inactive-color="#666666"></uv-line-progress>
							</view>
							<view class="w-full flex items-center justify-between text-24 text-#ccc mt-15">
								<view class="">{{ memberCardInfo.growthValue }}/{{ memberCardInfo.growValue }}</view>
								<view class="">还差{{ computedGrowthValue() }}</view>
							</view>
						</view>
						<view class="mt-15">
							<view class="text-#D9C395 text-22 font-500">开通VIP 享专属权益</view>
						</view>
					</view>
				</template>
				<template v-else>
					<view class="w-full h-325 relative pt-90 pl-95">
						<view class="">
							<app-image src="@/pages/my/static/icon_vip_logo.png" width="190" height="44" mode=""></app-image>
						</view>
						<view class="mt-40">
							<view class="text-#D9C395 text-22 font-500"> {{ memberCardInfo.currentMemberVO.memberName }} - {{ memberCardInfo.currentMemberVO?.memberCardValidTime || new Date().toLocaleDateString() }} 到期 </view>
						</view>
					</view>
				</template>
			</view>

			<view class="w-full h-220 px-30" style="margin-top: -80rpx">
				<view class="w-full h-220" style="background: linear-gradient(0deg, #0f0d07 0%, #161512 52%, #1a1812 100%); border-radius: 0rpx 0rpx 30rpx 30rpx">
					<view class="w-full h-full flex items-center justify-between px-20">
						<scroll-view scroll-x="true" class="w-full">
							<template v-if="memberPaidMemberRankItem && Array.isArray(memberPaidMemberRankItem.relevancyRightsList)">
								<view class="w-full flex items-center">
									<template v-for="(item, index) in memberPaidMemberRankItem.relevancyRightsList">
										<view class="flex flex-col flex-center w-180 px-10 flex-shrink-0" @click="showRelevancyRightInfo(item)">
											<app-image :src="item.rightsIcon" size="78"></app-image>
											<view class="mt-10 text-#E2E1E0 text-24 font-500 uv-line-1">{{ item.rightsName }}</view>
											<view class="mt-8 text-#747270 text-22 font-500 uv-line-1">
												{{
													!item.rightsType ? item.rightsName : strategyPatternHandler[item?.rightsType] ? strategyPatternHandler[item?.rightsType].msg(item) : item.rightsExplain
												}}
											</view>
										</view>
									</template>
								</view>
							</template>
						</scroll-view>
					</view>
				</view>
			</view>
		</view>

		<view class="w-full pt-30 pb-50 px-20 bg-#F3F4F7" style="border-radius: 30rpx 30rpx 0rpx 0rpx; margin-top: -30rpx">
			<view class="w-full bg-#fff border-rd-20 py-30 px-25">
				<view class="flex items-center">
					<view class="w-7 h-30 border-rd-2 bg-#BC6E3E mr-16"></view>
					<view class="text-32 font-bold">选择会员类型</view>
				</view>

				<view class="grid grid-cols-3 gap-20 py-20">
					<template v-for="(item, index) in memberPaidMemberRank" :key="index">
						<view @click="changeMemberPaidMemberRank(index)" class="bg-#F2F4F8 border-rd-8 h-60 text-center flex flex-center mx-0 border-solid border-1 border-#F2F4F8" :style="[memberPaidMemberRankIndex === index && 'background-color: #fcf8f0; border-color: #D9C395;']">
							<text class="text-26 text-#323232 font-500" :style="[memberPaidMemberRankIndex === index && 'color: #D9C395; font-weight: bold']">
								{{ item.paidMemberName }}
							</text>
						</view>
					</template>
				</view>

				<template v-if="memberPaidMemberRankItem && Array.isArray(memberPaidMemberRankItem.paidRuleJson)">
					<view class="flex items-center mt-0">
						<view class="w-7 h-30 border-rd-2 bg-#BC6E3E mr-16"></view>
						<view class="text-32 font-bold">选择会员套餐</view>
					</view>
					<view class="w-full py-5 px-5 flex flex-wrap">
						<template v-for="(item, index) in memberPaidMemberRankItem.paidRuleJson" :key="index">
							<view class="meal-item-box ac-op" :class="changeMemberPaidMemberRankRuleIndex === index && 'active'" @click="changeMemberPaidMemberRankRule(index)">
								<view class="text-center text-34 font-bold mt-26">{{ efficientTimeTypeCn[item.effectiveDurationType] }}</view>

								<view class="w-full flex items-end justify-center">
									<view class="text-#B57838 text-48 font-bold">{{ item.price && divTenThousand(item.price) }}</view>
									<view class="text-24 pb-8">元</view>
								</view>

								<view class="text-center text-#888 text-22 font-500" v-if="strategyPatternHeader[item.effectiveDurationType].everyDayPrice(item)"> 每天仅需{{ strategyPatternHeader[item.effectiveDurationType].everyDayPrice(item) }}元 </view>
							</view>
						</template>
					</view>
				</template>

				<view class="w-full mt-30">
					<uv-button
						color="linear-gradient(-65deg, #2E2721 0%, #433A33 100%)"
						:customStyle="{
							height: '96rpx',
							borderRadius: '16rpx'
						}"
						@click="submit">
						<view class="w-full text-center flex flex-center text-#fff text-30 font-bold">
							<view class="">立即支付</view>
							<template v-if="changeMemberPaidMemberRankRuleItem">
								<view class="">￥</view>
								<view class="text-42 pb-8">{{ changeMemberPaidMemberRankRuleItem.price && divTenThousand(changeMemberPaidMemberRankRuleItem.price) }}</view>
								<view class="">元</view>
							</template>
						</view>
					</uv-button>
				</view>

				<view class="w-full mt-20 flex flex-center">
					<view class="text-22 text-#777 font-500">开通视为同意</view>
					<view class="text-22 text-#C09966 font-500 ac-op" @click="goArticlePage('hyxy')">《和家无忧会员协议》</view>
				</view>
			</view>

			<my-uv-modal
				ref="relevancyRightInfoModalRef"
				:title="relevancyRightInfo.rightsName"
				:content="
					!relevancyRightInfo.rightsType
						? relevancyRightInfo.rightsName
						: strategyPatternHandler[relevancyRightInfo?.rightsType]
						? strategyPatternHandler[relevancyRightInfo?.rightsType].msg(relevancyRightInfo)
						: relevancyRightInfo.rightsExplain
				"
				confirmText="知道了"
			></my-uv-modal>

			<app-pay-select
				ref="paySelectRef"
				v-model:show="showPay"
				v-model:btnLoading="payLoading"
				:price="payInfo.price"
				:balanceTotal="payInfo.balanceTotal"
				:balanceTotalShow="payInfo.balanceTotalShow"
				:timeout="payInfo.timeout"
				@submit="paySubmit"
				@change="paySelectChange"
			></app-pay-select>

			<view class="w-full bg-#fff border-rd-20 py-30 px-25 mt-20" v-if="false">
				<view class="flex items-center">
					<!-- <view class="w-7 h-30 border-rd-2 bg-#BC6E3E mr-16"></view> -->
					<view class="text-32 font-bold">和家无忧专属折扣</view>
					<view class="text-24 text-#C09966 font-500 pt-8 ml-16">覆盖全类目商品/服务</view>
				</view>
				<view class="w-full py-20 flex items-center justify-between">
					<view class="w-156 h-42 border-rd-8 flex flex-center text-#222 text-22 font-500 bg-#F4DBC2">商城商品</view>

					<view class="w-156 h-42 border-rd-8 flex flex-center text-#222 text-22 font-500 bg-#F2F6F9">护理服务</view>

					<view class="w-156 h-42 border-rd-8 flex flex-center text-#222 text-22 font-500 bg-#F2F6F9">陪诊服务</view>

					<view class="w-156 h-42 border-rd-8 flex flex-center text-#222 text-22 font-500 bg-#F2F6F9">家政服务</view>
				</view>

				<view class="w-full mt-20 flex items-center">
					<view class="flex-1 flex items-center justify-between">
						<view class="flex flex-col">
							<app-image :src="logo" size="180" mode=""></app-image>
							<view class="mt-20 flex flex-center text-#222">
								<view class="text-26 font-500">省</view>
								<view class="text-32 font-bold">￥4.99</view>
							</view>
						</view>
						<view class="flex flex-col">
							<app-image :src="logo" size="180" mode=""></app-image>
							<view class="mt-20 flex flex-center text-#222">
								<view class="text-26 font-500">省</view>
								<view class="text-32 font-bold">￥4.99</view>
							</view>
						</view>
						<view class="flex flex-col">
							<app-image :src="logo" size="180" mode=""></app-image>
							<view class="mt-20 flex flex-center text-#222">
								<view class="text-26 font-500">省</view>
								<view class="text-32 font-bold">￥4.99</view>
							</view>
						</view>
					</view>
					<view class="w-82 h-212 border-rd-10 bg-#FBF5F1 text-24 font-500 text-#64351A flex flex-col flex-center ml-12">
						<view class="">更多</view>
						<view class="">折扣</view>
						<view class="">商品</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<style lang="scss" scoped>
.member-center {
	min-height: 100vh;
	background: #f3f4f7;
}

.header-box {
	background: #0f0d07;
	padding-bottom: 50rpx;
}

.type-item-box {
}

.meal-item-box {
	width: 190rpx;
	height: 198rpx;

	margin: 20rpx 13rpx 0rpx;
	border-width: 4rpx;
	border-style: solid;

	background: linear-gradient(-50deg, #f9ede4 0%, #fcf8f5 100%);
	border-radius: 20rpx;

	display: flex;
	flex-direction: column;
	align-items: center;

	border-color: #f9ede4;
	&.active {
		border-color: #bc6e3e;
	}
}
</style>
