<script setup>
import { reactive, ref } from 'vue';

import { onLoad } from '@dcloudio/uni-app';

// import {
// 	getVipOrder
// } from '@/server/api'

const pageData = reactive({
	page: 1,
	limit: 10,
	total: 0,
	dataList: []
});

const pagePaging = ref(null);

onLoad(() => {
	getData();
});

function onRefresh() {
	pageData.page = 1;
	getData();
}

function scrolltolower() {
	if (pageData.dataList.length >= pageData.total) {
		return;
	}
	pageData.page += 1;
	getData();
}

function getData() {
	uni.showLoading({
		title: '加载中...'
	});

	uni.hideLoading();
	pageData.dataList = [];
	if (pagePaging.value) pagePaging.value.complete(pageData.dataList);
	return;

	getVipOrder({
		page: pageData.page,
		limit: pageData.limit,
		filter: JSON.stringify({
			'unifyorder.pay_status': 1
		})
	})
		.then((res) => {
			if (res.apiStatus) {
				pageData.total = res.total;
				const list = res.rows.map((elem) => {
					elem.packagejson = JSON.parse(elem.packagejson);
					return elem;
				});
				if (pageData.page == 1) {
					pageData.dataList = list;
				} else {
					pageData.dataList = [...pageData.dataList, ...list];
				}
			}
			uni.hideLoading();
			pagePaging.value.complete([]);
		})
		.catch((err) => {
			uni.hideLoading();
			pagePaging.value.complete([]);
		});
}
</script>
<template>
	<app-layout>
		<z-paging ref="pagePaging" refresher-only @onRefresh="onRefresh" @scrolltolower="scrolltolower">
			<view class="openRecord p-30">
				<template v-for="(item, i) in pageData.dataList" :key="i">
					<view class="border-rd-29 bg-#fff mb-24 relative" :style="{ color: true ? '#111327' : '#888888' }">
						<view
							class="w-full h-90 pl-37 flex items-center border-rd-20 text-34 text-#3F3120 font-bold"
							style="background: linear-gradient(-90deg, #f6e6d3 0%, #fcecdc 38%, #e3c3ac 100%)"
						>
							VIP生效中
						</view>
						<view class="pl-29 pr-29 pt-34 pb-28">
							<view class="text-34 font-bold">VIP￥30—金牌会员</view>
							<view class="mt-25 text-24">有效期：2024.11.26-2024.12.26</view>
							<view class="mt-24 text-24">订单号：484526578982562848462</view>
							<!-- status: 1生效中 2已失效 0未生效 -->
							<!-- 	<view class="absolute w-100 h-50 text-center line-height-50 bottom-0 right-0 text-24 font-bold"
								style="border-radius: 20rpx 0rpx 20rpx 0rpx;"
								:class="[item.status == 1 && 'bg-#7D46F5 color-#fff',item.status == 2 && 'bg-#F96437 color-#fff',item.status == 0 && 'bg-#F8B47C color-#fff']">
								{{item.status_text}}
							</view> -->

							<app-image src="@/pages/my/static/icon_vip_shixiaoi.png" size="100" class="absolute w-100 h-100 bottom-44 right-37" mode=""></app-image>
						</view>
					</view>
				</template>
				<view v-if="pageData.dataList.length == 0" class="mt-200">
					<uv-empty mode="list" text="没有数据哦~"></uv-empty>
				</view>
			</view>
		</z-paging>
	</app-layout>
</template>
<style>
body {
	background-color: #f2f4f5;
}
</style>
<style lang="scss" scoped></style>
