<script setup>
import { ref, reactive, getCurrentInstance, nextTick } from "vue";

import { onLoad } from "@dcloudio/uni-app";

import { getUserFootprintList, delUserFootprint } from "@/server/api";

import { getPriceInfo, route } from "@/common/utils";

const paging = ref(null);

const pageData = reactive({
	activeTab: 0,
	sort: 1,
	dataList: [],

	isEditor: false,

	footprintList: [],

	value: 1,
});

const checkProductValue = ref([]);

function changeType(e) {
	pageData.activeTab = e;
}

function changeSort(e) {
	pageData.sort = e;
}

// 商品详情
function goGoodsDetails(goods) {
	route("/pages/goods/pages/productDetails/productDetails", {
		productId: goods.productId,
		shopId: goods.shopId,
	});
}

async function delFootprints() {
	if (checkProductValue.value.length > 0) {
		uni.showModal({
			title: "提醒",
			content: "确定要删除吗？",
			success(res) {
				if (res.confirm) {
					const items = checkProductValue.value.map((i) => {
						return pageData.dataList[i];
					});
					const itemIds = items.map((i) => i.id);
					delUserFootprint(itemIds, {
						params: {
							userFootMarkStatus: "DELETE", // EMPTY->清空 DELETE->删除
						},
					}).then((res) => {
						if (res.apiStatus) {
							checkProductValue.value = [];
							if (paging.value) paging.value.reload();
						}
					});
				}
			},
		});
	} else {
		openEditor(false);
	}
}

function openEditor(state = true) {
	checkProductValue.value = [];
	pageData.isEditor = state;
}

const queryMonth = reactive({ month: 0, footMarkDate: "" });

function useFootprintCollection(params = []) {
	const footprintCollection = [];
	for (let i = 0; i < params.length; i++) {
		let isHere = footprintCollection.some((item) => item.date === params[i].date);
		if (!isHere) {
			footprintCollection.push({
				date: params[i].date,
				records: [Object.assign({ ...params[i], index: i }, { done: false })],
			});
		} else {
			for (let j = 0; j < footprintCollection.length; j++) {
				if (footprintCollection[j].date === params[i].date) {
					footprintCollection[j].records.push(Object.assign({ ...params[i], index: i }, { done: false }));
				}
			}
		}
	}
	return footprintCollection;
}

function queryList(pageNo, pageSize) {
	const params = {
		pageNo: pageNo,
		current: pageNo,
		pageSize: pageSize,
		...queryMonth,
	};

	getUserFootprintList({ ...params })
		.then((res) => {
			if (res.apiStatus) {
				paging.value.complete(res.data.records).then((res) => {
					if (paging.value) pageData.footprintList = useFootprintCollection(pageData.dataList);
				});
			} else {
				if (paging.value) paging.value.complete(false);
			}
		})
		.catch((err) => {
			if (paging.value) paging.value.complete(false);
		});
}

onLoad(() => {});
</script>
<template>
	<app-layout>
		<z-paging ref="paging" bg-color="#F0F3F7" v-model="pageData.dataList" @query="queryList">
			<template #top>
				<app-navBar bgColor="#fff" back rightPlaceholderSize="140rpx" leftPlaceholderSize="140rpx">
					<template #content>
						<view class="w-full h-full flex justify-between items-center px-0">
							<view class="flex-1 nav-center">
								<text class="text-32 text-#000 font-bold">足迹</text>
							</view>
						</view>
					</template>
					<template #right>
						<template v-if="pageData.isEditor">
							<view class="flex items-center justify-end">
								<text class="text-30 font-500 flex-shrink-0" @click="openEditor(false)">完成</text>
								<text class="text-30 font-500 flex-shrink-0 ml-20 text-#FC3F33" @click="delFootprints">删除</text>
							</view>
						</template>
						<template v-if="!pageData.isEditor">
							<view class="flex items-center justify-end">
								<text class="text-30 font-500" @click="openEditor(true)">管理</text>
							</view>
						</template>
					</template>
				</app-navBar>
			</template>
			<view class="evaluation py-20">
				<uv-checkbox-group v-model="checkProductValue" placement="column" shape="circle" activeColor="#09C1B1">
					<template v-for="(dateItem, dateIndex) in pageData.footprintList" :key="dateIndex">
						<view class="px-20">
							<view class="w-full text-30 text-#222222 font-bold mb-20">{{ dateItem.date }}</view>
							<template v-for="(item, index) in dateItem.records" :key="index">
								<view class="w-full bg-#fff border-rd-20 py-35-n py-26 px-26 mb-20">
									<view class="w-full">
										<view class="w-full flex items-center mt-0">
											<template v-if="pageData.isEditor">
												<view class="mr-20">
													<uv-checkbox :name="item.index"></uv-checkbox>
												</view>
											</template>

											<view class="">
												<view class="w-180 h-180 border-1 border-solid border-#F5F5F5 border-rd-20" @click="goGoodsDetails(item)">
													<app-image :src="item.productPic" size="178" rd="20" mode=""></app-image>
												</view>
											</view>
											<view class="flex-1 ml-20 min-h-180" @click="goGoodsDetails(item)">
												<view class="w-full flex flex-col">
													<view class="text-30 font-bold uv-line-1">{{ item.productName }}</view>
													<view class="w-full mt-18">
														<view class="h-46"></view>
														<!-- <view class="w-full bg-#F8F8F8 border-rd-6 h-46 flex items-center px-10">
															<view class="flex-1 text-#555555 text-24 font-500 uv-line-1"></view>

															<view class="pl-20">
																<app-image :src="common.iconJiantouBottomHui" size="20" mode=""></app-image>
															</view>
														</view> -->
													</view>
													<view class="w-full flex items-center justify-between mt-30">
														<view class="flex items-end text-#FC3F33 font-bold">
															<view class="text-30">￥</view>
															<view class="text-34">{{ getPriceInfo(item.productPrice).integer }}.{{ getPriceInfo(item.productPrice).decimalText }}</view>
														</view>

														<!-- <view class="">
															<uv-number-box v-model="pageData.value" min="1" :integer="true" inputWidth="60rpx" buttonSize="50rpx" bgColor="#F3F4F6"></uv-number-box>
														</view> -->
													</view>
												</view>
											</view>
										</view>
									</view>
								</view>
							</template>
						</view>
					</template>
				</uv-checkbox-group>
			</view>
		</z-paging>
	</app-layout>
</template>
<style lang="scss" scoped>
.evaluation {
}
</style>
