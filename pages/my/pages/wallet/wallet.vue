<script setup>
import { reactive, ref, getCurrentInstance } from 'vue';

import { onLoad, onReady, onShow } from '@dcloudio/uni-app';

import { getUserPerson, getCouponNumber, getAccounts, getPaymentHistory, getSavingManage } from '@/server/api';

import { route, getPriceInfo } from '@/common/utils';

import storage from '@/common/storage';

import useConvert from '@/common/useConvert';

const { divTenThousand, salesVolumeToStr } = useConvert();

const dealTypeCn = {
	SYSTEM_GIVE: '系统赠送',
	PERSONAL_CHARGING: '个人充值',
	SYSTEM_CHARGING: '系统充值',
	SHOPPING_PURCHASE: '购物消费',
	PURCHASE_MEMBER: '购买会员',
	REFUND_SUCCEED: '退款成功',
	WITHDRAW: '提现',
	INVITE: '邀请佣金'
};

const pageData = reactive({
	barBg: 'rgba(255,255,255,0)',
	walletDetail: {
		beans: 0,
		money: 0,
		score: 0,
		totalincome: 0,
		withdraw: 0
	},

	userPerson: {
		balance: 0,
		collectCount: 0,
		footprint: 0,
		integral: 0,

		goodsCollectCount: 0,
		shopCollectCount: 0,

		couponCount: 0,
		bankCount: 0
	},

	dataList: [],
	streamTotal: 0
});
const pagePaging = ref(null);

const topUpParam = ref({
	switching: false,
	discountsState: true,
	ruleJson: [],
	id: ''
});

async function queryList(pageNo, pageSize) {
	const params = {
		pageNo: pageNo,
		current: pageNo,
		pageSize: pageSize,
		changeType: '',
		dealAggregationType: 'ALL',
		queryTime: ''
	};

	if (pageNo === 1) {
		try {
			const userPersonRes = await getUserPerson();

			if (userPersonRes && userPersonRes.apiStatus && userPersonRes.data) {
				Object.assign(pageData.userPerson, userPersonRes.data);
			}
		} catch (error) {
			//TODO handle the exception
		}

		try {
			const couponNumberRes = await getCouponNumber();

			if (couponNumberRes && couponNumberRes.apiStatus && couponNumberRes.data) {
				pageData.userPerson.couponCount = couponNumberRes.data.unuseNum;
			}
		} catch (error) {
			//TODO handle the exception
		}

		try {
			const accountsRes = await getAccounts();
			if (accountsRes.apiStatus && accountsRes.data) pageData.userPerson.bankCount = Object.keys(accountsRes.data).length;
		} catch (error) {
			//TODO handle the exception
		}

		try {
			const savingManageRes = await getSavingManage();
			if (savingManageRes.apiStatus && savingManageRes.data) {
				topUpParam.value = savingManageRes.data;
				storage.set('top_up_param', topUpParam.value);
			}
		} catch (error) {
			//TODO handle the exception
		}
	}

	getPaymentHistory({ ...params }).then((res) => {
		if (res.apiStatus) {
			if (pagePaging.value) pagePaging.value.complete(res.data.records);
		}
	});
}

onShow(() => {
	if (pagePaging.value) pagePaging.value.reload();
});
</script>
<template>
	<app-layout>
		<z-paging
			ref="pagePaging"
			:paging-style="{
				backgroundColor: '#F0F3F7',
				backgroundImage: 'linear-gradient(180deg, #D7FFF7 80%, #F0F3F7)',
				backgroundRepeat: 'no-repeat',
				backgroundSize: '100% 446rpx'
			}"
			v-model="pageData.dataList"
			@query="queryList"
		>
			<template #top>
				<app-navBar back>
					<template #content>
						<view class="w-full flex items-center h-full">
							<view class="text-32 text-#000 font-bold nav-center flex-1">我的钱包</view>
						</view>
					</template>
				</app-navBar>
			</template>
			<view class="px-30">
				<view class="w-full h-300 border-rd-20 mt-16" style="background: linear-gradient(87deg, #00c9a7 34%, #00c9a7)">
					<view class="h-158 pl-31 pr-40 flex justify-between items-center">
						<view class="">
							<view class="text-26 text-#fff font-bold">余额(元)</view>
							<!-- <view class="text-48 text-#fff font-bold mt-20">{{ salesVolumeToStr(divTenThousand(pageData.userPerson.balance)) }}</view> -->
							<view class="text-48 text-#fff font-bold mt-20">{{ divTenThousand(pageData.userPerson.balance) }}</view>
						</view>
						<view class="flex justify-end items-center">
							<view
								@click="route('/pages/my/pages/wallet/withdraw/withdraw', { balance: divTenThousand(pageData.userPerson.balance).toFixed(2) })"
								class="w-130 h-64 border-rd-32 flex justify-center items-center border-solid border-2 box-border border-#95F8E7 text-28 text-#fff font-bold"
							>
								提现
							</view>
							<template v-if="topUpParam.switching">
								<view
									@click="route('/pages/my/pages/wallet/recharge/recharge')"
									class="w-130 h-64 ml-16 border-rd-32 flex justify-center items-center text-28 text-#00B496 font-bold"
									style="background: linear-gradient(-34deg, #ddfff7 0%, #fffafa 100%)"
								>
									充值
								</view>
							</template>
						</view>
					</view>
					<view class="w-full h-1 bg-#fff op-20"></view>
					<view class="w-full h-141 flex justify-around items-center">
						<view class="" @click="route('/pages/my/pages/coupon/coupon')">
							<view class="flex justify-start items-center">
								<view class="text-22 text-#fff">优惠劵</view>
							</view>
							<view class="text-24 text-#fff font-bold text-center mt-18">
								<text class="text-32">{{ salesVolumeToStr(pageData.userPerson.couponCount) }}</text>
							</view>
						</view>

						<view class="" @click="route('/pages/my/pages/integral/integral')">
							<view class="flex justify-start items-center">
								<view class="text-22 text-#fff">积分</view>
							</view>
							<view class="text-24 text-#fff font-bold text-center mt-18">
								<text class="text-32">{{ salesVolumeToStr(pageData.userPerson.integral) }}</text>
							</view>
						</view>

						<view class="" @click="route('/pages/my/pages/account/account')">
							<view class="flex justify-start items-center">
								<view class="text-22 text-#fff">银行卡</view>
							</view>
							<view class="text-24 text-#fff font-bold text-center mt-18">
								<text class="text-32">{{ salesVolumeToStr(pageData.userPerson.bankCount) }}</text>
							</view>
						</view>
					</view>
					<!-- <view class="w-full h-141 flex justify-between items-center">
						<view class="w-344 pl-34">
							<view class="flex justify-start items-center">
								<view class="w-6 h-19 bg-#FFF3F4 border-rd-2 mr-12"></view>
								<view class="text-22 text-#fff">
									今日收入(元)
								</view>
							</view>
							<view class="text-24 text-#fff font-bold ml-17 mt-20">
								￥
								<text class="text-32">
									0
								</text>
							</view>
						</view>
						<view class="w-1 h-79 bg-#fff op-20"></view>
						<view class="w-344 pl-54">
							<view class="flex justify-start items-center">
								<view class="w-6 h-19 bg-#FFF3F4 border-rd-2 mr-12"></view>
								<view class="text-22 text-#fff">
									总计收入(元)
								</view>
							</view>
							<view class="text-24 text-#fff font-bold ml-17 mt-20">
								￥
								<text class="text-32">
									0
								</text>
							</view>
						</view>
					</view> -->
				</view>
				<view class="flex justify-start items-center mt-50">
					<view class="w-6 h-24 bg-#00B698 border-rd-2 mr-14"></view>
					<view class="text-34 text-#000 font-bold">余额明细</view>
				</view>
				<view class="bg-#fff border-rd-16 mt-30 px-30">
					<template v-for="(item, index) in pageData.dataList" :key="index">
						<view class="py-30 border-b-solid border-1 border-b-#EAEDF3">
							<view class="flex justify-between items-center">
								<text class="text-30 text-#222 font-bold">{{ dealTypeCn[item.dealType] }}</text>
								<template v-if="item.changeType === 'INCREASE'">
									<text class="text-30 font-bold text-#00B698">+{{ getPriceInfo(item.money).integer }}.{{ getPriceInfo(item.money).decimalText }}</text>
								</template>
								<template v-else>
									<text class="text-30 font-bold">-{{ getPriceInfo(item.money).integer }}.{{ getPriceInfo(item.money).decimalText }}</text>
								</template>
							</view>
							<view class="text-24 text-#43414188 mt-20">{{ item.createTime }}</view>
						</view>
					</template>
					<!-- <view class="py-30 border-b-solid border-1 border-b-#EAEDF3">
						<view class="flex justify-between items-center">
							<text class="text-30 text-#222 font-bold">充值</text>
							<text class="text-30 font-bold text-#00B698">+500</text>
						</view>
						<view class="text-24 text-#43414188 mt-20">2024-11-24 14:20</view>
					</view>
					<view class="py-30 border-b-solid border-1 border-b-#EAEDF3">
						<view class="flex justify-between items-center">
							<text class="text-30 text-#222 font-bold">提现</text>
							<text class="text-30 font-bold text-#000">-60</text>
						</view>
						<view class="text-24 text-#43414188 mt-20">2024-11-24 14:20</view>
					</view> -->
				</view>
			</view>
		</z-paging>
	</app-layout>
</template>
<style lang="scss" scoped></style>
