<script setup>
import { reactive, ref, getCurrentInstance, computed } from "vue";

import { onLoad, onShow } from "@dcloudio/uni-app";

import { getAccounts, withdrawBalance, getWithdrawBalanceLog } from "@/server/api";

import { route } from "@/common/utils";

import useConvert from "@/common/useConvert";
import { common } from "@/common/images";

const { divTenThousand, salesVolumeToStr, mulTenThousand } = useConvert();

const $props = defineProps({
	balance: {
		type: [Number, String],
		default: "",
	},
});

const pageData = reactive({
	amount: "", // 提现金额

	checkIndex: 0,

	bankTypeData: {
		WECHAT: {
			name: "微信",
			addUrl: "/pages/my/pages/account/bankCardWeiXin/bankCardWeiXin",
			data: {},
		},
		ALIPAY: {
			name: "支付宝",
			addUrl: "/pages/my/pages/account/bankCardAli/bankCardAli",
			data: {},
		},
		BANK_CARD: {
			name: "银行卡",
			addUrl: "/pages/my/pages/account/bankCard/bankCard",
			data: {},
		},
	},

	bankTypeList: [],
	bankTypeIndex: 0,

	productid: "",
	iapOrder: "",
	iap: "",
});

const payPopup = ref(null);
const successModal = ref(null);

const payLoading = ref(false);

const bankTypeInfo = computed(() => {
	if (pageData.bankTypeList.length > 0) {
		return pageData.bankTypeList[pageData.bankTypeIndex];
	} else {
		return null;
	}
});

function loadData() {
	getAccounts().then((res) => {
		if (res.apiStatus) {
			if (typeof res.data === "object") {
				for (const [key, value] of Object.entries(pageData.bankTypeData)) {
					if (res.data[key]) {
						pageData.bankTypeData[key].data = res.data[key];
					} else {
						pageData.bankTypeData[key].data = null;
					}
				}

				let arr = [];

				for (const [key, value] of Object.entries(res.data)) {
					arr.push({
						...value,
						type: key,
					});
				}

				pageData.bankTypeList = arr;
			}
		}
	});
}

onLoad(() => {});

onShow(() => {
	loadData();
});

function openPayPopup() {
	if (!bankTypeInfo.value) {
		route("/pages/my/pages/account/account");
		return;
	}

	pageData.checkIndex = pageData.bankTypeIndex;
	payPopup.value.open();
}

function closePayPopup() {
	payPopup.value.close();
}

function checkPayType() {
	pageData.bankTypeIndex = pageData.checkIndex;
	closePayPopup();
}

async function submit() {
	let amount = mulTenThousand(pageData.amount);
	let type = bankTypeInfo.value.type;

	if (Number(amount) <= 0) {
		return uni.showToast({
			icon: "none",
			title: "请输入金额",
		});
	}

	payLoading.value = true;

	const { code, msg, data, apiStatus } = await withdrawBalance({
		amount,
		type,
	});

	payLoading.value = false;

	if (!apiStatus) return;

	pageData.amount = 0;

	if (successModal.value) successModal.value.open();
}

function lookMoney(state) {
	uni.navigateBack();
}
</script>
<template>
	<app-layout>
		<z-paging
			ref="pagePaging"
			:paging-style="{
				background: 'linear-gradient(180deg, #01C8A7 40%, #F2F4F5 100%)',
				backgroundColor: '#F0F3F7',
				backgroundRepeat: 'no-repeat',
				backgroundSize: '100% 440rpx',
			}">
			<template #top>
				<app-navBar :back="true" :back-icon="common.iconLeftBai">
					<template #content>
						<view class="w-full flex items-center h-full">
							<view class="text-32 text-#fff font-bold nav-center flex-1">提现</view>
						</view>
					</template>
				</app-navBar>
			</template>
			<view class="px-20">
				<view class="bg-#fff border-rd-20 px-46 py-40 mt-24">
					<view class="text-30 text-#000 font-bold">提现金额</view>
					<view class="mt-30">
						<uv-input v-model="pageData.amount" type="digit" border="bottom" fontSize="72rpx" :customInputStyle="{ height: '74rpx' }">
							<template #prefix>
								<text class="text-60 text-[#000000] line-height-60 pt-12 pr-20">¥</text>
							</template>
							<template #suffix>
								<template v-if="balance">
									<text class="text-26 text-[#2398FE]" @click="pageData.amount = Number(balance)">全部提现</text>
								</template>
							</template>
						</uv-input>
					</view>
					<view @click="openPayPopup()" class="flex justify-between items-center mt-55">
						<view class="text-30 text-#000 font-bold">提现至</view>
						<template v-if="bankTypeInfo">
							<template v-if="bankTypeInfo.type === 'WECHAT'">
								<view class="flex justify-end items-center">
									<!-- <app-image class="w-30 h-30 mr-13" :src="pageData.checkType.icon" size="30" mode=""></app-image> -->
									<text class="text-26 text-[#16161F] font-bold mr-10"> 微信{{ bankTypeInfo.account ? `(****${bankTypeInfo.account.slice(bankTypeInfo.account.length - 4)})` : "" }} </text>
									<uv-icon name="arrow-right" color="#9D9E9F" :bold="true" size="12"></uv-icon>
								</view>
							</template>
							<template v-if="bankTypeInfo.type === 'ALIPAY'">
								<view class="flex justify-end items-center">
									<!-- <app-image class="w-30 h-30 mr-13" :src="pageData.checkType.icon" size="30" mode=""></app-image> -->
									<text class="text-26 text-[#16161F] font-bold mr-10"> 支付宝{{ bankTypeInfo.account ? `(****${bankTypeInfo.account.slice(bankTypeInfo.account.length - 4)})` : "" }} </text>
									<uv-icon name="arrow-right" color="#9D9E9F" :bold="true" size="12"></uv-icon>
								</view>
							</template>
							<template v-if="bankTypeInfo.type === 'BANK_CARD'">
								<view class="flex justify-end items-center">
									<!-- <app-image class="w-30 h-30 mr-13" :src="pageData.checkType.icon" size="30" mode=""></app-image> -->
									<text class="text-26 text-[#16161F] font-bold mr-10"> {{ bankTypeInfo.bankName }}{{ bankTypeInfo.cardNo ? `(****${bankTypeInfo.cardNo.slice(bankTypeInfo.cardNo.length - 4)})` : "" }} </text>
									<uv-icon name="arrow-right" color="#9D9E9F" :bold="true" size="12"></uv-icon>
								</view>
							</template>
						</template>
						<template v-else>
							<view class="flex justify-end items-center">
								<text class="text-26 text-[#16161F] font-bold mr-10">请先添加银行卡</text>
								<uv-icon name="arrow-right" color="#9D9E9F" :bold="true" size="12"></uv-icon>
							</view>
						</template>
					</view>
				</view>
			</view>
			<view class="w-full bg-#fff fixed left-0 bottom-0">
				<view class="py-14 px-40 border-t-solid border-1 border-#EFF2F9">
					<uv-button color="#00B496" shape="circle" text="确认提现" customTextStyle="font-size: 30rpx;font-weight: bold;" customStyle="height: 84rpx;" :loading="payLoading" @click="submit"></uv-button>
				</view>
				<app-safeAreaBottom></app-safeAreaBottom>
			</view>

			<uv-popup ref="payPopup" mode="bottom" :round="10" :safeAreaInsetBottom="true">
				<view>
					<view class="h-106 border-b-solid border-1 border-b-#EDEEF0 flex justify-between items-center px-30">
						<uv-icon name="close" color="rgba(0,0,0,0)" :bold="true" size="15"></uv-icon>
						<view class="text-30 text-[#000] font-bold">提现至</view>
						<uv-icon @click="closePayPopup()" name="close" color="#9D9E9F" :bold="true" size="15"></uv-icon>
					</view>
					<view class="px-69 py-40">
						<template v-for="(item, index) in pageData.bankTypeList" :key="index">
							<view @click="pageData.checkIndex = index" class="flex justify-between items-center my-37">
								<view class="flex justify-start items-center">
									<!-- <app-image class="w-38 h-38 mr-24" :src="item.icon" size="38" mode=""></app-image> -->
									<template v-if="item.type === 'WECHAT'">
										<view class="">微信{{ item.account ? `(****${item.account.slice(item.account.length - 4)})` : "" }}</view>
									</template>
									<template v-if="item.type === 'ALIPAY'">
										<view class="">支付宝{{ item.account ? `(****${item.account.slice(item.account.length - 4)})` : "" }}</view>
									</template>
									<template v-if="item.type === 'BANK_CARD'">
										<view class="">{{ item.bankName }}{{ item.cardNo ? `(****${item.cardNo.slice(item.cardNo.length - 4)})` : "" }}</view>
									</template>
								</view>
								<view class="">
									<template v-if="pageData.checkIndex === index">
										<app-image :src="common.iconXuanzeXhuanzhong" size="32" mode=""></app-image>
									</template>
									<template v-else>
										<app-image :src="common.iconXuianzeWeixuanzhong" size="32" mode=""></app-image>
									</template>
								</view>
							</view>
						</template>
					</view>
					<view class="py-14 px-50 border-t-solid border-1 border-#F2F2F2">
						<uv-button @click="checkPayType()" color="#00B496" text="确认" class="w-650 flex-center" shape="circle" custom-style="height: 88rpx;" customTextStyle="font-size: 30rpx; font-weight: bold;color: #fff;"></uv-button>
						<app-safeAreaBottom></app-safeAreaBottom>
					</view>
				</view>
			</uv-popup>

			<uni-popup ref="successModal" type="dialog">
				<uni-popup-dialog type="info" confirmText="确定" title="提示" content="提现申请成功!" :showClose="false" @close="lookMoney(false)" @confirm="lookMoney(true)"></uni-popup-dialog>
			</uni-popup>
		</z-paging>
	</app-layout>
</template>
<style lang="scss" scoped></style>
