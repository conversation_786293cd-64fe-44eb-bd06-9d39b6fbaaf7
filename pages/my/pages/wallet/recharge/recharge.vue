<script setup>
import { reactive, ref, getCurrentInstance, unref } from "vue";

import { onLoad, onReady } from "@dcloudio/uni-app";

import storage from "@/common/storage";

import { rechargeSavingPay, getOrderIsPaySuccess } from "@/server/api";

import useConvert from "@/common/useConvert";

import { ORDERPAYMENT, PAY_TYPE } from "@/common/types/goods";

import { route, canENV } from "@/common/utils";

import { Decimal } from "decimal.js";
import { common, icon } from "@/common/images";

const { divTenThousand, salesVolumeToStr, mulTenThousand } = useConvert();

const pageData = reactive({
	amount: "", // 充值金额
	pay_type: "wechat", // 支付方式

	checkIndex: 0,
	payList: [
		{
			label: "微信",
			icon: icon.iconPayWx,
			key: "WECHAT",
			type: "wechat",
			payment: "wxpay",
		},
		// #ifdef APP || H5
		{
			label: "支付宝",
			icon: icon.iconPayZfb,
			key: "ALIPAY",
			type: "alipay",
			payment: "alipay",
		},
		// #endif
	],
	checkType: {
		label: "微信",
		icon: icon.iconPayWx,
		key: "WECHAT",
		type: "wechat",
		payment: "wxpay",
	},

	ruleIndex: -1,

	productid: "",
	iapOrder: "",
	iap: "",
});

const topUpParam = ref({
	switching: false,
	discountsState: true,
	ruleJson: [],
	id: "",
});

const payPopup = ref(null);
const successModal = ref(null);

const inputRef = ref();

function countDecimalPlaces(num) {
	let numStr = num.toString();
	if (numStr.includes(".")) {
		let decimalPart = numStr.split(".")[1]; // 获取小数部分
		return decimalPart.length;
	} else {
		return 0; // 如果没有小数点，则返回0
	}
}

function changeRule(index) {
	if (pageData.ruleIndex !== index) {
		if (topUpParam.value.discountsState) {
			pageData.amount = "";
		} else {
			pageData.amount = Number(divTenThousand(topUpParam.value.ruleJson[index].ladderMoney).toFixed(2));
		}

		pageData.ruleIndex = index;
	} else {
		pageData.ruleIndex = -1;
	}
}

function inputAmount(e) {
	// if (topUpParam.value.ruleJson.length > 0 && pageData.ruleIndex >= 0) {
	// 	const amount = Number(divTenThousand(topUpParam.value.ruleJson[pageData.ruleIndex].ladderMoney).toFixed(2));
	// 	if (Number(e) !== amount) {
	// 		pageData.ruleIndex = -1;
	// 	}
	// }
	// console.log(countDecimalPlaces(e));
	// if (countDecimalPlaces(e) > 2) {
	// 	pageData.amount = Number(Number(e).toFixed(2));
	// }
}

function inputFormatter(e) {
	if (topUpParam.value.ruleJson.length > 0 && pageData.ruleIndex >= 0) {
		const amount = Number(divTenThousand(topUpParam.value.ruleJson[pageData.ruleIndex].ladderMoney).toFixed(2));
		if (Number(e) !== amount) {
			pageData.ruleIndex = -1;
		}
	}

	if (!e) return e;
	if (countDecimalPlaces(e) > 2) {
		return Number(e).toFixed(2);
	} else {
		return e;
	}
}

function loadData() {
	topUpParam.value = storage.get("top_up_param");
}

function reloadList() {
	// pageData.amount = '';
	// pageData.ruleIndex = -1;
}

onReady(() => {
	if (inputRef.value) inputRef.value.setFormatter(inputFormatter);
});

onLoad(() => {
	loadData();
});

function openPayPopup() {
	const index = pageData.payList.findIndex((elem) => elem.type == pageData.pay_type);
	pageData.checkIndex = index;
	payPopup.value.open();
}

function closePayPopup() {
	payPopup.value.close();
}

function checkPayType() {
	pageData.checkType = pageData.payList[pageData.checkIndex];
	pageData.pay_type = pageData.checkType.type;
	closePayPopup();
}

function submit() {
	let price = "";
	let ruleId = "";

	if (pageData.ruleIndex >= 0) {
		price = topUpParam.value.ruleJson[pageData.ruleIndex].ladderMoney;
		ruleId = topUpParam.value.ruleJson[pageData.ruleIndex].id;
	} else {
		price = mulTenThousand(pageData.amount);
	}

	if (Number(price) <= 0) {
		return uni.showToast({
			icon: "none",
			title: "请输入金额",
		});
	}

	// console.log(pageData.checkType);
	balancePay(price, pageData.checkType.key, pageData.checkType.payment, ruleId);
}

const paySelectRef = ref();
const showPay = ref(false);
const payLoading = ref(false);
const payInfo = reactive({
	price: 0,
	balanceTotalShow: true,
	balanceTotal: 0,
	timeout: 0,
});

const orderNumber = ref("");
const payExtra = ref();
const payFrom = ref(PAY_TYPE.BALANCE);

async function balancePay(totalPrice = "", currentPayType = "", payment = "", ruleId = "") {
	const price = totalPrice;
	const { code, msg, data, apiStatus } = await rechargeSavingPay({
		payType: currentPayType, // 'WECHAT' | 'ALIPAY'
		payAmount: totalPrice, // 支付金额
		ruleId: ruleId, // 储值规则 id
	});
	if (!apiStatus) return;
	canENV(() => {
		console.log(payment, data);
	});
	orderNumber.value = data.outTradeNo;
	requestPayment(payment, data.data);
}

/**
 * uni requestPayment 支付
 * @param {*} data
 */
function requestPayment(provider, data) {
	uni.requestPayment({
		provider,
		orderInfo: data,
		...data,
		success: (res) => {
			canENV(() => {
				console.log("支付成功 -> ", res);
			});
			loopCheckPayStatus();
		},
		fail: (err) => {
			canENV(() => {
				console.log("支付失败 -> ", err);
			});
			closeLoopCheck(false, true);
		},
	});
}

let time = null;

/**
 * 循环检查支付状态
 */
function loopCheckPayStatus() {
	uni.showLoading({
		title: "支付校验中...",
		mask: true,
	});
	let count = 1;
	checkPayStatus(false);
	try {
		//先清除一次定时器
		time && clearInterval(time);
		// 开启轮训
		time = setInterval(() => checkPayStatus(++count > 10), 800);
	} catch (error) {
		uni.hideLoading();
		time && clearInterval(time);
	}
	// payBtnLoading.value = false;
}

/**
 * 定时器循环体
 */
function checkPayStatus(skipLoop) {
	if (skipLoop) {
		return closeLoopCheck(false, true);
	}
	getOrderIsPaySuccess({
		outTradeNo: unref(orderNumber),
	}).then((res) => {
		if (res.apiStatus) {
			closeLoopCheck(true, true);
		}
	});
}

/**
 * 取消轮训检查
 */
function closeLoopCheck(state = false, isPayStatus = false) {
	time && clearInterval(time);
	uni.hideLoading();
	if (isPayStatus) {
		if (state) {
			paySuccess(unref(orderNumber), payFrom.value);
		} else {
			payError(unref(orderNumber), payFrom.value);
		}
	} else {
		payLoading.value = false;
		showPay.value = false;
		// reloadList();
	}
}

async function paySuccess(orderType, payFrom) {
	// uni.hideLoading();
	payLoading.value = false;
	showPay.value = false;
	// uni.showToast({
	// 	icon: 'success',
	// 	title: '支付成功',
	// 	duration: 1000,
	// 	mask: true
	// });

	if (successModal.value) successModal.value.open();

	// reloadList();
}

async function payError(orderType, payFrom) {
	// uni.hideLoading();
	payLoading.value = false;
	showPay.value = false;
	uni.showToast({
		icon: "error",
		title: "支付失败",
		duration: 1500,
		mask: true,
	});

	reloadList();
}

function lookMoney(state) {
	if (state) {
		uni.navigateBack();
	} else {
		pageData.amount = "";
		pageData.ruleIndex = -1;
	}
}
</script>
<template>
	<app-layout>
		<z-paging
			ref="pagePaging"
			:paging-style="{
				background: 'linear-gradient(180deg, #01C8A7 40%, #F2F4F5 100%)',
				backgroundColor: '#F0F3F7',
				backgroundRepeat: 'no-repeat',
				backgroundSize: '100% 440rpx',
			}">
			<template #top>
				<app-navBar :back="true" :back-icon="common.iconLeftBai">
					<template #content>
						<view class="w-full flex items-center h-full">
							<view class="text-32 text-#fff font-bold nav-center flex-1">充值</view>
						</view>
					</template>
				</app-navBar>
			</template>
			<view class="px-20">
				<view class="bg-#fff border-rd-20 px-46 py-40 mt-24">
					<template v-if="topUpParam.ruleJson.length > 0">
						<view class="w-full">
							<view class="text-30 text-#000 font-bold">充值</view>
							<view class="grid grid-cols-3 gap-10 my-20">
								<template v-for="(item, index) in topUpParam.ruleJson" :key="index">
									<view @click="changeRule(index)" class="bg-#F2F4F8 border-rd-8 h-60 text-center flex flex-center mx-0 border-solid border-1 border-#F2F4F8" :style="[pageData.ruleIndex === index && 'background-color: #EFFAF7; border-color: #09C1B1;']">
										<text class="text-26 text-#323232 font-500" :style="[pageData.ruleIndex === index && 'color: #09C1B1; font-weight: bold']"> 充值{{ item.ladderMoney && Number(divTenThousand(item.ladderMoney).toFixed(2)) }}元 </text>
									</view>
								</template>
							</view>
							<template v-if="topUpParam.discountsState && pageData.ruleIndex >= 0">
								<view class="my-20">
									<template v-if="topUpParam.ruleJson[pageData.ruleIndex].presentedMoney || topUpParam.ruleJson[pageData.ruleIndex].presentedGrowthValue">
										<view class="text-#999 text-24">
											<text>充值优惠：</text>
											<text v-if="topUpParam.ruleJson[pageData.ruleIndex].presentedMoney"> 赠送{{ divTenThousand(topUpParam.ruleJson[pageData.ruleIndex].presentedMoney) }}元； </text>
											<text v-if="topUpParam.ruleJson[pageData.ruleIndex].presentedGrowthValue"> 赠送{{ topUpParam.ruleJson[pageData.ruleIndex].presentedGrowthValue || 0 }}成长值 </text>
										</view>
									</template>
								</view>
							</template>
						</view>
					</template>

					<view class="text-30 text-#000 font-bold">充值金额</view>
					<view class="mt-30">
						<uv-input ref="inputRef" v-model="pageData.amount" @input="inputAmount" type="digit" border="bottom" fontSize="72rpx" :customInputStyle="{ height: '74rpx' }">
							<template #prefix>
								<text class="text-60 text-[#000000] line-height-60 pt-12 pr-20">¥</text>
							</template>
						</uv-input>
					</view>
					<template v-if="topUpParam.discountsState">
						<view class="my-20">
							<view class="text-#999 text-24">
								<text>自定义金额不享受充值优惠</text>
							</view>
						</view>
					</template>
					<view @click="openPayPopup()" class="flex justify-between items-center mt-55">
						<view class="text-30 text-#000 font-bold">支付方式</view>
						<view class="flex justify-end items-center">
							<app-image class="w-30 h-30 mr-13" :src="pageData.checkType.icon" size="30" mode=""></app-image>
							<text class="text-26 text-[#16161F] font-bold mr-10">{{ pageData.checkType.label }}</text>
							<uv-icon name="arrow-right" color="#9D9E9F" :bold="true" size="12"></uv-icon>
						</view>
					</view>
				</view>
				<!-- 	<view class="bg-#fff border-rd-20 px-46 py-40 mt-20">
					<view class="text-30 text-[#000] font-bold">充值须知</view>
					<view class="text-26 text-[#616774] mt-30">1.单次充值金额必须大于0.1元，支付宝单次充值最多XX元，支付宝单次充值最多XX元； 2.每日最多充值XX次；</view>
				</view> -->
			</view>
			<view class="w-full bg-#fff fixed left-0 bottom-0">
				<view class="py-14 px-40 border-t-solid border-1 border-#EFF2F9">
					<uv-button :loading="payLoading" color="#00B496" shape="circle" text="确认充值" customTextStyle="font-size: 30rpx;font-weight: bold;" customStyle="height: 84rpx;" @click="submit"></uv-button>
				</view>
				<app-safeAreaBottom></app-safeAreaBottom>
			</view>
			<uv-popup ref="payPopup" mode="bottom" :round="10" :safeAreaInsetBottom="true">
				<view>
					<view class="h-106 border-b-solid border-1 border-b-#EDEEF0 flex justify-between items-center px-30">
						<uv-icon name="close" color="rgba(0,0,0,0)" :bold="true" size="15"></uv-icon>
						<view class="text-30 text-[#000] font-bold">支付方式</view>
						<uv-icon @click="closePayPopup()" name="close" color="#9D9E9F" :bold="true" size="15"></uv-icon>
					</view>
					<view class="px-69 py-40">
						<template v-for="(item, index) in pageData.payList" :key="index">
							<view @click="pageData.checkIndex = index" class="flex justify-between items-center my-37">
								<view class="flex justify-start items-center">
									<app-image class="w-38 h-38 mr-24" :src="item.icon" size="38" mode=""></app-image>
									<view class="">
										{{ item.label }}
									</view>
								</view>
								<view class="">
									<template v-if="pageData.checkIndex === index">
										<app-image :src="common.iconXuanzeXhuanzhong" size="32" mode=""></app-image>
									</template>
									<template v-else>
										<app-image :src="common.iconXuianzeWeixuanzhong" size="32" mode=""></app-image>
									</template>
								</view>
							</view>
						</template>
					</view>
					<view class="py-14 px-50 border-t-solid border-1 border-#F2F2F2">
						<uv-button @click="checkPayType()" color="#00B496" text="确认" class="w-650 flex-center" custom-style="height: 88rpx; border-radius: 44rpx" customTextStyle="font-size: 30rpx; font-weight: bold;color: #fff;"></uv-button>
						<app-safeAreaBottom></app-safeAreaBottom>
					</view>
				</view>
			</uv-popup>
			<uni-popup ref="successModal" type="dialog">
				<uni-popup-dialog type="success" confirmText="查看余额" title="提示" content="充值成功!" @close="lookMoney(false)" @confirm="lookMoney(true)"></uni-popup-dialog>
			</uni-popup>
		</z-paging>
	</app-layout>
</template>
