<template>
	<view class="user-code">
		<app-navBar :back-icon="common.iconLeftBai" back>
			<template #content>
				<view class="w-full h-full flex items-center">
					<view class="text-34 font-bold text-#fff w-full nav-center">会员码</view>
				</view>
			</template>
		</app-navBar>

		<view class="pt-210 px-50">
			<view class="w-full bg-#fff border-rd-20 pb-65">
				<view class="header-box h-75 mt-0 flex flex-center">
					<view class="avatar-box w-150 h-150 bg-#fff border-rd-75 flex flex-center" style="transform: translateY(-37.5rpx)">
						<app-image :src="userStore.userData.avatar" size="138rpx" rd="50%" mode=""></app-image>
					</view>
				</view>

				<view class="w-full flex flex-center mt-25">
					<view class="text-center text-34 text-#101010 font-bold">
						{{ userStore.userData.nickname }}
					</view>
				</view>

				<view class="w-full pt-96 flex flex-center">
					<uv-qrcode
						ref="qrcodeRef"
						canvas-id="qrcode"
						size="378rpx"
						:value="userStore.userData.userId"
						:options="{ areaColor: 'rgba(0, 0, 0, 0)', foregroundColor: '#000' }"
					></uv-qrcode>
				</view>

				<view class="w-full mt-53 text-center">
					<view class="text-#888888 text-26 font-500">该会员卡仅限本人使用</view>
				</view>

				<view class="mt-48 flex flex-center">
					<app-image :src="logo" mode="" size="130"></app-image>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { onLoad } from '@dcloudio/uni-app';

import { ref, reactive } from 'vue';

import { useUserStore } from '@/store';

import { canENV } from "@/common/utils";
import { common, logo } from "@/common/images";

import uvQrcode from '@/uni_modules/uv-qrcode/components/uv-qrcode/uv-qrcode.vue';

const userStore = useUserStore();

const pageData = reactive({});

const qrcodeRef = ref();

function loadData() {}

onLoad(() => {
	loadData();
});
</script>

<style lang="scss" scoped>
.user-code {
	width: 100vw;
	min-height: 100vh;
	background: linear-gradient(61deg, #01c8a7 33%, #14d1d5 100%);
}
</style>
