<script setup>
import { onLoad } from '@dcloudio/uni-app';

import { ref, reactive } from 'vue';

import { useUserStore } from '@/store';

import { canENV, route } from '@/common/utils';

import { addCaregiver, editCaregiver, getCaregiverData, getCaregiverDetails, delCaregiver } from '@/server/api';

import { REGEX_CITIZEN_ID, REGEX_MOBILE } from '@/common/test';

const userStore = useUserStore();

const props = defineProps({
	id: {
		type: [String, Number]
	}
});

const pageData = reactive({
	relationList: [
		[
			/* 直系血亲 */
			{
				text: '配偶关系',
				value: '配偶关系'
			},
			{
				text: '父子关系',
				value: '父子关系'
			},
			{
				text: '父女关系',
				value: '父女关系'
			},
			{
				text: '母子关系',
				value: '母子关系'
			},
			{
				text: '母女关系',
				value: '母女关系'
			},
			{
				text: '祖孙关系',
				value: '祖孙关系'
			},
			{
				text: '外祖孙关系',
				value: '外祖孙关系'
			},
			// { "text": "曾祖孙关系", "value": "曾祖孙关系" },

			/* 拟制血亲 */
			// { "text": "养父子关系", "value": "养父子关系" },
			// { "text": "养父女关系", "value": "养父女关系" },
			// { "text": "养母子关系", "value": "养母子关系" },
			// { "text": "养母女关系", "value": "养母女关系" },
			// { "text": "继父子关系", "value": "继父子关系" },
			// { "text": "继父女关系", "value": "继父女关系" },
			// { "text": "继母子关系", "value": "继母子关系" },
			// { "text": "继母女关系", "value": "继母女关系" },

			/* 直系姻亲 */
			{
				text: '翁婿关系',
				value: '翁婿关系'
			},
			{
				text: '翁媳关系',
				value: '翁媳关系'
			},
			{
				text: '岳母婿关系',
				value: '岳母婿关系'
			},
			{
				text: '婆媳关系',
				value: '婆媳关系'
			},
			// { "text": "岳婿关系", "value": "岳婿关系" },
			// { "text": "公媳关系", "value": "公媳关系" },

			/* 旁系血亲 */
			{
				text: '兄弟关系',
				value: '兄弟关系'
			},
			{
				text: '兄妹关系',
				value: '兄妹关系'
			},
			{
				text: '姐弟关系',
				value: '姐弟关系'
			},
			{
				text: '姐妹关系',
				value: '姐妹关系'
			},
			{
				text: '堂兄弟关系',
				value: '堂兄弟关系'
			},
			{
				text: '堂兄妹关系',
				value: '堂兄妹关系'
			},
			{
				text: '堂姐弟关系',
				value: '堂姐弟关系'
			},
			{
				text: '堂姐妹关系',
				value: '堂姐妹关系'
			},
			{
				text: '表兄弟关系',
				value: '表兄弟关系'
			},
			{
				text: '表兄妹关系',
				value: '表兄妹关系'
			},
			{
				text: '表姐弟关系',
				value: '表姐弟关系'
			},
			{
				text: '表姐妹关系',
				value: '表姐妹关系'
			},
			{
				text: '叔侄关系',
				value: '叔侄关系'
			},
			{
				text: '姑侄关系',
				value: '姑侄关系'
			},

			/* 旁系姻亲 */
			// { "text": "姑婿关系", "value": "姑婿关系" },
			// { "text": "叔嫂关系", "value": "叔嫂关系" },
			// { "text": "舅甥关系", "value": "舅甥关系" },
			// { "text": "姨甥关系", "value": "姨甥关系" },
			// { "text": "妯娌关系", "value": "妯娌关系" },
			// { "text": "连襟关系", "value": "连襟关系" },
			// { "text": "姑嫂关系", "value": "姑嫂关系" },
			// { "text": "婶侄关系", "value": "婶侄关系" },
			// { "text": "甥舅关系", "value": "甥舅关系" }

			/* 其他 */
			{
				text: '其他关系',
				value: '其他关系'
			}
		]
	],
	relationIndex: [[]],
	relationValue: [[]],

	genderList: [
		[
			{
				text: '男',
				value: 1
			},
			{
				text: '女',
				value: 2
			}
		]
	],
	genderIndex: [[]],
	genderValue: [[]],

	ageList: [[]], // 年龄
	ageListIndex: [],
	ageListValue: [],

	heightList: [[]], // 身高
	heightListIndex: [],
	heightListValue: [],

	weightList: [[]], // 体重
	weightindex: [],
	weightValue: [],

	medicalHistoryList: [
		[
			{
				text: '无病史',
				value: '无病史'
			},
			{
				text: '高血压',
				value: '高血压'
			},
			{
				text: '高血脂',
				value: '高血脂'
			},
			{
				text: '糖尿病',
				value: '糖尿病'
			},
			{
				text: '心脑血管疾病',
				value: '心脑血管疾病'
			},
			{
				text: '基础代谢障碍性疾病',
				value: '基础代谢障碍性疾病'
			},
			{
				text: '慢性消耗性疾病',
				value: '慢性消耗性疾病'
			},
			{
				text: '癌症',
				value: '癌症'
			},
			{
				text: '免疫功能低下',
				value: '免疫功能低下'
			},
			{
				text: '帕金森病',
				value: '帕金森病'
			},
			{
				text: '老年痴呆',
				value: '老年痴呆'
			},
			{
				text: '其他',
				value: '其他'
			}
		]
	],
	medicalHistoryIndex: [[]],
	medicalHistoryValue: [[]]
});

const formData = reactive({
	careName: '', // 姓名

	relationText: '配偶关系', // 关系
	relation: '配偶关系',

	idCard: '', // 身份证号

	genderText: '男', // 性别
	gender: 1,

	height: '', // 身高
	heightText: '',

	weight: '', // 体重
	weightText: '',

	age: '', // 年龄,
	ageText: '',

	medicalHistory: '无病史', // 病史
	medicalHistoryRemark: '', // 其他病史

	caredMobile: '' // 联系方式
});

const formRules = {
	careName: [
		{
			required: true,
			message: '请输入名称',
			trigger: 'blur'
		}
	],
	relation: [
		{
			required: true,
			message: '请选择与被照护人的亲属关系',
			trigger: 'blur'
		}
	],
	idCard: [
		{
			required: true,
			message: '请填写被照护人的身份证号',
			trigger: 'blur'
		},
		{
			validator: (rule, value, callback) => {
				if (!REGEX_CITIZEN_ID(value)) {
					callback(new Error('请输入正确的身份证号'));
				} else {
					callback();
				}
			},
			trigger: 'blur'
		}
	],
	caredMobile: [
		{
			required: true,
			message: '请输入手机号',
			trigger: ['blur']
		},
		{
			validator: (rule, value, callback) => {
				if (!REGEX_MOBILE(value)) {
					callback(new Error('请输入正确的手机号'));
				} else {
					callback();
				}
			},
			trigger: 'blur'
		}
	],
	gender: [
		// 	{
		// 	required: true,
		// 	message: '请选择被照护人的性别',
		// 	trigger: 'blur',
		// },
	],
	age: [],
	height: [],
	weight: []
};

const pageFormRef = ref();

function calculateAge(idCard) {
	// 检查身份证号是否为18位
	if (idCard.length !== 18) {
		return {
			status: false,
			age: 0,
			gender: 0,
			year: '',
			month: '',
			day: ''
		};
	}

	// 提取出生日期部分（YYYYMMDD）
	const birthDateStr = idCard.substring(6, 14);
	const birthDate = new Date(birthDateStr.substring(0, 4), birthDateStr.substring(4, 6) - 1, birthDateStr.substring(6, 8));
	const currentDate = new Date();

	// 计算年龄
	let age = currentDate.getFullYear() - birthDate.getFullYear();
	const m = currentDate.getMonth() - birthDate.getMonth();
	if (m < 0 || (m === 0 && currentDate.getDate() < birthDate.getDate())) {
		age -= 1; // 如果当前月份小于出生月份，或者月份相同但当前日小于出生日，则年龄减1
	}

	// 确定性别
	const gender = idCard.charAt(16) % 2 === 0 ? 0 : 1;

	return {
		status: true,
		age,
		gender,
		year: birthDateStr.substring(0, 4),
		month: birthDateStr.substring(4, 6),
		day: birthDateStr.substring(6, 8)
	};
}

function changeIdCard(e) {
	const cardInfo = calculateAge(e);
	if (cardInfo.status) {
		formData.gender = [2, 1][cardInfo.gender];

		formData.age = `${cardInfo.age}`;
		formData.ageText = `${cardInfo.age}岁`;
	}
}

async function loadData() {
	// 年龄
	for (let i = 10; i <= 80; i++) {
		pageData.ageList[0].push({
			text: `${i}岁`,
			value: `${i}`
		});
	}

	// 身高
	for (let i = 130; i <= 200; i++) {
		pageData.heightList[0].push({
			text: `${i}cm`,
			value: `${i}`
		});
	}

	// 体重
	for (let i = 40; i <= 100; i++) {
		pageData.weightList[0].push({
			text: `${i}kg`,
			value: `${i}`
		});
	}

	if (props.id) {
		uni.setNavigationBarTitle({
			title: '修改被照护人'
		});
		await getItemDetails();
	}
}

async function getItemDetails() {
	try {
		const res = await getCaregiverData({
			userId: userStore.userData.userId
		});

		// const res = await getCaregiverDetails({
		// 	userId: userStore.userData.userId,
		// 	id: props.id
		// })

		if (res.apiStatus) {
			const list = res.data;
			const findIndex = list.findIndex((i) => i.id === props.id);

			if (findIndex >= 0) {
				const item = list[findIndex];

				for (let key of Object.keys(formData)) {
					if (typeof item[key] !== 'undefined') {
						formData[key] = item[key];

						// 身高
						if (key === 'height') {
							const valStr = String(formData[key]);
							const findIndex = pageData.heightList[0].findIndex((i) => i.value === valStr);
							if (findIndex >= 0) {
								const item = pageData.heightList[0][findIndex];
								pageData.heightListIndex.push(findIndex);
								pageData.heightListValue.push(item);
								formData.height = valStr;
								formData.heightText = item.text;
							}
						}

						// 体重
						if (key === 'weight') {
							const valStr = String(formData[key]);
							const findIndex = pageData.weightList[0].findIndex((i) => i.value === valStr);
							if (findIndex >= 0) {
								const item = pageData.weightList[0][findIndex];
								pageData.weightindex.push(findIndex);
								pageData.weightValue.push(item);
								formData.weight = valStr;
								formData.weightText = item.text;
							}
						}

						// 年龄
						if (key === 'age') {
							const valStr = String(formData[key]);
							const findIndex = pageData.ageList[0].findIndex((i) => i.value === valStr);
							if (findIndex >= 0) {
								const item = pageData.ageList[0][findIndex];
								pageData.ageListIndex.push(findIndex);
								pageData.ageListValue.push(item);
								formData.age = valStr;
								formData.ageText = item.text;
							}
						}
					}
				}
			}
		}
	} catch (error) {}
}

function changeMedicalHistory(item, index) {
	if (formData.medicalHistory !== item.value) {
		formData.medicalHistory = item.value;
		formData.medicalHistoryRemark = '';
	}
}

const delModal = ref();

function del(verify = false) {
	if (!verify) {
		delModal.value.open();
		return;
	}
	delCaregiver({
		userId: userStore.userData.userId,
		id: props.id,
		...formData
	}).then((res) => {
		if (res.apiStatus) {
			route({
				type: 'back'
			});
		}
	});
}

function submit() {
	if (pageFormRef.value) {
		canENV(() => {
			console.log(formData);
		});
		if (userStore.checkLogin) {
			pageFormRef.value.validate().then((res) => {
				if (props.id) {
					editCaregiver({
						userId: userStore.userData.userId,
						id: props.id,
						...formData
					}).then((res) => {
						if (res.apiStatus) {
							route({
								type: 'back'
							});
						}
					});
				} else {
					addCaregiver({
						userId: userStore.userData.userId,
						...formData
					}).then((res) => {
						if (res.apiStatus) {
							route({
								type: 'back'
							});
						}
					});
				}
			});
		} else {
			uni.showToast({
				icon: 'fail',
				title: '请先登录'
			});
		}
	}
}

onLoad(() => {
	loadData();
});

const formStyle = ref({
	labelStyle: {
		fontSize: '30rpx',
		fontWeight: 'bold'
	},
	inputStyle: {
		height: '98rpx',
		padding: '0rpx 0rpx',
		borderBottom: `solid 1rpx #E9EDF1`
	},
	labelStyle2: {
		fontSize: '26rpx',
		fontWeight: 'bold',
		color: '#1152D7'
	},
	inputStyle2: {
		height: '88rpx',
		borderRadius: '10rpx',
		background: '#fff',
		padding: '0rpx 30rpx'
	},
	inputStyle3: {
		borderRadius: '16rpx',
		background: '#F2F6FB',
		padding: '25rpx 30rpx'
	},
	inputStyle4: {
		borderRadius: '16rpx',
		background: '#fff',
		padding: '25rpx 30rpx'
	},
	inputStyle5: {
		height: '105rpx',
		borderRadius: '16rpx',
		background: '#F2F6FB',
		padding: '0rpx 30rpx'
	},
	labelStyle6: {
		fontSize: '28rpx',
		fontWeight: '500',
		color: '#1A1A1B'
	},
	inputStyle6: {
		fontWeight: 'bold',
		fontSize: '28rpx'
	},
	labelStyle7: {
		fontSize: '28rpx',
		fontWeight: '500',
		color: '#1A1A1B'
	},
	inputStyle7: {
		fontWeight: '500',
		fontSize: '30rpx',
		padding: 0
	},
	inputStyle8: {
		// borderRadius: '16rpx',
		background: '#F3F4F7',
		padding: '25rpx 30rpx',
		borderRadius: '53rpx'
	},
	inputPlaceholderStyle: `font-size: 30rpx; font-weight: 500; color: #AEAEB4;`,
	inputPlaceholderStyle3: `font-size: 26rpx; font-weight: 500; color: #B1B6BC;`,
	inputPlaceholderStyle4: `font-size: 30rpx; font-weight: 500; color: #B1B6BC;`,
	inputPlaceholderStyle6: `font-size: 28rpx; font-weight: 500; color: #939398;`,
	inputPlaceholderStyle7: `font-size: 30rpx; font-weight: 500; color: #93939C;`
});
</script>

<template>
	<view class="add-caregiver pb-150">
		<view class="w-full py-30 px-20">
			<view class="w-full border-rd-20 bg-#FFFFFF px-30">
				<uv-form :model="formData" :rules="formRules" :labelStyle="formStyle.labelStyle6" label-width="200rpx" labelPosition="left" errorType="toast" ref="pageFormRef">
					<!-- 姓名 -->
					<uv-form-item label="姓名" prop="careName" class="w-full" :customStyle="{ padding: '42rpx 0' }" required borderBottom>
						<uv-input
							v-model="formData.careName"
							placeholder="请输入姓名"
							:placeholderStyle="formStyle.inputPlaceholderStyle6"
							:customStyle="{ ...formStyle.inputStyle6 }"
							fontSize="28rpx"
							border="none"
							type="text"
							inputAlign="right"
						></uv-input>
					</uv-form-item>

					<!-- 亲属关系 -->
					<uv-form-item label="亲属关系" prop="relation" class="w-full" :customStyle="{ padding: '42rpx 0' }" required borderBottom>
						<page-uv-picker
							keyName="text"
							:columns="pageData.relationList"
							v-model:indexs="pageData.relationIndex"
							v-model:values="pageData.relationValue"
							@confirm="
								({ value }) => {
									formData.relationText = value[0].text;
									formData.relation = value[0].value;
								}
							"
						>
							<uv-input
								v-model="formData.relationText"
								placeholder="请选择与被照护人的亲属关系"
								:placeholderStyle="formStyle.inputPlaceholderStyle6"
								:customStyle="formStyle.inputStyle6"
								fontSize="28rpx"
								border="none"
								type="text"
								inputAlign="right"
								readonly
							></uv-input>
						</page-uv-picker>

						<template #right>
							<uv-icon name="arrow-right"></uv-icon>
						</template>
					</uv-form-item>

					<!-- 联系电话 -->
					<uv-form-item label="联系电话" prop="caredMobile" class="w-full" :customStyle="{ padding: '42rpx 0' }" required borderBottom>
						<uv-input
							v-model="formData.caredMobile"
							placeholder="请填写被照护人的联系电话"
							:placeholderStyle="formStyle.inputPlaceholderStyle6"
							:customStyle="{ ...formStyle.inputStyle6 }"
							fontSize="28rpx"
							border="none"
							type="number"
							inputAlign="right"
						></uv-input>
					</uv-form-item>

					<!-- 身份证号 -->
					<uv-form-item label="身份证号" prop="idCard" class="w-full" :customStyle="{ padding: '42rpx 0' }" required borderBottom>
						<uv-input
							v-model="formData.idCard"
							placeholder="请填写被照护人的身份证号"
							:placeholderStyle="formStyle.inputPlaceholderStyle6"
							:customStyle="{ ...formStyle.inputStyle6 }"
							fontSize="28rpx"
							border="none"
							type="idcard"
							inputAlign="right"
							@change="changeIdCard"
						></uv-input>
					</uv-form-item>

					<!-- <uv-form-item label="性别" prop="gender" class="w-full" :customStyle="{ padding: '42rpx 0' }" required
						borderBottom>
						<page-uv-picker keyName="text" :columns="pageData.genderList" v-model:indexs="pageData.genderIndex"
							v-model:values="pageData.genderValue" @confirm="
					    ({ value }) => {
					      formData.genderText = value[0].text;
					      formData.gender = value[0].value;
					    }
					  ">
							<uv-input v-model="formData.genderText" placeholder="请选择"
								:placeholderStyle="formStyle.inputPlaceholderStyle6" :customStyle="formStyle.inputStyle6"
								fontSize="28rpx" border="none" type="text" inputAlign="right" readonly>
							</uv-input>
						</page-uv-picker>

						<template #right>
							<uv-icon name="arrow-right"></uv-icon>
						</template>
					</uv-form-item> -->
					<uv-form-item label="性别" prop="gender" class="w-full" :customStyle="{ padding: '42rpx 0' }" required borderBottom>
						<!-- <view class="flex items-center justify-end"> -->
						<uv-radio-group v-model="formData.gender" activeColor="#09C1B1" iconSize="34rpx" labelSize="30rpx">
							<view class="flex-1 flex items-center justify-end">
								<template v-for="(item, index) in pageData.genderList[0]" :key="index">
									<uv-radio :customStyle="{ margin: '0rpx 16rpx' }" :label="item.text" :name="item.value"></uv-radio>
								</template>
							</view>
						</uv-radio-group>
						<!-- </view> -->
					</uv-form-item>

					<uv-form-item label="年龄" prop="age" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
						<page-uv-picker
							keyName="text"
							:columns="pageData.ageList"
							v-model:indexs="pageData.ageListIndex"
							v-model:values="pageData.ageListValue"
							@confirm="
								({ value }) => {
									formData.ageText = value[0].text;
									formData.age = value[0].value;
								}
							"
						>
							<uv-input
								v-model="formData.ageText"
								placeholder="请选择被照护人年龄"
								:placeholderStyle="formStyle.inputPlaceholderStyle6"
								:customStyle="formStyle.inputStyle6"
								fontSize="28rpx"
								border="none"
								type="text"
								inputAlign="right"
								readonly
							></uv-input>
						</page-uv-picker>
						<template #right>
							<uv-icon name="arrow-right"></uv-icon>
						</template>
					</uv-form-item>

					<uv-form-item label="身高(cm)" prop="height" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
						<page-uv-picker
							keyName="text"
							:columns="pageData.heightList"
							v-model:indexs="pageData.heightIndex"
							v-model:values="pageData.heightListValue"
							@confirm="
								({ value }) => {
									formData.heightText = value[0].text;
									formData.height = value[0].value;
								}
							"
						>
							<uv-input
								v-model="formData.heightText"
								placeholder="请选择身高"
								:placeholderStyle="formStyle.inputPlaceholderStyle6"
								:customStyle="formStyle.inputStyle6"
								fontSize="28rpx"
								border="none"
								type="text"
								inputAlign="right"
								readonly
							></uv-input>
						</page-uv-picker>
						<template #right>
							<uv-icon name="arrow-right"></uv-icon>
						</template>
					</uv-form-item>

					<uv-form-item label="体重(kg)" prop="weight" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
						<page-uv-picker
							keyName="text"
							:columns="pageData.weightList"
							v-model:indexs="pageData.weightindex"
							v-model:values="pageData.weightValue"
							@confirm="
								({ value }) => {
									formData.weightText = value[0].text;
									formData.weight = value[0].value;
								}
							"
						>
							<uv-input
								v-model="formData.weightText"
								placeholder="请选择体重"
								:placeholderStyle="formStyle.inputPlaceholderStyle6"
								:customStyle="formStyle.inputStyle6"
								fontSize="28rpx"
								border="none"
								type="text"
								inputAlign="right"
								readonly
							></uv-input>
						</page-uv-picker>
						<template #right>
							<uv-icon name="arrow-right"></uv-icon>
						</template>
					</uv-form-item>

					<uv-form-item label="病史" prop="medicalHistory" class="w-full" :customStyle="{ padding: '42rpx 0' }" labelPosition="top">
						<view class="">
							<view class="w-full flex flex-wrap justify-between">
								<template v-for="(item, index) in pageData.medicalHistoryList[0]" :key="index">
									<view
										class="w-304 h-70 border-rd-70 flex flex-center text-28 font-500 mt-30 ac-op"
										:class="[formData.medicalHistory === item.value ? 'bg-#E8FAF3 text-#00B698' : 'bg-#F2F6F9 text-#616161']"
										@click="changeMedicalHistory(item, index)"
									>
										{{ item.text }}
									</view>
								</template>
							</view>
							<view class="h-80 mt-20 w-full px-30 border-rd-80 bg-#F2F6F9 flex items-center" v-if="formData.medicalHistory === '其他'">
								<uv-input
									v-model="formData.medicalHistoryRemark"
									placeholder="请输入病史"
									:placeholderStyle="formStyle.inputPlaceholderStyle6"
									:customStyle="{ ...formStyle.inputStyle6 }"
									fontSize="28rpx"
									border="none"
									type="text"
									inputAlign="left"
								></uv-input>
							</view>
						</view>
					</uv-form-item>
				</uv-form>
			</view>
		</view>
		<view class="w-full h-142 px-30 pt-14 bg-#fff fixed left-0 bottom-0 border-t-solid border-t-#EDEEF0 flex items-center">
			<view class="flex-1 mr-20" v-if="id">
				<uv-button
					@click="del()"
					type="error"
					text="删除"
					class="h-88"
					custom-style="border-radius: 44rpx;"
					customTextStyle="font-size: 30rpx; color: #fff; font-weight: bold;"
				></uv-button>
			</view>
			<view class="flex-1">
				<uv-button
					@click="submit()"
					color="#00B496"
					:text="id ? '修改' : '确认'"
					class="flex-1 h-88"
					custom-style="border-radius: 44rpx;"
					customTextStyle="font-size: 30rpx; color: #fff; font-weight: bold;"
				></uv-button>
			</view>
		</view>

		<my-uv-modal
			ref="delModal"
			confirmText="删除"
			:confirmColor="'#38B597'"
			:lineColor="'#F2F6FB'"
			cancelText="取消"
			:duration="200"
			width="580rpx"
			showCancelButton
			@confirm="del(true)"
			:asyncClose="true"
			:closeOnClickOverlay="false"
		>
			<view class="flex-center h-100">
				<view class="text-34 font-bold">确定删除吗？</view>
			</view>
		</my-uv-modal>
	</view>
</template>

<style lang="scss" scoped>
.add-caregiver {
	min-height: calc(100vh - var(--window-top) - var(--window-bottom));
	background-color: #f1f3f7;
}
</style>