<script setup>
import { ref, reactive, getCurrentInstance, onMounted } from "vue";

import { onLoad, onShow, onReady } from "@dcloudio/uni-app";

import { useUserStore } from "@/store";

import { route } from "@/common/utils";

import { getCaregiverData } from "@/server/api";

import { deepClone, sleep } from "@/uni_modules/uv-ui-tools/libs/function/index.js";
import { common } from "@/common/images";

const userStore = useUserStore();

const paging = ref(null);

const $props = defineProps({
	select: {
		type: [String, Number],
		default: "0",
	},
});

let instance = null;
let eventChannel = null;

const pageData = reactive({
	dataList: [],
});

async function clickItem(item) {
	if (Number($props.select) === 1) {
		if (eventChannel) {
			eventChannel.emit("chooseCaregiver", item);
			await sleep(100);
			uni.navigateBack();
		}
	}
}

function queryList(pageNo, pageSize) {
	const params = {
		pageNo: pageNo,
		pageSize: pageSize,
	};

	if (userStore.checkLogin) {
		getCaregiverData({
			userId: userStore.userData.userId,
		})
			.then((res) => {
				if (res.apiStatus) {
					paging.value.complete(res.data);
				}
			})
			.catch((err) => {
				paging.value.complete([]);
			});
	} else {
		paging.value.complete([]);
	}
}

onShow(() => {
	if (paging.value) paging.value.reload();
});

function submit(id = "") {
	route("/pages/my/pages/caregiver/addCaregiver/addCaregiver", {
		id,
	});
}

onReady(() => {
	instance = getCurrentInstance().proxy;
	eventChannel = instance.getOpenerEventChannel();
});
</script>
<template>
	<app-layout>
		<z-paging ref="paging" bg-color="#F0F3F7" v-model="pageData.dataList" @query="queryList">
			<template #top>
				<!-- <app-navBar bgColor="#fff" back rightPlaceholderSize="70rpx">
					<template #content>
						<view class="w-full h-full flex justify-between items-center px-0">
							<view class="flex-1 nav-center">
								<text class="text-32 text-#000 font-bold">被照护人</text>
							</view>
						</view>
					</template>
					<template #right>
						<view class="flex flex-center">
							<text class="text-30 font-500">管理</text>
						</view>
					</template>
				</app-navBar> -->
			</template>
			<view class="caregiver py-30 px-30" v-if="pageData.dataList.length > 0">
				<template v-for="(item, index) in pageData.dataList" :key="index">
					<view class="w-full bg-#fff border-rd-20 py-32 px-31 mb-20">
						<view class="w-full flex items-center justify-between" @click="submit(item.id)">
							<view class="flex items-center">
								<view class="text-32 font-bold">
									{{ item.careName }}
								</view>
								<view class="w-1 h-22 bg-#D7D7D7 mx-15"></view>
								<view class="text-28 text-#292B2B font-500">{{ ["", "男", "女"][item.gender] }} {{ item.age && `${item.age}岁` }}</view>
							</view>

							<view class="">
								<app-image :src="common.iconRightHui" size="20" mode=""></app-image>
							</view>
						</view>
						<view class="mt-28 border-rd-20 bg-#F0FCFC py-29 px-36" @click="clickItem(item)">
							<view class="flex items-center">
								<view class="mr-12">
									<app-image src="@/pages/my/static/icon_beizhaohurenguanxi.png" size="30" mode=""></app-image>
								</view>
								<view class="text-28 text-#222 font-500">亲属关系：{{ item.relation }}</view>
							</view>
							<view class="flex items-center mt-30">
								<view class="mr-12">
									<app-image src="@/pages/my/static/icon_bingshi.png" size="30" mode=""></app-image>
								</view>
								<view class="text-28 text-#222 font-500"> 病史：{{ item.medicalHistory !== "其他" ? item.medicalHistory : item.medicalHistoryRemark ? item.medicalHistoryRemark : item.medicalHistory }} </view>
							</view>
						</view>
					</view>
				</template>
			</view>

			<template #bottom>
				<view class="w-full py-15 px-30 border-#EDEEF0 border-top-solid border-1 bg-#fff" v-if="userStore.checkLogin">
					<uv-button @click="submit()" color="#00B496" text="新增被照护人" class="w-690 h-84" custom-style="border-radius: 44rpx;" customTextStyle="font-size: 30rpx; color: #fff; font-weight: bold;"></uv-button>
				</view>
			</template>

			<template #empty>
				<view class="w-full h-full flex flex-center flex-col bg-#fff">
					<app-image src="@/pages/my/static/icon_kong_beizhaohuren.png" size="340" mode=""></app-image>
					<view class="w-full text-center text-#888888 text-28 font-500">暂无被照护人信息</view>
				</view>
			</template>
		</z-paging>
	</app-layout>
</template>
<style lang="scss" scoped>
.caregiver {
}
</style>
