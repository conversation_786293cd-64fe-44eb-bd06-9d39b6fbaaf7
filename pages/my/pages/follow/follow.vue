<script setup>
import { onLoad } from "@dcloudio/uni-app";

import { ref, reactive } from "vue";

import { getConcernListFromMine, cancelAttentionAndAttention } from "@/server/api";

import { useUserStore } from "@/store";

import { canENV, salesVolumeToStr, goShopHome } from "@/common/utils";
import { common } from "@/common/images";

const userStore = useUserStore();

const paging = ref(null);

const pageData = reactive({
	dataList: [],
});

async function changeShopAttention(item) {
	uni.showModal({
		title: "提示",
		content: "确认要取消关注吗？",
		success: (res) => {
			if (res.confirm) {
				cancelAttentionAndAttention({
					name: item.shopName,
					shopId: item.shopId,
					shopLogo: item.logo,
					isFollow: false,
				}).then((res) => {
					if (res.apiStatus) {
						uni.showToast({
							icon: "none",
							title: `取消成功`,
						});
						if (paging.value) paging.value.reload();
					}
				});
			}
		},
	});
}

function queryList(pageNo, pageSize) {
	const params = {
		current: pageNo,
		pageNo: pageNo,
		pageSize: pageSize,
		status: "ALL_SHOP",
	};

	getConcernListFromMine(params)
		.then((res) => {
			if (res.apiStatus) {
				paging.value.complete(res.data.records);
			} else {
				paging.value.complete(false);
			}
		})
		.catch((err) => {
			paging.value.complete(false);
		});
}

function loadData() {}

onLoad(() => {
	loadData();
});
</script>

<template>
	<view class="follow">
		<app-layout>
			<z-paging ref="paging" bg-color="#fff" v-model="pageData.dataList" @query="queryList">
				<template #top>
					<app-navBar bgColor="#fff" back rightPlaceholderSize="70rpx">
						<template #content>
							<view class="w-full h-full flex justify-between items-center px-0">
								<view class="flex-1 nav-center">
									<text class="text-32 text-#000 font-bold">关注店铺</text>
								</view>
							</view>
						</template>
						<template #right></template>
					</app-navBar>
				</template>
				<view class="w-full h-14 bg-#F0F3F7"></view>
				<view class="w-full">
					<template v-for="(item, index) in pageData.dataList" :key="index">
						<view class="px-30 py-30 flex">
							<view class="w-100 h-100 border-rd-100 border-solid border-#F5F5F5 border-1 mr-28">
								<app-image :src="item.logo" size="98" rd="50%"></app-image>
							</view>

							<view class="flex-1 flex items-center justify-between">
								<view class="flex-1">
									<view class="text-32 font-bold">{{ item.shopName }}</view>
									<view class="mt-22 text-#7D7D7D text-24 font-500">{{ salesVolumeToStr(item.numberFollowers) }}粉丝</view>
								</view>
								<view class="flex items-center">
									<view class="w-110 h-56 border-2 border-solid border-#66C5B5 flex flex-center ac-op border-rd-56" @click="goShopHome(item.shopId)">
										<view class="text-24 text-#00B496 font-500">进店</view>
									</view>

									<view class="ml-22 ac-op">
										<app-image :src="common.iconMenuHui" size="26rpx" mode="" @click="changeShopAttention(item)"></app-image>
									</view>
								</view>
							</view>
						</view>
					</template>
				</view>
			</z-paging>
		</app-layout>
	</view>
</template>

<style lang="scss" scoped>
.follow {
}
</style>
