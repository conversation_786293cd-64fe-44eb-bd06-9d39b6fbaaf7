<script setup>
import { ref, reactive, getCurrentInstance } from "vue";

import { getCoupon<PERSON>ist, consumerCollectCoupon } from "@/server/api";
import { common } from "@/common/images";

const { ctx } = getCurrentInstance();

const paging = ref(null);

const pageData = reactive({
	activeTab: 0,
	sort: 1,
	dataList: [],

	value: 1,
});

const checkProductValue = ref([]);

function changeType(e) {
	if (pageData.activeTab === e) return;
	pageData.activeTab = e;
	if (paging.value) paging.value.reload();
}

function changeSort(e) {
	pageData.sort = e;
}

function onReceiveClick(type, coupon) {
	consumerCollectCoupon(
		{
			shopId: coupon.shopId,
			couponId: coupon.id,
		},
		{
			custom: {
				loading: {
					awaitTime: 500,
				},
			},
		}
	).then((res) => {
		if (res.apiStatus) {
			uni.showToast({
				icon: "none",
				title: "领取成功",
			});
			if (paging.value) paging.value.reload();
		}
	});
}

function queryList(pageNo, pageSize) {
	const params = {
		pageNo: pageNo,
		current: pageNo,
		pageSize: pageSize,
		isPlatform: true,
		status: "UNCLAIMED",
		serveGoods: pageData.activeTab,
	};

	getCouponList({ ...params }).then((res) => {
		if (res.apiStatus) {
			if (paging.value) paging.value.complete(res.data.records);
		}
	});
}
</script>
<template>
	<app-layout>
		<z-paging ref="paging" bg-color="rgba(255,255,255,0)" v-model="pageData.dataList" @query="queryList">
			<template #top>
				<app-navBar bg-color="rgba(255,255,255,0)" back rightPlaceholderSize="70rpx" :backIcon="common.iconLeftBai" :fixed="true" :fixed-placeholder="false"></app-navBar>
				<view class="w-full">
					<app-image src="@/pages/my/static/bg_lingquanzhongxin_header.png" width="750" height="476" mode=""></app-image>
				</view>
				<view class="w-full pt-38 pb-20 bg-#fff relative" style="border-radius: 20rpx 20rpx 0rpx 0rpx; margin-top: -42rpx">
					<view class="text-36 font-bold px-40">热门商家劵</view>
					<view class="flex px-30 pt-20 justify-between">
						<view @click="changeType(0)" class="bg-#F2F4F8 border-rd-8 flex-1 h-60 text-center flex flex-center mx-10 border-solid border-1 border-#F2F4F8" :style="[pageData.activeTab == 0 && 'background-color: #FFEAEA; border-color: #FFCECE;']">
							<text class="text-26 text-#323232 font-500" :style="[pageData.activeTab == 0 && 'color: #FF3838; font-weight: bold']">商品券</text>
						</view>
						<view @click="changeType(1)" class="bg-#F2F4F8 border-rd-8 flex-1 h-60 text-center flex flex-center mx-10 border-solid border-1 border-#F2F4F8" :style="[pageData.activeTab == 1 && 'background-color: #FFEAEA; border-color: #FFCECE;']">
							<text class="text-26 text-#323232 font-500" :style="[pageData.activeTab == 1 && 'color: #FF3838; font-weight: bold']">服务券</text>
						</view>
						<!-- <view
							@click="changeType(2)"
							class="bg-#F2F4F8 border-rd-8 flex-1 h-60 text-center flex flex-center mx-10 border-solid border-1 border-#F2F4F8"
							:style="[pageData.activeTab == 2 && 'background-color: #FFEAEA; border-color: #FFCECE;']"
						>
							<text class="text-26 text-#323232 font-500" :style="[pageData.activeTab == 2 && 'color: #FF3838; font-weight: bold']">陪诊</text>
						</view> -->
						<!-- <view
							@click="changeType(3)"
							class="bg-#F2F4F8 border-rd-8 flex-1 h-60 text-center flex flex-center mx-10 border-solid border-1 border-#F2F4F8"
							:style="[pageData.activeTab == 3 && 'background-color: #FFEAEA; border-color: #FFCECE;']"
						>
							<text class="text-26 text-#323232 font-500" :style="[pageData.activeTab == 3 && 'color: #FF3838; font-weight: bold']">家政</text>
						</view> -->
					</view>
				</view>
			</template>
			<view class="evaluation py-20 pt-30">
				<template v-for="(item, index) in pageData.dataList" :key="index">
					<view class="px-40 mb-20">
						<page-coupon-card-item :item="item" :index="index" :type="1" @receiveClick="onReceiveClick($event, item)" couponCenter></page-coupon-card-item>
					</view>
				</template>
			</view>
		</z-paging>
	</app-layout>
</template>
<style lang="scss" scoped>
.evaluation {
}
</style>
