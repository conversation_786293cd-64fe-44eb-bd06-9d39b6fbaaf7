<script setup>
import { onLoad } from "@dcloudio/uni-app";

import { ref, reactive } from "vue";

import { useUserStore } from "@/store";

import { canENV, route } from "@/common/utils";

import { addCaregiver, editCaregiver, getCaregiverData, getCaregiverDetails, delCaregiver } from "@/server/api";

import { REGEX_CITIZEN_ID, REGEX_MOBILE } from "@/common/test";

import { locToAddress, addressToLocItem } from "@/pages/my/common/amap";

import { getAddressDetails, addAddress, editAddress, delAddress } from "@/server/api";
import { common } from "@/common/images";

const userStore = useUserStore();

const props = defineProps({
	id: {
		type: [String, Number],
		default: "",
	},
});

const pageData = reactive({
	recognizeText: "",
	recognizeLoading: false,
});

const formData = reactive({
	name: "",
	mobile: "",
	// 详细地址不包含省市区
	address: "",
	// 物理定位
	location: {
		type: "Point",
		// 坐标
		coordinates: [],
	},
	// 省市区 数组
	area: [],
	isDefault: false,
	fakeAddress: "", // 假的地址，用于展示门牌号等信息(不可包含字符串: '~' )
	// 地址类型
	serverType: 1, // 0->收货地址 1->服务地址
});

const formRules = {
	name: [
		{
			required: true,
			message: "请输入名称",
			trigger: ["blur"],
		},
	],
	mobile: [
		{
			required: true,
			message: "请输入手机号",
			trigger: ["blur"],
		},
		{
			validator: (rule, value, callback) => {
				if (!REGEX_MOBILE(value)) {
					callback(new Error("请输入正确的手机号"));
				} else {
					callback();
				}
			},
			trigger: "blur",
		},
	],
	area: [
		{
			required: true,
			trigger: ["blur"],
			validator: (rule, value, callback) => {
				if (!Boolean(value.length)) {
					callback(new Error("请选择所在地区"));
				} else {
					callback();
				}
			},
		},
	],
	fakeAddress: [
		{
			required: true,
			message: "请填写详细地址",
			trigger: ["blur"],
		},
		{
			message: "详细地址不能包含~号",
			trigger: ["blur"],
			validator: (rule, value, callback) => {
				if (value.includes("~")) {
					callback(new Error("详细地址不能包含~"));
				} else {
					callback();
				}
			},
		},
	],
};

const pageFormRef = ref();

async function loadData() {
	if (props.id) {
		uni.setNavigationBarTitle({
			title: "修改地址",
		});
		await getItemDetails();
	}
}

async function getItemDetails() {
	try {
		const res = await getAddressDetails({
			id: props.id,
		});

		if (res.apiStatus) {
			const item = res.data;

			for (let key of Object.keys(formData)) {
				if (typeof item[key] !== "undefined") {
					formData[key] = item[key];

					// if(key === '')

					// 身高
					// if (key === 'height') {
					// 	const valStr = String(formData[key]);
					// 	const findIndex = pageData.heightList[0].findIndex((i) => i.value === valStr);
					// 	if (findIndex >= 0) {
					// 		const item = pageData.heightList[0][findIndex];
					// 		pageData.heightListIndex.push(findIndex);
					// 		pageData.heightListValue.push(item);
					// 		formData.height = valStr;
					// 		formData.heightText = item.text;
					// 	}
					// }
				}
			}
		}
	} catch (error) {}
}

// 选择地址
const handleChooseRes = async (res) => {
	if (!res.longitude) return;
	if (!res.name) return;
	if (!res.address) return;

	const checkedLocation = {
		type: "Point",
		coordinates: [res.longitude, res.latitude],
	};

	formData.location = checkedLocation;

	try {
		const { area, address } = await locToAddress(res);

		if (area) {
			formData.area = area;
		}
		formData.address = address || res.name;

		// console.log(formData);
	} catch (err) {}
};

async function toChooseAddress() {
	// #ifdef H5
	// console.log('开始定位 -> ');
	// const res = await uni.getLocation({
	// 	type: 'gcj02'
	// });
	// console.log(res);
	// #endif

	uni.chooseLocation({
		// latitude: formData.location.coordinates[1] || '',
		// longitude: formData.location.coordinates[0] || '',
		success: (res) => {
			// console.log(res);
			handleChooseRes(res);
		},
		fail: (err) => {
			// console.log(err);
		},
	});
}

// OCR 识别
function recognizeAddress() {
	if (pageData.recognizeText.length < 3) {
		uni.showToast({
			icon: "none",
			title: "请输入正确的信息",
		});
		return false;
	}
	pageData.recognizeLoading = true;

	return;

	request
		.post("/api/Util/addressRecognize", {
			text: this.recognizeText,
		})
		.then(
			(res) => {
				const data = res.data;

				if (data) {
					this.addressForm.realname = data.name;
					this.addressForm.mobile = data.phone;

					if (data.code.divcode || data.address.length > 0) {
						const divisionArr = data.code.division_name.split(";");
						const divisionText = divisionArr.join("");
						let addressDetails = "";
						if (data.address.length > 0) {
							addressDetails = data.address[data.address.length - 1].word;
						}

						if (divisionText || addressDetails) {
							const addressText = divisionText + addressDetails;

							addressToLocItem({
								address: addressText,
								city: divisionArr[1],
							})
								.then((res) => {
									console.log(res);

									pageData.recognizeLoading = false;
								})
								.catch((err) => {
									uni.showToast({
										icon: "none",
										title: "地址解析失败！",
										success() {
											console.error(err);
										},
									});
									pageData.recognizeLoading = false;
								});
						}
					} else {
						this.recognizeLoading = false;
					}
				} else {
					this.recognizeLoading = false;
				}
			},
			(err) => {
				uni.showToast({
					icon: "none",
					title: "识别异常！",
				});
				this.recognizeLoading = false;
			}
		);
}

const delModal = ref();

function del(verify = false) {
	if (!verify) {
		delModal.value.open();
		return;
	}
	delAddress({
		id: props.id,
	}).then((res) => {
		if (res.apiStatus) {
			route({
				type: "back",
			});
		}
	});
}

function submit() {
	if (pageFormRef.value) {
		canENV(() => {
			console.log(formData);
		});
		if (userStore.checkLogin) {
			pageFormRef.value.validate().then((res) => {
				const data = {
					...formData,
				};

				data.address = `${data.address}${data.fakeAddress && "~"}${data.fakeAddress}`;
				delete data.fakeAddress;

				if (props.id) {
					editAddress({
						id: props.id,
						...data,
					}).then((res) => {
						// console.log(res);
						if (res.apiStatus) {
							route({
								type: "back",
							});
						}
					});
				} else {
					addAddress({
						...data,
					}).then((res) => {
						if (res.apiStatus) {
							route({
								type: "back",
							});
						}
					});
				}
			});
		} else {
			uni.showToast({
				icon: "fail",
				title: "请先登录",
			});
		}
	}
}

onLoad(() => {
	loadData();
});

const formStyle = ref({
	labelStyle: {
		fontSize: "30rpx",
		fontWeight: "bold",
	},
	inputStyle: {
		height: "98rpx",
		padding: "0rpx 0rpx",
		borderBottom: `solid 1rpx #E9EDF1`,
	},
	labelStyle2: {
		fontSize: "26rpx",
		fontWeight: "bold",
		color: "#1152D7",
	},
	inputStyle2: {
		height: "88rpx",
		borderRadius: "10rpx",
		background: "#fff",
		padding: "0rpx 30rpx",
	},
	inputStyle3: {
		borderRadius: "16rpx",
		background: "#F2F6FB",
		padding: "25rpx 30rpx",
	},
	inputStyle4: {
		borderRadius: "16rpx",
		background: "#fff",
		padding: "25rpx 30rpx",
	},
	inputStyle5: {
		height: "105rpx",
		borderRadius: "16rpx",
		background: "#F2F6FB",
		padding: "0rpx 30rpx",
	},
	labelStyle6: {
		fontSize: "28rpx",
		fontWeight: "500",
		color: "#1A1A1B",
	},
	inputStyle6: {
		fontWeight: "bold",
		fontSize: "28rpx",
	},
	labelStyle7: {
		fontSize: "28rpx",
		fontWeight: "500",
		color: "#1A1A1B",
	},
	inputStyle7: {
		fontWeight: "500",
		fontSize: "30rpx",
		padding: 0,
	},
	inputStyle8: {
		// borderRadius: '16rpx',
		background: "#F3F4F7",
		padding: "25rpx 30rpx",
		borderRadius: "53rpx",
	},
	inputPlaceholderStyle: `font-size: 30rpx; font-weight: 500; color: #AEAEB4;`,
	inputPlaceholderStyle3: `font-size: 26rpx; font-weight: 500; color: #B1B6BC;`,
	inputPlaceholderStyle4: `font-size: 30rpx; font-weight: 500; color: #B1B6BC;`,
	inputPlaceholderStyle6: `font-size: 28rpx; font-weight: 500; color: #A7ACB7;`,
	inputPlaceholderStyle7: `font-size: 30rpx; font-weight: 500; color: #93939C;`,
});
</script>

<template>
	<view class="add-caregiver pb-150">
		<view class="w-full py-30 px-20">
			<view class="w-full border-rd-20 bg-#FFFFFF px-30 py-30 mb-20" v-if="false">
				<view class="w-full">
					<textarea v-model="pageData.recognizeText" cols="30" rows="5" confirm-type="done" :maxlength="500" auto-height placeholder="试试粘贴收件人姓名、手机号、收货地址，可快速识别您的收货信息" class="min-h-150 w-full text-26"></textarea>
				</view>
				<view class="w-full mt-20 flex items-center justify-end">
					<view class="1-150">
						<uv-button @click="recognizeAddress()" color="linear-gradient(-65deg, #00B496 0%, #02C9A8 100%)" :loading="pageData.recognizeLoading" text="立即识别" loadingText="识别中..." class="h-48" custom-style="border-radius: 24rpx;" customTextStyle="font-size: 22rpx; color: #fff; font-weight: 500;"></uv-button>
					</view>
				</view>
			</view>

			<view class="w-full border-rd-20 bg-#FFFFFF px-30">
				<uv-form :model="formData" :rules="formRules" :labelStyle="formStyle.labelStyle6" label-width="150rpx" labelPosition="left" errorType="toast" ref="pageFormRef">
					<!-- 姓名 -->
					<uv-form-item label="被服务人" prop="name" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
						<uv-input v-model="formData.name" placeholder="请输入姓名" :placeholderStyle="formStyle.inputPlaceholderStyle6" :customStyle="{ ...formStyle.inputStyle6 }" fontSize="28rpx" border="none" type="text" inputAlign="left"></uv-input>
					</uv-form-item>

					<!-- 手机号 -->
					<uv-form-item label="联系电话" prop="mobile" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
						<uv-input v-model="formData.mobile" placeholder="请输入手机号" :placeholderStyle="formStyle.inputPlaceholderStyle6" :customStyle="{ ...formStyle.inputStyle6 }" fontSize="28rpx" border="none" type="number" inputAlign="left"></uv-input>
					</uv-form-item>

					<!-- 地区 -->
					<uv-form-item label="选择地区" prop="area" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
						<!-- <uv-input
							:value="pageData.address"
							placeholder="请选择地址（省、市、区）"
							:placeholderStyle="formStyle.inputPlaceholderStyle6"
							:customStyle="formStyle.inputStyle6"
							fontSize="28rpx"
							border="none"
							type="text"
							inputAlign="left"
							readonly
						></uv-input> -->
						<view class="w-full text-28 font-500" @click="toChooseAddress">
							<template v-if="formData.area.length > 0">
								<view>{{ formData.area.join(" ") }}</view>
							</template>
							<template v-else>
								<view class="text-#A7ACB7">请选择地址（省、市、区）</view>
							</template>
						</view>

						<template #right>
							<view class="flex items-center" @click="toChooseAddress">
								<app-image :src="common.iconDingweiLan" size="22" mr="6" mode=""></app-image>
								<view class="text-#4AA8FF text-24 font-500">定位</view>
							</view>
						</template>
					</uv-form-item>

					<!-- 详细地址 -->
					<uv-form-item label="详细地址" prop="address" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
						<uv-textarea v-model="formData.address" placeholder="例如：街道、门牌号、小区栋号、 单元室等" :placeholderStyle="formStyle.inputPlaceholderStyle6" :customStyle="{ ...formStyle.inputStyle6 }" fontSize="28rpx" border="none" type="text" inputAlign="left" height="90rpx"></uv-textarea>
					</uv-form-item>

					<!-- 默认地址 -->
					<uv-form-item label="设为默认" class="w-full" :customStyle="{ padding: '42rpx 0' }">
						<template #right>
							<view>
								<uv-switch v-model="formData.isDefault" :inactiveValue="false" :activeValue="true" size="45rpx" :activeColor="'#38B597'"></uv-switch>
							</view>
						</template>
					</uv-form-item>
				</uv-form>
			</view>
		</view>
		<view class="w-full px-30 pt-15 bg-#fff fixed left-0 bottom-0 border-t-1 border-t-solid border-t-#EDEEF0 flex items-center">
			<!-- <view class="flex-1 mr-20" v-if="id">
				<uv-button
					@click="del()"
					type="error"
					text="删除"
					class="h-88"
					custom-style="border-radius: 44rpx;"
					customTextStyle="font-size: 30rpx; color: #fff; font-weight: bold;"
				></uv-button>
			</view> -->
			<view class="flex-1">
				<uv-button @click="submit()" color="#00B496" :text="id ? '修改' : '确认'" class="flex-1 h-88" custom-style="border-radius: 44rpx;" customTextStyle="font-size: 30rpx; color: #fff; font-weight: bold;"></uv-button>

				<app-safeAreaBottom></app-safeAreaBottom>
			</view>
		</view>

		<my-uv-modal ref="delModal" confirmText="删除" :confirmColor="'#38B597'" :lineColor="'#F2F6FB'" cancelText="取消" :duration="200" width="580rpx" showCancelButton @confirm="del(true)" :asyncClose="true" :closeOnClickOverlay="false">
			<view class="flex-center h-100">
				<view class="text-34 font-bold">确定删除吗？</view>
			</view>
		</my-uv-modal>
	</view>
</template>

<style lang="scss" scoped>
.add-caregiver {
	min-height: calc(100vh - var(--window-top) - var(--window-bottom));
	background-color: #f1f3f7;
}
</style>
