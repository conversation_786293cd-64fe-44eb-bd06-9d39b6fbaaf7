<script setup>
import { ref, reactive, getCurrentInstance, onMounted } from 'vue';

import { onLoad, onShow, onReady } from '@dcloudio/uni-app';

import { useUserStore } from '@/store';

import { route } from '@/common/utils';

import { delAddress, getAddressList } from '@/server/api';

import { deepClone, sleep } from '@/uni_modules/uv-ui-tools/libs/function/index.js';

const $props = defineProps({
	select: {
		type: [String, Number],
		default: '0'
	}
});

let instance = null;
let eventChannel = null;

const userStore = useUserStore();

const paging = ref(null);

const pageData = reactive({
	dataList: [],

	swipeActionOptions: [
		{
			text: '删除',
			style: {
				backgroundColor: '#F94B4A',
				fontSize: '24rpx'
			}
		}
	]
});

function submit() {
	route('/pages/my/pages/address/addAddress/addAddressGoods');
}

function egitItem(item) {
	// console.log(item);
	route('/pages/my/pages/address/addAddress/addAddressGoods', {
		id: item.id
	});
}

async function clickItem(item) {
	if (Number($props.select) === 1) {
		if (eventChannel) {
			eventChannel.emit('selectAddress', item);
			await sleep(100);
			uni.navigateBack();
		}
	}
}

function clickActionItem({ position, index }, item) {
	if (position === 'right' && index === 0) {
		delAddress({
			...item
		}).then((res) => {
			if (res.apiStatus) {
				if (item.isDefault) {
					uni.$emit('updateAddress');
				}
				if (paging.value) paging.value.reload();
			}
		});
	}
}

function queryList(pageNo, pageSize) {
	const params = {
		pageNo: pageNo,
		pageSize: pageSize
	};

	if (userStore.checkLogin) {
		getAddressList({
			size: 100,
			serverType: 0
		})
			.then((res) => {
				if (res.apiStatus) {
					paging.value.complete(res.data.records);
				}
			})
			.catch((err) => {
				paging.value.complete([]);
			});
	} else {
		paging.value.complete([]);
	}
}

onShow(() => {
	if (paging.value) paging.value.reload();
});

onReady(() => {
	instance = getCurrentInstance().proxy;
	eventChannel = instance.getOpenerEventChannel();
});
</script>
<template>
	<app-layout>
		<z-paging ref="paging" bg-color="#F0F3F7" v-model="pageData.dataList" @query="queryList">
			<template #top></template>
			<view class="caregiver py-10" v-if="pageData.dataList.length > 0">
				<uni-swipe-action>
					<template v-for="(item, index) in pageData.dataList" :key="index">
						<view class="px-20 py-10">
							<view class="w-full bg-#fff border-rd-20 overflow-hidden">
								<uni-swipe-action-item :right-options="pageData.swipeActionOptions" @click="clickActionItem($event, item)">
									<view class="w-full py-32 px-30 flex items-center">
										<view class="flex-1 flex flex-col" @click="clickItem(item)">
											<view class="uv-line-2">
												<template v-if="item.isDefault">
													<text class="w-46 h-24 mr-20 text-18 font-500 text-#fff border-rd-3 bg-#34B1FF inline-block line-height-24 text-center">默认</text>
												</template>

												<text class="text-28 font-bold">{{ item.address }}</text>
											</view>
											<view class="flex items-center">
												<view class="text-#555555 text-26 font-500 mr-20">{{ item.name }}</view>
												<view class="text-#555555 text-26 font-500">{{ item.mobile }}</view>
											</view>
										</view>
										<view class="w-30 h-30 flex-shrink-0 ml-10">
											<uv-icon name="edit-pen" color="#ADB2B9" size="40rpx" @click="egitItem(item)"></uv-icon>
										</view>
									</view>
								</uni-swipe-action-item>
							</view>
						</view>
					</template>
				</uni-swipe-action>
			</view>

			<template #bottom>
				<view class="w-full pt-15 px-30 pb-0 border-t-1 border-t-solid border-t-#EDEEF0 bg-#fff" v-if="userStore.checkLogin">
					<uv-button
						@click="submit()"
						color="#00B496"
						text="新增地址"
						class="w-690 h-84"
						custom-style="border-radius: 44rpx;"
						customTextStyle="font-size: 30rpx; color: #fff; font-weight: bold;"
					></uv-button>
					<app-safeAreaBottom></app-safeAreaBottom>
				</view>
			</template>

			<template #empty>
				<view class="w-full h-full flex flex-center flex-col bg-#fff">
					<app-image src="@/pages/my/static/icon_kong_beizhaohuren.png" size="340" mode=""></app-image>
					<view class="w-full text-center text-#888888 text-28 font-500">还没有添加地址</view>
				</view>
			</template>
		</z-paging>
	</app-layout>
</template>
<style lang="scss" scoped>
.caregiver {
}
</style>
