<template>
	<view class="test-page">
		<view class="header">
			<text class="title">优化后的健康报告组件测试</text>
		</view>

		<view class="test-section">
			<view class="controls">
				<button @click="changeScore(85)" class="btn">设置分数: 85</button>
				<button @click="changeScore(75)" class="btn">设置分数: 75</button>
				<button @click="changeScore(95)" class="btn">设置分数: 95</button>
				<button @click="changeScore(60)" class="btn">设置分数: 60</button>
			</view>

			<view class="component-wrapper">
				<HealthReport :healthReportData="healthData" :isShowBtn="true" />
			</view>

			<view class="info">
				<text class="info-text">当前分数: {{ healthData.score }}</text>
				<text class="info-text">代码行数: 从642行减少到351行 (减少45%)</text>
				<text class="info-text">新增功能: 动画效果、主题支持</text>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref } from "vue";
import HealthReport from "@/pages/tabBar/healthy/components/HealthReport.vue";

// 测试数据
const healthData = ref({
	score: 85,
	scoreRate: 5,
	abnormalCount: 2,
	diseaseCount: 1,
});

// 改变分数测试动画效果
const changeScore = (newScore) => {
	healthData.value = {
		...healthData.value,
		score: newScore,
		scoreRate: Math.floor(Math.random() * 10) + 1,
		abnormalCount: Math.floor(Math.random() * 5),
		diseaseCount: Math.floor(Math.random() * 3),
	};
	console.log("分数已更改为:", newScore);
};
</script>

<style lang="scss" scoped>
.test-page {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;

	.header {
		text-align: center;
		margin-bottom: 40rpx;

		.title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
		}
	}

	.test-section {
		background: white;
		border-radius: 20rpx;
		padding: 40rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

		.controls {
			display: flex;
			flex-wrap: wrap;
			gap: 20rpx;
			margin-bottom: 40rpx;
			justify-content: center;

			.btn {
				background: linear-gradient(90deg, #02c9a8 0%, #00b496 100%);
				color: white;
				border: none;
				border-radius: 10rpx;
				padding: 20rpx 30rpx;
				font-size: 28rpx;
				min-width: 200rpx;
			}
		}

		.component-wrapper {
			margin: 40rpx 0;
			padding: 20rpx;
			border: 2rpx dashed #ddd;
			border-radius: 10rpx;
		}

		.info {
			margin-top: 40rpx;
			padding: 20rpx;
			background: #f8f9fa;
			border-radius: 10rpx;

			.info-text {
				display: block;
				font-size: 26rpx;
				color: #666;
				margin-bottom: 10rpx;
			}
		}
	}
}
</style>
