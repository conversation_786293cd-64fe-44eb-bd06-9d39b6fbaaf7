<template>
	<view class="test-page">
		<!-- Canvas测试区域 -->
		<view class="canvas-section">
			<text class="section-title">Canvas圆环测试</text>
			<view class="canvas-container">
				<!-- Canvas元素 -->
				<!-- #ifdef MP-WEIXIN -->
				<canvas v-if="canvasManager?.use2dCanvas" type="2d" :id="canvasId" class="test-canvas" :style="canvasStyle"></canvas><canvas v-else :canvas-id="canvasId" :id="canvasId" class="test-canvas" :width="canvasSize" :height="canvasSize" :style="canvasStyle"></canvas>
				<!-- #endif -->
				<!-- #ifndef MP-WEIXIN -->
				<canvas :canvas-id="canvasId" :id="canvasId" class="test-canvas" :width="canvasSize" :height="canvasSize" :style="canvasStyle"></canvas>
				<!-- #endif -->
				<!-- 分数和状态显示（现在在Canvas中绘制） -->
				<!-- <view class="score-display"><text class="score-number">{{ testData.score }}</text><text class="score-status">{{ healthLevel }}</text></view> -->
				<!-- 状态显示 -->
				<view class="canvas-status">
					<text class="status-text" :class="statusClass">{{ statusText }}</text>
				</view>
				<!-- 颜色指示器 -->
				<view class="color-indicator">
					<view class="color-dot" :style="{ backgroundColor: currentColor }"></view>
					<text class="color-text">{{ healthLevel }}</text>
				</view>
			</view>
		</view>
		<!-- 控制面板 -->
		<view class="controls">
			<view class="control-group">
				<text class="control-label">健康分数: {{ testData.score }}</text>
				<slider :value="testData.score" @change="onScoreChange" min="0" max="100" step="1" activeColor="#00b496" backgroundColor="#f0f0f0" />
			</view>
			<view class="control-group">
				<text class="control-label">主题选择: {{ THEMES[currentTheme].name }}</text>
				<view class="theme-selector">
					<button v-for="theme in availableThemes" :key="theme" @click="changeTheme(theme)" class="theme-btn" :class="{ active: currentTheme === theme }">{{ THEMES[theme].name }}</button>
				</view>
			</view>
			<view class="control-group">
				<text class="control-label">动画设置</text>
				<view class="animation-controls">
					<button @click="toggleAnimation" class="animation-btn" :class="{ active: animationEnabled }">{{ animationEnabled ? "动画开启" : "动画关闭" }}</button>
					<view class="animation-duration" v-if="animationEnabled">
						<text class="duration-label">时长: {{ animationDuration }}ms</text>
						<slider :value="animationDuration" @change="onDurationChange" min="200" max="2000" step="100" activeColor="#52c41a" backgroundColor="#f0f0f0" />
					</view>
				</view>
			</view>
			<view class="button-group">
				<button @click="randomData" class="test-btn">随机数据</button>
				<button @click="resetData" class="test-btn">重置数据</button>
				<button @click="testCanvas" class="test-btn canvas-btn">测试Canvas</button>
				<button @click="showPopup" class="test-btn popup-btn">显示弹窗</button>
				<button @click="testIndependentAnimation" class="test-btn independent-btn">独立动画</button>
				<button @click="testOverlapRings" class="test-btn overlap-btn">重叠环效果</button>
			</view>
		</view>
		<!-- uv-popup弹窗测试 -->
		<uv-popup ref="popupRef" mode="center" :round="20" :closeable="true" :close-on-click-overlay="true">
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-title">Canvas层级测试</text>
				</view>
				<view class="popup-body">
					<text class="popup-text">这是一个测试弹窗，用于验证Canvas是否存在层级问题。</text>
					<text class="popup-text">如果Canvas正常实现，这个弹窗应该能够正常覆盖在Canvas上方。</text>

					<!~~ 弹窗中的Canvas测试 ~~>
					<view class="popup-canvas-test">
						<text class="test-label">弹窗中的Canvas测试:</text>
						<view class="mini-canvas-container">
							<!~~ #ifdef MP-WEIXIN ~~>
							<canvas v-if="popupCanvasManager?.use2dCanvas" type="2d" :id="popupCanvasId" class="mini-canvas" style="width: 60px; height: 60px"></canvas>
							<canvas v-else :canvas-id="popupCanvasId" :id="popupCanvasId" class="mini-canvas" :width="60" :height="60" style="width: 60px; height: 60px"></canvas>
							<!~~ #endif ~~>

							<!~~ #ifndef MP-WEIXIN ~~>
							<canvas :canvas-id="popupCanvasId" :id="popupCanvasId" class="mini-canvas" :width="60" :height="60" style="width: 60px; height: 60px"></canvas>
							<!~~ #endif ~~>

							<view class="mini-score">{{ testData.score }}</view>
						</view>
					</view>
				</view>
				<view class="popup-footer">
					<button @click="closePopup" class="popup-btn">关闭</button>
				</view>
			</view>
		</uv-popup>

		<!-- 状态显示 -->
		<view class="status-section">
			<text class="section-title">系统信息</text>

			<view class="status-item">
				<text class="status-label">平台:</text>
				<text class="status-value">{{ platform }}</text>
			</view>

			<view class="status-item">
				<text class="status-label">SDK版本:</text>
				<text class="status-value">{{ sdkVersion }}</text>
			</view>

			<view class="status-item">
				<text class="status-label">像素比:</text>
				<text class="status-value">{{ pixelRatio }}</text>
			</view>

			<view class="status-item">
				<text class="status-label">Canvas类型:</text>
				<text class="status-value">{{ canvasManager?.use2dCanvas ? "Canvas 2D" : "旧版Canvas" }}</text>
			</view>

			<view class="status-item">
				<text class="status-label">Canvas ID:</text>
				<text class="status-value">{{ canvasId || "未生成" }}</text>
			</view>

			<view class="status-item">
				<text class="status-label">Canvas状态:</text>
				<text class="status-value" :class="statusClass">{{ statusText }}</text>
			</view>

			<view class="status-item">
				<text class="status-label">当前颜色:</text>
				<view class="color-info">
					<view class="status-color-dot" :style="{ backgroundColor: currentColor }"></view>
					<text class="status-value">{{ currentColor }} ({{ healthLevel }})</text>
				</view>
			</view>
		</view>

		<!-- 测试数据预设 -->
		<view class="preset-section">
			<text class="section-title">预设数据</text>
			<view class="preset-buttons">
				<button v-for="preset in presets" :key="preset.name" @click="applyPreset(preset)" class="preset-btn">
					{{ preset.name }}
				</button>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, getCurrentInstance } from "vue";
import { onReady } from "@dcloudio/uni-app";
import { createCanvasManager, THEMES, getHealthLevel, getHealthColor, renderWithAnimation } from "@/utils/canvasUtils.js";

// 获取组件实例
const instance = getCurrentInstance();

// Canvas配置
const canvasSize = 100;
const canvasId = ref("");
const canvasManager = ref(null);
const isDrawing = ref(false);

// 弹窗相关
const popupCanvasId = ref("");
const popupCanvasManager = ref(null);

// 主题相关
const currentTheme = ref("health");
const availableThemes = Object.keys(THEMES);

// 动画相关
const animationEnabled = ref(true);
const animationDuration = ref(800);

// 测试数据（使用您的健康等级范围）
const testData = ref({
	score: 95, // 良好等级
	abnormalCount: 1,
	diseaseCount: 0,
});

// 系统信息
const platform = ref("");
const sdkVersion = ref("");
const pixelRatio = ref(1);

// 计算属性
const canvasStyle = computed(() => {
	return `width:${canvasSize}px;height:${canvasSize}px;`;
});

const statusText = computed(() => {
	if (!canvasManager.value) return "未初始化";
	if (canvasManager.value.error) return `错误: ${canvasManager.value.error}`;
	if (isDrawing.value) return "绘制中...";
	if (canvasManager.value.isReady) return "就绪";
	return "初始化中...";
});

const statusClass = computed(() => {
	if (!canvasManager.value) return "status-warning";
	if (canvasManager.value.error) return "status-error";
	if (canvasManager.value.isReady) return "status-success";
	return "status-warning";
});

// 当前圆环颜色
const currentColor = computed(() => {
	return getHealthColor(testData.value.score);
});

// 健康等级
const healthLevel = computed(() => {
	return getHealthLevel(testData.value.score);
});

// 预设数据（适配您的健康等级范围和颜色）
const presets = [
	{
		name: "良好(98分) 🟢",
		data: { score: 98, abnormalCount: 0, diseaseCount: 0 },
	},
	{
		name: "良好(95分) 🟢",
		data: { score: 95, abnormalCount: 1, diseaseCount: 0 },
	},
	{
		name: "一般(85分) 🟢",
		data: { score: 85, abnormalCount: 2, diseaseCount: 1 },
	},
	{
		name: "一般(80分) 🟢",
		data: { score: 80, abnormalCount: 3, diseaseCount: 1 },
	},
	{
		name: "警惕(75分) 🟠",
		data: { score: 75, abnormalCount: 4, diseaseCount: 2 },
	},
	{
		name: "警惕(60分) 🟠",
		data: { score: 60, abnormalCount: 5, diseaseCount: 3 },
	},
];

// 切换动画开关
const toggleAnimation = () => {
	animationEnabled.value = !animationEnabled.value;
	console.log("动画", animationEnabled.value ? "开启" : "关闭");
};

// 动画时长变化
const onDurationChange = (e) => {
	animationDuration.value = e.detail.value;
	console.log("动画时长:", animationDuration.value + "ms");
};

// 随机数据
const randomData = () => {
	testData.value = {
		score: Math.floor(Math.random() * 100),
		abnormalCount: Math.floor(Math.random() * 6),
		diseaseCount: Math.floor(Math.random() * 4),
	};
};

// 重置数据
const resetData = () => {
	testData.value = {
		score: 95, // 良好等级
		abnormalCount: 1,
		diseaseCount: 0,
	};
};

// 应用预设
const applyPreset = (preset) => {
	testData.value = { ...preset.data };
	// 应用预设后重新绘制主Canvas
	setTimeout(() => {
		drawCircle(testData.value.score);
	}, 100);
	// 如果弹窗打开，也更新弹窗中的Canvas
	if (popupCanvasManager.value) {
		setTimeout(() => {
			drawPopupCircle(testData.value.score);
		}, 150);
	}
};

// 初始化Canvas
const initCanvas = async () => {
	try {
		console.log("初始化Canvas...");

		// 生成唯一Canvas ID
		canvasId.value = `test-canvas${(instance._ && instance._.uid) || instance._uid || instance.uid || Date.now()}`;

		// 创建Canvas管理器
		canvasManager.value = createCanvasManager({
			canvasId: canvasId.value,
			instance: instance,
			width: canvasSize,
			height: canvasSize,
		});

		console.log("Canvas初始化完成:", canvasManager.value.getStatus());
	} catch (error) {
		console.error("Canvas初始化失败:", error);
	}
};

// 绘制圆环
const drawCircle = async (score) => {
	if (!canvasManager.value || isDrawing.value) return;

	try {
		isDrawing.value = true;
		console.log("开始绘制圆环:", score);

		// 使用新的render方法和主题系统，动态动画设置
		const result = await canvasManager.value.render("circle", score, {
			radius: 35,
			lineWidth: 8,
			bgLineWidth: 15,
			theme: THEMES[currentTheme.value],
			animation: animationEnabled.value,
			animationDuration: animationDuration.value,
			animationEasing: "easeInOutCubic",
			showText: true,
			scoreSize: 20,
			statusSize: 12,
			textColor: "#1a1a1a",
		});

		if (result.success) {
			console.log("圆环绘制成功:", result);
		} else {
			console.error("圆环绘制失败:", result.error);
		}
	} catch (error) {
		console.error("绘制圆环异常:", error);
	} finally {
		isDrawing.value = false;
	}
};

// 测试Canvas功能
const testCanvas = async () => {
	try {
		if (!canvasManager.value) {
			await initCanvas();
			await nextTick();
			setTimeout(() => {
				drawCircle(testData.value.score);
			}, 200);
		} else {
			drawCircle(testData.value.score);
		}
	} catch (error) {
		console.error("测试Canvas失败:", error);
	}
};
const popupRef = ref(null);
// 初始化弹窗Canvas
const initPopupCanvas = async () => {
	try {
		console.log("初始化弹窗Canvas...");

		// 生成唯一Canvas ID
		popupCanvasId.value = `popup-canvas${(instance._ && instance._.uid) || instance._uid || instance.uid || Date.now()}`;

		// 创建Canvas管理器
		popupCanvasManager.value = createCanvasManager({
			canvasId: popupCanvasId.value,
			instance: instance,
			width: 60,
			height: 60,
		});

		console.log("弹窗Canvas初始化完成:", popupCanvasManager.value.getStatus());
	} catch (error) {
		console.error("弹窗Canvas初始化失败:", error);
	}
};

// 绘制弹窗中的圆环
const drawPopupCircle = async (score) => {
	if (!popupCanvasManager.value) return;

	try {
		console.log("开始绘制弹窗圆环:", score);

		// 使用新的render方法，动态动画设置
		const result = await popupCanvasManager.value.render("circle", score, {
			radius: 20,
			lineWidth: 4,
			theme: THEMES[currentTheme.value],
			animation: animationEnabled.value,
			animationDuration: Math.min(animationDuration.value, 600), // 弹窗动画稍快
			animationEasing: "easeInOutCubic",
			showText: true,
			scoreSize: 18,
			statusSize: 10,
			textColor: "#1a1a1a",
		});

		if (result.success) {
			console.log("弹窗圆环绘制成功:", result);
		}
	} catch (error) {
		console.error("绘制弹窗圆环失败:", error);
	}
};

// 显示弹窗
const showPopup = async () => {
	if (popupRef.value) {
		popupRef.value.open();
	}

	// 等待弹窗显示后初始化Canvas
	await nextTick();
	setTimeout(async () => {
		await initPopupCanvas();
		await nextTick();
		setTimeout(() => {
			drawPopupCircle(testData.value.score);
		}, 200);
	}, 300);
};

// 关闭弹窗
const closePopup = () => {
	if (popupRef.value) {
		popupRef.value.close();
	}
	// 重置弹窗Canvas
	popupCanvasManager.value = null;
};

// 切换主题
const changeTheme = (theme) => {
	currentTheme.value = theme;
	console.log("切换主题:", theme, THEMES[theme].name);

	// 重新绘制主Canvas
	setTimeout(() => {
		drawCircle(testData.value.score);
	}, 100);

	// 如果弹窗打开，也重新绘制弹窗Canvas
	if (popupCanvasManager.value) {
		setTimeout(() => {
			drawPopupCircle(testData.value.score);
		}, 150);
	}
};

// 测试独立动画函数
const testIndependentAnimation = async () => {
	if (!canvasManager.value) {
		console.warn("Canvas未初始化");
		return;
	}

	try {
		console.log("开始测试独立动画函数");

		// 简单的渲染函数（不依赖类）
		const renderSimpleCircle = async (score) => {
			const ctx = await canvasManager.value.getContext();

			// 清空画布
			canvasManager.value.clear();

			// 绘制圆环
			const centerX = canvasManager.value.width / 2;
			const centerY = canvasManager.value.height / 2;
			const radius = 35;
			const progress = score / 100;
			const endAngle = -Math.PI / 2 + progress * 2 * Math.PI;

			// 背景环
			ctx.beginPath();
			ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
			if (canvasManager.value.use2dCanvas) {
				ctx.strokeStyle = "#f0f0f0";
			} else {
				ctx.setStrokeStyle("#f0f0f0");
			}
			ctx.lineWidth = 8;
			ctx.stroke();

			// 进度环
			if (score > 0) {
				ctx.beginPath();
				ctx.arc(centerX, centerY, radius, -Math.PI / 2, endAngle);
				if (canvasManager.value.use2dCanvas) {
					ctx.strokeStyle = "#ff4d4f"; // 使用红色突出显示
					ctx.lineCap = "round";
				} else {
					ctx.setStrokeStyle("#ff4d4f");
					ctx.setLineCap("round");
				}
				ctx.lineWidth = 8;
				ctx.stroke();
			}

			canvasManager.value.draw();
			return { success: true, score };
		};

		// 使用独立的动画函数
		const animationId = `independent-${canvasManager.value.canvasId}`;
		const fromScore = 0;
		const toScore = 90;

		const result = await renderWithAnimation(
			animationId,
			fromScore,
			toScore,
			// 渲染函数
			renderSimpleCircle,
			// 动画选项
			{
				duration: 1200,
				easing: "easeOutCubic",
				onUpdate: (score) => {
					console.log("独立动画进度:", score.toFixed(1));
				},
				onComplete: (score) => {
					console.log("独立动画完成:", score);
					// 2秒后恢复原来的绘制
					setTimeout(() => {
						drawCircle(testData.value.score);
					}, 2000);
				},
			}
		);

		console.log("独立动画结果:", result);
	} catch (error) {
		console.error("独立动画测试失败:", error);
	}
};

// 测试重叠环效果
const testOverlapRings = async () => {
	if (!canvasManager.value) {
		console.warn("Canvas未初始化");
		return;
	}

	try {
		console.log("开始测试重叠环效果");

		// 使用不同的线宽展示重叠效果
		const result = await canvasManager.value.render("circle", testData.value.score, {
			radius: 35, // 统一的中线半径
			lineWidth: 6, // 进度环线宽（较细）
			bgLineWidth: 12, // 背景环线宽（较粗）
			theme: THEMES[currentTheme.value],
			animation: animationEnabled.value,
			animationDuration: animationDuration.value,
			animationEasing: "easeInOutCubic",
			showText: true,
			scoreSize: 20,
			statusSize: 12,
			textColor: "#1a1a1a",
		});

		if (result.success) {
			console.log("重叠环效果绘制成功:", {
				radius: result.radius,
				bgLineWidth: result.bgLineWidth,
				fgLineWidth: result.fgLineWidth,
			});

			// 3秒后恢复正常效果
			setTimeout(() => {
				console.log("恢复正常环效果");
				drawCircle(testData.value.score);
			}, 3000);
		}
	} catch (error) {
		console.error("重叠环效果测试失败:", error);
	}
};

// 监听分数变化
const onScoreChange = (e) => {
	testData.value.score = e.detail.value;
	// 分数变化时重新绘制主Canvas
	setTimeout(() => {
		drawCircle(testData.value.score);
	}, 50);
	// 如果弹窗打开，也更新弹窗中的Canvas
	if (popupCanvasManager.value) {
		setTimeout(() => {
			drawPopupCircle(testData.value.score);
		}, 100);
	}
};

// 初始化
onMounted(async () => {
	try {
		const systemInfo = uni.getSystemInfoSync();
		platform.value = systemInfo.platform || "unknown";
		sdkVersion.value = systemInfo.SDKVersion || "unknown";
		pixelRatio.value = systemInfo.pixelRatio || 1;

		// 初始化Canvas
		await nextTick();
		setTimeout(() => {
			testCanvas();
		}, 500);
	} catch (error) {
		console.error("初始化失败:", error);
	}
});

// onReady生命周期
onReady(async () => {
	console.log("onReady - 开始Canvas测试");
	await nextTick();
	setTimeout(() => {
		testCanvas();
	}, 300);
});
</script>

<style lang="scss" scoped>
.test-page {
	padding: 32rpx;
	background: #f7f8fa;
	min-height: 100vh;
}

.canvas-section {
	margin-bottom: 32rpx;

	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #1a1a1a;
		margin-bottom: 16rpx;
		display: block;
	}

	.canvas-container {
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
		background: white;
		border-radius: 16rpx;
		padding: 32rpx;

		.test-canvas {
			display: block;
		}

		.score-display {
			position: absolute;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;

			.score-number {
				font-size: 48rpx;
				font-weight: bold;
				color: #1a1a1a;
				line-height: 1;
			}

			.score-status {
				font-size: 24rpx;
				color: #666;
				margin-top: 4rpx;
				line-height: 1;
			}
		}

		.canvas-status {
			position: absolute;
			bottom: 16rpx;
			right: 16rpx;

			.status-text {
				font-size: 20rpx;
				padding: 4rpx 8rpx;
				border-radius: 4rpx;

				&.status-success {
					background: #f6ffed;
					color: #52c41a;
				}

				&.status-warning {
					background: #fffbe6;
					color: #faad14;
				}

				&.status-error {
					background: #fff2f0;
					color: #ff4d4f;
				}
			}
		}

		.color-indicator {
			position: absolute;
			top: 16rpx;
			right: 16rpx;
			display: flex;
			align-items: center;

			.color-dot {
				width: 16rpx;
				height: 16rpx;
				border-radius: 50%;
				margin-right: 8rpx;
			}

			.color-text {
				font-size: 20rpx;
				color: #666;
			}
		}
	}
}

.controls {
	background: white;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 32rpx;

	.control-group {
		margin-bottom: 32rpx;

		.control-label {
			font-size: 28rpx;
			color: #1a1a1a;
			margin-bottom: 16rpx;
			display: block;
		}

		.theme-selector {
			display: flex;
			flex-wrap: wrap;
			gap: 12rpx;

			.theme-btn {
				background: #f0f0f0;
				color: #666;
				border: none;
				padding: 12rpx 24rpx;
				border-radius: 6rpx;
				font-size: 24rpx;
				flex: 1;
				min-width: 120rpx;

				&.active {
					background: #1890ff;
					color: white;
				}
			}
		}

		.animation-controls {
			.animation-btn {
				background: #f0f0f0;
				color: #666;
				border: none;
				padding: 12rpx 24rpx;
				border-radius: 6rpx;
				font-size: 24rpx;
				margin-bottom: 16rpx;

				&.active {
					background: #52c41a;
					color: white;
				}
			}

			.animation-duration {
				.duration-label {
					font-size: 24rpx;
					color: #666;
					margin-bottom: 8rpx;
					display: block;
				}
			}
		}
	}

	.button-group {
		display: flex;
		justify-content: space-around;

		.test-btn {
			background: #1890ff;
			color: white;
			border: none;
			padding: 16rpx 32rpx;
			border-radius: 8rpx;
			font-size: 28rpx;
			flex: 1;
			margin: 0 8rpx;

			&.canvas-btn {
				background: #00b496;
			}

			&.popup-btn {
				background: #722ed1;
			}

			&.independent-btn {
				background: #eb2f96;
			}

			&.overlap-btn {
				background: #13c2c2;
			}
		}
	}
}

// 弹窗样式
.popup-content {
	width: 600rpx;
	background: white;
	border-radius: 20rpx;
	overflow: hidden;

	.popup-header {
		padding: 32rpx;
		text-align: center;
		border-bottom: 1rpx solid #f0f0f0;

		.popup-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #1a1a1a;
		}
	}

	.popup-body {
		padding: 32rpx;

		.popup-text {
			display: block;
			font-size: 28rpx;
			color: #666;
			line-height: 1.6;
			margin-bottom: 16rpx;
		}

		.popup-canvas-test {
			margin-top: 32rpx;
			padding: 24rpx;
			background: #f7f8fa;
			border-radius: 12rpx;

			.test-label {
				font-size: 26rpx;
				color: #1a1a1a;
				margin-bottom: 16rpx;
				display: block;
			}

			.mini-canvas-container {
				position: relative;
				display: flex;
				justify-content: center;
				align-items: center;

				.mini-canvas {
					display: block;
				}

				.mini-score {
					position: absolute;
					font-size: 24rpx;
					font-weight: bold;
					color: #1a1a1a;
				}
			}
		}
	}

	.popup-footer {
		padding: 24rpx 32rpx;
		text-align: center;
		border-top: 1rpx solid #f0f0f0;

		.popup-btn {
			background: #1890ff;
			color: white;
			border: none;
			padding: 16rpx 48rpx;
			border-radius: 8rpx;
			font-size: 28rpx;
		}
	}
}

.status-section,
.preset-section,
.info-section {
	background: white;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 32rpx;

	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #1a1a1a;
		margin-bottom: 24rpx;
		display: block;
	}

	.status-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 12rpx 0;
		border-bottom: 1rpx solid #f0f0f0;

		&:last-child {
			border-bottom: none;
		}

		.status-label {
			font-size: 28rpx;
			color: #666;
		}

		.status-value {
			font-size: 28rpx;
			color: #1a1a1a;
		}

		.color-info {
			display: flex;
			align-items: center;

			.status-color-dot {
				width: 20rpx;
				height: 20rpx;
				border-radius: 50%;
				margin-right: 12rpx;
			}
		}
	}

	.preset-buttons {
		display: flex;
		flex-wrap: wrap;
		gap: 16rpx;

		.preset-btn {
			background: #52c41a;
			color: white;
			border: none;
			padding: 12rpx 24rpx;
			border-radius: 8rpx;
			font-size: 24rpx;
			flex: 1;
			min-width: 120rpx;
		}
	}

	.info-list {
		.info-item {
			display: block;
			font-size: 26rpx;
			color: #666;
			margin-bottom: 12rpx;
			line-height: 1.5;
		}
	}
}
</style>
