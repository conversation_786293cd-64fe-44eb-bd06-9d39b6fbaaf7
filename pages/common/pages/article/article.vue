<script setup>
import { onLoad } from '@dcloudio/uni-app'

import { ref } from 'vue'

import { getArticleDetails, getBannerDetails } from '@/server/api'

import { useArticleStore } from '@/store'

const props = defineProps({
    type: {
        type: String,
        required: true,
    },
    nav: {
        type: [String, Number],
        default: '1',
    },
    ids: {
        type: [String, Number],
        default: '',
    },
    title: {
        type: String,
        default: '',
    },
    platform: {
        type: [String, Number],
        default: '1',
    },
})

const pageTypes = {
    fwxy: {
        id: 100,
        title: '服务协议',
        content: '服务协议内容',
        footer: '服务协议底部',
        showTime: false,
    },
    yszc: {
        id: 102,
        title: '隐私政策',
        content: '隐私政策内容',
        footer: '隐私政策底部',
        showTime: false,
    },
    dsfsdk: {
        id: 103,
        title: '第三方SDK信息共享清单',
        content: '第三方SDK信息共享清单内容',
        footer: '第三方SDK信息共享清单底部',
        showTime: false,
    },
    grxxqd: {
        id: 104,
        title: ' 个人信息清单',
        content: ' 个人信息清单内容',
        footer: ' 个人信息清单底部',
        showTime: false,
    },
    qxjytsm: {
        id: 105,
        title: '权限及用途说明',
        content: '权限及用途说明内容',
        footer: '权限及用途说明底部',
        showTime: false,
    },
    zhaq: {
        id: 106,
        title: '账户安全',
        content: '账户安全内容',
        footer: '账户安全底部',
        showTime: false,
    },
    gywm: {
        id: 107,
        title: '关于我们',
        content: '关于我们内容',
        footer: '关于我们底部',
        showTime: false,
    },
    wcnrysxy: {
        id: 108,
        title: '未成年人隐私保护政策',
        content: '未成年人隐私保护政策内容',
        footer: '未成年人隐私保护政策底部',
        showTime: false,
    },
    hyxy: {
        id: 109,
        title: '会员协议',
        content: '会员协议内容',
        footer: '会员协议底部',
        showTime: false,
    },
    custom: {
        id: -1,
        title: '',
        content: '',
        footer: '',
        showTime: false,
    },
    advertising: {
        id: 0,
        title: '',
        content: '',
        footer: '',
        showTime: false,
    },
}

const pageLoading = ref(true)

const updateTime = ref('')
const createTime = ref('')

const content = ref('')

// 使用article store
const articleStore = useArticleStore()

onLoad((option) => {
    // #ifdef H5
    if (Number(props.nav) === 0) {
        document.querySelector('uni-page-head').style.display = 'none'
    }
    // #endif

    // 优先检查从store获取的数据
    const storeData = articleStore.getArticleData()
    if (storeData && storeData.title) {
        content.value = storeData.content || ''
        uni.setNavigationBarTitle({
            title: storeData.title,
        })
        pageLoading.value = false
        // 使用完数据后清除
        articleStore.clearArticleData()
        return
    }

    if (typeof props.type === 'string' && typeof pageTypes[props.type] === 'object') {
        let title = ''

        if (props.type === 'advertising') {
            if (props.ids) {
                getBannerDetails({
                    id: props.ids,
                }).then((res) => {
                    if (res.apiStatus) {
                        title = res.data.title

                        content.value = res.data.content || ''

                        uni.setNavigationBarTitle({
                            title,
                        })

                        pageLoading.value = false
                    }
                })
            } else {
                uni.setNavigationBarTitle({
                    title: '页面异常',
                })
            }
        } else if (props.type === 'custom') {
            const pageTypeItem = pageTypes[props.type]
            title = props.title
            // let footer = pageTypeItem.footer;

            getArticleDetails({
                platform: props.platform || '',
                id: props.ids,
            }).then((res) => {
                const { code, data } = res
                if (code === 200) {
                    if (pageTypeItem.showTime) {
                        if (data.createtime_text) {
                            createTime.value = data.createtime_text.split(' ')[0]
                        }

                        if (data.updatetime_text) {
                            updateTime.value = data.updatetime_text.split(' ')[0]
                        }
                    }

                    content.value = data.platformPrivacyAgreementText || ''
                }

                uni.setNavigationBarTitle({
                    title,
                })

                pageLoading.value = false
            })
            // content.value = pageTypeItem.content || '';
        } else {
            const pageTypeItem = pageTypes[props.type]
            title = pageTypeItem.title
            // let footer = pageTypeItem.footer;

            getArticleDetails({
                platform: props.platform || '',
                id: pageTypeItem.id,
            }).then((res) => {
                const { code, data } = res
                if (code === 200) {
                    if (pageTypeItem.showTime) {
                        if (data.createtime_text) {
                            createTime.value = data.createtime_text.split(' ')[0]
                        }

                        if (data.updatetime_text) {
                            updateTime.value = data.updatetime_text.split(' ')[0]
                        }
                    }

                    content.value = data.platformPrivacyAgreementText || ''
                }

                uni.setNavigationBarTitle({
                    title,
                })

                pageLoading.value = false
            })
            // content.value = pageTypeItem.content || '';
        }
    } else {
        uni.setNavigationBarTitle({
            title: '页面异常',
        })
    }
})
</script>

<template>
    <app-layout>
        <view class="article px-33 py-40">
            <template v-if="pageLoading">
                <uv-loading-icon text="加载中..." vertical></uv-loading-icon>
            </template>
            <template v-else>
                <view class="text-6D7992" v-if="updateTime">更新日期：{{ updateTime }}</view>
                <view class="text-6D7992 mt-5" v-if="createTime">生效日期：{{ createTime }}</view>
                <uv-parse class="mt-10" :content="content"></uv-parse>
            </template>
        </view>
    </app-layout>
</template>

<style lang="scss" scoped></style>
