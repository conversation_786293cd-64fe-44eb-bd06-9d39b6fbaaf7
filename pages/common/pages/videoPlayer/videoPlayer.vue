<script setup>
import { reactive } from "vue";
import { onLoad } from "@dcloudio/uni-app";

import { useAppStore } from "@/store";

import { getUploadSrc } from "@/common/utils";
import { common } from "@/common/images";

const appStore = useAppStore();

const $data = reactive({
	videoSrc: "",
});

onLoad((options) => {
	$data.videoSrc = appStore.videoUrl;
});

function goBack() {
	uni.navigateBack({
		delta: 1,
	});
}
</script>

<template>
	<view class="videoPlayer page-main">
		<cover-view class="fixed w-full flex justify-between items-center h-88 z-999 top-[var(--status-bar-height)]">
			<cover-view class="w-full flex items-center justify-between px-24 pt-10">
				<cover-image @click="goBack" :src="common.iconLeftHeidi" class="w-60 h-60" mode=""></cover-image>
				<!-- <cover-image @click="goBack" :src="common.iconLeftBai" class="w-40 h-40" mode=""></cover-image> -->
			</cover-view>
		</cover-view>
		<video class="video" :autoplay="true" id="myVideo" :src="getUploadSrc($data.videoSrc)" :loop="true" :poster="getUploadSrc($data.videoSrc, 'videoCover')" controls :show-fullscreen-btn="false" :show-play-btn="false"></video>
	</view>
</template>
<style lang="scss" scoped>
.video {
	min-width: 100vw;
	min-height: calc(100vh - var(--window-top) - var(--window-bottom));
}
</style>
