<script setup>
import { onLoad, onNavigationBarButtonTap } from '@dcloudio/uni-app'

import { ref, reactive } from 'vue'

import { useAppStore } from '@/store'

const appStore = useAppStore()

const webviewStyles = reactive({
    progress: {
        color: '#03baa7',
    },
})

const pageTitle = ref('')

onLoad((options) => {
    pageTitle.value = options.title || ''
    loadData()
})

onNavigationBarButtonTap((e) => {
    if (e.type === 'close') {
        uni.navigateBack({
            delta: 9,
            animationType: 'slide-out-bottom',
        })
    } else if (e.type === 'home') {
        // 处理回到首页按钮点击事件
        uni.switchTab({
            url: '/pages/tabBar/home/<USER>',
        })
    }
})

function loadData() {
    if (pageTitle.value) {
        uni.setNavigationBarTitle({
            title: pageTitle.value,
        })
    }
}
</script>

<template>
    <view class="web-view">
        <web-view :webview-styles="webviewStyles" :src="appStore.webViewUrl"></web-view>
    </view>
</template>

<style lang="scss" scoped>
.web-view {
}
</style>
