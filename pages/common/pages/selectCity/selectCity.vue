<script setup>
import { onLoad, onReady } from "@dcloudio/uni-app";

import { ref, reactive, computed, getCurrentInstance } from "vue";

import { useUserStore } from "@/store";

// import { cityData } from '@/common/city.data';
// import { cityData } from './city.data';
import { cityData } from "@/pages/common/common/city.data";

import { deepClone } from "@/uni_modules/uv-ui-tools/libs/function/index";

import permision from "@/js_sdk/wa-permission/permission";
import { common } from "@/common/images";

const userStore = useUserStore();
const instance = getCurrentInstance();
const query = uni.createSelectorQuery().in(instance.proxy);

const pageData = reactive({
	locationPermission: false,
	locationStatic: -1,
	locationInfo: {
		province: "",
		city: "",
		area: "",
		latitude: 0, // 纬度
		longitude: 0, // 经度
	},

	selectInputFocus: false,
	searchKeyword: "",

	searchList: [],

	hotCity: [
		{
			name: "北京市",
			tags: "BEIJING,北京市",
			cityid: 1,
		},
		{
			name: "上海市",
			tags: "SHANGHAI,上海市",
			cityid: 4,
		},
		{
			name: "南京市",
			tags: "NANJING,南京市",
			cityid: 11,
		},
		{
			name: "杭州市",
			tags: "HANGZHOU,杭州市",
			cityid: 5,
		},
		{
			name: "苏州市",
			tags: "SUZHOU,苏州市",
			cityid: 23,
		},
		{
			name: "深圳市",
			tags: "SHENZHEN,深圳市",
			cityid: 2,
		},
		{
			name: "广州市",
			tags: "GUANGZHOU,广州市",
			cityid: 3,
		},
		{
			name: "武汉市",
			tags: "WUHAN,武汉市",
			cityid: 6,
		},
	],

	allCityIndex: [],
	allCityData: [],

	headerBoxHeight: 0,
});

const isSearch = computed(() => {
	return pageData.selectInputFocus || pageData.searchKeyword.length > 0;
});

async function loadLocation() {
	// 定位
	let status = true;
	// #ifdef APP
	status = await permision.checkPermission("location");
	// #endif
	// #ifdef MP-WEIXIN
	try {
		const res = await uni.getSetting();

		if (res) {
			if (res.authSetting["scope.userLocation"]) {
				status = true;
			} else {
				status = false;
			}
		}
	} catch (error) {
		//TODO handle the exception
	}
	// #endif

	pageData.locationPermission = status;

	if (status) {
		pageData.locationStatic = 0;
		uni.getLocation({
			type: "gcj02",
			geocode: true,
			success(res) {
				pageData.locationInfo = {
					province: res.address ? res.address.province : "",
					city: res.address ? res.address.city : "",
					area: res.address ? res.address.district : "",
					latitude: res.latitude, // 纬度
					longitude: res.longitude, // 经度
				};
				console.log(res);
				// userStore.changeUserLocationInfo(res.longitude, res.latitude, res.address || {})
				pageData.locationStatic = 1;
			},
			fail(err) {
				console.log(err);
				pageData.locationStatic = -1;
			},
		});
	} else {
		// uni.showModal({
		// 	title: '授权提醒',
		// 	content: '获取当前位置需要定位权限，是否继续？',
		// 	success(res) {
		// 		if (res.confirm) {
		// 			permision.requestPermission('location')
		// 		}
		// 	}
		// })
	}
}

async function clickGetLocation() {
	// 定位
	let status = true;
	// #ifdef APP
	status = await permision.checkPermission("location");
	// #endif
	// #ifdef MP-WEIXIN
	try {
		const res = await uni.getSetting();

		if (res) {
			if (res.authSetting["scope.userLocation"]) {
				status = true;
			} else {
				status = false;
			}
		}
	} catch (error) {
		//TODO handle the exception
	}
	// #endif

	pageData.locationPermission = status;

	if (pageData.locationPermission) {
		loadLocation();
	} else {
		uni.showModal({
			title: "授权提醒",
			content: "获取当前位置需要定位权限，是否继续？",
			success(res) {
				if (res.confirm) {
					// #ifdef APP
					permision.requestPermission("location");
					// #endif

					// #ifdef MP-WEIXIN
					uni.authorize({
						scope: "scope.userLocation",
						success() {
							clickGetLocation();
						},
						fail() {
							uni.showModal({
								title: "授权提示",
								content: "为了提供更好的服务，请允许我们获取您的位置信息",
								success: (res) => {
									if (res.confirm) {
										// 用户点击了确认，再次尝试请求授权
										uni.openSetting();
									}
								},
							});
						},
					});
					// #endif
				}
			},
		});
	}
}

function clickCurrentLocation() {
	selectCity({
		name: pageData.locationInfo.city,
	});
}

function selectCity(item) {
	const eventChannel = instance.proxy.getOpenerEventChannel();
	eventChannel.emit("selectCity", item);
	uni.navigateBack();
}

function initHeader() {
	query
		.select(".header-box")
		.boundingClientRect((data) => {
			pageData.headerBoxHeight = data.height;
			// #ifndef APP
			pageData.headerBoxHeight += 1;
			// #endif
		})
		.exec();
}

function loadData() {
	// #ifdef APP
	loadLocation();
	// #endif

	pageData.allCityData = deepClone(cityData);

	for (let index of pageData.allCityData.keys()) {
		const item = pageData.allCityData[index];
		pageData.allCityIndex.push(item.name);
	}
}

function clearSearchKeyword() {
	pageData.searchKeyword = "";
	pageData.selectInputFocus = false;

	inputSearch();
}

function fuzzySearch(list, keyword) {
	let data = [];
	if (list.length != 0 && keyword) {
		// var reg = new RegExp(search);
		let str = `\S*${keyword}\S*`;
		let reg = new RegExp(str, "i"); //不区分大小写
		list.map((item) => {
			item.cities.map((e) => {
				if (reg.test(e.tags)) {
					data.push(e);
				}
			});
		});
	}
	return data;
}

function inputSearch() {
	submitSearch();
}

function submitSearch() {
	if (pageData.searchKeyword) {
		pageData.searchList = fuzzySearch(pageData.allCityData, pageData.searchKeyword);
	}
}

onReady(() => {
	initHeader();
});

onLoad((options) => {
	loadData();
});
</script>

<template>
	<app-layout>
		<view class="select-city">
			<view class="header-box bg-#fff">
				<view class="w-full py-10 px-30" v-if="false">
					<view class="w-full h-66 bg-#F6F6F6 border-rd-full flex items-center pl-22">
						<view class="w-40 h-40 flex flex-center">
							<app-image :src="common.iconSearchQianhui" class="w-full" size="40rpx" mode="widthFix"></app-image>
						</view>
						<view class="flex-1 ml-7 flex flex-center">
							<input v-model="pageData.searchKeyword" class="text-26 font-500 flex-1" type="text" placeholder="输入城市名进行搜索" @blur="pageData.selectInputFocus = false" @focus="pageData.selectInputFocus = true" @input="inputSearch" />
						</view>
						<view class="w-28 h-28 ml-10">
							<template v-if="pageData.searchKeyword.length > 0">
								<app-image :src="common.iconCloseHei" class="w-28" size="28rpx" mode="widthFix" @click="clearSearchKeyword"></app-image>
							</template>
						</view>
						<view class="w-120 h-56 ml-26">
							<view class="w-full h-full flex flex-center ac-op border-rd-full bg-#09C1B1">
								<view class="text-26 font-500 color-#fff">搜索</view>
							</view>
						</view>
					</view>
				</view>

				<!-- #ifdef APP -->
				<view class="w-full flex items-center p-30" v-if="!isSearch">
					<app-image :src="common.iconDingweiLan" class="w-30 h-30" mode=""></app-image>
					<view class="ml-16 text-30 font-500 text-[#2D9AFF]">
						<template v-if="pageData.locationPermission">
							<template v-if="pageData.locationStatic === -1">
								<view @click="clickGetLocation">获取失败，点击重试</view>
							</template>

							<template v-if="pageData.locationStatic === 0">
								<view>获取中...</view>
							</template>

							<template v-if="pageData.locationStatic === 1">
								<view @click="clickCurrentLocation">当前定位城市：{{ pageData.locationInfo.city }}</view>
							</template>
						</template>
						<template v-else>
							<view @click="clickGetLocation">点击获取当前位置</view>
						</template>
					</view>
				</view>
				<!-- #endif -->
			</view>

			<view class="">
				<template v-if="pageData.headerBoxHeight > 0 && !isSearch">
					<uv-index-list :index-list="pageData.allCityIndex" :customNavHeight="`${pageData.headerBoxHeight}px`" activeColor="#09C1B1">
						<template #header>
							<view class="">
								<view class="pt-50 pr-50 pb-0 pl-30">
									<view class="text-26 font-500 text-#495056">热门城市</view>
								</view>
								<view class="pt-20 pr-40 pb-30 pl-20">
									<view class="flex flex-wrap">
										<template v-for="(city, index) in pageData.hotCity" :key="index">
											<view class="w-152 h-60 bg-#F5F5F7 ac-op flex flex-center border-rd-10 mx-10 my-10" @click="selectCity(city)">
												{{ city.name }}
											</view>
										</template>
									</view>
								</view>
							</view>
						</template>
						<template v-for="(item, index) in pageData.allCityData" :key="index">
							<!-- #ifdef APP-NVUE -->
							<uv-index-anchor :text="item.name"></uv-index-anchor>
							<!-- #endif -->
							<uv-index-item>
								<!-- #ifndef APP-NVUE -->
								<uv-index-anchor :text="item.name"></uv-index-anchor>
								<!-- #endif -->
								<template v-for="(city, ind) in item.cities" :key="ind">
									<view class="list-cell" @click="selectCity(city)">
										{{ city.name }}
									</view>
								</template>
							</uv-index-item>
						</template>
					</uv-index-list>
				</template>
				<template v-if="isSearch">
					<uv-cell-group>
						<template v-for="(city, index) in pageData.searchList" :key="index">
							<uv-cell :title="city.name" @click="selectCity(city)"></uv-cell>
						</template>
					</uv-cell-group>
				</template>
			</view>
		</view>
	</app-layout>
</template>

<style scoped lang="scss">
.select-city {
}

.list-cell {
	/* #ifndef APP-PLUS */
	display: flex;
	box-sizing: border-box;
	width: 100%;
	/* #endif */
	padding: 10px 24rpx;
	overflow: hidden;
	color: #323233;
	font-size: 14px;
	line-height: 24px;
	background-color: #fff;
}

.header-box {
	position: sticky;
	top: var(--window-top);
	z-index: 9;
}
</style>
