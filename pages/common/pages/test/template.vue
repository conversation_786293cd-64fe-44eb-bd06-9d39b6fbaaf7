<template>
	<view class="template">

	</view>
</template>

<script setup>
	import {
		onLoad
	} from '@dcloudio/uni-app';

	import {
		ref,
		reactive
	} from 'vue';

	import {
		useUserStore
	} from '@/store';

	import {
		canENV
	} from '@/common/utils'

	const userStore = useUserStore()

	const pageData = reactive({})

	function loadData() {

	}

	onLoad(() => {
		loadData()
	})
</script>

<style lang="scss" scoped>
	.template {}
</style>