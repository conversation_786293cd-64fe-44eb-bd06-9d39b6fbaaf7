<script setup>
import { onLoad, onReady, onShareAppMessage, onShareTimeline, onShow, onUnload } from '@dcloudio/uni-app'
import { ref, reactive, nextTick, watch, computed } from 'vue'
import { getBannerList, getKing<PERSON>ongList, getWinnowGoodsList, changeUserData } from '@/server/api'
import { route, openWebView, goArticlePage, sliceArray, canENV, goArticlePageWithData } from '@/common/utils'
import storage from '@/common/storage'
import { locToAddress } from '@/common/amap'
import debounce from '@/uni_modules/uv-ui-tools/libs/function/debounce.js'
import swiperItemList from './components/swiper-item-list/swiper-item-list.vue'
import itemList from './components/item-list/item-list'
import CouponPopup from './components/coupon-popup/coupon-popup.vue'
import { common, home } from '@/common/images'
import { useAppStore, usePublicStore, useUserStore } from '@/store'
// #ifndef MP-WEIXIN
import permision from '@/js_sdk/wa-permission/permission'
const regionData = []
// #endif
// #ifdef MP-WEIXIN
const regionData = []
// #endif

const network = ref(true)

function detectNetwork(e) {
    network.value = e.isConnected
}

uni.getNetworkType({
    success: (res) => {
        if (res.networkType === 'none') {
            network.value = false
        } else {
            network.value = true
        }
    },
})
uni.onNetworkStatusChange(detectNetwork)

onUnload(() => {
    uni.offNetworkStatusChange(detectNetwork)
})

const userStore = useUserStore()
const appStore = useAppStore()

const pageHeight = ref(0)
const scrollable = ref(true)
const headerHeight = ref(9999)
const navHeader = ref(0)

const pagingRef = ref(null)
const tabs = ref(null)
const swiperItemListRef = ref()
const itemListRes = ref()

const searchHeight = ref(40)

const pageListSwiper = ref(false)

const pageData = reactive({
    swiperList: [],
    opacityValue: 0,
    searchTop: 88,
    searchWidth: 690,
    tabList: [
        {
            name: '家政服务',
            type: 'homemaking',
            value: 0,
        },
        {
            name: '居家护理',
            type: 'nurse',
            value: 1,
        },
        {
            name: '陪诊就医',
            type: 'attend',
            value: 2,
        },
        {
            name: '精选商城',
            type: 'shop',
            value: 3,
        },
    ],
    current: 0,

    localdata: [],

    dataList: [],
    pageLoading: false,
    dataReset: false,
})

pageData.tabList.map((item, index) => {
    pageData.tabList[index].value = Number(useAppStore().goodsServerTypeList[item.type].id || 0)
})

const kongListCurrent = ref(0)
const kongList = ref([])

function kongListSwipeChange(e) {
    kongListCurrent.value = e.detail.current
}

// 部分菜单提示敬请期待
const kongListTips = ['社区助餐', '辅具租赁', '安全防护']

function clickKongItem(item) {
    if (kongListTips.includes(item.title)) {
        uni.showToast({ title: '敬请期待', icon: 'none' })
        return
    }

    // 部分菜单跳转页面查看详情
    if (item.typeName === 'content') {
        goArticlePageWithData(item, item.id)
        return
    }
    // 快速问诊
    if (item.title === '网上医院') {
        openWebView('https://hd.guahao.com/u/33076?utm_trace=6nYAFXX-Tf&_cp=6nYAFXX-Tf', item.title || '')
        return
    }
    // 名医在线
    if (item.title === '名医在线') {
        openWebView('https://wy.guahao.com/search?tab=doctor', item.title || '')
        return
    }

    if (item.typeName === 'shop') {
        route('/pages/home/<USER>/productHome/productHome', {
            categoryId: item.categoryId || '',
        })
    }

    if (item.typeName === 'serpro') {
        route('/pages/home/<USER>/serverHome/serverHome', {
            categoryId: item.categoryId || '',
        })
    }
}

function clickSwiperItem(index) {
    const item = pageData.swiperList[index]

    if (item.skipType === 'content') {
        goArticlePage('advertising', item.id)
        return
    }

    if (item.skipType === 'link') {
        const url = String(item.skipUrl)
        if (url) {
            if (url.startsWith('http://') || url.startsWith('https://')) {
                openWebView(url, item.title || '')
            } else {
                if (userStore.checkLogin) {
                    route(url)
                } else {
                    appStore.changeShowLoginModal(true)
                }
            }
        }
    }
}

const refresherEnabled = ref(true)

function onscroll(e) {
    const scrollTop = e.detail.scrollTop

    // #ifdef MP-WEIXIN
    if (refresherEnabled.value && scrollTop > 0) refresherEnabled.value = false

    debounce(() => {
        if (scrollTop === 0 && !refresherEnabled.value) {
            refresherEnabled.value = true
        }
    }, 500)

    let opacityValue = scrollTop / 60
    if (opacityValue < 0) {
        opacityValue = 0
    }

    pageData.opacityValue = opacityValue
    if (opacityValue < 0.8) {
        pageData.searchTop = 88
        pageData.searchWidth = 690
    } else {
        pageData.searchTop = 10
        pageData.searchWidth = 520
    }
    // #endif

    // #ifndef MP-WEIXIN
    pageData.opacityValue = scrollTop / 60
    pageData.searchTop = 88 - scrollTop >= 10 ? 88 - scrollTop : 10
    // #endif

    let errorNum = 1
}

function scrolltoupper() {
    onscroll({ detail: { scrollTop: 0 } })
}

function queryList(pageNo, pageSize) {
    if (pageListSwiper.value) {
        if (swiperItemListRef.value)
            swiperItemListRef.value[pageData.current].reload(() => {
                pagingRef.value.endRefresh()
            })
    } else {
        // 加载数据
        // itemQueryList(pageNo, pageSize);
        pagingRef.value?.clear()
        pagingRef.value?.refresh()
    }
}

async function itemQueryList(pageNo, pageSize) {
    // 组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
    // 这里的pageNo和pageSize会自动计算好，直接传给服务器即可
    // 模拟请求服务器获取分页数据，请替换成自己的网络请求
    const tabItem = pageData.tabList[pageData.current]

    const params = {
        // pages: pageNo,
        current: pageNo,
        size: pageSize,
        serverGoods: tabItem.value || 0,
        choice: Number(tabItem.value === 0),
        area: tabItem.value === 0 ? '' : userStore.userData.area[2] ? userStore.userData.area.join('/') : '',
        // searchTotalStockGtZero: true
    }

    pageData.pageLoading = true

    const apiHeader = {}

    // if (params.serverGoods === 1 && userStore.checkLogin) {
    // 	apiHeader['agency-id'] = userStore.userAreaAgencyId;
    // }

    if (params.serverGoods === 1 && userStore.userAreaAgencyId) {
        apiHeader['agency-id'] = userStore.userAreaAgencyId
    }

    const res = await getWinnowGoodsList(
        {
            ...params,
        },
        {
            header: apiHeader,
        },
    ).catch((err) => {
        console.error(err)
    })

    let list = []

    if (res && res.apiStatus) {
        list = res.data.records
    }

    if (pagingRef.value) pagingRef.value.complete(list)

    pageData.dataReset = false
    pageData.pageLoading = false
}

function setScrollable(setScrollable) {
    scrollable.value = setScrollable
}

function setStickyed() {
    scrollable.value = false
    pagingRef.value.scrollToY(headerHeight.value)
}

function tabsChange(index) {
    pageData.current = index
    if (!pageListSwiper.value) {
        pageData.dataReset = true
        // pageData.dataList = [];
        // pagingRef.value?.reload();
        pagingRef.value?.clear()
        pagingRef.value?.refresh()
    }
}

function swiperTransition(e) {
    tabs.value.setDx(e.detail.dx)
}

function swiperAnimationfinish(e) {
    pageData.current = e.detail.current
    swiperItemListRef.value[pageData.current].setScrollable(!scrollable.value)
    tabs.value.unlockDx()
}

function loadData() {
    pageData.localdata = regionData

    // 获取 Banner
    getBannerList({
        current: 1,
        size: 10,
        status: 'STATUSN', // STATUSY -> 是 | STATUSN -> 否
        type: 'IbANNER', // IbANNER -> 首页轮播 | INDRE -> 首页招募图
    }).then((res) => {
        if (res.apiStatus) {
            pageData.swiperList = res.data.records
        }
    })

    // 获取首页金刚区
    getKingKongList().then((res) => {
        if (res.apiStatus) {
            const allData = [...res.data]
            kongList.value = sliceArray(allData, 12)
        }
    })
}

// 根据konglist数量，显示菜单区高度
const swiperHeightClass = computed(() => {
    if (kongList.value.length > 0) {
        if (kongList.value[0].length > 8) {
            return 'h-500'
        } else if (kongList.value[0].length > 4) {
            return 'h-320'
        } else {
            return 'h-140'
        }
    }
})
// 点击我的社区图片提示
function handleToast() {
    uni.showToast({
        title: '敬请期待',
        icon: 'none',
    })
}

const locationPopupRef = ref()

// 检测是否为iOS平台
const isIOS = computed(() => {
    // #ifdef APP
    return uni.getSystemInfoSync().osName === 'ios'
    // #endif
    // #ifndef APP
    return false
    // #endif
})

async function initUserLocation() {
    // 首先检查用户是否已经明确拒绝过定位权限
    const notShowOpenLocationPopup = storage.get('not_show_open_location_popup')

    // 如果用户已经选择不再显示定位弹窗，直接返回
    if (notShowOpenLocationPopup) {
        return
    }

    // #ifdef APP
    const appAuthorizeSetting = uni.getAppAuthorizeSetting()

    if (appAuthorizeSetting.locationAuthorized === 'not determined') {
        // 权限未确定，显示授权弹窗
        if (locationPopupRef.value) locationPopupRef.value.open()
    } else if (appAuthorizeSetting.locationAuthorized === 'denied') {
        // 用户已明确拒绝权限，设置不再显示弹窗标记
        enablePositioningNoSubmit()
    } else {
        // 权限已授权，直接获取位置
        enablePositioningSubmit()
    }
    // #endif

    // #ifdef MP-WEIXIN
    const wxAuthorizeSetting = await uni.getSetting({ withSubscriptions: false })

    if (wxAuthorizeSetting.authSetting['scope.userFuzzyLocation']) {
        // 权限已授权，直接获取位置
        enablePositioningSubmit()
    } else {
        // 权限未授权，显示授权弹窗
        if (locationPopupRef.value) locationPopupRef.value.open()
    }
    // #endif
}

function enablePositioningSubmit(notVerification = false) {
    storage.remove('not_show_open_location_popup')

    // #ifdef APP
    if (locationPopupRef.value) locationPopupRef.value.close()
    const appAuthorizeSetting = uni.getAppAuthorizeSetting()
    if (appAuthorizeSetting.locationAuthorized === 'authorized' || appAuthorizeSetting.locationAuthorized === 'not determined') {
        uni.getLocation({
            type: 'gcj02',
            geocode: true,
            success: (location) => {
                if (location.address) {
                    changeUserAddressData([location.address.province, location.address.city, location.address.district])
                } else {
                    locToAddress({
                        longitude: location.longitude,
                        latitude: location.latitude,
                    })
                        .then((res) => {
                            changeUserAddressData(res.area)
                        })
                        .catch((err) => {
                            uni.showToast({
                                icon: 'none',
                                title: '位置信息获取失败',
                            })
                        })
                }
            },
            fail: (err) => {
                uni.showToast({
                    icon: 'none',
                    title: '位置信息获取失败',
                })
            },
        })
    } else {
        if (uni.getSystemInfoSync().osName === 'android') {
            permision
                .requestAndroidPermission('android.permission.ACCESS_FINE_LOCATION')
                .then((res) => {
                    if (res === 1) {
                        enablePositioningSubmit()
                    }
                    if (res === -1) {
                        // 用户拒绝权限，设置不再显示弹窗标记
                        enablePositioningNoSubmit()
                        uni.showModal({
                            title: '权限提示',
                            content: '禁止后我们将无法为您推荐附近服务内容，是否前往设置开启？',
                            success(modalRes) {
                                if (modalRes.confirm) {
                                    uni.openAppAuthorizeSetting()
                                }
                                // 无论用户选择什么，都已经设置了不再显示弹窗的标记
                            },
                        })
                    }
                    if (res === 0) {
                        // 权限状态未确定，用户可能关闭了弹窗，也设置不再显示
                        enablePositioningNoSubmit()
                    }
                })
                .catch((err) => {
                    // 权限请求失败，设置不再显示弹窗标记
                    enablePositioningNoSubmit()
                    uni.showModal({
                        title: '权限提示',
                        content: '获取权限失败，是否前往设置开启？',
                        success(modalRes) {
                            if (modalRes.confirm) {
                                uni.openAppAuthorizeSetting()
                            }
                            // 无论用户选择什么，都已经设置了不再显示弹窗的标记
                        },
                    })
                })
        } else {
            // iOS平台处理 - 直接打开系统设置，更符合iOS原生体验
            uni.openAppAuthorizeSetting({
                success: () => {
                    // 用户从设置页面返回，重新检查权限状态
                    setTimeout(() => {
                        const newAppAuthorizeSetting = uni.getAppAuthorizeSetting()
                        if (newAppAuthorizeSetting.locationAuthorized === 'authorized') {
                            // 用户在设置中开启了权限，移除不显示弹窗的标记
                            enablePositioningSubmit()
                        } else {
                            enablePositioningNoSubmit()
                            uni.showToast({
                                icon: 'none',
                                title: '获取权限失败',
                            })
                        }
                    }, 500) // 延迟检查，确保设置已生效
                },
                fail: () => {
                    enablePositioningNoSubmit()
                    uni.showToast({
                        icon: 'none',
                        title: '打开设置失败',
                    })
                },
            })
        }
    }
    // #endif

    // #ifdef MP-WEIXIN
    if (notVerification) {
        uni.getFuzzyLocation({
            type: 'gcj02',
            success: (location) => {
                locToAddress(location)
                    .then((res) => {
                        changeUserAddressData(res.area)
                    })
                    .catch((err) => {
                        uni.showToast({
                            icon: 'none',
                            title: '位置信息获取失败',
                        })
                    })
            },
            fail: (err) => {
                canENV(() => {
                    console.log(err)
                })
                uni.showToast({
                    icon: 'none',
                    title: '位置信息获取失败',
                })
            },
        })
    } else {
        uni.authorize({
            scope: 'scope.userFuzzyLocation',
            success: (res) => {
                if (locationPopupRef.value) locationPopupRef.value.close()
                enablePositioningSubmit(true)
            },
            fail: (err) => {
                if (locationPopupRef.value) locationPopupRef.value.close()
                // 用户拒绝授权，设置不再显示弹窗标记
                enablePositioningNoSubmit()
                uni.showModal({
                    title: '提示',
                    content: '您已拒绝授权我们获取您的位置信息，拒绝后我们将无法为您推荐附近服务内容，是否前往设置手动开启？',
                    success(modalRes) {
                        if (modalRes.confirm) {
                            uni.openSetting({
                                success: (setRes) => {
                                    if (setRes.authSetting['scope.userFuzzyLocation']) {
                                        // 用户在设置中开启了权限，移除不显示弹窗的标记
                                        storage.remove('not_show_open_location_popup')
                                        enablePositioningSubmit(true)
                                    } else {
                                        uni.showToast({
                                            icon: 'none',
                                            title: '获取权限失败',
                                        })
                                    }
                                },
                                fail: (setErr) => {
                                    uni.showToast({
                                        icon: 'none',
                                        title: '获取权限失败',
                                    })
                                },
                            })
                        }
                        // 无论用户选择什么，都已经设置了不再显示弹窗的标记
                    },
                })
            },
        })
    }

    // #endif
}
function enablePositioningNoSubmit() {
    locationPopupRef.value.close()
    storage.set('not_show_open_location_popup', true, 3)
}

const locationSetModalRef = ref()
const locationSetModalArea = ref([])
function changeUserAddressData(area, immediately = false) {
    if (!Array.isArray(area)) return
    const userArea = userStore.userData.area || []
    if (JSON.stringify([area[0], area[1]]) !== JSON.stringify([userArea[0], userArea[1]])) {
        if (userArea.length === 0 || immediately) {
            storage.remove('not_show_set_location_modal')
            changeAddressData(area, false)
        } else {
            const notShowSetLocationModal = storage.get('not_show_set_location_modal')
            if (!notShowSetLocationModal) {
                locationSetModalArea.value = area
                if (locationSetModalRef.value) locationSetModalRef.value.open()
            }
        }
    }
}
function notChangeUserAddressData() {
    storage.set('not_show_set_location_modal', true, 3)
}

watch([() => pageData.swiperList, kongList], () => {
    readData()
})

function readData() {
    // #ifdef MP-WEIXIN
    let menuButtonInfo = uni.getMenuButtonBoundingClientRect()

    searchHeight.value = menuButtonInfo.height
    // #endif
    searchHeight.value = uni.upx2px(74)

    // #ifdef APP || H5 || MP-WEIXIN
    pageHeight.value = uni.getWindowInfo().windowHeight
    navHeader.value = uni.getWindowInfo().statusBarHeight + uni.upx2px(88)
    // #endif
    // #ifndef APP || H5 || MP-WEIXIN
    pageHeight.value = uni.getWindowInfo().windowHeight
    navHeader.value = uni.getWindowInfo().statusBarHeight + uni.upx2px(88)
    // #endif

    nextTick(() => {
        setTimeout(() => {
            const query = uni.createSelectorQuery()
            query
                .select('.home_content')
                .boundingClientRect((data) => {
                    if (data) headerHeight.value = data.height - navHeader.value
                })
                .exec()
        }, 500)
    })
}

function changeAddressPicker(e) {
    const area = e.detail.value.map((i) => i.text)
    changeAddressData(area)
}

function changeAddressPickerWX(e) {
    changeAddressData(e.detail.value)
}

async function changeAddressData(area, setUserInfo = true) {
    if (!userStore.checkLogin && setUserInfo) {
        useAppStore().changeShowLoginModal(true)
        return
    }

    uni.showLoading({
        title: '设置中...',
    })

    if (userStore.checkLogin) {
        changeUserData({
            ...userStore.userData,
            area: JSON.stringify(area),
        })
            .then((res) => {
                if (res.apiStatus) {
                    userStore
                        .getAllUserInfo()
                        .then((res) => {
                            uni.hideLoading()
                            if (res.apiStatus) {
                                loadData()
                                queryList()
                            }
                        })
                        .catch((err) => {
                            uni.hideLoading()
                        })
                }
            })
            .catch((err) => {
                uni.hideLoading()
            })
    } else {
        await userStore.changeUserData({
            ...userStore.userData,
            area: JSON.stringify(area),
        })
        uni.hideLoading()
        loadData()
        queryList()
    }
}

function canICallBack(cb, type = '') {
    if (userStore.checkLogin) {
        if (typeof cb === 'function') cb()
    } else {
        useAppStore().changeShowLoginModal(true)
    }
}

function connected() {
    loadData()
    queryList()
}

function rpx2px(rpx) {
    return uni.upx2px(rpx)
}

onShareAppMessage(() => {
    return {
        path: `/pages/tabBar/home/<USER>
    }
})

onShareTimeline(() => {
    return {
        query: `referral_code=${userStore.userData.inviteCode}`,
    }
})

const couponRef = ref(null)
const initCouponPopup = () => {
    nextTick(() => {
        if (couponRef.value) {
            couponRef.value.initCoupon()
        }
    })
}

const healthyBannerList = [{ image: 'https://hejiawuyou.oss-accelerate.aliyuncs.com/app/2025/7/7686b8253e4b0dd2324293048.jpg' }]
function handleHealthyBanner() {
    route({ url: '/pages/tabBar/healthy/healthy', type: 'switchTab' })
}

onReady(() => {
    // readData()
    initUserLocation()
})

onShow(() => {
    initCouponPopup()
})

onLoad(() => {
    loadData()
})
</script>
<template>
    <app-layout>
        <z-paging
            ref="pagingRef"
            v-model="pageData.dataList"
            @scroll="onscroll"
            :scrollable="scrollable"
            @query="itemQueryList"
            @scrolltoupper="scrolltoupper"
            :lower-threshold="pageListSwiper ? '1px' : ''"
            :scrollViewEnhanced="true"
            :scrollViewBounces="false"
            :refresher-enabled="refresherEnabled"
            refresher-theme-style="black"
            :refresher-only="pageListSwiper"
            use-refresher-status-bar-placeholder
            class="relative"
        >
            <template #top></template>
            <template v-if="!pageListSwiper">
                <app-navBar :bgColor="`rgba(255,255,255,${pageData.opacityValue})`" height="88rpx">
                    <template #content class="w-full">
                        <view class="relative w-full h-full flex justify-between items-center nav-box">
                            <view class="w-full h-full px-20 flex justify-between items-center">
                                <app-image
                                    class="w-217 h-44 ml-18"
                                    :src="common.logoTextBai"
                                    width="217"
                                    height="44"
                                    mode=""
                                    v-if="network"
                                ></app-image>
                                <view class="flex justify-end items-center mr-18">
                                    <view class="flex items-center">
                                        <!-- #ifndef MP-WEIXIN -->
                                        <uni-data-picker
                                            collection="opendb-city-china"
                                            field="code as value, name as text"
                                            orderby="value asc"
                                            self-field="code"
                                            parent-field="parent_code"
                                            :preload="true"
                                            :step-searh="true"
                                            popup-title="请选择省市区"
                                            @change="changeAddressPicker"
                                        >
                                            <view class="flex items-center">
                                                <text class="text-32 text-#FFFFFF font-bold">{{ userStore.userData.area[1] || '设置地区' }}</text>
                                                <app-image
                                                    class="w-20 h-20 ml-13 mr-31"
                                                    :src="common.iconLocationDownBai"
                                                    mode="widthFix"
                                                    size="20"
                                                    v-if="network"
                                                ></app-image>
                                            </view>
                                        </uni-data-picker>
                                        <!-- #endif -->
                                        <!-- #ifdef MP-WEIXIN -->
                                        <picker mode="region" @change="changeAddressPickerWX">
                                            <view class="flex items-center">
                                                <text class="text-32 text-#FFFFFF font-bold">{{ userStore.userData.area[1] || '设置地区' }}</text>
                                                <app-image
                                                    class="w-20 h-20 ml-13 mr-31"
                                                    :src="common.iconLocationDownBai"
                                                    mode="widthFix"
                                                    size="20"
                                                    v-if="network"
                                                ></app-image>
                                            </view>
                                        </picker>
                                        <!-- #endif -->
                                    </view>

                                    <app-image
                                        class="w-40 h-40"
                                        :src="common.iconCarBai"
                                        mode="widthFix"
                                        @click="canICallBack(() => route('/pages/goods/pages/shoppingCart/shoppingCart'))"
                                        v-if="network"
                                        size="40rpx"
                                    ></app-image>
                                </view>
                            </view>
                            <!-- <view class="absolute w-710 px-20 py-16"
							:style="[`top: ${pageData.searchTop}rpx`,`background: rgba(255,255,255,${pageData.opacityValue})`]"> -->
                            <view
                                class="absolute w-750 px-20 pb-10 search-box-box"
                                :style="[`top: ${pageData.searchTop}rpx`, `background: rgba(255,255,255,${pageData.opacityValue})`]"
                            >
                                <view
                                    class="flex justify-between items-center w-full h-74-n border-rd-37 bg-#fff px-8 border-solid border-1 border-#09C1B1 ac-op search-box"
                                    :style="[`width: ${pageData.searchWidth}rpx`, `height: ${searchHeight}px`]"
                                    @click="route('/pages/home/<USER>/search/search')"
                                >
                                    <view class="flex justify-start items-center">
                                        <app-image
                                            class="w-26 h-26 ml-20"
                                            :src="common.iconSearchQianhui"
                                            size="26"
                                            mode=""
                                            v-if="network"
                                        ></app-image>
                                        <text class="text-#98A2B4 text-28 font-500 ml-14">搜索</text>
                                    </view>
                                    <view class="flex justify-end items-center">
                                        <app-image class="w-42 h-42" size="42" :src="home.voiceSearchHui" mode="" v-if="network"></app-image>
                                        <view
                                            class="w-2 h-34 mx-16"
                                            style="background: linear-gradient(0deg, #ffffff 0%, #eeeced 35%, #eeeced 68%, #ffffff 100%)"
                                        ></view>
                                        <view class="w-126 h-58 bg-#00B496 border-rd-29 text-#fff text-28 font-bold flex justify-center items-center"
                                            >搜索</view
                                        >
                                    </view>
                                </view>
                            </view>
                        </view>
                    </template>
                </app-navBar>

                <app-image :src="home.homeTopBg" class="absolute w-full top-0" mode="widthFix" v-if="network"></app-image>
            </template>

            <view class="home_content relative">
                <template v-if="pageListSwiper">
                    <app-image :src="home.homeTopBg" class="absolute w-full" mode="widthFix" v-if="network"></app-image>

                    <app-navBar :bgColor="`rgba(255,255,255,${pageData.opacityValue})`" height="88rpx">
                        <template #content class="w-full">
                            <view class="relative w-full h-full flex justify-between items-center nav-box">
                                <view class="w-full h-full px-20 flex justify-between items-center">
                                    <app-image
                                        class="w-217 h-44 ml-18"
                                        :src="common.logoTextBai"
                                        width="217"
                                        height="44"
                                        mode=""
                                        v-if="network"
                                    ></app-image>
                                    <view class="flex justify-end items-center mr-18">
                                        <view class="flex items-center">
                                            <!-- #ifndef MP-WEIXIN -->
                                            <uni-data-picker
                                                collection="opendb-city-china"
                                                field="code as value, name as text"
                                                orderby="value asc"
                                                self-field="code"
                                                parent-field="parent_code"
                                                :preload="true"
                                                :step-searh="true"
                                                popup-title="请选择省市区"
                                                @change="changeAddressPicker"
                                            >
                                                <view class="flex items-center">
                                                    <text class="text-32 text-#FFFFFF font-bold">{{ userStore.userData.area[2] || '设置地区' }}</text>
                                                    <app-image
                                                        class="w-20 h-20 ml-13 mr-31"
                                                        :src="common.iconLocationDownBai"
                                                        mode="widthFix"
                                                        size="20"
                                                        v-if="network"
                                                    ></app-image>
                                                </view>
                                            </uni-data-picker>
                                            <!-- #endif -->
                                            <!-- #ifdef MP-WEIXIN -->
                                            <picker mode="region" @change="changeAddressPickerWX">
                                                <view class="flex items-center">
                                                    <text class="text-32 text-#FFFFFF font-bold">{{ userStore.userData.area[2] || '设置地区' }}</text>
                                                    <app-image
                                                        class="w-20 h-20 ml-13 mr-31"
                                                        :src="common.iconLocationDownBai"
                                                        mode="widthFix"
                                                        size="20"
                                                        v-if="network"
                                                    ></app-image>
                                                </view>
                                            </picker>
                                            <!-- #endif -->
                                        </view>

                                        <app-image
                                            class="w-40 h-40"
                                            :src="common.iconCarBai"
                                            mode="widthFix"
                                            @click="canICallBack(() => route('/pages/goods/pages/shoppingCart/shoppingCart'))"
                                            size="40rpx"
                                            v-if="network"
                                        ></app-image>
                                    </view>
                                </view>
                                <!-- <view class="absolute w-710 px-20 py-16"
								:style="[`top: ${pageData.searchTop}rpx`,`background: rgba(255,255,255,${pageData.opacityValue})`]"> -->
                                <view
                                    class="absolute w-750 px-20 pb-10"
                                    :style="[`top: ${pageData.searchTop}rpx`, `background: rgba(255,255,255,${pageData.opacityValue})`]"
                                >
                                    <view
                                        class="flex justify-between items-center w-full h-74-n border-rd-37 bg-#fff px-8 border-solid border-1 border-#09C1B1 ac-op"
                                        :style="[`width: ${pageData.searchWidth}rpx`, `height: ${searchHeight}px`]"
                                        @click="route('/pages/home/<USER>/search/search')"
                                    >
                                        <view class="flex justify-start items-center">
                                            <app-image
                                                class="w-26 h-26 ml-20"
                                                :src="common.iconSearchQianhui"
                                                size="26"
                                                mode=""
                                                v-if="network"
                                            ></app-image>
                                            <text class="text-#98A2B4 text-28 font-500 ml-14">搜索</text>
                                        </view>
                                        <view class="flex justify-end items-center">
                                            <app-image class="w-42 h-42" size="42" :src="home.voiceSearchHui" mode="" v-if="network"></app-image>
                                            <view
                                                class="w-2 h-34 mx-16"
                                                style="background: linear-gradient(0deg, #ffffff 0%, #eeeced 35%, #eeeced 68%, #ffffff 100%)"
                                            ></view>
                                            <view
                                                class="w-126 h-58 bg-#00B496 border-rd-29 text-#fff text-28 font-bold flex justify-center items-center"
                                                >搜索</view
                                            >
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </template>
                    </app-navBar>
                </template>

                <!-- banner -->
                <view class="px-20">
                    <view class="mt-110">
                        <uv-swiper
                            :list="pageData.swiperList"
                            keyName="image"
                            indicatorMode="dot"
                            height="320rpx"
                            radius="20rpx"
                            @click="clickSwiperItem"
                            indicator
                            circular
                        ></uv-swiper>
                    </view>
                </view>
                <!-- 菜单区 -->
                <template v-if="kongList.length > 0">
                    <view class="px-20 mt-20">
                        <view class="py-20 bg-#fff border-rd-20">
                            <!-- <uni-swiper-dot :current="kongListCurrent" :mode="'default '"> -->
                            <swiper
                                :class="[swiperHeightClass]"
                                :current="kongListCurrent"
                                @change="kongListSwipeChange"
                                :indicator-dots="false"
                                indicator-color="rgba(221, 221, 221, .8)"
                                indicator-active-color="rgba(64, 201, 169, .8)"
                            >
                                <template v-for="(swiperItem, index) in kongList" :key="index">
                                    <swiper-item>
                                        <view class="grid grid-cols-4 gap-y-35rpx">
                                            <template v-for="(item, ind) in swiperItem" :key="ind">
                                                <view class="flex flex-col items-center ac-op" @click="clickKongItem(item)">
                                                    <app-image :src="item.icon" size="88" mode=""></app-image>
                                                    <view class="mt-15 text-center text-#343434 text-26 h-30">
                                                        {{ item.title }}
                                                    </view>
                                                </view>
                                            </template>
                                        </view>
                                    </swiper-item>
                                </template>
                            </swiper>
                            <!-- </uni-swiper-dot> -->
                            <view class="w-full mt-20">
                                <view class="w-full flex flex-center">
                                    <template v-for="(swiperItem, index) in kongList" :key="index">
                                        <view
                                            class="w-10 h-5 border-rd-0 bg-[rgba(221,221,221,.8)] mx-5"
                                            :class="[index === kongListCurrent ? 'bg-[rgba(64,201,169,.8)] w-20' : '']"
                                        ></view>
                                    </template>
                                </view>
                            </view>
                        </view>
                    </view>
                </template>
                <!-- 健康报告 -->
                <view v-if="network" class="mx-20 mt-20">
                    <uv-swiper
                        :list="healthyBannerList"
                        keyName="image"
                        indicatorMode="dot"
                        height="160rpx"
                        radius="20rpx"
                        indicator
                        circular
                        @click="handleHealthyBanner"
                    ></uv-swiper>
                </view>
                <!-- 我的社区图片 -->
                <view class="px-20 mt-20 pb-20" @click="handleToast">
                    <app-image v-if="network" :src="home.myCommunity" height="716" mode="widthFix"></app-image>
                </view>
            </view>

            <view
                class="paging-content"
                :style="{ height: pageListSwiper ? `${pageHeight - navHeader}px` : 'auto', minHeight: `${pageHeight - navHeader}px` }"
            >
                <view
                    style="z-index: 95; position: sticky"
                    :style="{ top: pageListSwiper ? 0 : `${rpx2px(88) + usePublicStore().statusBarHeight}px` }"
                >
                    <z-tabs
                        :bar-style="{
                            background: '#198B6B',
                            borderRadius: '3rpx',
                        }"
                        :inactive-style="{
                            fontWeight: 500,
                            fontSize: '30rpx',
                        }"
                        :active-style="{
                            fontWeight: 'bold',
                            fontSize: '32rpx',
                        }"
                        ref="tabs"
                        :list="pageData.tabList"
                        :current="pageData.current"
                        :scroll-count="5"
                        bar-width="50rpx"
                        bottom-space="18rpx"
                        active-color="#292929"
                        inactive-color="#292929"
                        bar-height="5rpx"
                        @change="tabsChange"
                        :bg-color="`rgba(255,255,255,${pageData.opacityValue / 9})`"
                    ></z-tabs>
                </view>

                <template v-if="pageListSwiper">
                    <swiper class="swiper" :current="pageData.current" @transition="swiperTransition" @animationfinish="swiperAnimationfinish">
                        <swiper-item class="swiper-item" v-for="(item, index) in pageData.tabList" :key="index">
                            <swiperItemList
                                ref="swiperItemListRef"
                                :tabIndex="index"
                                :tabItem="item"
                                :currentIndex="pageData.current"
                                @setScrollable="setScrollable"
                                @setStickyed="setStickyed"
                            />
                        </swiper-item>
                    </swiper>
                </template>
                <template v-if="!pageListSwiper">
                    <item-list
                        ref="itemListRef"
                        :list="pageData.dataList"
                        :currentList="pageData.tabList"
                        :currentIndex="pageData.current"
                        :tabItem="pageData.tabList[pageData.current]"
                        v-if="!pageData.dataReset"
                    ></item-list>

                    <!-- <uv-load-more :status="'loading'" v-if="pageData.pageLoading" /> -->
                </template>
            </view>

            <my-uv-popup ref="locationPopupRef" bg-color="none">
                <view class="popup-mask flex items-center justify-center h-690">
                    <view class="app-location-box w-540 h-530 flex flex-col bg-#fff border-rd-20 relative">
                        <app-image
                            src="https://hejiawuyou.oss-accelerate.aliyuncs.com/app/2025/6/1768512985e4b0dd23c21ae7e0.png"
                            class="absolute app-location-bg w-full"
                            mode="widthFix"
                            v-if="network"
                        ></app-image>
                        <view class="app-welcome-content relative w-full flex-1 pt-190 flex flex-col">
                            <view class="app-welcome-title w-full text-40 font-bold text-center">
                                <text>定位权限未开启</text>
                            </view>
                            <view class="app-welcome-info w-full flex-1 overflow-y-auto text-26 pt-20 px-50 text-center text-#7A7A8A">
                                <text>同意授权后才能为您推荐附近的服务</text>
                            </view>
                            <view class="app-welcome-bottom flex flex-col items-center justify-between px-63 pt-50 pb-47">
                                <view class="w-full">
                                    <uv-button
                                        color="#198B6B"
                                        text="继续"
                                        class="w-full flex-center"
                                        custom-style="height: 88rpx; border-radius: 44rpx; font-weight: bold;"
                                        @click="enablePositioningSubmit()"
                                    ></uv-button>
                                </view>
                                <!-- 非iOS平台显示暂不授权选项 -->
                                <!-- #ifdef APP -->
                                <view v-if="!isIOS" class="mt-20 text-26 text-92A2AF">
                                    <text @click="enablePositioningNoSubmit">暂不授权</text>
                                </view>
                                <!-- #endif -->
                                <!-- #ifdef MP-WEIXIN -->
                                <view class="mt-20 text-26 text-92A2AF">
                                    <text @click="enablePositioningNoSubmit">暂不授权</text>
                                </view>
                                <!-- #endif -->
                            </view>
                        </view>
                    </view>
                </view>
            </my-uv-popup>

            <CouponPopup ref="couponRef"></CouponPopup>
            <my-uv-modal
                ref="locationSetModalRef"
                title="地区设置"
                confirmText="立即切换"
                cancelText="暂不切换"
                :duration="200"
                width="580rpx"
                showCancelButton
                @close="notChangeUserAddressData"
                @confirm="changeUserAddressData(locationSetModalArea, true)"
            >
                <view class="flex flex-wrap line-height-35">
                    <view class="text-24">检测到您当前已处于{{ locationSetModalArea[1] }} - {{ locationSetModalArea[2] }}，是否切换到当前地区？</view>
                </view>
            </my-uv-modal>

            <uv-no-network @connected="connected" zIndex="999"></uv-no-network>

            <template #empty>
                <view></view>
            </template>
        </z-paging>
    </app-layout>
</template>

<style>
page {
    background-color: #f0f3f7;
}
</style>

<style lang="scss" scoped>
.page-body {
    width: 100%;
    min-height: 100vh;
    background-color: #f0f3f7;
}

.home_content {
    // background-repeat: no-repeat;
    // background-size: 100% 521rpx;
}

.paging-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.swiper {
    flex: 1;
}

.app-location-box {
    .app-location-bg {
        top: -80rpx;
    }
}

:deep(.zp-paging-container-content) {
    position: relative;
}

.search-box-box {
    /* #ifdef MP-WEIXIN */
    transition: top 0.3s;
    /* #endif */
}
.search-box {
    /* #ifdef MP-WEIXIN */
    transition: width 0.3s;
    /* #endif */
}
</style>
