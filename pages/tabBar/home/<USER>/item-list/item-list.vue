<script setup>
import 'uno.css'

import { ref, reactive, nextTick, watch } from 'vue'

import { useUserStore } from '@/store'

import { getPrimaryTypeGoodsList, getTypeGoodsList, getWinnowGoodsList, searchProduct } from '@/server/api'
import { common } from '@/common/images'

const userStore = useUserStore()

const props = defineProps({
    tabIndex: {
        type: Number,
        default: 0,
    },
    tabItem: {
        type: Object,
        default: () => ({}),
    },
    currentList: {
        type: Array,
        default: () => [],
    },
    currentIndex: {
        type: Number,
        default: 0,
    },
    list: {
        type: Array,
        default: () => [],
    },
})

const emit = defineEmits(['setScrollable', 'setStickyed'])

const pageData = reactive({
    dataList: [],

    list1: [],
    list2: [],

    scrollTop: 0,
    pageLoading: false,
    firstLoaded: false,
    scrollable: false,
    stickyed: false,
    completeFunc: null,
})

const paging = ref(null)
const waterfall = ref(null)

async function queryList(pageNo, pageSize) {
    // 组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
    // 这里的pageNo和pageSize会自动计算好，直接传给服务器即可
    // 模拟请求服务器获取分页数据，请替换成自己的网络请求
    const params = {
        // pages: pageNo,
        current: pageNo,
        size: pageSize,
        serverGoods: props.tabItem || 0,
        choice: Number(props.tabItem === 0),
        area: props.tabItem === 0 ? '' : userStore.userData.area[2] ? userStore.userData.area.join('/') : '',
        // searchTotalStockGtZero: true
    }

    pageData.pageLoading = true

    const res = await getWinnowGoodsList({
        ...params,
    }).catch((err) => {
        console.error(err)
    })

    let list = []

    if (res && res.apiStatus) {
        list = res.data.records
    }

    if (paging.value) paging.value.complete(list)

    pageData.firstLoaded = true
    pageData.pageLoading = false

    if (pageData.completeFunc) {
        pageData.completeFunc()
    }

    // $request.queryList(params).then(res => {
    // 	paging.value.complete(res.data.list);
    // 	pageData.firstLoaded = true;
    // 	if (pageData.completeFunc) {
    // 		pageData.completeFunc();
    // 	}
    // }).catch(res => {
    // 	paging.value.complete(false);
    // 	if (pageData.completeFunc) {
    // 		pageData.completeFunc();
    // 	}
    // })
}

watch(
    () => props.currentIndex,
    (newVal) => {
        if (newVal === props.tabIndex) {
            if (!pageData.firstLoaded) {
                nextTick(() => {
                    setTimeout(() => {
                        if (paging.value) paging.value.reload()
                        setTimeout(() => {
                            if (pageData.stickyed) {
                                pageData.scrollable = true
                            }
                        }, 100)
                    }, 100)
                })
            }
            if (pageData.scrollTop === 0 && pageData.firstLoaded) {
                emit('setScrollable', true)
            }
        }
    },
    {
        immediate: true,
    },
)

function setScrollable(scrollable) {
    if (pageData.scrollable !== scrollable) {
        pageData.scrollable = scrollable
        pageData.stickyed = pageData.scrollable
    }
}

function reload(completeFunc) {
    pageData.completeFunc = completeFunc
    if (paging.value) paging.value.reload()
    pageData.dataList = []

    if (waterfall.value) waterfall.value.clear()

    pageData.list1 = []
    pageData.list2 = []
}

function scrolltoupper() {
    pageData.scrollTop = 0
    emit('setScrollable', true)
}

function scroll(e) {
    pageData.scrollTop = e.detail.scrollTop
    if (pageData.scrollTop > 10) {
        emit('setScrollable', false)
    }
    if (!pageData.stickyed) {
        emit('setStickyed', false)
    }
}

function touchDirectionChange(direction) {
    if (!(pageData.scrollTop === 0 && pageData.stickyed)) return
    if (direction === 'top') {
        emit('setScrollable', true)
        pageData.scrollable = false
    } else if (direction === 'bottom') {
        emit('setScrollable', false)
        pageData.scrollable = true
    }
}

function changeList(e) {
    // console.log(e);
    pageData[e.name].push(e.value)
}

function onRefresh() {
    // console.log('开始刷新');
}

watch(
    () => props.list,
    () => {
        if (props.list.length === 0) {
            pageData.list1 = []
            pageData.list2 = []
        }

        nextTick(() => {
            pageData.dataList = props.list
        })
    },
    {
        immediate: true,
    },
)

defineExpose({
    setScrollable,
    reload,
})
</script>

<template>
    <view class="content">
        <!-- 	<z-paging
			ref="paging"
			@scroll="scroll"
			v-model="pageData.dataList"
			@query="queryList"
			:watch-touch-direction-change="true"
			@touchDirectionChange="touchDirectionChange"
			:fixed="false"
			:scrollable="pageData.scrollable"
			:refresher-enabled="false"
			@scrolltoupper="scrolltoupper"
			:auto="false"
			:scrollViewEnhanced="true"
			:scrollViewBounces="false"
		> -->
        <view class="px-10 py-20 flex flex-wrap">
            <template v-for="(item, index) in pageData.dataList" :key="index">
                <view class="mb-20 w-345 mx-10">
                    <!-- 普通商城 -->
                    <template v-if="tabItem.value === 0">
                        <page-product-card-item :item="item" :index="index"></page-product-card-item>
                    </template>

                    <!-- 家政服务 -->
                    <template v-if="tabItem.value === 1">
                        <page-jiazheng-card-item :item="item" :index="index"></page-jiazheng-card-item>
                    </template>

                    <!-- 陪诊服务 -->
                    <template v-if="tabItem.value === 2">
                        <page-peizhen-card-item :item="item" :index="index"></page-peizhen-card-item>
                    </template>

                    <!-- 护理服务 -->
                    <template v-if="tabItem.value === 3">
                        <page-huli-card-item :item="item" :index="index"></page-huli-card-item>
                    </template>
                </view>
            </template>

            <template v-if="false">
                <uv-waterfall
                    ref="waterfall"
                    v-model="pageData.dataList"
                    :add-time="100"
                    :columnCount="2"
                    left-gap="0rpx"
                    right-gap="0rpx"
                    column-gap="20rpx"
                    @changeList="changeList"
                >
                    <template #list1>
                        <view>
                            <template v-for="(item, index) in pageData.list1" :key="index">
                                <view class="mb-20">
                                    <!-- 普通商城 -->
                                    <template v-if="tabItem.value === 0">
                                        <page-product-card-item :item="item" :index="index"></page-product-card-item>
                                    </template>

                                    <!-- 家政服务 -->
                                    <template v-if="tabItem.value === 1">
                                        <page-jiazheng-card-item :item="item" :index="index"></page-jiazheng-card-item>
                                    </template>

                                    <!-- 陪诊服务 -->
                                    <template v-if="tabItem.value === 2">
                                        <page-peizhen-card-item :item="item" :index="index"></page-peizhen-card-item>
                                    </template>

                                    <!-- 护理服务 -->
                                    <template v-if="tabItem.value === 3">
                                        <page-huli-card-item :item="item" :index="index"></page-huli-card-item>
                                    </template>
                                </view>
                            </template>
                        </view>
                    </template>
                    <template #list2>
                        <template v-for="(item, index) in pageData.list2" :key="index">
                            <view class="mb-20">
                                <!-- 普通商城 -->
                                <template v-if="tabItem.value === 0">
                                    <page-product-card-item :item="item" :index="index"></page-product-card-item>
                                </template>

                                <!-- 家政服务 -->
                                <template v-if="tabItem.value === 1">
                                    <page-jiazheng-card-item :item="item" :index="index"></page-jiazheng-card-item>
                                </template>

                                <!-- 陪诊服务 -->
                                <template v-if="tabItem.value === 2">
                                    <page-peizhen-card-item :item="item" :index="index"></page-peizhen-card-item>
                                </template>

                                <!-- 护理服务 -->
                                <template v-if="tabItem.value === 3">
                                    <page-huli-card-item :item="item" :index="index"></page-huli-card-item>
                                </template>
                            </view>
                        </template>
                    </template>
                </uv-waterfall>
            </template>
        </view>

        <!-- <uv-load-more :status="'loading'" v-if="pageData.pageLoading && !pageData.firstLoaded" /> -->

        <!-- <view class="item" v-for="(item,index) in dataList" :key="index">
				<view class="item-title">{{item.title}}</view>
				<view class="item-detail">{{item.detail}}</view>
				<view class="item-line"></view>
			</view> -->
        <!-- </z-paging> -->
        <template v-if="pageData.dataList.length === 0">
            <uv-empty :icon="common.empty" text="当前区域暂无商品~"></uv-empty>
        </template>
    </view>
</template>

<style lang="scss" scoped>
.content {
    height: 100%;
}

.item {
    position: relative;
    height: 150rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0rpx 30rpx;
}

.item-detail {
    padding: 5rpx 15rpx;
    border-radius: 10rpx;
    font-size: 28rpx;
    color: white;
    background-color: #007aff;
}

.item-line {
    position: absolute;
    bottom: 0rpx;
    left: 0rpx;
    height: 1px;
    width: 100%;
    background-color: #eeeeee;
}
</style>
