<template>
  <my-uv-modal ref="couponRef" :showConfirmButton="false" :showCancelButton="false" @close="closeModal"
    bgColor="#D74F37">
    <view class="w-full flex flex-col bg-#D74F37 gap-20">
      <view class="flex flex-col gap-0 items-center color-#FEF1D9 font-600 text-50 mt-30 mb-20">
        <app-image height="120" mode="heightFix" :src="home.firework" class="absolute top-4 left-20"> </app-image>
        <app-image height="120" mode="heightFix" :src="home.firework" class="absolute top-25 right-20"> </app-image>
        <view class="absolute top-25 right-20 w-[20px] h-[20px]">
          <uv-icon name="close" color="#fff" size="18" @click="close"></uv-icon>
        </view>
        🎉 欢迎加入！
        <text>
          专属新人福利已送达
        </text>
      </view>

      <template v-if="couponList.length === 1">
        <template v-for="item in couponList" :key="item.id">
          <view class="bg-#FFF1D8 text-center rounded-15 py-20 px-30 flex items-center">
            <view class="text-#EA4752">
              <text class="font-800 text-100 ">{{ item.amount }}</text> <text class="text-30">元</text>
              <view class="text-24 text-#A79B92">{{ `${item.days}天内有效` }}</view>
            </view>
            <view class="mx-30 w-0 b-r-dashed b-r-width-1px b-r-#D74F37 h-50%"></view>
            <view class="text-center flex-1">
              <view class="color-#EA4752 mb-20 text-38"> {{ item.name }}</view>
              <view class="text-24">
                {{ item.agencyId === 0 ? '平台券' : '机构券' }}
              </view>
            </view>
          </view>
        </template>

      </template>
      <template v-if="couponList.length === 2">
        <view class="flex gap-20">
          <template v-for="(item, index) in couponList" :key="item.id">
            <view class="bg-#FFF1D8 text-center rounded-15 py-20  flex-1">
              <view>
                <view class="text-#EA4752">
                  <text class="text-30">¥</text><text class="font-800 text-100 ">{{ item.amount }}</text> <text
                    class="text-30">元</text>
                </view>
                <view>
                  {{ couponTypeEnum[item.type] }}
                </view>
                <view class="text-24 text-#A79B92">{{ `${item.days}天内有效` }}</view>
              </view>
            </view>
          </template>
        </view>
      </template>

      <swiper v-if="couponList.length > 2" circular :indicator-dots="false" :autoplay="true" :display-multiple-items="2"
        :circular="true" class="coupon-swiper" next-margin="20rpx" :interval="1500" :duration="1000">
        <template v-for="(item, index) in couponList" :key="item.id">
          <swiper-item>
            <view class="bg-#FFF1D8 text-center rounded-15 py-20 mr-20">
              <view>
                <view class="text-#EA4752">
                  <text class="text-30">¥</text><text class="font-800 text-100 ">{{ item.amount }}</text> <text
                    class="text-30">元</text>
                </view>
                <view>
                  {{ couponTypeEnum[item.type] }}
                </view>
                <view class="text-24 text-#A79B92">{{ `${item.days}天内有效` }}</view>
              </view>
            </view>
          </swiper-item>
        </template>
      </swiper>

      <template v-for="item in productList" :key="item.id">
        <view class="flex w-full  items-center bg-#FFF1D8 rounded-15 py-20" @click="useCoupon">
          <view class="w-40% overflow-hidden p-10 rounded-15 ">
            <app-image height="180" mode="heightFix" :src="item.pic">
            </app-image>
          </view>
          <view class="flex-1">
            <view class="w-full font-bold text-32 mt-10 text-left mb-10">{{ item.name }}</view>
            <text class="color-#fff bg-#F79F44 rounded-8 px-5 py-2  text-24">首推商品</text>
            <view class="text-#EA4752 text-40 rounded-15 mt-20 font-800"><text class="text-30">¥</text>{{
              Math.floor(item.salePrice) }}<text class="text-30">.{{ item.salePrice -
                Math.floor(item.salePrice) }}</text></view>
          </view>
        </view>
      </template>

      <button class="bg-#F79F44 color-#FFF5E4 rounded-50 px-60 pt-5 mt-50 text-32" @click="useCoupon">
        立即使用
      </button>
      <text class="text-24 text-center color-#FCE7C8">优惠券已放入【券红包】</text>
    </view>
  </my-uv-modal>
</template>

<script setup>
import { onLoad, onReady } from "@dcloudio/uni-app";
import { ref, reactive, onMounted, getCurrentInstance } from "vue";
import { useUserStore, useAppStore } from "@/store";
import { checkIfShowedActivity, markUserGotCoupon } from "@/server/api";
import { home } from "@/common/images";


const userStore = useUserStore();
const props = defineProps();
const couponRef = ref(null);
const couponList = ref([]);
const productList = ref([]);
const instance = getCurrentInstance();
const couponWidth = ref(0);
const activityId = ref()

const open = () => {
  if (!couponRef.value) return;
  couponRef.value.open();
};
const close = () => {
  if (!couponRef.value) return;
  couponRef.value.close();
}
const closeModal = () => {
  markShowedCoupon()
}

const couponTypeEnum = reactive({
  1: "无门槛券",
  2: "无门槛券",
  3: "满赠券",
  4: "满折券",
})

const initCoupon = async () => {
  if (userStore.checkLogin && couponRef.value) {
    const shouldOpen = await checkIfGotCoupon()
    if (shouldOpen) {
      open()
    }
  }
}

const checkIfGotCoupon = async () => {
  try {
    // #ifdef MP-WEIXIN
    const visitType = 1
    // #endif
    // #ifdef APP-PLUS
    const visitType = 2
    // #endif
    // #ifdef H5
    return false
    // #endif

    const params = {
      userId: userStore.userData.userId,
      visitType
    }
    const { code, data } = await checkIfShowedActivity(params)
    if (code === 200 && data) {
      // 获取活动类型最小的活动 因为是第一个活动
      // app的活动id 和 小程序的活动id 不一样 所以只能通过这种方式
      const couponData = data.reduce((cur, item) => {
        if (item.activityType < cur.activityType || !cur.activityType) { return item }
        return cur
      }, {})
      activityId.value = couponData.activityId
      couponList.value = couponData.couponList
      productList.value = couponData.productList
      return true
    }
    return false
  } catch (error) {
    console.log(error)
  }
}

const markShowedCoupon = async () => {
  try {
    const params = {
      userId: userStore.userData.userId,
      activityId: activityId.value
    }
    const { code } = await markUserGotCoupon(params)
    if (code === 200) {
      console.log('标记成功')
    }
  } catch (error) {
    console.error(error)
  }
}

const useCoupon = () => {
  close()
  goForProduct()
}

const goForProduct = () => {
  const { id, shopId } = productList.value[0]
  uni.$uv.route("/pages/goods/pages/productDetails/productDetails", {
    productId: id,
    shopId,
  });
}

defineExpose({
  open,
  initCoupon
});
</script>
<style lang="scss" scoped>
:deep {
  .coupon-swiper {
    height: 260rpx !important;
  }

  .uvicon-close {
    color: #ffffff !important;
  }
}
</style>
