<script setup>
import { ref, reactive } from 'vue'

import { onLoad, onShow, onReady } from '@dcloudio/uni-app'

import { usePublicStore, useUserStore, useMessageStore } from '@/store'

import { getPigeonMessageShop, delMessagesChatListItem } from '@/server/api'

import { route, goPlatformCustomerService } from '@/common/utils'

import dayjs from 'dayjs'
import isToday from 'dayjs/plugin/isToday'

dayjs.extend(isToday)

const publicStore = usePublicStore()

const userStore = useUserStore()

const messageStore = useMessageStore()

const pageData = reactive({
    notificationPower: false,

    msgTypeList: [
        {
            // image: '@/static/message/icon_xitongtongzhi.png',
            image: 'https://hejiawuyou.oss-accelerate.aliyuncs.com/app/2025/4/25680b287ee4b0dd233e32d83c.png',
            name: '系统通知',
            type: 'system',
        },
        {
            // image: '@/static/message/icon_dingdantongzhi.png',
            image: 'https://hejiawuyou.oss-accelerate.aliyuncs.com/app/2025/4/25680b287ee4b0dd233e32d839.png',
            name: '订单通知',
            type: 'order',
        },
        {
            // image: '@/static/message/icon_hudongxiaoxi.png',
            image: 'https://hejiawuyou.oss-accelerate.aliyuncs.com/app/2025/4/25680b287ee4b0dd233e32d83a.png',
            name: '互动消息',
            type: 'interaction',
        },
        {
            // image: '@/static/message/icon_pingtaikefu.png',
            image: 'https://hejiawuyou.oss-accelerate.aliyuncs.com/app/2025/4/25680b287ee4b0dd233e32d83b.png',
            name: '平台客服',
            type: 'service',
        },
    ],

    msgList: [],

    msgListOptions: [{ text: '删除', style: { backgroundColor: '#f56c6c' } }],
})
const pagingRef = ref(null)

const MessageType = {
    //文本
    TEXT: 'TEXT',
    //图片
    IMAGE: 'IMAGE',
    //商品
    PRODUCT: 'PRODUCT',
    //未及时处理的消息
    UN_HANDLE: 'UN_HANDLE',
    // 订单信息
    ORDER: 'ORDER',
}

const renderMessage = (message) => {
    if (!message) return ''
    switch (message.messageType) {
        case MessageType.UN_HANDLE:
            return '[未处理消息]'
        case MessageType.PRODUCT:
            return '[商品]'
        case MessageType.IMAGE:
            return '[图片]'
        case MessageType.ORDER:
            return '[订单]'
        default:
            return message.message
    }
}

function getMsgTime(itme) {
    if (!itme) return ''

    const dateDay = dayjs(itme)

    return dateDay.isToday() ? dateDay.format('HH:mm') : dateDay.format('YYYY/MM/DD HH:mm')
}

function queryList(pageNo, pageSize) {
    const params = {
        pageNo: pageNo,
        pageSize: pageSize,
    }

    if (userStore.checkLogin) {
        getPigeonMessageShop({
            pages: 1,
            current: 1,
            size: 999,
            userId: userStore.userData.userId,
            chatWithType: 'CONSUMER',
        })
            .then((res) => {
                if (res.apiStatus) {
                    pagingRef.value.complete(res.data.records)
                }
            })
            .catch((err) => {
                if (pagingRef.value) pagingRef.value.complete([])
            })
    } else {
        if (pagingRef.value) pagingRef.value.complete([])
    }
}

function closeTop() {
    pageData.notificationPower = true
}

function clickGetLocation() {}

function openMessage(item) {
    if (item.type === 'service') {
        goPlatformCustomerService()
    } else {
        route('/pages/message/pages/system/system')
    }
}

function openShopMessage(item) {
    messageStore.setImUserInfo({
        ...item.chatWithShopInfo,
    })
    route('/pages/message/pages/im/im')
}

// 点击滑动单元格操作
function clickAction(e, item) {
    if (e.index === 0) {
        uni.showModal({
            title: '请确认',
            content: `是否删除与${item?.chatWithShopInfo?.shopName || '此商家'}的咨询？`,
            success: async ({ confirm }) => {
                if (confirm) {
                    delMessagesChatListItem({
                        toId: item.chatWithShopInfo.shopId,
                    }).then((res) => {
                        if (res.apiStatus) {
                            if (pagingRef.value) pagingRef.value.reload()
                        }
                    })
                }
            },
        })
    }
}

onShow(() => {
    if (pagingRef.value) pagingRef.value.reload()
})

uni.$on('receivedIMMessage', (connectType, msg) => {
    if (connectType === 'SUCCESS' || connectType === 'SUBSCRIBE') {
        if (pagingRef.value) pagingRef.value.reload()
    }
})
</script>
<template>
    <app-layout>
        <z-paging ref="pagingRef" v-model="pageData.msgList" @query="queryList" :show-loading-more-no-more-view="false">
            <template #top>
                <app-navBar :back="true">
                    <template #content>
                        <view class="w-full h-full flex items-center px-36">
                            <text class="text-38 text-#000 font-bold">消息</text>
                        </view>
                    </template>
                </app-navBar>

                <uv-gap height="8" bgColor="#F2F4F5"></uv-gap>
                <view class="" v-if="!pageData.notificationPower && false">
                    <view class="flex justify-between items-center h-72 pl-36 pr-30">
                        <view class="flex flex--justify items-center">
                            <app-image :src="common.iconCloseHui" class="w-20 h-20 mr-18" size="20" @click="closeTop"></app-image>
                            <view class="text-24 text-[#192027]">开启系统通知，重要消息不错过</view>
                        </view>
                        <uv-button
                            color="#00B496"
                            text="去开启"
                            class="w-106 flex-center"
                            @click="clickGetLocation"
                            custom-style="height: 44rpx; border-radius: 44rpx"
                            customTextStyle="font-size: 24rpx; font-weight: bold;"
                        ></uv-button>
                    </view>
                    <uv-gap height="8" bgColor="#F2F4F5"></uv-gap>
                </view>
            </template>

            <view class="w-full">
                <view class="flex items-center justify-between pt-38 pb-50" v-if="false">
                    <template v-for="item in pageData.msgTypeList">
                        <view class="flex flex-col flex-center items-center w-full pl-32" @click="openMessage(item)">
                            <app-image :src="item.image" class="w-94 w-94" size="94rpx" mode=""></app-image>
                            <view class="flex-1 pt-26">
                                <view class="w-full flex justify-between items-center">
                                    <view class="text-26 text-[#131314] font-bold">
                                        {{ item.name }}
                                    </view>
                                </view>
                            </view>
                        </view>
                    </template>
                </view>

                <view class="pl-0">
                    <uv-swipe-action>
                        <template v-for="(item, index) in pageData.msgList" :key="item.chatWithShopInfo.shopId">
                            <uv-swipe-action-item
                                @click="clickAction($event, item)"
                                :name="item.chatWithShopInfo.shopId"
                                :options="pageData.msgListOptions"
                            >
                                <view class="flex justify-start items-center py-0 pl-32" @click="openShopMessage(item)">
                                    <app-image :src="item.chatWithShopInfo.shopLogo" size="100" rd="20" mode=""></app-image>
                                    <view class="flex-1 pr-32 border-b-solid border-#EDEEF0 border-1 ml-10 py-30">
                                        <view class="flex justify-between items-center">
                                            <view class="text-32 text-#101010 font-bold">{{ item.chatWithShopInfo.shopName }}</view>
                                            <view class="text-24 text-#888888 font-500" v-if="item.lastMessage">
                                                {{ getMsgTime(item.lastMessage.sendTime) }}
                                            </view>
                                        </view>
                                        <view class="flex justify-between items-center mt-10" v-if="item.lastMessage">
                                            <view class="text-28 text-#616161 font-500 flex items-center">
                                                <!-- <template v-if="item.lastMessage.sender.senderType !== 'CONSUMER'">
													<template v-if="item.lastMessage.read">
														<view class="text-28 text-#616161 font-500">[已读]</view>
													</template>
													<template v-else>
														<view class="text-#EA5B69">[未读]</view>
													</template>
												</template> -->

                                                <view class="ml-0">{{ renderMessage(item.lastMessage) }}</view>
                                            </view>
                                            <view class="">
                                                <template v-if="item.lastMessage.sender.senderType !== 'CONSUMER'">
                                                    <template v-if="!item.lastMessage.read">
                                                        <uv-badge
                                                            type="error"
                                                            max="99"
                                                            :value="1"
                                                            isDot
                                                            :customStyle="{ minWidth: '20rpx', minHeight: '20rpx' }"
                                                        ></uv-badge>
                                                    </template>
                                                </template>
                                            </view>
                                        </view>
                                        <view class="flex justify-between items-center mt-10" v-else>
                                            <view class="text-28 text-#616161 font-500 flex items-center">
                                                <view class="ml-0" style="color: transparent">暂无消息</view>
                                            </view>
                                            <view class="">
                                                <!-- <template v-if="item.lastMessage.read"></template>
												<template v-else>
													<uv-badge type="error" max="99" :value="1" isDot></uv-badge>
												</template> -->
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </uv-swipe-action-item>
                        </template>
                    </uv-swipe-action>
                </view>
            </view>
        </z-paging>
    </app-layout>
</template>
<style lang="scss" scoped></style>
