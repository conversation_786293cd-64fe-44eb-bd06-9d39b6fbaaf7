<!-- 资讯 -->
<script setup>
import { ref, computed, nextTick } from 'vue'
import { onLoad, onReady, onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
import { common, information } from '@/common/images'

import { getCategoryList, getArticleInformationList, getKingKongList, getBannerList } from '@/server/api'
import { useUserStore, usePublicStore } from '@/store'
import { useBannerNavigation, useMenuNavigation } from '@/common/useNavigation'
import swiperItemList from './components/swiper-item-list/swiper-item-list.vue'
import PageInfoSharePopup from '@/components/page-components/page-info-share-popup/page-info-share-popup.vue'

const pageRef = ref(null)
const userStore = useUserStore()
const shareActionRef = ref(null)
const currentArticle = ref(null)

// 使用导航组合式函数
const { handleBannerClick } = useBannerNavigation()
const { handleMenuClick } = useMenuNavigation()

// 页面高度和滚动相关
const pageHeight = ref(0)
const navHeader = ref(0)

const tabs = ref([])
const currentArticleList = ref([]) // 当前激活tab的数据列表

const activeStyle = {
    color: '#292929',
    fontSize: '32rpx',
    fontWeight: 'bold',
}
const inactiveStyle = {
    color: '#fff',
    fontSize: '28rpx',
    fontWeight: '500',
}
const logoHeight = ref(0)

// 默认分享配置
const defaultShareData = {
    title: '健康资讯',
    path: '/pages/tabBar/information/information',
    imageUrl: 'https://hejiawuyou.oss-cn-shanghai.aliyuncs.com/wxapp/imgzip/logo.png',
    summary: '发现更多健康资讯，关注您的健康生活',
}

function getMenuButtonHeight() {
    // #ifdef MP-WEIXIN
    let menuButtonInfo = uni.getMenuButtonBoundingClientRect()

    logoHeight.value = menuButtonInfo.height
    // #endif
    logoHeight.value = uni.upx2px(88)
}

const tabsRef = ref(null)
const tabCurrent = ref(0)

const tabsChange = (index) => {
    if (tabCurrent.value === index) return
    tabCurrent.value = index
    currentArticle.value = null

    // 切换tab时重新加载数据，使用refreshToPage(1)避免位置重置
    pageRef.value?.refreshToPage(1)
}

// z-paging查询方法
const queryList = async (pageNo, pageSize) => {
    const currentTab = tabs.value[tabCurrent.value]
    if (!currentTab) {
        pageRef.value?.completeByError()
        return
    }

    try {
        const userId = userStore.userData.userId
        const tabId = currentTab.id

        // 如果categoryId是fixed就查询推荐的
        const params = tabId === 'fixed' ? { pageNo, pageSize, strategy: 1, userId } : { pageNo, pageSize, categoryId: tabId, userId }

        const { data, code } = await getArticleInformationList(params)

        if (code === 200 && data) {
            pageRef.value?.complete(data.list)
        } else {
            pageRef.value?.completeByError()
        }
    } catch (error) {
        console.error('获取资讯列表失败:', error)
        pageRef.value?.completeByError()
    }
}

// 获取tab列表
const fetchTabs = async () => {
    try {
        const { data, code } = await getCategoryList()
        if (code === 200 && data) {
            tabs.value = [
                {
                    categoryName: '推荐',
                    id: 'fixed',
                },
            ].concat(data.list)
        }
        nextTick(() => {
            pageRef.value?.refresh()
        })
    } catch (error) {
        console.error('获取资讯类目失败：', error)
    }
}

// 当前分享数据
const currentShareData = computed(() => {
    if (currentArticle.value) {
        return {
            title: currentArticle.value.title,
            path: `/pages/information/pages/infoDetail/infoDetail?id=${currentArticle.value.id}`,
            imageUrl: currentArticle.value.cover,
            summary: currentArticle.value.title || '健康生活，从这里开始',
        }
    }
    return defaultShareData
})

// 处理打开分享弹窗
const handleOpenShare = (item) => {
    currentArticle.value = item
    shareActionRef.value.open()
}

// 计算页面高度
function readData() {
    // #ifdef H5
    const height = uni.upx2px(104)
    // #endif
    // #ifndef H5
    const height = uni.upx2px(94)
    // #endif
    pageHeight.value = uni.getWindowInfo().windowHeight
    navHeader.value = uni.getWindowInfo().statusBarHeight + height
}

// 举报功能
const handleReport = () => {
    if (currentArticle.value) {
        uni.navigateTo({
            url: `/pages/information/pages/infoReport/infoReport?id=${currentArticle.value.id}`,
        })
    }
}

// 轮播图列表
const bannerList = ref([])
//轮播图
const initBanner = async () => {
    try {
        const { data } = await getBannerList({
            current: 1,
            size: 10,
            status: 'STATUSN', // STATUSY -> 是 | STATUSN -> 否
            type: 'AMUSEMENT', // IbANNER -> 首页轮播 | INDRE -> 首页招募图
        })
        bannerList.value = data.records
    } catch (error) {
        console.log(error)
    }
}

// 菜单区
const menuList = ref([])

// 获取菜单区
async function getMenuList() {
    try {
        const { data } = await getKingKongList({ pageType: 3 })
        menuList.value = data
    } catch (error) {
        console.error('获取菜单区列表失败:', error)
    }
}

// 菜单项点击处理
function clickKongItem(item) {
    handleMenuClick(item)
}

// 轮播图点击处理
function clickSwiperItem(index) {
    handleBannerClick(bannerList.value, index)
}

// tas吸附高度
let tabsStickyHeight = 0
// #ifdef H5
tabsStickyHeight = usePublicStore().statusBarHeight
// #endif

onLoad(() => {
    fetchTabs()
    getMenuList()
    initBanner()
})

onReady(() => {
    getMenuButtonHeight()
    readData()

    // 小程序分享配置
    onShareAppMessage(() => {
        const shareData = currentShareData.value
        return {
            title: shareData.title,
            path: shareData.path,
            imageUrl: shareData.imageUrl,
        }
    })

    onShareTimeline(() => {
        const shareData = currentShareData.value
        return {
            title: shareData.title,
            query: currentArticle.value ? `id=${currentArticle.value.id}` : '',
            imageUrl: shareData.imageUrl,
        }
    })
})
</script>

<template>
    <app-layout>
        <z-paging ref="pageRef" v-model="currentArticleList" @query="queryList" :lower-threshold="50" refresher-theme-style="black" :auto="false">
            <template #top>
                <view class="bg-#00C8A7">
                    <app-navBar bgColor="transparent" :isBack="false" :fixed="true" height="99rpx">
                        <template #content class="w-full">
                            <view class="relative w-full h-full flex justify-between items-center nav-box" :style="`height: ${logoHeight}px`">
                                <view class="w-full h-full px-20 flex justify-between items-center h-44">
                                    <app-image class="w-217 h-44 ml-18" :src="common.logoTextBai" width="217" height="44" mode=""></app-image>
                                </view>
                            </view>
                        </template>
                    </app-navBar>
                </view>
            </template>

            <!-- banner -->
            <view class="px-20 mt-20">
                <uv-swiper
                    :list="bannerList"
                    keyName="image"
                    indicatorMode="dot"
                    height="320rpx"
                    radius="20rpx"
                    indicator
                    circular
                    @click="clickSwiperItem"
                ></uv-swiper>
            </view>
            <!-- 菜单区 -->
            <template v-if="menuList.length > 0">
                <view class="px-20 my-20">
                    <view class="py-20 bg-#fff border-rd-20">
                        <swiper
                            class="h-140"
                            :indicator-dots="false"
                            indicator-color="rgba(221, 221, 221, .8)"
                            indicator-active-color="rgba(64, 201, 169, .8)"
                        >
                            <swiper-item>
                                <view class="grid grid-cols-4 gap-y-35rpx">
                                    <view
                                        class="flex flex-col items-center ac-op"
                                        v-for="item in menuList"
                                        :key="item.id"
                                        @click="clickKongItem(item)"
                                    >
                                        <app-image :src="item.icon" size="88" mode=""></app-image>
                                        <view class="mt-15 text-center text-#343434 text-26 h-30">
                                            {{ item.title }}
                                        </view>
                                    </view>
                                </view>
                            </swiper-item>
                        </swiper>
                    </view>
                </view>
            </template>
            <!-- 分页内容区域 -->
            <view class="paging-content" :style="{ minHeight: `${pageHeight - navHeader}px` }">
                <!-- tabs吸附区域 -->
                <view
                    id="tabs-container"
                    class="header-color sticky-tabs"
                    style="z-index: 95; position: sticky"
                    :style="{ top: `${tabsStickyHeight}px` }"
                >
                    <z-tabs
                        :list="tabs"
                        name-key="categoryName"
                        value-key="id"
                        :current="tabCurrent"
                        @change="tabsChange"
                        lineColor="#198B6B"
                        bgColor="transparent"
                        activeColor="#198B6B"
                        lineWidth="50rpx"
                        barWidth="50rpx"
                        bar-height="5rpx"
                        bottom-space="18rpx"
                        :scroll-count="5"
                        :activeStyle="activeStyle"
                        :inactiveStyle="inactiveStyle"
                        :tabs-style="{
                            border: 'none',
                            height: '90rpx',
                        }"
                        ref="tabsRef"
                    >
                    </z-tabs>
                </view>

                <!-- 列表内容 -->
                <template v-if="currentArticleList.length === 0">
                    <uv-empty :icon="common.empty" text="暂无数据"></uv-empty>
                </template>
                <template v-else>
                    <view class="bg-#F0F3F7 w-full flex-1 px-20">
                        <swiperItemList :list="currentArticleList" @openShare="handleOpenShare" />
                    </view>
                </template>
            </view>
            <template #empty> </template>
        </z-paging>
        <!-- 分享弹窗 -->
        <PageInfoSharePopup ref="shareActionRef" :shareData="currentShareData" @handleReport="handleReport" />
    </app-layout>
</template>

<style>
page {
    background-color: #f0f3f7;
}
</style>
<style lang="scss" scoped>
.divider-line {
    background: linear-gradient(0deg, #ffffff 0%, #eeeced 35%, #eeeced 68%, #ffffff 100%);
}

.header-color {
    background: linear-gradient(to bottom, #00c8a7 2%, #92d6ca 100%);
}

.sticky-tabs {
    transition: background 0.3s ease;
    will-change: transform;
    transform: translateZ(0);
}

.paging-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}
</style>
