<template>
    <view class="w-full px-20 pt-40 pb-30 bg-#fff mt-20 b-rd-10" v-for="item in props.list" :key="item.id">
        <view class="flex gap-36 mb-20" @click="goDetail(item.id)">
            <text class="flex-1 font-500 color-#222222 text-32 lh-6">{{ item.title }}</text>
            <app-image :src="item.cover" size="220" height="166" :imageClass="'border-rd-12'"></app-image>
        </view>
        <view class="flex justify-between">
            <view class="flex gap-15 text-26 color-#777">
                <text>{{ item.categoryName }}</text>
                <text>{{ getRelativeTime(item.updateTime) }}</text>
                <text>{{ salesVolumeToStr(item.views) }}阅读</text>
                <text>{{ salesVolumeToStr(item.thumbs) }}赞</text>
            </view>
            <app-image class="w-26 h-26 ml-20" :src="information.more" size="26" mode="" @click.stop="openPopup(item)"> </app-image>
        </view>
    </view>
</template>

<script setup>
import { information } from '@/common/images'
import { getRelativeTime } from '@/utils'
import { salesVolumeToStr } from '@/common/utils'

const props = defineProps({
    list: {
        type: Array,
        default: () => [],
    },
})

const emit = defineEmits(['openShare'])

const goDetail = (id) => {
    uni.navigateTo({
        url: `/pages/information/pages/infoDetail/infoDetail?id=${id}`,
    })
}

const openPopup = (item) => {
    emit('openShare', item)
}
</script>
