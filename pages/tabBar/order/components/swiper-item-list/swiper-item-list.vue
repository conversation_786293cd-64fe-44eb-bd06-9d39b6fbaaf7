<script setup>
import { reactive, ref, watch, nextTick, computed, unref } from 'vue';

import dayjs from 'dayjs';

import {
	getOrder,
	getServerOrder,
	closeOrderByOrderNo,
	putOrderReceiver,
	getUserBalance,
	getOrderPayPage,
	getOrderPayPageAgain,
	getOrderIsPaySuccess,
	confirmGoods,
	getAfsOrder
} from '@/server/api';

import { useUserStore } from '@/store';

import { ORDERPAYMENT, PAY_TYPE } from '@/common/types/goods';

import { getPriceInfo, route, canENV, goLogin } from '@/common/utils';

import { deepClone, sleep } from '@/uni_modules/uv-ui-tools/libs/function/index.js';

import orderCardItemGoods from '../order-card-item-goods/order-card-item-goods.vue';

import orderCardItemGoodsAfter from '../order-card-item-goods-after/order-card-item-goods-after.vue';

import orderCardItemServer from '../order-card-item-server/order-card-item-server.vue';

import orderCardItemServerAfter from '../order-card-item-server-after/order-card-item-server-after.vue';

const $props = defineProps({
	index: {
		type: Number,
		default: 0
	},
	item: {
		type: Object,
		default: () => ({})
	},
	tabIndex: {
		type: Number,
		default: 0
	},
	tabList: {
		type: Array,
		default: () => []
	},
	typeIndex: {
		type: Number,
		default: 0
	},
	typeList: {
		type: Array,
		default: () => []
	}
});

const compIndex = computed(() => $props.index);

const emptySlotName = ref('');

const goodsOrder = computed(() => {
	const typeItem = $props.typeList[$props.typeIndex];
	if (typeItem) {
		return typeItem.id === '0';
	}
	return true;
});

const $data = reactive({
	dataList: []
});

const paging = ref();
const firstLoaded = ref(false);

const paySelectRef = ref();
const showPay = ref(false);
const payLoading = ref(false);
const payInfo = reactive({
	price: 0,
	balanceTotalShow: true,
	balanceTotal: 0,
	timeout: 0
});
const payOrderType = ref('order');

const orderNumber = ref('');
const payExtra = ref();
const payFrom = ref(PAY_TYPE.ORDER);

async function initUserBalance() {
	const { code, msg, data, apiStatus } = await getUserBalance();
	payInfo.balanceTotalShow = apiStatus;
	payInfo.balanceTotal = apiStatus ? `${getPriceInfo(data).integer}.${getPriceInfo(data).decimalText}` : '';
}

// 取消订单
function cancelOrder(order) {
	uni.showModal({
		title: '请确认',
		content: '是否需要取消？',
		success: async ({ confirm }) => {
			if (!confirm) return;
			const { code, data, msg, apiStatus } = await closeOrderByOrderNo({ orderNo: order.no });
			// if (!apiStatus) return uni.showToast({ icon: 'none', title: `${msg ? msg : '取消订单失败'}` });
			if (!apiStatus) return;
			uni.showToast({ icon: 'none', title: `订单已取消` });
			reloadList();
		}
	});
}

// 更换地址
function changeOrderAddress(order) {
	route({
		type: 'to',
		url: '/pages/my/pages/address/addressGoods',
		params: {
			select: 1
		},
		events: {
			async selectAddress(data) {
				await sleep(150);
				uni.showModal({
					title: '请确认',
					content: `是否更改地址为${data.address}？`,
					success: async ({ confirm }) => {
						if (!confirm) return;
						const { apiStatus } = await putOrderReceiver({ orderNo: order.no, ...data });
						// if (!apiStatus) return uni.showToast({ icon: 'none', title: `${msg ? msg : '修改失败'}` });
						if (!apiStatus) return;
						uni.showToast({ icon: 'none', title: `修改成功` });
						reloadList();
					}
				});
			}
		}
	});
}

async function paySubmit(e) {
	if (!goodsOrder.value) {
		// #ifdef  MP-WEIXIN
		// 调起客户端小程序订阅消息界面，返回用户订阅消息的操作结果。当用户勾选了订阅面板中的“总是保持以上选择，不再询问”时，模板消息会被添加到用户的小程序设置页。
		try {
			const res = await uni.requestSubscribeMessage({
				tmplIds: [
					// 'HA8G6APZuXamC-W8WUeFJNyEGaSH8og0Oxb38MprqiQ', // 发货
					// 'Yjtuq-FfzGWD9SR_XNWQWIhEXW34TVXAJABa-aHavzo' // 完成

					// 'IjhUqEhCGqqpYQ6i_8suidaGqNYrwHMD5Nk-fl7VTac', // 订单确认

					'IjhUqEhCGqqpYQ6i_8suieMstG7SoJAOXp3LHnGA9_Y', // 价格变化
					'fS1bkoRJpogM86SxOte7XZGN_YSWYqg4QeY_opB0LUA' // 分配人员
				]
			});
		} catch (error) {
			//TODO handle the exception
			console.log('订阅失败 => ', error);
		}
		// #endif
	}

	if (payOrderType.value === 'subOrder') {
		paySubmitAgain(e);
		return;
	}

	if (e.type === 'balance') {
		const { code, data, msg, apiStatus } = await getOrderPayPage({
			orderNo: unref(orderNumber),
			payType: ORDERPAYMENT.BALANCE,
			rebate: false
		}).catch((e) => {
			payLoading.value = false;
		});

		if (!apiStatus) {
			payError(unref(orderNumber), payFrom.value);

			return;
		}

		orderNumber.value = data.outTradeNo;
		loopCheckPayStatus();
	}

	if (e.type === 'wechat') {
		const payRes = await getOrderPayPage({
			orderNo: unref(orderNumber),
			payType: ORDERPAYMENT.WECHAT,
			rebate: false
		}).catch((e) => {
			payLoading.value = false;
		});

		canENV(() => {
			console.log(payRes);
		});

		if (!payRes || !payRes.apiStatus) {
			payError(unref(orderNumber), payFrom.value);
			return;
		}

		orderNumber.value = payRes.data.outTradeNo;

		requestPayment(e.payItem.payment, payRes.data.data);
	}

	if (e.type === 'alipay') {
		const payRes = await getOrderPayPage({
			orderNo: unref(orderNumber),
			payType: ORDERPAYMENT.ALIPAY,
			rebate: false
		}).catch((e) => {
			payLoading.value = false;
		});

		canENV(() => {
			console.log(payRes);
		});

		if (!payRes || !payRes.apiStatus) {
			payError(unref(orderNumber), payFrom.value);
			return;
		}

		orderNumber.value = payRes.data.outTradeNo;

		requestPayment(e.payItem.payment, payRes.data.data);
	}
}

/**
 * uni requestPayment 支付
 * @param {*} data
 */
function requestPayment(provider, data) {
	uni.requestPayment({
		provider,
		orderInfo: data,
		...data,
		success: () => {
			loopCheckPayStatus();
		},
		fail: () => {
			closeLoopCheck(false, true);
		}
	});
}

let time = null;

/**
 * 循环检查支付状态
 */
function loopCheckPayStatus() {
	uni.showLoading({
		title: '支付校验中...',
		mask: true
	});
	let count = 1;
	checkPayStatus(false);
	try {
		//先清除一次定时器
		time && clearInterval(time);
		// 开启轮训
		time = setInterval(() => checkPayStatus(++count > 10), 800);
	} catch (error) {
		uni.hideLoading();
		time && clearInterval(time);
	}
	// payBtnLoading.value = false;
}

/**
 * 定时器循环体
 */
function checkPayStatus(skipLoop) {
	if (skipLoop) {
		return closeLoopCheck(false, true);
	}
	getOrderIsPaySuccess({
		outTradeNo: unref(orderNumber)
	}).then((res) => {
		if (res.apiStatus) {
			closeLoopCheck(true, true);
		}
	});
}

/**
 * 取消轮训检查
 */
function closeLoopCheck(state = false, isPayStatus = false) {
	time && clearInterval(time);
	uni.hideLoading();
	if (isPayStatus) {
		if (state) {
			paySuccess(unref(orderNumber), payFrom.value);
		} else {
			payError(unref(orderNumber), payFrom.value);
		}
	} else {
		payLoading.value = false;
		showPay.value = false;
		// reloadList();
	}
}

async function paySuccess(orderType, payFrom) {
	// uni.hideLoading();
	payLoading.value = false;
	showPay.value = false;
	uni.showToast({
		icon: 'success',
		title: '支付成功',
		duration: 1000,
		mask: true
	});

	reloadList();
}

async function payError(orderType, payFrom) {
	// uni.hideLoading();
	payLoading.value = false;
	showPay.value = false;
	uni.showToast({
		icon: 'error',
		title: '支付失败',
		duration: 1500,
		mask: true
	});

	reloadList();
}

function paySelectChange(e) {
	if (!e.show) {
		closeLoopCheck(false, false);
	}
}

// 再次支付
async function openPay(order) {
	uni.showLoading({
		title: '加载中...'
	});

	orderNumber.value = order.no;

	await initUserBalance();
	payInfo.timeout = order.timeout.payTimeout * 1000 - (dayjs().valueOf() - dayjs(order.createTime).valueOf());
	payInfo.price = `${getPriceInfo(order.orderPayment.payAmount).integer}.${getPriceInfo(order.orderPayment.payAmount).decimalText}`;
	payOrderType.value = 'order';
	showPay.value = true;
	uni.hideLoading();
}

// 子订单支付
async function openSubOrderPay(order) {
	uni.showLoading({
		title: '加载中...'
	});

	orderNumber.value = order.no;

	if (Number(order.payment.payAmount) === 0) {
		paySubmit({ type: 'balance' });
	} else {
		await initUserBalance();
		payInfo.timeout = 0;
		payInfo.price = `${getPriceInfo(order.payment.payAmount).integer}.${getPriceInfo(order.payment.payAmount).decimalText}`;
		payOrderType.value = 'subOrder';
		showPay.value = true;
	}

	uni.hideLoading();
}
async function paySubmitAgain(e) {
	if (e.type === 'balance') {
		const { code, data, msg, apiStatus } = await getOrderPayPageAgain({
			orderNo: unref(orderNumber),
			payType: ORDERPAYMENT.BALANCE,
			rebate: false
		}).catch((e) => {
			payLoading.value = false;
		});

		if (!apiStatus) {
			payError(unref(orderNumber), payFrom.value);

			return;
		}

		orderNumber.value = data.outTradeNo;
		loopCheckPayStatus();
	}

	if (e.type === 'wechat') {
		const payRes = await getOrderPayPageAgain({
			orderNo: unref(orderNumber),
			payType: ORDERPAYMENT.WECHAT,
			rebate: false
		}).catch((e) => {
			payLoading.value = false;
		});

		canENV(() => {
			console.log(payRes);
		});

		if (!payRes || !payRes.apiStatus) {
			payError(unref(orderNumber), payFrom.value);
			return;
		}

		orderNumber.value = payRes.data.outTradeNo;

		requestPayment(e.payItem.payment, payRes.data.data);
	}

	if (e.type === 'alipay') {
		const payRes = await getOrderPayPageAgain({
			orderNo: unref(orderNumber),
			payType: ORDERPAYMENT.ALIPAY,
			rebate: false
		}).catch((e) => {
			payLoading.value = false;
		});

		canENV(() => {
			console.log(payRes);
		});

		if (!payRes || !payRes.apiStatus) {
			payError(unref(orderNumber), payFrom.value);
			return;
		}

		orderNumber.value = payRes.data.outTradeNo;

		requestPayment(e.payItem.payment, payRes.data.data);
	}
}

async function queryList(pageNo, pageSize) {
	const params = {
		page: pageNo,
		current: pageNo,
		limit: pageSize
	};

	if (!useUserStore().checkLogin) {
		// emptySlotName.value = 'empty';
		paging.value.complete(true);
		return;
	}

	const typeItem = $props.typeList[$props.typeIndex];

	let list = [];
	const status = true;

	if (goodsOrder.value) {
		const state = $props.item.goodsStateValue;
		if (state !== 'AFTER_SALE') {
			params.status = state;
			const res = await getOrder({ ...params }).catch((err) => {});
			if (res && res.apiStatus) {
				list = res.data.records.map((i) => {
					return {
						...i,
						typeItem,
						listOrderState: state,
						goodsOrder: goodsOrder.value
					};
				});
			} else {
				status = false;
			}
		} else {
			params.serveGoods = 0;
			const res = await getAfsOrder({ ...params }).catch((err) => {});
			if (res && res.apiStatus) {
				list = res.data.records.map((i) => {
					return {
						...i,
						typeItem,
						listOrderState: state,
						goodsOrder: goodsOrder.value
					};
				});
			} else {
				status = false;
			}
		}
	} else {
		const state = $props.item.serveStateValue;
		const typeItem = $props.typeList[$props.typeIndex];
		if (state !== 'SERVER_BAD') {
			params.status = state;
			params.serveTypeId = typeItem.id;
			const res = await getServerOrder({ ...params }).catch((err) => {});
			if (res && res.apiStatus) {
				list = res.data.records.map((i) => {
					let orderStatus = i.status;
					if (i.shopOrders[0] && i.shopOrders[0].status) {
						if (i.shopOrders[0].status !== 'OK') {
							if (['BUYER_CLOSED', 'SYSTEM_CLOSED', 'SELLER_CLOSED'].includes(i.shopOrders[0].status)) {
								orderStatus = i.shopOrders[0].status;
							}
						} else if (orderStatus === 'COMPLETED' || orderStatus === 'SERVEREND') {
							const shopOrderItems = i.shopOrders[0].shopOrderItems;

							//检查是否全部关闭
							const deliverConfig = {
								okNum: 0,
								// 已评价
								evaluation: 0
							};
							for (let shopOrderItem of shopOrderItems) {
								if (shopOrderItem.status === 'OK') {
									deliverConfig.okNum += 1;
									deliverConfig.evaluation += ['BUYER_COMMENTED_COMPLETED', 'SYSTEM_COMMENTED_COMPLETED'].includes(shopOrderItem.packageStatus) ? 1 : 0;
								}
							}
							if (deliverConfig.evaluation !== deliverConfig.okNum) {
								orderStatus = 'UN_COMMENT';
							}
						}
					}

					return {
						...i,
						typeItem,
						listOrderState: state,
						goodsOrder: goodsOrder.value,
						status: orderStatus
					};
				});
			} else {
				status = false;
			}
		} else {
			params.serveGoods = 1;
			params.serveTypeId = typeItem.id;
			const res = await getAfsOrder({ ...params }).catch((err) => {});
			if (res && res.apiStatus) {
				list = res.data.records.map((i) => {
					return {
						...i,
						typeItem,
						listOrderState: state,
						goodsOrder: goodsOrder.value
					};
				});
			} else {
				status = false;
			}
		}
	}

	if (status) {
		paging.value.complete(list);
	} else {
		paging.value.complete(false);
	}
}

function reloadList() {
	if (paging.value) paging.value.reload();
}

function onReloadList() {
	reloadList();
}

function orderOperation({ type, order }) {
	if (!order || !order.no) return;
	orderNumber.value = order.no;
	if (type === 'pay') {
		openPay(order);
	}
}

function subOrderOperation({ type, order }) {
	if (!order || !order.no) return;
	orderNumber.value = order.no;
	if (type === 'pay') {
		openSubOrderPay(order);
	}
}

watch(showPay, (val) => {
	// if (val) {
	// 	uni.hideTabBar();
	// } else {
	// 	uni.showTabBar();
	// }
});

// 页面通知当前子组件加载更多数据
function doLoadMore() {
	paging.value.doLoadMore();
}

function onShow(reset, pageFun) {
	if ($data.dataList.length === 0) {
		if (pageFun && $data.dataList.length > 0) {
			return;
		}
		nextTick(() => {
			paging.value.reload();
		});
	}
	uni.$emit('order-list-item-show');
}

function onHide() {
	showPay.value = false;
}

defineExpose({
	onShow,
	onHide,
	compIndex
});
</script>
<template>
	<z-paging
		ref="paging"
		v-model="$data.dataList"
		@query="queryList"
		:refresher-enabled="true"
		:watch-touch-direction-change="false"
		:fixed="false"
		:auto="false"
		:refresher-vibrate="true"
	>
		<template v-for="(order, orderIndex) in $data.dataList" :key="orderIndex">
			<view class="px-20 mt-20">
				<template v-if="goodsOrder">
					<template v-if="order.listOrderState !== 'AFTER_SALE'">
						<template v-for="(item, index) in order.shopOrders" :key="orderIndex">
							<view class="px-30 bg-#fff border-rd-20 pb-30">
								<orderCardItemGoods
									:index="index"
									:item="item"
									:orderIndex="orderIndex"
									:order="order"
									:orderType="$props.typeList[$props.typeIndex].type"
									@reloadList="onReloadList"
									@orderOperation="orderOperation"
								></orderCardItemGoods>
							</view>
						</template>
					</template>
					<template v-else>
						<view class="px-30 bg-#fff border-rd-20 pb-30">
							<orderCardItemGoodsAfter
								:index="orderIndex"
								:item="order"
								:orderIndex="orderIndex"
								:order="order"
								:orderType="$props.typeList[$props.typeIndex].type"
								@reloadList="onReloadList"
								@orderOperation="orderOperation"
							></orderCardItemGoodsAfter>
						</view>
					</template>
				</template>
				<template v-if="!goodsOrder">
					<template v-if="order.listOrderState !== 'SERVER_BAD'">
						<template v-for="(item, index) in order.shopOrders" :key="orderIndex">
							<view class="px-30 bg-#fff border-rd-20 pb-30">
								<orderCardItemServer
									:index="index"
									:item="item"
									:orderIndex="orderIndex"
									:order="order"
									:orderType="$props.typeList[$props.typeIndex].type"
									@reloadList="onReloadList"
									@orderOperation="orderOperation"
									@subOrderOperation="subOrderOperation"
								></orderCardItemServer>
							</view>
						</template>
					</template>
					<template v-else>
						<view class="px-30 bg-#fff border-rd-20 pb-30">
							<orderCardItemServerAfter
								:index="orderIndex"
								:item="order"
								:orderIndex="orderIndex"
								:order="order"
								:orderType="$props.typeList[$props.typeIndex].type"
								@reloadList="onReloadList"
								@orderOperation="orderOperation"
							></orderCardItemServerAfter>
						</view>
					</template>
				</template>
			</view>
		</template>

		<!-- <template #[emptySlotName]>
			<view>
				<uv-button
					@click="goLogin()"
					color="#00B496"
					text="前往登录"
					class="w-300 h-84"
					custom-style="border-radius: 44rpx;"
					customTextStyle="font-size: 30rpx; color: #fff; font-weight: bold;"
				></uv-button>
			</view>
		</template> -->

		<app-pay-select
			ref="paySelectRef"
			v-model:show="showPay"
			v-model:btnLoading="payLoading"
			:price="payInfo.price"
			:balanceTotal="payInfo.balanceTotal"
			:balanceTotalShow="payInfo.balanceTotalShow"
			:timeout="payInfo.timeout"
			@submit="paySubmit"
			@change="paySelectChange"
		></app-pay-select>
	</z-paging>
</template>
<style lang="scss" scoped></style>
