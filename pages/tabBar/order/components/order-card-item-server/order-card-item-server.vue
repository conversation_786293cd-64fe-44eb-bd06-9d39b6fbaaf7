<script setup>
import { reactive, ref, watch, nextTick, computed, onMounted } from "vue";

import { Decimal } from "decimal.js";

import dayjs from "dayjs";

import { getOrder, closeOrderByOrderNo, putOrderReceiver, getUserBalance, closeOrderServerGoods, getServerOrderContract, addOrderNumberServer } from "@/server/api";

import { useOrderStore } from "@/store";

import { getPriceInfo, route, goShopHome } from "@/common/utils";

// import { isUnpaidOrder, orderStatusPlus, orderStatusPlusInfo } from '@/pages/order/common/utils';

// import { getAfsStatusCn } from '@/pages/order/common/types/order';

import { isUnpaidOrder, orderStatusPlus, orderStatusPlusInfo } from "@/common/order";

import { getAfsStatusCn } from "@/common/types/order";

import { deepClone, sleep } from "@/uni_modules/uv-ui-tools/libs/function/index.js";
import { common } from "@/common/images";

const $props = defineProps({
	index: {
		type: Number,
		default: 0,
	},
	item: {
		type: Object,
		default: () => ({}),
	},
	orderIndex: {
		type: Number,
		default: 0,
	},
	order: {
		type: Object,
		default: () => ({}),
	},
	orderType: {
		type: String,
		default: "shop",
	},
});

const $emit = defineEmits(["reloadList", "orderOperation", "subOrderOperation"]);

function mergeItemsArr(pre, current) {
	pre.num += current.num;
	pre.fixPrice = new Decimal(pre.fixPrice).plus(current.fixPrice).toString();
	pre.freightPrice = new Decimal(pre.freightPrice).plus(current.freightPrice).toString();
	//当前是关闭状态 下一个是未关闭状态 则设置为正常状态
	const changeCloseStatus = pre.status === "CLOSED" && current.status === "OK";
	//判断是否设置为正常状态
	if (changeCloseStatus) {
		//部分关闭 说明有未关闭的 合并后的状态设置为正常
		pre.status = current.status;
		pre.packageStatus = current.packageStatus;
	}
	if (pre.afsStatus === "NONE" && current.afsStatus !== "NONE") {
		pre.afsStatus = current.afsStatus;
	}
	//部分发货 设置为待发货状态
	if (!!pre.packageId && !current.packageId) {
		pre.packageId = current.packageId;
	}
	if (pre.packageStatus !== "WAITING_FOR_DELIVER" && current.packageStatus === "WAITING_FOR_DELIVER") {
		pre.packageStatus = current.packageStatus;
	}
	//如果

	pre.merged = true;
	return pre;
}

function orderMapComputed(shopOrderItems = []) {
	const shopOrderItemsMap = shopOrderItems.reduce((pre, item) => {
		const id = `${item.productId}${item.skuId}${item.specs}`;
		const currentItem = pre.get(id);
		if (currentItem) {
			const current = { ...item };
			currentItem.merged = mergeItemsArr(currentItem.merged, current);
			currentItem.items.push(current);
		} else {
			pre.set(id, {
				items: [{ ...item }],
				merged: { ...item },
			});
		}
		return pre;
	}, new Map());

	return shopOrderItemsMap;
}

function computeOrderPrice(data) {
	if (data.id) {
		return data.shopOrderItems.reduce((pre, cur) => {
			return pre.add(new Decimal(cur.num).mul(cur.dealPrice).add(cur.fixPrice).add(cur.freightPrice));
		}, new Decimal(0));
	}
	return $props.order.shopOrders.reduce((pre, item) => pre.add(new Decimal(item.dealPrice).mul(item.num).add(item.freightPrice).add(item.fixPrice)), new Decimal(0));
}

function reloadList() {
	$emit("reloadList");
}

function countDownFinish(e) {
	reloadList();
}

const $data = reactive({
	btnItemStyle: {
		height: "64rpx",
		borderRadius: "40rpx",
		fontSize: "26rpx",
	},
});

const paySelectRef = ref();
const showPay = ref(false);
const payLoading = ref(false);
const payInfo = reactive({
	price: 0,
	balanceTotalShow: true,
	balanceTotal: 0,
	timeout: 0,
});

function getOrderTime(order) {
	const timeout = order.timeout.payTimeout * 1000 - (dayjs().valueOf() - dayjs(order.createTime).valueOf());

	return timeout;
}

async function initUserBalance() {
	const { code, msg, data, apiStatus } = await getUserBalance();
	payInfo.balanceTotalShow = apiStatus;
	payInfo.balanceTotal = apiStatus ? `${getPriceInfo(data).integer}.${getPriceInfo(data).decimalText}` : "";
}

// 取消订单
function cancelOrder(order) {
	uni.showModal({
		title: "请确认",
		content: "是否需要取消？",
		success: async ({ confirm }) => {
			if (!confirm) return;
			const { code, data, msg, apiStatus } = await closeOrderByOrderNo({ orderNo: order.no });
			// if (!apiStatus) return uni.showToast({ icon: 'none', title: `${msg ? msg : '取消订单失败'}` });
			if (!apiStatus) return;
			uni.showToast({ icon: "none", title: `订单已取消` });
			reloadList();
		},
	});
}

// 更换地址
function changeOrderAddressServer(order) {
	route({
		type: "to",
		url: "/pages/my/pages/address/addressServer",
		params: {
			select: 1,
		},
		events: {
			async selectAddress(data) {
				uni.$once("order-list-item-show", () => {
					uni.showModal({
						title: "请确认",
						content: `是否更改地址为${data.address}？`,
						success: async ({ confirm }) => {
							if (!confirm) return;
							const { apiStatus } = await putOrderReceiver({ orderNo: order.no, ...data });
							// if (!apiStatus) return uni.showToast({ icon: 'none', title: `${msg ? msg : '修改失败'}` });
							if (!apiStatus) return;
							uni.showToast({ icon: "none", title: `修改成功` });
							reloadList();
						},
					});
				});
			},
		},
	});
}

// 立即支付
async function openPay(order) {
	// uni.showLoading({
	// 	title: '加载中...'
	// });
	// await initUserBalance();
	// payInfo.timeout = order.timeout.payTimeout * 1000 - (dayjs().valueOf() - dayjs(order.createTime).valueOf());
	// payInfo.price = `${getPriceInfo(order.orderPayment.payAmount).integer}.${getPriceInfo(order.orderPayment.payAmount).decimalText}`;
	// showPay.value = true;
	// uni.hideLoading();
	$emit("orderOperation", {
		type: "pay",
		order,
	});
}

// 催
async function urge(type, order) {
	if (!type || !order) return;

	const typeMap = {
		shipment: {
			name: "催发货",
			msg: "已提醒商家尽快发货,请您耐心等待~",
		},
		take: {
			name: "催接单",
			msg: "已提醒商家尽快接单,请您耐心等待~",
		},
		allocation: {
			name: "催分配",
			msg: "已提醒商家尽快分配服务人员,请您耐心等待~",
		},
		takeMaster: {
			name: "催接单",
			msg: "已提醒服务人员尽快接单,请您耐心等待~",
		},
	};

	const item = typeMap[type];

	if (!item) return;

	uni.showLoading({
		title: "加载中...",
	});
	await sleep(500);
	uni.hideLoading();
	uni.showToast({
		icon: "none",
		title: item.msg,
	});
}

// 查看合同
async function checkContract(order) {
	uni.showLoading({
		title: "加载中...",
	});

	try {
		const res = await getServerOrderContract(
			{ orderNo: order.no }
			// {
			// 	header: {
			// 		'Shop-Id': order.shopOrders[0].shopId
			// 	}
			// }
		);
		uni.hideLoading();
		openContract(res);
	} catch (error) {
		//TODO handle the exception
		uni.hideLoading();
		uni.showToast({
			icon: "error",
			title: "查看失败",
		});
	}
}
// 打开合同
async function openContract(res) {
	console.log(res);
	if (res.apiStatus) {
		if (res.data) {
			let arr = [];
			if (res.data.contractImg) {
				arr.push("查看合同图片");
			}
			if (res.data.contractPdf) {
				arr.push("查看合同文件");
			}
			if (arr.length > 1) {
				uni.showActionSheet({
					itemList: arr,
					success: (e) => {
						if (e.tapIndex === 0) {
							openImage(res.data.contractImg);
						}
						if (e.tapIndex === 1) {
							previewDocument(res.data.contractPdf);
						}
					},
				});
			} else if (arr.length === 1) {
				if (arr.includes("查看合同图片")) {
					openImage(res.data.contractImg);
				}
				if (arr.includes("查看合同文件")) {
					previewDocument(res.data.contractPdf);
				}
			}
		} else {
			uni.showToast({
				icon: "error",
				title: "暂未上传合同",
			});
		}
	} else {
		uni.showToast({
			icon: "error",
			title: "查看失败",
		});
	}
}

function canConfirmOrder(order) {
	let show = false;
	let obj = {
		show: false,
		order: null,
	};

	if (Array.isArray(order.orderItems)) {
		const findItem = order.orderItems.find((i) => (i.type === "T10001" || i.type === "T10003" || i.type === "T10004") && i.status === "UNPAY");
		if (findItem) {
			obj.show = true;
			obj.order = findItem;
		}
	}

	return obj;
}
function canSubOrderPay(order) {
	let show = false;
	let obj = {
		show: false,
		order: null,
	};

	if (Array.isArray(order.orderItems)) {
		const findItem = order.orderItems.find((i) => i.type === "T10002" && i.status === "UNPAY");
		if (findItem) {
			obj.show = true;
			obj.order = findItem;
		}
	}

	return obj;
}
// 确认订单
async function confirmOrder(order) {
	const state = canConfirmOrder(order);
	if (state.show && state.order) {
		openSubOrderPay(state.order);
	}
}
// 再次支付
async function subOrderPay(order) {
	const state = canSubOrderPay(order);
	if (state.show && state.order) {
		openSubOrderPay(state.order);
	}
}
// 子订单支付
async function openSubOrderPay(order) {
	$emit("subOrderOperation", {
		type: "pay",
		order,
	});
}

const masterInfo = ref({
	masterId: "",
	masterMobile: "",
	masterName: "",
	masterAvatar: "",
	masterSex: "",
	masterAge: "",
});
const masterInfoRef = ref();
// 联系服务人员
function showMasterInfo(order) {
	if (!order.masterId) {
		uni.showToast({
			icon: "error",
			title: "暂无服务人员",
		});
	}

	masterInfo.value = {
		masterId: order.masterId || "",
		masterMobile: order.masterMobile || "",
		masterName: order.masterName || "",
		masterAvatar: order.masterVO?.avatar || "",
		masterSex: order.masterVO?.sex || "",
		masterAge: order.masterVO?.age || "",
	};
	if (masterInfoRef.value) masterInfoRef.value.open();
}

const orderNumAddRef = ref();
const addNumberOrder = ref({});
const addNumberNum = ref(1);
// 订单加时
function addOrderNumber(order) {
	addNumberOrder.value = order;
	addNumberNum.value = 1;
	if (orderNumAddRef.value) orderNumAddRef.value.open();
}
async function submitServerNum() {
	try {
		const res = await addOrderNumberServer({
			orderNo: addNumberOrder.value.no,
			addTime: addNumberNum.value,
		});

		if (orderNumAddRef.value) orderNumAddRef.value.close();
		if (res.apiStatus) {
			uni.showToast({ icon: "none", title: `加时成功` });
			reloadList();
		}
	} catch (error) {
		//TODO handle the exception
		if (orderNumAddRef.value) orderNumAddRef.value.close();
	}
}

// 结束服务
function confirmReceive(order) {
	const showModalProps = {
		content: "是否确认结束服务",
		showClose: true,
		isSubmit: true,
	};

	// 通过每一个商品的售后得到 可以改变包状态的数组
	const shop = $props.item;
	if (!shop.shopOrderItems.every((item) => getAfsStatusCn(item.afsStatus).canChangePackageStatus)) {
		showModalProps.content = "该订单中存在退款服务，等待商家确认";
		showModalProps.isSubmit = false;
		showModalProps.showClose = false;
	} else if (!shop.shopOrderItems.every((item) => !getAfsStatusCn(item.afsStatus).canChangePackageStatusText)) {
		showModalProps.content = "该订单中存在退款服务，结束服务将关闭退款";
	}
	// 该订单中存在退款服务，等待商家确认  该订单中存在退款服务，结束服务将关闭退款
	uni.showModal({
		// title: '提示',
		confirmColor: "#f12f22",
		content: `${showModalProps.content}`,
		showCancel: showModalProps.showClose,
		success: async (res) => {
			if (res.cancel || !showModalProps.isSubmit) return;
			const { packageId } = shop.shopOrderItems[0];
			const { code, msg, apiStatus } = await closeOrderServerGoods({ orderNo: order.no, shopId: shop.shopId, status: "SERVEREND" });
			// if (!apiStatus) return uni.showToast({ title: `${msg ? msg : '确认收货失败'}`, icon: 'none' });
			if (!apiStatus) return;
			uni.showToast({ icon: "none", title: `结束成功` });
			reloadList();
		},
	});
}

// 删除订单
function delOrder(order) {
	const showModalProps = {
		content: "是否删除订单",
		showClose: true,
		isSubmit: true,
	};

	// 通过每一个商品的售后得到 可以改变包状态的数组
	// const shop = $props.item;
	// if (!shop.shopOrderItems.every((item) => getAfsStatusCn(item.afsStatus).canChangePackageStatus)) {
	// 	showModalProps.content = '该订单中存在退款宝贝，等待商家确认收货';
	// 	showModalProps.isSubmit = false;
	// 	showModalProps.showClose = false;
	// } else if (!shop.shopOrderItems.every((item) => !getAfsStatusCn(item.afsStatus).canChangePackageStatusText)) {
	// 	showModalProps.content = '该订单中存在退款宝贝，确认收货将关闭退款';
	// }
	// 该订单中存在退款宝贝，等待商家确认收货  该订单中存在退款宝贝，确认收货将关闭退款
	uni.showModal({
		title: "提示",
		confirmColor: "#f12f22",
		content: `${showModalProps.content}`,
		showCancel: showModalProps.showClose,
		success: async (res) => {
			if (res.cancel || !showModalProps.isSubmit) return;
			// const { packageId } = shop.shopOrderItems[0];
			// const { code, msg, apiStatus } = await confirmGoods({ orderNo: order.no, shopId: shop.shopId });
			// // if (!apiStatus) return uni.showToast({ title: `${msg ? msg : '确认收货失败'}`, icon: 'none' });
			// if (!apiStatus) return;
			// uni.showToast({ icon: 'none', title: `收货成功` });
			// reloadList();
		},
	});
}

// 申请售后
function applyAfterSale(order) {}

// 商品详情
function goGoodsDetails(goods) {
	route("/pages/goods/pages/productDetails/productDetails", {
		productId: goods.productId,
		shopId: goods.shopId,
	});
}

// 订单详情
function goOrderDetails(order) {
	const shop = $props.item;
	route("/pages/order/pages/orderDetailServer/orderDetailServer", {
		orderNo: order.no,
		shopId: shop.shopId,
		orderType: $props.orderType,
		packageId: shop.shopOrderItems[0].packageId || "",
	});
}

function previewDocument(url) {
	if (!url) {
		uni.showToast({
			icon: "error",
			title: "查看失败",
		});
		return;
	}
	uni.showLoading({
		title: "加载中...",
	});
	uni.downloadFile({
		url,
		success: (res) => {
			const filePath = res.tempFilePath;
			uni.openDocument({
				filePath: filePath,
				showMenu: true,
				success: (res2) => {},
				fail: (err) => {
					uni.showToast({
						icon: "error",
						title: "查看失败",
					});
				},
				complete: () => {
					uni.hideLoading();
				},
			});
		},
		fail: (err) => {
			uni.showToast({
				icon: "error",
				title: "查看失败",
			});
		},
	});
}
function isImage(str) {
	return /.(gif|jpg|jpeg|png|gif|jpg|png|webp|avif|jfif)$/i.test(str);
}
function openImage(images) {
	if (!images) {
		uni.showToast({
			icon: "error",
			title: "查看失败",
		});
		return;
	}
	const imageUrlArr = images.split(",");
	const imagesArr = [];
	for (let url of imageUrlArr) {
		// if (isImage(url)) {
		imagesArr.push(url);
		// }
	}
	if (imagesArr.length > 0) {
		previewImage(imagesArr);
	} else {
		uni.showToast({
			icon: "error",
			title: "查看失败",
		});
	}
}
function previewImage(url) {
	if (!url) return;
	uni.previewImage({
		urls: Array.isArray(url) ? url : [url],
		count: Array.isArray(url) ? url[0] : url,
	});
}
function onCopy(text) {
	uni.setClipboardData({
		data: String(text),
		showToast: true,
	});
}
function callTel(tel) {
	if (!tel) return;

	uni.makePhoneCall({
		phoneNumber: String(tel),
		complete: (res) => {
			// console.log(res);
		},
	});
}

const tabIndex = ref(1);

onMounted(() => {});

defineExpose({});
</script>
<template>
	<view class="w-full">
		<view class="px-0 bg-#fff-0 border-rd-0">
			<view class="h-102 flex justify-between items-center">
				<view class="flex justify-start items-center" @click="goShopHome(item.shopId)">
					<app-image :src="item.shopLogo" size="42" class="w-42 h-42 border-1/2" mode=""></app-image>
					<text class="text-28 text-#222222 ml-12 mr-6">{{ item.shopName }}</text>
					<uv-icon size="12" name="arrow-right" color="#999999" :bold="true"></uv-icon>
				</view>

				<!-- 待付款 -->
				<template v-if="order.status === 'UNPAID'">
					<text class="text-#F94B4A text-26 font-500">待付款</text>
				</template>

				<!-- 待接单 -->
				<template v-if="order.status === 'SERVER_UN_DELIVERY' || order.status === 'PAID'">
					<view class="flex justify-end items-center">
						<text class="text-#00B496 text-26 font-500">{{ "待接单" }}</text>
					</view>
				</template>

				<!-- 待确认 -->
				<template v-if="order.status === 'SERVEROK'">
					<view class="flex justify-end items-center">
						<text class="text-#00B496 text-26 font-500">{{ "待确认" }}</text>
					</view>
				</template>

				<!-- 等待分配服务人员 -->
				<template v-if="order.status === 'SERVERPAYED'">
					<view class="flex justify-end items-center">
						<text class="text-#00B496 text-26 font-500">{{ "等待分配服务人员" }}</text>
					</view>
				</template>

				<!-- 等待服务人员接单 -->
				<template v-if="order.status === 'SERVERMASTERAWAIT'">
					<view class="flex justify-end items-center">
						<text class="text-#00B496 text-26 font-500">{{ "等待服务人员接单" }}</text>
					</view>
				</template>

				<!-- 服务人员已接单 -->
				<template v-if="order.status === 'SERVERMASTEROK'">
					<view class="flex justify-end items-center">
						<text class="text-#00B496 text-26 font-500">{{ "待服务" }}</text>
					</view>
				</template>

				<!-- 服务人员取消订单 -->
				<template v-if="order.status === 'SERVERMASTERNO'">
					<view class="flex justify-end items-center">
						<text class="text-#F94B4A text-26 font-500">{{ "等待再次分配服务人员" }}</text>
					</view>
				</template>

				<!-- 服务中 -->
				<template v-if="order.status === 'SERVERGO'">
					<view class="flex justify-end items-center">
						<text class="text-#00B496 text-26 font-500">{{ "服务中" }}</text>
					</view>
				</template>

				<!-- 待评价 -->
				<template v-if="order.status === 'UN_COMMENT'">
					<view class="flex justify-end items-center">
						<!-- <text class="text-#888888 text-22">{{ order.createTime }}下单</text>
						<view class="w-1 h-20 bg-#EEEEEE mx-14"></view> -->
						<text class="text-#FF950A text-26 font-500">待评价</text>
					</view>
				</template>

				<!-- 已完成 -->
				<template v-if="order.status === 'COMPLETED' || order.status === 'SERVEREND'">
					<view class="flex justify-end items-center">
						<!-- <text class="text-#888888 text-22">{{ order.createTime }}下单</text>
						<view class="w-1 h-20 bg-#EEEEEE mx-14"></view> -->
						<text class="text-#888888 text-26 font-500">服务完成</text>
					</view>
				</template>

				<!-- 已关闭 -->
				<template v-if="order.status === 'BUYER_CLOSED' || order.status === 'SYSTEM_CLOSED' || order.status === 'SELLER_CLOSED'">
					<view class="flex justify-end items-center">
						<!-- <text class="text-#888888 text-22">{{ order.createTime }}下单</text>
						<view class="w-1 h-20 bg-#EEEEEE mx-14"></view> -->
						<text class="text-#888888 text-26 font-500">{{ { BUYER_CLOSED: "取消订单", SYSTEM_CLOSED: "自动取消", SELLER_CLOSED: "商家关闭" }[order.status] }}</text>
					</view>
				</template>
			</view>
			<view class="w-full h-1 bg-#F1F3F8"></view>

			<template v-for="(goods, index) in Array.from(orderMapComputed(item.shopOrderItems).values())" :key="index">
				<view class="flex mt-25" @click="goOrderDetails(order)">
					<image :src="goods.merged.image" class="w-164 h-164 min-w-164 border-solid border-1 border-#F5F5F5 border-rd-16" mode=""></image>
					<view class="flex-1 flex flex-col justify-between ml-20">
						<view class="">
							<view class="text-30 text-#222222 font-bold uv-line-1">{{ goods.merged.productName }}</view>
							<view class="text-26 text-#555555 font-500 mt-10 flex justify-between items-center">
								<text>{{ goods.merged.specs.join(" ") }}</text>
								<text>x{{ goods.merged.num }}</text>
							</view>
						</view>
						<view class="text-#222222 font-bold">
							<view class="flex items-end">
								<view class="text-30 pb-4">¥</view>
								<view class="text-36">
									{{ getPriceInfo(goods.merged.salePrice).integer }}
								</view>
								<view class="text-30 pb-4">.{{ getPriceInfo(goods.merged.salePrice).decimalText }}</view>
							</view>
						</view>
					</view>
				</view>
			</template>

			<!-- 护理服务 -->
			<template v-if="order.typeItem.type === 'nurse'">
				<view class="bg-#F6F6F6 p-26 border-rd-16 mt-20">
					<uv-read-more show-height="150rpx" textIndent="0px" :shadowStyle="{ textIndent: '0em' }" color="#999999" closeText="展开" :toggle="false">
						<view class="" @click="goOrderDetails(order)">
							<template v-if="order.orderReceiver">
								<view class="flex justify-between my-10">
									<view class="flex-shrink-0">
										<view class="text-28 text-#222 font-500 min-w-150">上门地址</view>
									</view>

									<view class="flex items-center">
										<view class="text-28 text-#222 font-500 text-right flex-1"> {{ order.orderReceiver.area[1] }} {{ order.orderReceiver.area[2] }} {{ order.orderReceiver.address }} </view>
										<!-- <image
											:src="common.iconCopyBai"
											class="w-20 h-20 min-w-20 mt-8 ml-10"
											mode=""
											@click="onCopy(`${order.orderReceiver.area[1]}${order.orderReceiver.area[2]}${order.orderReceiver.address}`)"
										></image> -->
									</view>
								</view>
								<view class="flex justify-between my-10">
									<view class="flex-shrink-0">
										<view class="text-28 text-#222 font-500 min-w-150">预约时间</view>
									</view>

									<view class="flex items-center">
										<view class="text-28 font-500 text-right flex-1 text-#00B496">
											{{ order.orderReceiver.startTime }}
										</view>
									</view>
								</view>
							</template>

							<view class="flex justify-between my-10">
								<view class="flex-shrink-0">
									<view class="text-28 text-#222 font-500 min-w-150">订单编号</view>
								</view>

								<view class="flex items-center">
									<view class="text-28 text-right flex-1 text-#888888">
										{{ order.no }}
									</view>
								</view>
							</view>

							<view class="flex justify-between my-10">
								<view class="flex-shrink-0">
									<view class="text-28 text-#222 font-500 min-w-150">下单时间</view>
								</view>

								<view class="flex items-center">
									<view class="text-28 text-right flex-1 text-#888888">
										{{ order.updateTime }}
									</view>
								</view>
							</view>
						</view>
					</uv-read-more>
				</view>
			</template>

			<!-- 陪诊服务 -->
			<template v-if="order.typeItem.type === 'attend'">
				<view class="bg-#F6F6F6 p-26 border-rd-16 mt-20">
					<uv-read-more show-height="150rpx" textIndent="0px" :shadowStyle="{ textIndent: '0em' }" color="#999999" closeText="展开" :toggle="false">
						<view class="" @click="goOrderDetails(order)">
							<template v-if="order.orderReceiver">
								<view class="flex justify-between my-10">
									<view class="flex-shrink-0">
										<view class="text-28 text-#222 font-500 min-w-150">就诊医院</view>
									</view>

									<view class="flex items-center">
										<view class="text-28 text-#222 font-500 text-right flex-1">
											{{ order.orderReceiver.hospitals }}
										</view>
										<!-- <image
											:src="common.iconCopyBai"
											class="w-20 h-20 min-w-20 mt-8 ml-10"
											mode=""
											@click="onCopy(`${order.orderReceiver.area[1]}${order.orderReceiver.area[2]}${order.orderReceiver.address}`)"
										></image> -->
									</view>
								</view>
								<view class="flex justify-between my-10">
									<view class="flex-shrink-0">
										<view class="text-28 text-#222 font-500 min-w-150">就诊时间</view>
									</view>

									<view class="flex items-center">
										<view class="text-28 font-500 text-right flex-1 text-#00B496">
											{{ order.orderReceiver.startTime }}
										</view>
									</view>
								</view>
								<view class="flex justify-between my-10">
									<view class="flex-shrink-0">
										<view class="text-28 text-#222 font-500 min-w-150">就诊人</view>
									</view>

									<view class="flex items-center">
										<view class="text-28 text-#222 text-right flex-1">
											{{ order.orderReceiver.caredUsername }}
										</view>
									</view>
								</view>
								<view class="flex justify-between my-10">
									<view class="flex-shrink-0">
										<view class="text-28 text-#222 font-500 min-w-150">就诊人电话</view>
									</view>

									<view class="flex items-center">
										<view class="text-28 text-#222 text-right flex-1">
											{{ order.orderReceiver.caredMobile }}
										</view>
									</view>
								</view>
							</template>

							<!-- <view class="flex justify-between my-10">
								<view class="flex-shrink-0">
									<view class="text-28 text-#222 font-500 min-w-150">订单编号</view>
								</view>
			
								<view class="flex items-center">
									<view class="text-28 text-right flex-1 text-#888888">
										{{ order.no }}
									</view>
								</view>
							</view> -->

							<!-- <view class="flex justify-between my-10">
								<view class="flex-shrink-0">
									<view class="text-28 text-#222 font-500 min-w-150">下单时间</view>
								</view>
			
								<view class="flex items-center">
									<view class="text-28 text-right flex-1 text-#888888">
										{{ order.updateTime }}
									</view>
								</view>
							</view> -->
						</view>
					</uv-read-more>
				</view>
			</template>

			<!-- 家政服务 -->
			<template v-if="order.typeItem.type === 'homemaking'">
				<view class="bg-#F6F6F6 p-26 border-rd-16 mt-20">
					<uv-read-more show-height="150rpx" textIndent="0px" :shadowStyle="{ textIndent: '0em' }" color="#999999" closeText="展开" :toggle="false">
						<view class="" @click="goOrderDetails(order)">
							<template v-if="order.orderReceiver">
								<view class="flex justify-between my-10">
									<view class="flex-shrink-0">
										<view class="text-28 text-#222 font-500 min-w-150">上门时间</view>
									</view>

									<view class="flex items-center">
										<view class="text-28 font-500 text-right flex-1 text-#00B496">
											{{ order.orderReceiver.startTime }}
										</view>
									</view>
								</view>
							</template>

							<view class="flex justify-between my-10">
								<view class="flex-shrink-0">
									<view class="text-28 text-#222 font-500 min-w-150">下单时间</view>
								</view>

								<view class="flex items-center">
									<view class="text-28 text-right flex-1 text-#888888">
										{{ order.updateTime }}
									</view>
								</view>
							</view>
						</view>
					</uv-read-more>
				</view>
			</template>

			<!-- 子订单支付 -->
			<template v-if="Array.isArray(order.orderItems) && order.orderItems.length > 0">
				<view class="bg-#F6F6F6 px-26 py-16 border-rd-16 mt-20" @click="goOrderDetails(order)">
					<template v-for="(payItem, payIndex) in order.orderItems" :key="payIndex">
						<view class="flex justify-between my-10">
							<view class="flex-shrink-0">
								<view class="text-28 text-#222 font-500 min-w-150">
									{{ { T10001: "签约附加金", T10002: "服务附加金", T10003: "签约附加金", T10004: "签约附加金" }[payItem.type] }}
								</view>
							</view>

							<view class="flex items-center">
								<template v-if="payItem.status === 'UNPAY'">
									<view class="flex items-center">
										<view class="text-24">待支付：</view>
										<view class="font-bold flex items-end text-FC3F33">
											<view class="text-24 pb-0">¥</view>
											<view class="text-28">
												{{ getPriceInfo(payItem.payment.payAmount).integer }}
											</view>
											<view class="text-24 pb-0">.{{ getPriceInfo(payItem.payment.payAmount).decimalText }}</view>
										</view>
									</view>
								</template>
								<template v-else>
									<view class="flex items-center">
										<!-- <view class="text-24">已支付:</view> -->
										<view class="font-bold flex items-end">
											<view class="text-24 pb-0">¥</view>
											<view class="text-28">
												{{ getPriceInfo(payItem.payment.payAmount).integer }}
											</view>
											<view class="text-24 pb-0">.{{ getPriceInfo(payItem.payment.payAmount).decimalText }}</view>
										</view>
									</view>
								</template>
							</view>
						</view>
					</template>
				</view>
			</template>

			<!-- 订单支付倒计时 -->
			<template v-if="order.status === 'UNPAID'">
				<view class="text-26 flex justify-end items-center py-24">
					<template v-if="getOrderTime(order) > 0">
						<view class="flex items-center mr-20">
							<view class="text-#222222">剩余:</view>
							<view class="text-#FC3F33 font-bold flex items-end">
								<view class="text-30 pb-0">
									<my-uv-count-down
										:time="Number(getOrderTime(order))"
										format="HH:mm:ss"
										ref="payCountDown"
										:autoStart="true"
										:textStyle="{
											fontSize: '30rpx',
											color: '#F85D5D',
											fontWeight: 'bold',
										}"
										@finish="countDownFinish"></my-uv-count-down>
								</view>
							</view>
						</view>
					</template>

					<view class="flex items-center">
						<view class="text-#222222">待付款:</view>
						<view class="text-#FC3F33 font-bold flex items-end">
							<view class="text-30 pb-4">¥</view>
							<view class="text-36">
								{{ getPriceInfo(computeOrderPrice(item)).integer }}
							</view>
							<view class="text-30 pb-4">.{{ getPriceInfo(computeOrderPrice(item)).decimalText }}</view>
						</view>
					</view>
				</view>
			</template>

			<!-- 待付款 -->
			<template v-if="order.status === 'UNPAID'">
				<view class="w-full h-1 bg-#F1F3F8 mt-24 mb-24"></view>
				<view class="w-full flex justify-between items-center">
					<view class="text-#888888 text-28 font-500">
						<!-- 更多 -->
					</view>

					<view class="flex justify-end items-center">
						<view class="btn-item-box">
							<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="cancelOrder(order)">
								<view class="btn-text text-#323232">取消订单</view>
							</uv-button>
						</view>

						<view class="btn-item-box">
							<uv-button :custom-style="$data.btnItemStyle" color="#00B496" plain @click="changeOrderAddressServer(order)">
								<view class="btn-text text-#00B496">更换地址</view>
							</uv-button>
						</view>

						<view class="btn-item-box">
							<uv-button :custom-style="$data.btnItemStyle" color="#66C5B5" @click="openPay(order)">
								<view class="btn-text text-#fff">立即支付</view>
							</uv-button>
						</view>
					</view>
				</view>
			</template>

			<!-- 待接单 -->
			<template v-if="order.status === 'SERVER_UN_DELIVERY' || order.status === 'PAID'">
				<view class="w-full h-1 bg-#F1F3F8 mt-24 mb-24"></view>
				<view class="w-full flex justify-between items-center">
					<view class="text-#888888 text-28 font-500">
						<!-- 更多 -->
					</view>

					<view class="flex justify-end items-center">
						<!-- <view class="btn-item-box">
							<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="goOrderDetails(order)">
								<view class="btn-text text-#323232">申请售后</view>
							</uv-button>
						</view> -->
						<view class="btn-item-box">
							<uv-button :custom-style="$data.btnItemStyle" color="#00B496" plain @click="urge('take', order)">
								<view class="btn-text text-#00B496">催接单</view>
							</uv-button>
						</view>
					</view>
				</view>
			</template>

			<!-- 待确认 -->
			<template v-if="order.status === 'SERVEROK'">
				<view class="w-full h-1 bg-#F1F3F8 mt-24 mb-24"></view>
				<view class="w-full flex justify-between items-center">
					<view class="text-#888888 text-28 font-500">
						<!-- 更多 -->
					</view>

					<view class="flex justify-end items-center">
						<template v-if="order.contractPdf || order.contractImg">
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#00B496" plain @click="openContract({ apiStatus: true, data: { contractPdf: order.contractPdf || '', contractImg: order.contractImg || '' } })">
									<view class="btn-text text-#00B496">查看合同</view>
								</uv-button>
							</view>
						</template>
						<template v-if="canConfirmOrder(order).show">
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#00B496" @click="confirmOrder(order)">
									<view class="btn-text text-#fff">确认费用</view>
								</uv-button>
							</view>
						</template>
						<template v-if="canSubOrderPay(order).show">
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#00B496" @click="subOrderPay(order)">
									<view class="btn-text text-#fff">支付费用</view>
								</uv-button>
							</view>
						</template>
					</view>
				</view>
			</template>

			<!-- 等待分配服务人员 -->
			<template v-if="order.status === 'SERVERPAYED'">
				<view class="w-full h-1 bg-#F1F3F8 mt-24 mb-24"></view>
				<view class="w-full flex justify-between items-center">
					<view class="text-#888888 text-28 font-500">
						<!-- 更多 -->
					</view>

					<view class="flex justify-end items-center">
						<!-- <view class="btn-item-box">
							<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="applyAfterSale(order)">
								<view class="btn-text text-#323232">申请售后</view>
							</uv-button>
						</view> -->
						<template v-if="order.contractPdf || order.contractImg">
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#00B496" plain @click="openContract({ apiStatus: true, data: { contractPdf: order.contractPdf || '', contractImg: order.contractImg || '' } })">
									<view class="btn-text text-#00B496">查看合同</view>
								</uv-button>
							</view>
						</template>
						<view class="btn-item-box">
							<uv-button :custom-style="$data.btnItemStyle" color="#00B496" @click="urge('allocation', order)">
								<view class="btn-text text-#fff">催分配</view>
							</uv-button>
						</view>
						<template v-if="canSubOrderPay(order).show">
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#00B496" @click="subOrderPay(order)">
									<view class="btn-text text-#fff">支付费用</view>
								</uv-button>
							</view>
						</template>
					</view>
				</view>
			</template>

			<!-- 等待服务人员接单 -->
			<template v-if="order.status === 'SERVERMASTERAWAIT'">
				<view class="w-full h-1 bg-#F1F3F8 mt-24 mb-24"></view>
				<view class="w-full flex justify-between items-center">
					<view class="text-#888888 text-28 font-500">
						<!-- 更多 -->
					</view>

					<view class="flex justify-end items-center">
						<!-- <view class="btn-item-box">
							<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="applyAfterSale(order)">
								<view class="btn-text text-#323232">申请售后</view>
							</uv-button>
						</view> -->
						<template v-if="order.contractPdf || order.contractImg">
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#00B496" plain @click="openContract({ apiStatus: true, data: { contractPdf: order.contractPdf || '', contractImg: order.contractImg || '' } })">
									<view class="btn-text text-#00B496">查看合同</view>
								</uv-button>
							</view>
						</template>
						<view class="btn-item-box">
							<uv-button :custom-style="$data.btnItemStyle" color="#00B496" @click="urge('takeMaster', order)">
								<view class="btn-text text-#fff">催接单</view>
							</uv-button>
						</view>
						<template v-if="canSubOrderPay(order).show">
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#00B496" @click="subOrderPay(order)">
									<view class="btn-text text-#fff">支付费用</view>
								</uv-button>
							</view>
						</template>
					</view>
				</view>
			</template>

			<!-- 服务人员已接单 -->
			<template v-if="order.status === 'SERVERMASTEROK'">
				<view class="w-full h-1 bg-#F1F3F8 mt-24 mb-24"></view>
				<view class="w-full flex justify-between items-center">
					<view class="text-#888888 text-28 font-500">
						<!-- 更多 -->
					</view>

					<view class="flex justify-end items-center">
						<!-- <view class="btn-item-box">
							<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="applyAfterSale(order)">
								<view class="btn-text text-#323232">申请售后</view>
							</uv-button>
						</view> -->
						<template v-if="order.contractPdf || order.contractImg">
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#00B496" plain @click="openContract({ apiStatus: true, data: { contractPdf: order.contractPdf || '', contractImg: order.contractImg || '' } })">
									<view class="btn-text text-#00B496">查看合同</view>
								</uv-button>
							</view>
						</template>

						<view class="btn-item-box">
							<uv-button :custom-style="$data.btnItemStyle" color="#00B496" @click="showMasterInfo(order)">
								<view class="btn-text text-#fff">联系服务人员</view>
							</uv-button>
						</view>

						<template v-if="canSubOrderPay(order).show">
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#00B496" @click="subOrderPay(order)">
									<view class="btn-text text-#fff">支付费用</view>
								</uv-button>
							</view>
						</template>
					</view>
				</view>
			</template>

			<!-- 服务人员取消订单 -->
			<template v-if="order.status === 'SERVERMASTERNO'">
				<view class="w-full h-1 bg-#F1F3F8 mt-24 mb-24"></view>
				<view class="w-full flex justify-between items-center">
					<view class="text-#888888 text-28 font-500">
						<!-- 更多 -->
					</view>

					<view class="flex justify-end items-center">
						<!-- <view class="btn-item-box">
							<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="applyAfterSale(order)">
								<view class="btn-text text-#323232">申请售后</view>
							</uv-button>
						</view> -->
						<template v-if="order.contractPdf || order.contractImg">
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#00B496" plain @click="openContract({ apiStatus: true, data: { contractPdf: order.contractPdf || '', contractImg: order.contractImg || '' } })">
									<view class="btn-text text-#00B496">查看合同</view>
								</uv-button>
							</view>
						</template>
						<view class="btn-item-box">
							<uv-button :custom-style="$data.btnItemStyle" color="#00B496" @click="urge('allocation', order)">
								<view class="btn-text text-#fff">催分配</view>
							</uv-button>
						</view>
						<template v-if="canSubOrderPay(order).show">
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#00B496" @click="subOrderPay(order)">
									<view class="btn-text text-#fff">支付费用</view>
								</uv-button>
							</view>
						</template>
					</view>
				</view>
			</template>

			<!-- 服务中 -->
			<template v-if="order.status === 'SERVERGO'">
				<view class="w-full h-1 bg-#F1F3F8 mt-24 mb-24"></view>
				<view class="w-full flex justify-between items-center">
					<view class="text-#888888 text-28 font-500">
						<!-- 更多 -->
					</view>

					<view class="flex justify-end items-center">
						<!-- <view class="btn-item-box">
							<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="applyAfterSale(order)">
								<view class="btn-text text-#323232">申请售后</view>
							</uv-button>
						</view> -->
						<template v-if="order.contractPdf || order.contractImg">
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#00B496" plain @click="openContract({ apiStatus: true, data: { contractPdf: order.contractPdf || '', contractImg: order.contractImg || '' } })">
									<view class="btn-text text-#00B496">查看合同</view>
								</uv-button>
							</view>
						</template>

						<!-- <view class="btn-item-box">
							<uv-button :custom-style="$data.btnItemStyle" color="#00B496" @click="showMasterInfo(order)">
								<view class="btn-text text-#fff">联系服务人员</view>
							</uv-button>
						</view> -->

						<template v-if="canSubOrderPay(order).show">
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#00B496" @click="subOrderPay(order)">
									<view class="btn-text text-#fff">支付费用</view>
								</uv-button>
							</view>
						</template>
						<template v-else>
							<template v-if="order.typeItem.type !== 'nurse'">
								<view class="btn-item-box">
									<uv-button :custom-style="$data.btnItemStyle" color="#00B496" plain @click="addOrderNumber(order)">
										<view class="btn-text text-#00B496">增加服务</view>
									</uv-button>
								</view>
							</template>

							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#66C5B5" @click="confirmReceive(order)">
									<view class="btn-text text-#fff">结束服务</view>
								</uv-button>
							</view>
						</template>
					</view>
				</view>
			</template>

			<!-- 待评价 -->
			<template v-if="order.status === 'UN_COMMENT'">
				<view class="w-full h-1 bg-#F1F3F8 mt-24 mb-24"></view>
				<view class="w-full flex justify-between items-center">
					<view class="text-#888888 text-28 font-500">
						<!-- 更多 -->
					</view>

					<view class="flex justify-end items-center">
						<!-- <view class="btn-item-box">
							<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="delOrder(order)">
								<view class="btn-text text-#323232">删除订单</view>
							</uv-button>
						</view> -->
						<!-- <view class="btn-item-box">
							<uv-button :custom-style="$data.btnItemStyle" color="#FF950A" @click="goOrderDetails(item.shopOrderItems[0])" plain>
								<view class="btn-text text-#FF950A">去评价</view>
							</uv-button>
						</view> -->
					</view>
				</view>
			</template>

			<!-- 已完成 -->
			<template v-if="order.status === 'COMPLETED' || order.status === 'SERVEREND'">
				<view class="w-full h-1 bg-#F1F3F8 mt-24 mb-24"></view>
				<view class="w-full flex justify-between items-center">
					<view class="text-#888888 text-28 font-500">
						<!-- 更多 -->
					</view>

					<view class="flex justify-end items-center">
						<!-- <view class="btn-item-box">
							<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="delOrder(order)">
								<view class="btn-text text-#323232">删除订单</view>
							</uv-button>
						</view> -->
						<view class="btn-item-box">
							<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" @click="goGoodsDetails(item.shopOrderItems[0])" plain>
								<view class="btn-text text-#323232">再来一单</view>
							</uv-button>
						</view>
					</view>
				</view>
			</template>

			<!-- 已关闭 -->
			<template v-if="order.status === 'BUYER_CLOSED' || order.status === 'SYSTEM_CLOSED' || order.status === 'SELLER_CLOSED'">
				<view class="w-full h-1 bg-#F1F3F8 mt-24 mb-24"></view>
				<view class="w-full flex justify-between items-center">
					<view class="text-#888888 text-28 font-500">
						<!-- 更多 -->
					</view>

					<view class="flex justify-end items-center">
						<!-- <view class="btn-item-box">
							<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="delOrder(order)">
								<view class="btn-text text-#323232">删除订单</view>
							</uv-button>
						</view> -->
						<view class="btn-item-box">
							<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" @click="goGoodsDetails(item.shopOrderItems[0])" plain>
								<view class="btn-text text-#323232">再来一单</view>
							</uv-button>
						</view>
					</view>
				</view>
			</template>
		</view>

		<my-uv-modal ref="masterInfoRef" title="服务人员信息" confirmText="立即呼叫" @confirm="callTel(masterInfo.masterMobile || '')" showCancelButton>
			<view class="w-full">
				<template v-if="masterInfo.masterAvatar && masterInfo.masterSex && masterInfo.masterAge">
					<view class="bg-#F6F6F6 px-15 py-15 border-rd-16 mt-20">
						<view class="flex items-center">
							<view class="w-100 h-100">
								<app-image :src="masterInfo.masterAvatar" size="100" rd="10" mode=""></app-image>
							</view>
							<view class="flex-1 ml-20">
								<view class="flex">
									<view class="text-30 text-#222 font-bold">{{ masterInfo.masterName }}</view>
									<view class="text-30 text-#222 font-500 ml-20">{{ masterInfo.masterMobile }}</view>
								</view>
								<view class="flex text-#777777 text-26 line-height-26 font-500 items-center mt-10">
									<view class="">{{ { MALE: "男", FEMALE: "女" }[masterInfo.masterSex] }}</view>
									<view class="w-1 h-20 bg-#DFDFDF mx-15"></view>
									<view class="">{{ masterInfo.masterAge }}岁</view>
								</view>
							</view>
						</view>
					</view>
				</template>
				<template v-else>
					<view class="bg-#F6F6F6 px-26 py-10 border-rd-16 mt-20">
						<view class="flex justify-between my-20">
							<view class="flex-shrink-0">
								<view class="text-30 text-#222 font-500 min-w-150">姓名</view>
							</view>

							<view class="flex items-center">
								<view class="text-30 text-#222 font-500 text-right flex-1">
									{{ masterInfo.masterName }}
								</view>
								<image :src="common.iconCopyBai" class="w-20 h-20 min-w-20 mt-8 ml-10" mode="" @click="onCopy(`${order.orderReceiver.area[1]}${order.orderReceiver.area[2]}${order.orderReceiver.address}`)"></image>
							</view>
						</view>

						<view class="flex justify-between my-20">
							<view class="flex-shrink-0">
								<view class="text-30 text-#222 font-500 min-w-150">手机号</view>
							</view>

							<view class="flex items-center">
								<view class="text-30 font-500 text-right flex-1 text-#00B496">
									{{ masterInfo.masterMobile }}
								</view>
							</view>
						</view>
					</view>
				</template>
			</view>
		</my-uv-modal>

		<my-uv-modal ref="orderNumAddRef" title="增加服务" @confirm="submitServerNum" :closeOnClickOverlay="false" showCancelButton asyncClose>
			<view class="w-full">
				<view class="bg-#fff p-26 border-rd-16 mt-20">
					<view class="w-full flex justify-center">
						<my-uv-number-box v-model="addNumberNum" :min="1" :max="10" bgColor="#F3F4F6" color="#111010" buttonSize="90rpx" inputWidth="120rpx" inputFontSize="42rpx" :inputCustomStyle="{ fontWeight: 500 }" integer></my-uv-number-box>
					</view>

					<template v-if="addNumberOrder && addNumberOrder.shopOrders">
						<view class="mt-20 text-24 text-#00B496 font-500"> 提示：￥{{ getPriceInfo(addNumberOrder.shopOrders[0].shopOrderItems[0].salePrice).integer }}.{{ getPriceInfo(addNumberOrder.shopOrders[0].shopOrderItems[0].salePrice).decimalText }}/次，按整次起加 </view>
					</template>
				</view>
			</view>
		</my-uv-modal>
	</view>
</template>
<style lang="scss" scoped>
.btn-item-box {
	height: 64rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 0rpx 0 14rpx;
}
.btn-text {
	font-size: 26rpx;
	font-weight: 500;
}
.btn-item {
	height: 64rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 26rpx;
	margin: 0 14rpx 0 0;
	border-radius: 40rpx;
	border-style: solid;
	border-width: 1rpx;
	font-size: 26rpx;
	font-weight: 500;
}

.text-FC3F33 {
	color: #fc3f33;
}
</style>
