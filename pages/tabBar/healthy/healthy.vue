<script setup>
import { ref, computed, onUnmounted } from 'vue'
import { onShow, onReady } from '@dcloudio/uni-app'
import MonitorItem from './components/MonitorItem.vue'
import HealthSteps from './components/HealthSteps.vue'
import HealthReport from './components/HealthReport.vue'
import ProductItemList from './components/ProductItemList.vue'
import { throttle } from '@/utils' // 导入获取周数的函数和节流工具
import { route } from '@/common/utils'
import { usePublicStore, useUserStore, useHealthMemberInfoStore, useAppStore } from '@/store'
import { useLoginCheck, useBannerNavigation, useMenuNavigation } from '@/common/useNavigation'
import {
    GetFamilyAllMembers,
    GetAllDeviceRealTimeData,
    GetUserRegisterStatusByUserId,
    GetHealthReportOverview,
    GetHealthReportWeekList,
    GenerateWeeklyReport,
    getB<PERSON>r<PERSON>ist,
    getTypeGoodsList,
    getCategoryLevelFromPlatform,
    getKingKongList,
} from '@/server/api'
import { healthy } from '@/common/images'

const publicStore = usePublicStore()
// #ifdef H5
const navTop = 40
const scrollTop = navTop + 12
// #endif
// #ifndef H5
const navTop = publicStore.menuButtonInfo.height + publicStore.menuButtonInfo.top
const scrollTop = navTop + 5
// #endif

const weekRange = ref('')
const pickerRef = ref()
const weekList = ref([]) // 改为响应式数据，从接口获取
const weekListLoaded = ref(false) // 周报列表是否已加载
const columns = computed(() => [weekList.value]) // 改为计算属性

// 格式化周期显示文本
function formatWeekDisplay(item) {
    return `第${item.weekNo}周(${item.weekRange})`
}

function confirmPicker({ value }) {
    const selectedItem = value[0]
    if (!selectedItem) return
    weekRange.value = selectedItem.label
    handleGenerateAHealthReportWithResultId(selectedItem.resultId)
}

const current = ref(0) // 当前选中的成员索引
const avatar_active = ref(0) // 选中的人员高亮
// 家庭成员
const familyMembers = ref([])

// 获取家庭成员
const appStore = useAppStore()
const userStore = useUserStore()
const userId = computed(() => userStore.userData.userId)
const healthMemberInfoStore = useHealthMemberInfoStore()
const familyName = ref('')
const healthStepsRef = ref()
const currentMemberInfo = ref({}) // 当前成员信息

// 使用导航组合式函数
const { checkLoginAndExecute } = useLoginCheck()
const { handleBannerClick } = useBannerNavigation()
const { handleMenuClick } = useMenuNavigation()

// 判断当前选中的成员是否为创建者（只有创建者才能查看周报）
const isCurrentMemberCreator = computed(() => {
    return currentMemberInfo.value?.isCreated === 1
})

async function getFamilyAllMembers() {
    try {
        const { data } = await GetFamilyAllMembers({ userId: userId.value })
        // 在res.data中根据isCreated找到当前账号信息
        if (!data || !data.length) return
        const currentUser = data.find((item) => item.isCreated)
        currentMemberInfo.value = currentUser
        familyName.value = currentUser.familyName
        familyMembers.value = data
        healthMemberInfoStore.setMemberInfo(data[0]) // 默认暂存第一个成员数据
        getAllDeviceRealTimeData(currentUser.userId)
        // 初始化健康报告流程
        initHealthReportFlow()
        avatar_active.value = 0
    } catch (error) {
        console.error('获取家庭成员失败:', error)
    }
}
const showMonitorCard = ref(false)
const monitorMap = {
    blood_pressure: {
        label: '血压',
        unit: 'mmhg',
        icon: healthy.cholesterol2,
    },
    blood_glucose: {
        label: '血糖',
        unit: 'mmol/L',
        icon: healthy.hypoglycemia,
    },
    uric_acid: {
        label: '尿酸',
        unit: 'μmol/L',
        icon: healthy.uricAcid,
    },
    sleep: { label: '睡眠', unit: 'h', icon: healthy.sleep },
    step_num: { label: '步数', unit: '步', icon: healthy.steps },
    heart_rate: { label: '心率', unit: '次/分', icon: healthy.pulseRate },
    breath: { label: '呼吸', unit: '次/分', icon: healthy.breathing },
    tc: { label: '总胆固醇', unit: 'mmol/L', icon: healthy.cholesterol },
    tg: { label: '甘油三酯', unit: 'mmol/L', icon: healthy.triglyceride },
    hdl: { label: '高密度脂蛋白', unit: 'mmol/L', icon: healthy.hdl },
    ldl: { label: '低密度脂蛋白', unit: 'mmol/L', icon: healthy.ldl },
    // 预留其他指标配置
    // blood_oxygen: { label: '血氧', unit: '%', icon: healthy.oximetry },
    // body_temperature: {
    //     label: '体温',
    //     unit: '℃',
    //     icon: healthy.temperaturesteps,
    // },
    // body_fat: { label: '体脂率', unit: '%', icon: healthy.bodyFatRate },
}
// 初始化默认体征列表
function initDefaultMonitorList() {
    return Object.keys(monitorMap).map((dataType) => {
        const { label, unit, icon } = monitorMap[dataType]
        return {
            dataType,
            label,
            dataValue: '--',
            upTime: '--',
            unit,
            icon,
        }
    })
}

const refresherEnabled = ref(true) // 通过控制refresherEnabled来控制下拉刷新是否可用
// 打开日期选择器
function openPicker() {
    refresherEnabled.value = false // 关闭下拉刷新
    pickerRef.value.open()
}

const monitorList = ref(initDefaultMonitorList())

// 获取当前人员体征数据
async function getAllDeviceRealTimeData(userId) {
    try {
        const { data } = await GetAllDeviceRealTimeData({ userId })
        // 先初始化默认列表
        const defaultList = initDefaultMonitorList()

        // 如果有实际数据，则更新对应的项
        if (data && data.length) {
            data.forEach((item) => {
                const index = defaultList.findIndex((def) => def.dataType === item.dataType)
                if (index !== -1) {
                    const { label, unit, icon } = monitorMap[item.dataType]
                    defaultList[index] = {
                        dataType: item.dataType,
                        label,
                        dataValue: item.dataValue,
                        upTime: item.upTime,
                        unit,
                        icon,
                    }
                }
            })
        }

        monitorList.value = defaultList
    } catch (error) {
        monitorList.value = initDefaultMonitorList()
        console.error('获取当前人员体征数据失败:', error)
    }
}

// 点击卡片跳转到日常监测页面，并传递指标类型
function toDaily({ dataType, dataValue }) {
    if (!dataValue || dataValue === '--') return

    // 检查是否有对应的配置
    if (!monitorMap[dataType]) {
        console.warn(`未找到 ${dataType} 的配置`)
        return
    }

    // 跳转到日常监测页面，传递 dataType 参数
    route({
        url: '/pages/healthy/pages/daily_monitoring',
        params: { dataType },
    })
}

const handleMemberClick = (index) => {
    avatar_active.value = index
    current.value = index
    const data = familyMembers.value[index]
    currentMemberInfo.value = data
    getAllDeviceRealTimeData(data.userId)
    healthMemberInfoStore.setMemberInfo(data) // 暂存选中的成员信息
    // 重置周报加载状态，重新初始化健康报告流程
    weekListLoaded.value = false
    initHealthReportFlow()
}

// 获取要显示的家庭成员列表（包含添加按钮）
const displayMembers = computed(() => {
    return [
        ...familyMembers.value,
        {
            isAddButton: true,
        },
    ]
})

// 去家庭圈前判断是否登录
const handleAddMember = () => {
    checkLoginAndExecute(() => {
        route('/pages/healthy/pages/family_circle')
    })
}

const footerMenus = ref([])

async function loadData() {
    try {
        const { data } = await getKingKongList({ pageType: 2 })
        footerMenus.value = data
    } catch (error) {
        console.error('获取菜单数据失败:', error)
    }
}

// 页面跳转
function toPath(item) {
    handleMenuClick(item, true)
}

const scrollLeft = ref(0)
// 处理滚动事件
const handleScroll = (e) => {
    const { scrollLeft: left } = e.detail
    scrollLeft.value = left
}

const reportStepsData = ref([]) // 健康报告步骤数据

/**
 * 1. 调用weeklist接口
   ├─ 有数据 → 取最新resultId → 调用总览接口
   │           ├─ status=0 → 显示"生成中" + 开始轮询
   │           └─ status=1 → 正常显示总览
   └─ 无数据 → 检查注册状态
               ├─ 全部完成(=1) → 显示"去生成"按钮
               └─ 有未完成 → 显示"去完成"按钮
 */
// 主控制函数：集中管理健康报告流程
async function initHealthReportFlow() {
    try {
        // 步骤1：先查weeklist接口
        const weekListResult = await getHealthReportWeekListData()

        if (weekListResult && weekListResult.length > 0) {
            weekList.value = weekListResult.map((item) => {
                return {
                    ...item,
                    label: formatWeekDisplay(item),
                }
            })
            // 有数据：取最新一条的resultId调用总览接口
            const latestReport = weekListResult[0]
            weekRange.value = formatWeekDisplay(latestReport)
            handleGenerateAHealthReportWithResultId(latestReport.resultId)
        } else {
            // 无数据：检查用户注册状态
            await handleNoWeekListData()
        }
    } catch (error) {
        console.error('健康报告流程初始化失败', error)
        reportStatus.value = 0
        reportGenerating.value = false
        clearPolling()
    }
}

const reportStatus = ref(0) // 报告生成状态 0 未生成 1 已生成
const reportGenerating = ref(false) // 报告生成中状态
const pollingTimer = ref(null) // 轮询定时器
const hrData = {
    year: '',
    weekRange: '',
    weekNo: 1, // 周数
    score: 0, // 得分结果
    scoreRate: 0, // 得分环比
    abnormalCount: 0, // 异常项
    diseaseCount: 0, // 风险项
}
const healthReportData = ref({ ...hrData })

// 清除轮询定时器
function clearPolling() {
    if (pollingTimer.value) {
        clearTimeout(pollingTimer.value)
        pollingTimer.value = null
    }
}

// 开始轮询weeklist接口
function startPolling() {
    clearPolling() // 先清除之前的定时器
    pollingTimer.value = setTimeout(async () => {
        console.log('轮询检查周报状态...')
        await initHealthReportFlow()
    }, 3000)
}

// 生成周报
async function generateWeeklyReport() {
    try {
        reportGenerating.value = true
        const res = await GenerateWeeklyReport({
            userId: currentMemberInfo.value.userId,
        })
        // 生成成功后开始轮询
        startPolling()
    } catch (error) {
        console.error('生成周报失败', error)
        reportGenerating.value = false
    }
}

// 处理报告按钮点击事件
function handleReportAction() {
    const { basicStatus, naireStatus, pressureStatus } = reportStepsData.value

    if (basicStatus === 1 && naireStatus === 1 && pressureStatus === 1) {
        // 所有状态都完成，执行生成报告
        generateWeeklyReport()
    } else {
        // 有未完成的状态，跳转到健康问卷页面
        route('/pages/healthy/pages/health_questionnaire')
    }
}

// 获取周报列表数据（纯数据获取，不更新UI状态）
async function getHealthReportWeekListData() {
    if (!userId.value) {
        console.warn('用户ID不存在，无法获取周报日期列表')
        return null
    }

    try {
        const { data } = await GetHealthReportWeekList({
            userId: currentMemberInfo.value.userId,
        })
        return data && Array.isArray(data) ? data : null
    } catch (error) {
        console.error('获取周报日期列表失败:', error)
        return null
    }
}

// 处理无周报数据的情况
async function handleNoWeekListData() {
    try {
        const { data } = await GetUserRegisterStatusByUserId({
            userId: currentMemberInfo.value.userId,
        })
        if (!data) {
            reportStatus.value = 0
            reportStepsData.value = []
            return
        }

        reportStepsData.value = data
        const { basicStatus, naireStatus, pressureStatus } = data

        if (basicStatus === 1 && naireStatus === 1 && pressureStatus === 1) {
            // 所有状态都完成，显示"去生成"按钮
            reportStatus.value = 0 // 未生成状态，显示生成按钮
        } else {
            // 有未完成的状态，显示"去完成"按钮
            reportStatus.value = 0
        }
    } catch (error) {
        console.error('获取用户注册状态失败', error)
        reportStatus.value = 0
        reportStepsData.value = []
    }
}

// 使用resultId调用总览接口
async function handleGenerateAHealthReportWithResultId(resultId) {
    const params = { resultId: resultId }

    try {
        const { data } = await GetHealthReportOverview(params)

        if (JSON.stringify(data) === '{}') {
            healthReportData.value = { ...hrData }
            reportStatus.value = 0
            return
        }

        // 根据status判断状态
        if (data.status === 0) {
            // 生成中，显示生成中状态并开始轮询
            reportStatus.value = 0
            reportGenerating.value = true
            uni.showToast({ title: '报告生成中，请稍后查看', icon: 'none' })
            startPolling()
        } else if (data.status === 1) {
            // 生成完成，正常显示
            reportStatus.value = 1
            reportGenerating.value = false
            healthReportData.value = data
            clearPolling()
        }
    } catch (error) {
        console.error('获取健康报告失败', error)
        reportStatus.value = 0
        reportGenerating.value = false
        clearPolling()
    }
}

const navbarBgColor = ref('transparent')
const stickyBarAnimClass = ref('')
const stickyNameBarTop = ref(0)

const pagingRef = ref(null)
async function onRefresh() {
    const loginSuccess = checkLoginAndExecute(async () => {
        // 重置周报加载状态
        weekListLoaded.value = false
        await getFamilyAllMembers()
    })

    // 刷新完成后，重新初始化健康报告流程
    if (pagingRef.value) pagingRef.value.complete()
}

// 性能优化：缓存上次透明度值
let lastOpacity = -1
const MAX_SCROLL_DISTANCE = 100

// 滚动处理函数（未节流版本）
function handleScrollChange(scrollTop) {
    // 计算导航栏背景透明度 - 默认透明，滚动时变白
    const opacity = Math.max(0, Math.min(1, scrollTop / MAX_SCROLL_DISTANCE))

    // 避免重复设置相同值（减少DOM操作）
    if (Math.abs(opacity - lastOpacity) > 0.01) {
        lastOpacity = opacity
        navbarBgColor.value = opacity === 0 ? 'transparent' : `rgba(255,255,255,${opacity})`
    }
}

// z-paging滚动监听（使用节流工具）
const handleScrollTopChange = throttle(handleScrollChange, 16) // 60fps

onShow(() => {
    initBanner()
    checkLoginAndExecute(() => {
        getFamilyAllMembers()
    })
})

const bannerList = ref([])
//轮播图
const initBanner = async () => {
    try {
        const { data } = await getBannerList({
            current: 1,
            size: 10,
            status: 'STATUSN',
            type: 'INDRE',
        })

        bannerList.value = data.records
    } catch (error) {
        console.log(error)
    }
}
//检测卡片是否隐藏 暂时这么写 为了过审
const checkMonitorCardShow = async (item) => {
    try {
        const { data } = await getKingKongList({ pageType: 5 })
        showMonitorCard.value = data && data.length > 0 ? true : false
    } catch (error) {
        console.error('获取检测卡片数据失败:', error)
    }
}
const handleSwiperClick = (index) => {
    handleBannerClick(bannerList.value, index)
}

const tabCurrent = ref(0)
const tabsRef = ref(null)
// tas吸附高度
let tabsStickyHeight = 0
// #ifdef H5
tabsStickyHeight = usePublicStore().statusBarHeight
// #endif

const tabsChange = (index) => {
    tabCurrent.value = index
    pagingRef.value?.clear()
    pagingRef.value?.refreshToPage(1)
}
// 页面高度和滚动相关
const pageHeight = ref(0)
const navHeader = ref(0)

// 计算页面高度
const getPageHeight = () => {
    pageHeight.value = uni.getWindowInfo().windowHeight
    navHeader.value = uni.getWindowInfo().statusBarHeight
}

onReady(() => {
    getPageHeight()
    loadData()
    checkMonitorCardShow()
})

//暂时固定放这些tab
const tempTabNameList = ['健康辅具', '健康食品', '生活日需', '科技产品']
const getTabList = async () => {
    try {
        const { code, data } = await getCategoryLevelFromPlatform({ current: 1, size: 999 })

        if (code === 200) {
            return data
        }
    } catch (error) {
        console.log(error)
    }
}
const pageData = ref({
    dataList: [],
    tabList: tempTabNameList.map((item, index) => {
        return {
            name: item,
            categoryId: index,
        }
    }),
    current: 0,
    dataReset: false,
    opacityValue: 0,
    loading: false,
})
const activeStyle = {
    color: '#292929',
    fontSize: '32rpx',
    fontWeight: 'bold',
}
const inactiveStyle = {
    color: '#1a1a1a',
    fontSize: '28rpx',
    fontWeight: '500',
}

const queryProductList = async (pageNo = 1, pageSize = 10) => {
    try {
        //如果还是预设tab就请求tab数据
        if (pageData.value.tabList.find((item) => item.categoryId === 0)) {
            const tabData = await getTabList()
            pageData.value.tabList = tabData.records.filter((item) => tempTabNameList.includes(item.name))
        }
        pageData.value.loading = true
        const platformCategoryId = pageData.value.tabList[tabCurrent.value].categoryId
        const params = { pageNo: pageNo, current: pageNo, size: pageSize, platformCategoryId }
        const { code, data } = await getTypeGoodsList(params)
        if (code === 200) {
            pagingRef.value?.complete(data.records)
        }
    } catch (error) {
        pagingRef.value.complete(false)
    } finally {
        pageData.value.loading = false
    }
}

// 页面卸载时清理定时器
onUnmounted(() => {
    clearPolling()
})
</script>

<template>
    <app-layout>
        <z-paging
            ref="pagingRef"
            v-model="pageData.dataList"
            @query="queryProductList"
            :auto="true"
            :scrollable="true"
            refresher-theme-style="black"
            use-refresher-status-bar-placeholder
            :refresher-enabled="refresherEnabled"
            @onRefresh="onRefresh"
            @scrollTopChange="handleScrollTopChange"
            bg-color="#F2F3F7"
        >
            <image class="fixed w-full h-418 left-0 top-0 z-0" :src="healthy.bg" mode="aspectFit" :style="{ opacity: 1 - lastOpacity }" />
            <app-navBar :fixed="true" :bg-color="navbarBgColor" class="z-1">
                <!-- 顶部栏 -->
                <template #content>
                    <view class="header-bar">
                        <view class="header-left" @click="handleAddMember">
                            <text class="header-title">{{ familyName }}</text>
                            <app-image :src="healthy.edit" size="40" mode=""></app-image>
                        </view>
                    </view>
                </template>
            </app-navBar>
            <view class="main-bg">
                <!-- 头像区 -->
                <view class="avatar-section">
                    <view class="avatar-row">
                        <scroll-view
                            class="avatar-scroll"
                            scroll-x
                            :show-scrollbar="false"
                            :scroll-into-view="'avatar-' + avatar_active"
                            :scroll-with-animation="true"
                            @scroll="handleScroll"
                        >
                            <view class="avatar-list">
                                <view
                                    class="avatar-col"
                                    v-for="(item, index) in displayMembers"
                                    :key="item.isAddButton ? 'add' : item.id"
                                    :id="'avatar-' + index"
                                    @click="item.isAddButton ? handleAddMember() : handleMemberClick(index)"
                                >
                                    <view :class="['avatar-box', item.isAddButton ? 'avatar-add' : avatar_active === index && 'avatar-selected']">
                                        <app-image :src="item.isAddButton ? healthy.addMember : healthy.avatarM1" size="86" mode=""></app-image>
                                    </view>
                                    <text
                                        :class="[
                                            'avatar-label',
                                            item.isAddButton ? 'avatar-label-add' : avatar_active === index && 'avatar-selected-label',
                                        ]"
                                    >
                                        {{ item.isAddButton ? '添加成员' : item.nickName || item.userName }}
                                    </text>
                                </view>
                            </view>
                        </scroll-view>
                    </view>
                </view>
                <!-- 健康周报区块 - 只有创建者才显示 -->
                <view class="card health-report">
                    <view class="w-full flex justify-between">
                        <text class="card-title">健康周报</text>
                        <!-- 健康报告时显示日期 -->
                        <view v-if="reportStatus" class="flex items-center" @click="openPicker">
                            <view class="font-size-28 text-#333333 m-r-16">
                                {{ weekRange }}
                            </view>
                            <uv-icon name="arrow-down" :size="16"></uv-icon>
                        </view>
                    </view>
                    <!-- 健康周报-步骤 -->
                    <template v-if="!reportStatus">
                        <HealthSteps ref="healthStepsRef" :reportStepsData="reportStepsData" :isShowBtn="isCurrentMemberCreator">
                            <template #finish>
                                <button v-if="!reportGenerating" class="main-btn" @click="handleReportAction">
                                    {{
                                        reportStepsData.basicStatus === 1 && reportStepsData.naireStatus === 1 && reportStepsData.pressureStatus === 1
                                            ? '去生成健康报告'
                                            : '去完成'
                                    }}
                                </button>
                                <button v-else class="main-btn generating-btn" disabled>生成中...</button>
                            </template>
                        </HealthSteps>
                    </template>
                    <!-- 健康周报-报告 -->
                    <HealthReport v-else :healthReportData="healthReportData"> </HealthReport>
                    <!-- 健康周报-报告-周期选择器 -->
                    <uv-picker
                        ref="pickerRef"
                        :columns="columns"
                        keyName="label"
                        @confirm="confirmPicker"
                        @close="refresherEnabled = true"
                    ></uv-picker>
                </view>
                <!-- 轮播图 -->
                <view class="swiper-wrapper my-24">
                    <uv-swiper
                        :list="bannerList"
                        keyName="image"
                        indicatorMode="dot"
                        height="160rpx"
                        radius="20rpx"
                        indicator
                        circular
                        @click="handleSwiperClick"
                    ></uv-swiper>
                </view>

                <!-- 底部功能区 只有创建者才显示 -->
                <view class="footer-bar grid grid-cols-4 gap-y-35rpx" v-if="currentMemberInfo.isCreated">
                    <view v-for="(item, idx) in footerMenus" :key="idx" class="footer-item" @click="toPath(item)">
                        <view class="footer-icon">
                            <app-image :src="item.icon" size="76" mode=""></app-image>
                        </view>
                        <text class="footer-label">{{ item.title }}</text>
                        <uv-badge v-if="item.dot" type="error" max="99" :value="1" absolute :offset="[-6, -6]"></uv-badge>
                    </view>
                </view>
                <!-- 日常监测区块 -->
                <view class="monitor-card mt-24" v-if="showMonitorCard">
                    <view class="monitor-header">
                        <text class="monitor-title">日常监测</text>
                    </view>
                    <view class="monitor-grid">
                        <MonitorItem
                            v-for="item in monitorList"
                            :key="item.id"
                            :icon="item.icon"
                            :label="item.label"
                            :value="item.dataValue"
                            :unit="item.unit"
                            :time="item.upTime"
                            @click="toDaily({ dataType: item.dataType, dataValue: item.dataValue })"
                        />
                    </view>
                </view>

                <!-- 热门推荐 -->
                <view :style="{ height: 'auto', minHeight: `${pageHeight - navHeader}px` }" class="population-wrapper mt-24">
                    <view style="z-index: 1000; position: sticky" :style="{ top: `${tabsStickyHeight}px` }">
                        <app-statusBar bgColor="#fff"></app-statusBar>
                        <view class="title bg-#fff">热门推荐</view>
                        <z-tabs
                            :list="pageData.tabList"
                            name-key="name"
                            value-key="id"
                            :current="tabCurrent"
                            @change="tabsChange"
                            lineColor="#198B6B"
                            bgColor="transparent"
                            activeColor="#198B6B"
                            lineWidth="50rpx"
                            barWidth="50rpx"
                            :activeStyle="activeStyle"
                            :inactiveStyle="inactiveStyle"
                            :tabs-style="{
                                border: 'none',
                                height: '90rpx',
                                background: '#fff',
                                'border-bottom': '1rpx solid #e5e6eb',
                            }"
                            ref="tabsRef"
                        >
                        </z-tabs>
                    </view>

                    <product-item-list
                        ref="itemListRef"
                        :list="pageData.dataList"
                        :currentList="pageData.tabList"
                        :currentIndex="pageData.current"
                        :tabItem="pageData.tabList[pageData.current]"
                        :loading="pageData.loading"
                    ></product-item-list>
                </view>
            </view>
            <template #empty> </template>
        </z-paging>
    </app-layout>
</template>

<style lang="scss" scoped>
// 变量区
$primary: #1abc9c;
$primary-light: #e6f9f5;
$primary-dark: #00b698;
$gray-bg: #f1f3f7;
$gray-card: #fff;
$gray-border: #e5e6eb;
$gray-text: #6d6d6c;
$gray-label: #888888;
$radius-lg: 24rpx;
$radius-md: 16rpx;
$radius-full: 50%;

.main-bg {
    background: $gray-bg;
    padding-bottom: 40rpx;
    // #ifdef H5
    background-image: url('https://hejiawuyou.oss-cn-shanghai.aliyuncs.com/wxapp/imgzip/healthy/bg.png');
    background-repeat: no-repeat;
    background-size: 100% 418rpx;
    // #endif
}

// 顶部栏
.header-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 32rpx 0 32rpx;

    .header-left {
        display: flex;
        align-items: center;

        .header-title {
            font-size: 32rpx;
            font-weight: bold;
            color: #222;
            margin-right: 16rpx;
        }
    }

    .header-right {
        position: relative;

        .msg-dot {
            position: absolute;
            top: -10rpx;
            right: -10rpx;
            width: 16rpx;
            height: 16rpx;
            background: #ff4d4f;
            border-radius: $radius-full;
            border: 2rpx solid #fff;
        }
    }
}

// 头像区
.avatar-section {
    padding: 40rpx 32rpx 24rpx 32rpx;

    .avatar-row {
        display: flex;
        align-items: flex-end;
        position: relative;
    }

    .avatar-scroll {
        flex: 1;
        overflow: hidden;
    }

    .avatar-list {
        display: flex;
        gap: 48rpx;
        padding-right: 48rpx;
    }

    .avatar-col {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex-shrink: 0;
        width: 88rpx;

        .avatar-box {
            width: 90rpx;
            height: 90rpx;
            border: 4rpx solid #fff;
            border-radius: $radius-full;
            background: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8rpx;
            transition: all 0.3s ease;

            &.avatar-selected {
                border: 4rpx solid $primary;
                box-shadow: 0 2rpx 8rpx 0 rgba(26, 188, 156, 0.08);
            }

            &.avatar-add {
                border-style: dashed;
            }
        }

        .avatar-label {
            font-size: 28rpx;
            color: $gray-label;
            transition: all 0.3s ease;
            white-space: nowrap;

            &.avatar-selected-label {
                color: $primary;
                font-weight: 500;
            }

            &.avatar-label-add {
                width: 88rpx;
                text-align: center;
            }
        }
    }
}

// 健康周报卡片
.card {
    background: $gray-card;
    border-radius: $radius-lg;
    margin: 0 32rpx;
    padding: 40rpx 30rpx 30rpx;

    .card-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #121212;
    }
}

//轮播图
.swiper-wrapper {
    padding: 0 30rpx;
}

// 底部功能区
.footer-bar {
    background: $gray-card;
    border-radius: $radius-lg;
    margin: 0 32rpx 0;
    padding: 30rpx 0;
    .footer-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        .footer-icon {
            width: 76rpx;
            height: 76rpx;
            margin-bottom: 8rpx;
        }

        .footer-label {
            font-size: 26rpx;
            color: #1a1a1a;
        }
    }
}

// 日常监测区块
.monitor-card {
    margin-left: 32rpx;
    margin-right: 32rpx;

    .monitor-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 24rpx;

        .monitor-title {
            font-size: 34rpx;
            font-weight: bold;
            color: #121212;
        }

        .monitor-more {
            font-size: 28rpx;
            color: $primary;
            border: 2rpx solid $primary;
            border-radius: 30rpx;
            padding: 8rpx 20rpx;
            display: flex;
            align-items: center;
            gap: 8rpx;
        }
    }

    .monitor-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 24rpx;
    }
}

.sticky-name-bar {
    position: fixed;
    left: 0;
    width: 100%;
    background: #fff;
    z-index: 100;
    box-shadow: 0 2rpx 8rpx 0 rgba(26, 188, 156, 0.08);
    padding: 24rpx 32rpx 0 32rpx;
    transition: opacity 0.3s, transform 0.3s;
    opacity: 0;
    transform: translateY(0);
}
.sticky-name-bar[style*='display: none'],
.sticky-name-bar.v-leave-active {
    opacity: 1;
    transform: translateY(-30rpx);
}

.app-navBar,
.z-1 {
    transition: background-color 0.3s;
}

.name-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 48rpx;
    padding-bottom: 12rpx;
}
.name-tab {
    font-size: 32rpx;
    color: #222;
    padding: 0 8rpx;
    cursor: pointer;
    position: relative;
}
.name-tab-active {
    color: #1abc9c;
    font-weight: bold;
}

.fade-slide-enter-active,
.fade-slide-leave-active {
    transition: opacity 0.3s, transform 0.3s;
}
.fade-slide-enter-from,
.fade-slide-leave-to {
    opacity: 0;
    transform: translateY(-30rpx);
}
.fade-slide-enter-to,
.fade-slide-leave-from {
    opacity: 1;
    transform: translateY(0);
}

.fade-in {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.3s, transform 0.3s;
}
.fade-out {
    opacity: 0;
    transform: translateY(-30rpx);
    transition: opacity 0.3s, transform 0.3s;
}
.main-btn {
    width: 320rpx;
    height: 72rpx;
    background: linear-gradient(90deg, #02c9a8 0%, #00b496 100%);
    color: #fff;
    font-size: 28rpx;
    font-weight: bold;
    border: none;
    border-radius: 36rpx;
    margin: 0 auto;
    display: block;
    box-shadow: 0 2rpx 8rpx 0 rgba(26, 188, 156, 0.08);

    &.generating-btn {
        background: #ccc;
        color: #999;
        cursor: not-allowed;
        box-shadow: none;
    }
}
.population-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    .title {
        font-size: 32rpx;
        font-weight: bold;
        color: #292929;
        text-align: center;
        padding: 20rpx 0;
        color: #1abc9c;
    }
}
</style>
