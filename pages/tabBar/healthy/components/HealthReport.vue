<script setup>
import { ref, onMounted, watch, computed, getCurrentInstance, nextTick } from 'vue'
import { onReady } from '@dcloudio/uni-app'
import { route } from '@/common/utils'
import { CanvasManager, THEMES, generateCanvasId, HEALTHY_SCORE, NORMAL_SCORE } from '@/utils/canvasUtils'

const props = defineProps({
    // 是否显示底部按钮
    isShowBtn: {
        type: Boolean,
        default: true,
    },
    healthReportData: {
        type: Object,
        default: () => ({
            score: 0, // 得分结果
            scoreRate: 0, // 得分环比
            abnormalCount: 0, // 异常项
            diseaseCount: 0, // 风险项
        }),
    },
})

//健康等级范围划分
const healthLevels = [
    { score: HEALTHY_SCORE, level: '良好' },
    { score: NORMAL_SCORE, level: '一般' },
]
const scoreClass = computed(() => {
    if (healthScore.value >= HEALTHY_SCORE) {
        return 'good'
    } else if (healthScore.value >= NORMAL_SCORE) {
        return 'normal'
    } else {
        return 'danger'
    }
})
// const healthScore = toRef(props.healthReportData, "score"); // 健康分数
const healthScore = computed(() => props.healthReportData.score)
// const healthScore = ref(props.healthReportData.score);

// 健康等级
const healthLevel = computed(() => {
    const level = healthLevels.find((item) => healthScore.value >= item.score)
    return level ? level.level : '警惕' // 默认返回最低等级
})

// 定义趋势类型映射
const trendTypes = {
    up: { text: '上升趋势', color: '#02c9a8' },
    down: { text: '下降趋势', color: '#ff4d4f' },
    stable: { text: '持平', color: '#02c9a8' },
}

const trendInfo = computed(() => {
    const rate = props.healthReportData.scoreRate
    if (rate > 0) {
        return trendTypes.up // 上升趋势
    } else if (rate < 0) {
        return trendTypes.down // 下降趋势
    } else {
        return trendTypes.stable // 平稳
    }
})

const trendText = computed(() => trendInfo.value.text)
const trendColor = computed(() => trendInfo.value.color)
const trendDelta = computed(() => {
    const rate = props.healthReportData.scoreRate
    if (rate > 0) {
        return `比上一个周提高了${rate}分`
    } else if (rate < 0) {
        return `比上一个周下降了${Math.abs(rate)}分`
    } else {
        return '与上周持平'
    }
}) // 比上周提升分数
const abnormalCount = computed(() => props.healthReportData.abnormalCount) // 异常指标数
const riskCount = computed(() => props.healthReportData.diseaseCount) // 疾病风险数

// Canvas管理器
const canvasManager = ref(null)
const canvasId = ref('')
const circleSize = 120 // 画布尺寸(px)

const instance = getCurrentInstance() // 获取实例

// 初始化Canvas管理器
const initCanvas = async () => {
    try {
        // 生成唯一Canvas ID
        canvasId.value = generateCanvasId('health-circle', instance)

        canvasManager.value = new CanvasManager({
            canvasId: canvasId.value,
            instance,
            width: circleSize,
            height: circleSize,
        })

        console.log('Canvas管理器初始化成功:', canvasId.value)
    } catch (error) {
        console.error('Canvas管理器初始化失败:', error)
    }
}

// 绘制健康圆环
const drawHealthCircle = async (score) => {
    if (!canvasManager.value) {
        console.warn('Canvas管理器未初始化')
        return
    }

    try {
        console.log('开始绘制健康圆环:', score)

        // 使用canvasUtils绘制圆环，配置与原来相似的视觉效果
        const result = await canvasManager.value.render('circle', score, {
            radius: 48, // 中线半径
            lineWidth: 20, // 进度环线宽
            bgLineWidth: 20, // 背景环线宽
            theme: THEMES.health, // 使用健康主题
            animation: true, // 启用动画
            animationDuration: 800, // 动画时长
            animationEasing: 'easeInOutCubic', // 动画缓动
            showText: false, // 不显示内置文字，使用外部文字
        })

        if (result.success) {
            console.log('健康圆环绘制成功')
        } else {
            console.error('健康圆环绘制失败:', result.error)
        }
    } catch (error) {
        console.error('绘制健康圆环异常:', error)
    }
}

// 页面加载时初始化Canvas并绘制
onReady(async () => {
    console.log('onReady - 初始化Canvas')
    await nextTick()

    // 延迟初始化，确保DOM完全渲染
    setTimeout(async () => {
        await initCanvas()
        if (healthScore.value > 0) {
            await drawHealthCircle(healthScore.value)
        }
    }, 800)
})

// 监听分数变化并重绘
watch(
    () => props.healthReportData.score,
    async (newScore) => {
        console.log('健康分数变化:', newScore)
        if (canvasManager.value && newScore >= 0) {
            await drawHealthCircle(newScore)
        }
    },
    { immediate: false },
)

// H5端兜底
onMounted(async () => {
    console.log('onMounted - 兜底初始化Canvas')
    await nextTick()

    setTimeout(async () => {
        if (!canvasManager.value) {
            await initCanvas()
            if (healthScore.value > 0) {
                await drawHealthCircle(healthScore.value)
            }
        }
    }, 1000)
})
</script>

<template>
    <view class="health-report-page">
        <view class="score-trend-row">
            <!-- 环形图与分数 -->
            <view class="score-section">
                <!-- 使用canvasUtils的统一canvas元素 -->
                <!-- #ifdef MP-WEIXIN -->
                <canvas type="2d" :id="canvasId" class="circle-canvas" :style="{ width: circleSize + 'px', height: circleSize + 'px' }"></canvas>
                <!-- #endif -->
                <!-- #ifndef MP-WEIXIN -->
                <canvas :canvas-id="canvasId" :id="canvasId" class="circle-canvas" :width="circleSize" :height="circleSize"></canvas>
                <!-- #endif -->

                <view class="score-center">
                    <text class="score" :class="scoreClass">{{ healthScore }}</text>
                    <text class="level">{{ healthLevel }}</text>
                </view>
            </view>
            <!-- 趋势提示 -->
            <view class="trend-section">
                <view class="flex items-center">
                    <text class="trend-text" :style="{ color: trendColor }">{{ trendText }}</text>
                </view>
                <text class="trend-delta">{{ trendDelta }}</text>
            </view>
        </view>
        <!-- 异常/风险数 -->
        <view class="summary-section">
            <view class="summary-item abnormal">
                <text class="summary-num">{{ abnormalCount }}</text>
                <text class="summary-label">异常指标数</text>
            </view>
            <view class="summary-item risk">
                <text class="summary-num">{{ riskCount }}</text>
                <text class="summary-label">疾病风险数</text>
            </view>
        </view>
        <!-- 提示语 -->
        <view class="tip-section">
            您存在<text class="highlight-abnormal">{{ abnormalCount }}条异常指标</text>、<text class="highlight-risk">{{ riskCount }}个疾病风险</text
            >，请查看详细报告
        </view>
        <!-- 按钮 -->
        <template v-if="isShowBtn">
            <button class="detail-btn" @click="route('/pages/healthy/pages/health_reports')">查看详细报告</button>
        </template>
    </view>
</template>

<style lang="scss" scoped>
$main-green: #02c9a8;
$main-green-dark: #00b496;
$main-gradient: linear-gradient(90deg, $main-green 0%, $main-green-dark 100%);
$trend-green: #00b496;
$trend-gray: #77838f;
$abnormal-red: #ff4d4f;
$risk-orange: #ffa940;
$tip-gray: #555555;
$bg-gray: #f7f8fa;
$white: #fff;

.health-report-page {
    box-sizing: border-box;

    .score-trend-row {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-around;
        margin-bottom: 8rpx;
        margin-top: 16rpx;
        .score-section {
            position: relative;

            .circle-canvas {
                display: block;
                margin: 0 auto;
                width: 250rpx;
                height: 250rpx;
                background: transparent;
                z-index: 1;
            }

            .score-center {
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                display: flex;
                flex-direction: column;
                align-items: center;
                z-index: 2;

                .score {
                    font-size: 58rpx;
                    font-weight: bold;
                    color: #1a1a1a;
                    &.good {
                        color: $trend-green;
                    }
                    &.normal {
                        color: $trend-green;
                    }
                    &.danger {
                        color: $risk-orange;
                    }
                }

                .level {
                    font-size: 20rpx;
                    color: #555555;
                    margin-top: 2rpx;
                }
            }
        }

        .trend-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-left: 36rpx;
            font-size: 32rpx;
            font-weight: 500;

            .trend-text {
                display: flex;
                align-items: center;
                font-size: 34rpx;
                font-weight: 500;
            }

            .trend-delta {
                font-size: 28rpx;
                color: #16202a;
                margin-top: 10rpx;
            }
        }
    }

    .summary-section {
        display: flex;
        justify-content: space-around;
        align-items: center;
        background-color: $bg-gray;
        border-radius: 10rpx;
        margin: 16rpx 0 20rpx 0;
        padding: 10rpx 0;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.03);

        .summary-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;

            .summary-num {
                font-size: 45rpx;
                font-weight: bold;
                margin-bottom: 4rpx;
            }

            &.abnormal .summary-num {
                color: $abnormal-red;
            }

            &.risk .summary-num {
                color: $risk-orange;
            }

            .summary-label {
                font-size: 26rpx;
                color: $trend-gray;
            }
        }
    }

    .tip-section {
        text-align: center;
        font-size: 24rpx;
        color: $tip-gray;
        margin-bottom: 32rpx;

        .highlight-abnormal {
            color: $abnormal-red;
            font-weight: 500;
        }

        .highlight-risk {
            color: $risk-orange;
            font-weight: 500;
        }
    }

    .detail-btn {
        width: 320rpx;
        height: 72rpx;
        background: $main-gradient;
        color: $white;
        font-size: 28rpx;
        border: none;
        border-radius: 36rpx;
        margin: 0 auto;
        display: block;
        box-shadow: 0 2rpx 8rpx 0 rgba(26, 188, 156, 0.08);
        text-align: center;
        letter-spacing: 2rpx;
        opacity: 0.95;
    }
}
</style>
