<script setup>
import { common } from '@/common/images'

const props = defineProps({
    loading: {
        type: Boolean,
        default: false,
    },
    list: {
        type: Array,
        default: () => [],
    },
})
</script>

<template>
    <view class="content">
        <view class="py-20 flex flex-wrap gap-24">
            <view v-if="props.loading" class="flex justify-center items-center w-full pt-50">
                <uv-loading-icon></uv-loading-icon>
            </view>

            <template v-for="(item, index) in props.list" v-else :key="index">
                <view class="single">
                    <page-product-card-item :item="item" :index="index"></page-product-card-item>
                </view>
            </template>
        </view>
        <template v-if="props.list.length === 0 && !props.loading">
            <uv-empty :icon="common.empty" text="暂无商品~"></uv-empty>
        </template>
    </view>
</template>

<style lang="scss" scoped>
.content {
    height: 100%;
    padding: 0rpx 30rpx;
}
.single {
    width: calc((100% - 24rpx) / 2);
}
.item {
    position: relative;
    height: 150rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0rpx 30rpx;
}

.item-detail {
    padding: 5rpx 15rpx;
    border-radius: 10rpx;
    font-size: 28rpx;
    color: white;
    background-color: #007aff;
}

.item-line {
    position: absolute;
    bottom: 0rpx;
    left: 0rpx;
    height: 1px;
    width: 100%;
    background-color: #eeeeee;
}
</style>
