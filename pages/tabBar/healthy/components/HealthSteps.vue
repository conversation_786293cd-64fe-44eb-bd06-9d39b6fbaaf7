<script setup>
import { ref, computed } from "vue";
import { useHealthMemberInfoStore } from "@/store";
import { route } from "@/common/utils";

const props = defineProps({
	isShowBtn: {
		type: Boolean,
		default: true,
	},
	reportStepsData: {
		type: Object,
		default: () => {
			return {
				basicStatus: 0, // 基础档案
				naireStatus: 0, // 问卷
				pressureStatus: 0, // 血压采集
			};
		},
	},
});

const healthMemberInfoStore = useHealthMemberInfoStore();
const currentMemberInfo = computed(() => healthMemberInfoStore.getMemberInfo());

const configData = [
	{
		num: "①",
		label: "填写个人基础档案",
		prop: "basicStatus",
		time: "（2分钟）",
		active: false,
		path: "/pages/healthy/pages/health_record",
	},
	{
		num: "②",
		label: "完成健康问卷填写",
		prop: "naireStatus",
		time: "（3分钟）",
		active: false,
		path: "/pages/healthy/pages/health_questionnaire",
	},
	{
		num: "③",
		label: "完成一次血压采集",
		prop: "pressureStatus",
		time: "（3分钟）",
		active: false,
	},
];
const reportSteps = computed(() => {
	return configData.map((step) => {
		return {
			...step,
			active: props.reportStepsData[step.prop],
		};
	});
});

// 已完成步骤数量
const finishedCount = computed(() => {
	return reportSteps.value.filter((step) => step.active).length;
});

// 获取未完成的步骤
function handleStepClick() {
	const unFinishedSteps = reportSteps.value.filter((step) => !step.active);
	console.log(unFinishedSteps);
	if (!unFinishedSteps.length) return;
	if (unFinishedSteps[0].prop === "pressureStatus") {
		uni.showToast({
			title: "请完成一次血压采集",
			icon: "none",
		});
		return;
	}
	// 跳转至未完成的步骤
	route(unFinishedSteps[0].path);
}
</script>

<template>
	<view class="p-x-12">
		<text class="card-desc">请完成以下3个步骤来开启您的健康之旅：</text>
		<view class="steps-list">
			<view v-for="(step, idx) in reportSteps" :key="idx" :class="['step-item', step.active ? 'step-active' : '']">
				<text class="m-r-10">{{ step.num }}</text>
				<text>{{ step.label }}</text>
				<text>{{ step.time }}</text>
			</view>
		</view>
		<template v-if="isShowBtn">
			<!-- 如果都完成的话显示【去生成健康报告】-->
			<template v-if="finishedCount < 3">
				<button class="main-btn" @click="handleStepClick">去完成{{ finishedCount }}/3</button>
			</template>
			<template v-else>
				<slot name="finish"></slot>
			</template>
		</template>
	</view>
</template>

<style lang="scss" scoped>
// 变量区
$primary: #1abc9c;
$primary-light: #e6f9f5;
$primary-dark: #00b698;
$gray-bg: #f1f3f7;
$gray-card: #fff;
$gray-border: #e5e6eb;
$gray-text: #6d6d6c;
$gray-label: #888888;
$radius-lg: 24rpx;
$radius-md: 20rpx;
$radius-full: 50%;

.card-desc {
	font-size: 28rpx;
	color: #16202a;
}

.steps-list {
	width: 100%;
	margin: 16rpx 0;

	.step-item {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		background: #f5f6fa;
		border-radius: $radius-md;
		// border: 2rpx solid $gray-border;
		border: 2rpx solid #f5f6fa;
		padding: 20rpx 24rpx;
		// height: 64rpx;
		background-color: #f5f5f5;
		color: $gray-text;
		font-size: 28rpx;

		&.step-active {
			background: $primary-light;
			border-color: $primary;
			color: $primary-dark;
		}
	}
}

.main-btn {
	width: 320rpx;
	height: 72rpx;
	background: linear-gradient(90deg, #02c9a8 0%, #00b496 100%);
	color: #fff;
	font-size: 28rpx;
	font-weight: bold;
	border: none;
	border-radius: 36rpx;
	margin: 0 auto;
	display: block;
	box-shadow: 0 2rpx 8rpx 0 rgba(26, 188, 156, 0.08);
}
</style>
