<script setup>
const props = defineProps({
	icon: String,
	label: String,
	value: String,
	unit: String,
	time: String,
});

const emit = defineEmits(["click"]);
</script>
<template>
	<view class="bg-white b-rd-20 p-24" @click="emit('click')">
		<view class="flex items-center">
			<app-image class="mr-10" :src="icon" size="64" mode=""></app-image>
			<text class="text-32 text-#545a61">{{ label }}</text>
		</view>
		<view class="flex flex-col items-center mt-30">
			<text class="text-54 font-bold text-#1a1a1a">{{ value }}</text>
			<text class="text-24 text-#77838f mt-16 mb-18">{{ unit }}</text>
			<text class="text-24 text-#77838f">{{ time }}</text>
		</view>
	</view>
</template>
