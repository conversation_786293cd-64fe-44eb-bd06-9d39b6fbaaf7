/** * 个人中心页面 * 功能：用户信息展示、订单管理、各种功能入口、合伙人激活等 */
<script setup>
import { onShow } from '@dcloudio/uni-app'
import useConvert from '@/common/useConvert'
import { ref, reactive, computed } from 'vue'
import { useUserStore, useAppStore, useOrderStore } from '@/store'
import { getUserPerson, getCouponNumber, getAccounts, getDistributeCode, subActivatePartners } from '@/server/api'
import { route, goLogin, goPlatformCustomerService } from '@/common/utils'
import { common, my, logo } from '@/common/images'
import IconMessage from '@/static/tabBar/message_active.png'

const userStore = useUserStore()
const { divTenThousand, salesVolumeToStr } = useConvert()

const pagingRef = ref(null)

// 用户个人数据
const userData = reactive({
    userPerson: {
        balance: 0, // 余额
        collectCount: 0, // 收藏总数
        footprint: 0, // 足迹数量
        integral: 0, // 积分

        goodsCollectCount: 0, // 商品收藏数
        shopCollectCount: 0, // 店铺关注数

        couponCount: 0, // 优惠券数量
        bankCount: 0, // 银行卡数量

        isPartner: false, // 是否为合伙人
    },
})

// 获取用户头像（计算属性）
const getUserAvatar = computed(() => {
    let avatarSrc = logo // 默认头像
    if (userStore.userData && userStore.userData.avatar) {
        avatarSrc = userStore.userData.avatar
    }
    return avatarSrc
})

// 刷新页面数据
async function onRefresh() {
    await loadCountData()
    if (pagingRef.value) pagingRef.value.complete()
}

/**
 * 检查API响应是否有效
 * @param {Object} response - API响应对象
 * @returns {boolean} 是否有效
 */
function isValidResponse(response) {
    return response && response.apiStatus && response.data
}

// 加载用户统计数据
async function loadCountData() {
    // 只有登录用户才加载数据
    if (!userStore.checkLogin) return

    // 获取用户个人信息
    try {
        const userPersonRes = await getUserPerson()
        if (isValidResponse(userPersonRes)) {
            Object.assign(userData.userPerson, userPersonRes.data)
        }
    } catch (error) {
        console.error('获取用户信息失败:', error)
    }

    // 获取优惠券数量
    try {
        const couponNumberRes = await getCouponNumber()
        if (isValidResponse(couponNumberRes)) {
            userData.userPerson.couponCount = couponNumberRes.data.unuseNum
        }
    } catch (error) {
        console.error('获取优惠券数量失败:', error)
    }

    // 获取银行卡数量
    try {
        const accountsRes = await getAccounts()
        if (isValidResponse(accountsRes)) {
            userData.userPerson.bankCount = Object.keys(accountsRes.data).length
        }
    } catch (error) {
        console.error('获取银行卡信息失败:', error)
    }

    // 检查是否为合伙人
    try {
        const distributeCodeRes = await getDistributeCode()
        if (isValidResponse(distributeCodeRes)) {
            userData.userPerson.isPartner = true
        }
    } catch (error) {
        console.error('获取分销码失败:', error)
    }
}

// 跳转到编辑用户信息页面
function toEditUserInfo() {
    if (userStore.checkLogin) {
        route('/pages/my/pages/editUserInfo/editUserInfo')
    } else {
        goLogin()
    }
}

// 订单状态映射
const orderStatusMap = {
    dfk: 'UNPAID', // 待付款
    dfh: 'UN_DELIVERY', // 待发货
    dsh: 'UN_RECEIVE', // 待收货
    dpj: 'UN_COMMENT', // 待评价
    tksh: 'AFTER_SALE', // 退款/售后
    all: '', // 全部订单
}
// 跳转到订单页面
function goToOrder(orderType) {
    const orderStatus = orderStatusMap[orderType] || ''

    // 设置订单信息到store
    useOrderStore().changeGoOrderInfo({
        orderGoodsType: 'shop',
        orderStatus,
    })

    // 跳转到订单页面
    route({
        url: '/pages/tabBar/order/order',
    })
}

// 检查登录状态并执行回调
function checkLoginAndExecute(callback) {
    if (userStore.checkLogin) {
        if (typeof callback === 'function') callback()
    } else {
        // 显示登录弹窗
        useAppStore().changeShowLoginModal(true)
    }
}

// 合伙人激活相关状态
const activatePartnersModalRef = ref() // 激活弹窗引用
const activateCode = ref() // 激活码
const activateLoading = ref(false) // 激活加载状态

// 显示合伙人激活弹窗
function showActivatePartnersModal() {
    activateCode.value = ''
    activateLoading.value = false
    if (activatePartnersModalRef.value) activatePartnersModalRef.value.open()
}

// 关闭合伙人激活弹窗
function closeActivatePartnersModal() {
    activateCode.value = ''
    activateLoading.value = false
    if (activatePartnersModalRef.value) activatePartnersModalRef.value.close()
}

// 提交合伙人激活
async function submitActivatePartners() {
    if (!activateCode.value) return

    activateLoading.value = true

    try {
        const response = await subActivatePartners(
            {},
            {
                params: {
                    keyCode: activateCode.value,
                },
            },
        )

        if (response.apiStatus) {
            closeActivatePartnersModal()
            onRefresh() // 刷新页面数据
            uni.showToast({
                icon: 'none',
                title: '激活成功',
            })
        }
    } catch (error) {
        console.error('激活合伙人失败:', error)
    }

    activateLoading.value = false
}

// 用户统计数据配置
const userStatsConfig = [
    {
        key: 'goodsCollectCount',
        label: '收藏夹',
        route: '/pages/my/pages/favorites/favorites',
    },
    {
        key: 'shopCollectCount',
        label: '关注店铺',
        route: '/pages/my/pages/follow/follow',
    },
    {
        key: 'footprint',
        label: '足迹',
        route: '/pages/my/pages/footmark/footmark',
    },
]

// 订单状态配置
const orderStatusConfig = [
    {
        type: 'dfk',
        label: '待付款',
        icon: my.iconDaifukuanHei,
    },
    {
        type: 'dfh',
        label: '待发货',
        icon: my.iconDaifahuoHei,
    },
    {
        type: 'dsh',
        label: '待收货',
        icon: my.iconDaishouhuoHei,
    },
    {
        type: 'dpj',
        label: '待评价',
        icon: my.iconDaipingjiaHei,
    },
    {
        type: 'tksh',
        label: '退款/售后',
        icon: my.iconTuikuanshouhouHei,
    },
]

// 用户资产配置
const userAssetsConfig = [
    {
        key: 'couponCount',
        label: '优惠劵',
        route: '/pages/my/pages/coupon/coupon',
        showIcon: false,
    },
    {
        key: 'integral',
        label: '积分',
        route: '/pages/my/pages/integral/integral',
        showIcon: false,
    },
    {
        key: 'balance',
        label: '余额',
        route: '/pages/my/pages/wallet/wallet',
        showIcon: false,
        transform: true, // 需要除以万
    },
    {
        key: 'bankCount',
        label: '银行卡',
        route: '/pages/my/pages/account/account',
        showIcon: false,
    },
    {
        key: 'wallet',
        label: '我的钱包',
        route: '/pages/my/pages/wallet/wallet',
        showIcon: true,
        icon: my.iconQianbaohei,
    },
]

// 功能菜单配置
const menuConfig = [
    {
        label: '消息',
        route: '/pages/tabBar/message/message',
        icon: IconMessage,
    },
    {
        label: '被照护人',
        route: '/pages/my/pages/caregiver/caregiver',
        icon: my.iconBeizhaohuren,
    },
    {
        label: '我的评价',
        route: '/pages/my/pages/evaluation/evaluation',
        icon: my.iconYijianfankui,
    },
    {
        label: '邀请好友',
        route: '/pages/invite/pages/invite/invite',
        icon: my.iconYaoqinghaoypu,
    },
    {
        label: '积分商城',
        route: '/pages/home/<USER>/integralProductlHome/integralProductlHome',
        icon: my.iconJifenshangcheng,
    },
    {
        label: '入驻商家',
        route: '/pages/my/pages/merchantEnter/merchantEnter?mode=COMMON',
        icon: my.iconShangjiaruzhu,
    },
    {
        label: '入驻服务商',
        route: '/pages/my/pages/merchantEnter/merchantEnter?mode=O2O',
        icon: my.iconRuzhufuwushang,
    },
    {
        label: '服务地址',
        route: '/pages/my/pages/address/addressServer',
        icon: my.iconFuwudizhi,
    },
    {
        label: '收货地址',
        route: '/pages/my/pages/address/addressGoods',
        icon: my.iconShouhuodizhi,
    },
    {
        label: '意见反馈',
        route: '/pages/settings/pages/feedback/feedback',
        icon: my.iconYijianfankui,
    },
    {
        label: '联系客服',
        action: 'customerService',
        icon: my.iconLianxikefu,
    },
]

// 获取用户资产显示值
function getAssetValue(key, transform = false) {
    const value = userData.userPerson[key]
    if (transform && key === 'balance') {
        return salesVolumeToStr(divTenThousand(value))
    }
    return salesVolumeToStr(value)
}

// 处理菜单点击事件
function handleMenuClick(item) {
    if (item.action === 'customerService') {
        checkLoginAndExecute(() => goPlatformCustomerService())
    } else {
        checkLoginAndExecute(() => route(item.route))
    }
}

onShow(() => {
    onRefresh()
})
</script>
<template>
    <app-layout style="max-width: 750rpx; overflow-x: hidden">
        <app-image
            src="https://hejiawuyou.oss-accelerate.aliyuncs.com/app/2025/4/25680b287ee4b0dd233e32d838.png"
            width="770rpx"
            mode="widthFix"
            class="absolute top-0 left--15"
        ></app-image>

        <z-paging ref="pagingRef" refresher-theme-style="white" refresher-only @onRefresh="onRefresh">
            <template #top></template>
            <app-navBar></app-navBar>

            <view class="page">
                <view class="flex items-center justify-end px-38">
                    <view class="flex items-center">
                        <view
                            class="flex items-center text-#fff text-26"
                            @click="checkLoginAndExecute(() => route('/pages/my/pages/userCode/userCode'))"
                        >
                            <app-image :src="my.iconHuiyuanmaBai" size="40" mr="5" class="w-40 h-40" mode=""></app-image>
                            <view class="">会员码</view>
                        </view>

                        <view class="flex items-center text-#fff text-26 ml-40">
                            <app-image :src="common.iconSetBai" size="40" mode="" @click="route('/pages/settings/pages/home/<USER>')"></app-image>
                        </view>
                    </view>
                </view>

                <view class="h-full flex justify-between pr-38 pl-45 mt-25" @click="toEditUserInfo">
                    <view class="flex justify-start items-center">
                        <app-image :src="getUserAvatar" size="112" rd="50%" mr="26" mode=""></app-image>
                        <template v-if="userStore.token">
                            <view class="h-120 flex flex-col justify-center">
                                <text class="text-42 text-#fff font-bold">{{ userStore.userData.nickname || '' }}</text>
                                <view class="flex justify-start items-center mt-10">
                                    <text class="text-28 text-#fff font-500">ID:{{ userStore.userData.userId || '' }}</text>
                                </view>
                            </view>
                        </template>
                        <template v-else>
                            <view class="h-120 flex flex-col justify-center">
                                <text class="text-42 text-#fff font-bold">请先登录</text>
                            </view>
                        </template>
                    </view>

                    <view class="flex flex-center">
                        <app-image :src="common.iconRightBai" size="26" mode=""></app-image>
                    </view>
                </view>

                <!-- 用户统计数据展示区域 -->
                <view class="flex items-center justify-around text-#fff text-26 font-500 mt-40">
                    <view
                        v-for="item in userStatsConfig"
                        :key="item.key"
                        class="flex flex-col min-w-180"
                        @click="checkLoginAndExecute(() => route(item.route))"
                    >
                        <view class="text-center text-48 font-bold">{{ salesVolumeToStr(userData.userPerson[item.key]) }}</view>
                        <view class="text-center mt-20">{{ item.label }}</view>
                    </view>
                </view>

                <view class="mt-30 px-30">
                    <view class="w-full h-160 relative">
                        <app-image
                            src="https://hejiawuyou.oss-accelerate.aliyuncs.com/app/2025/5/7681a76fce4b0dd233e32d8b1.png"
                            class="w-full absolute"
                            mode="widthFix"
                        ></app-image>
                        <view class="content relative w-full h-full pt-32 px-42">
                            <view class="flex items-center justify-between">
                                <view class="flex flex-col">
                                    <app-image :src="my.iconVipLogo" width="114" height="34" mode=""></app-image>

                                    <view class="mt-18 text-#D9C395 text-22 font-500">开通VIP 享专属权益</view>
                                </view>

                                <view class="">
                                    <view
                                        class="px-25 h-64 border-rd-50 flex flex-center ac-op"
                                        style="background: linear-gradient(-90deg, #f0ca97 0%, #f8edd0 100%)"
                                        @click="checkLoginAndExecute(() => route('/pages/my/pages/memberCenter/memberCenter'))"
                                    >
                                        <view class="text-26 text-#4D3A32 font-bold">会员中心</view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="bg-#F1F3F7 border-rd-24 px-30 pt-50 pb-50" style="margin-top: -25rpx">
                    <view class="w-full border-rd-20 bg-#fff px-30 py-35">
                        <view class="flex items-center">
                            <view class="flex-1 flex items-center justify-between">
                                <view class="flex-1 flex flex-col" @click="checkLoginAndExecute(() => route('/pages/my/pages/getCoupon/getCoupon'))">
                                    <view class="text-32 font-bold">领劵中心</view>
                                    <view class="flex items-center mt-18">
                                        <view class="text-24 text-#888">领取优惠劵</view>
                                        <app-image :src="common.iconRightHui" size="18" ml="8" mode=""></app-image>
                                    </view>
                                </view>

                                <view class="flex-shrink-0 w-80">
                                    <app-image :src="my.iconLingquanzhongxin" size="80" mode=""></app-image>
                                </view>
                            </view>

                            <view class="w-2 flex-shrink-0 h-42 bg-#F6F6F6 mx-30"></view>

                            <view class="flex-1 flex items-center justify-between">
                                <view class="flex-1 flex flex-col" @click="checkLoginAndExecute(() => route('/pages/my/pages/integral/integral'))">
                                    <view class="text-32 font-bold">我的积分</view>
                                    <view class="flex items-center mt-18">
                                        <view class="text-24 text-#888">查看积分明细</view>
                                        <app-image :src="common.iconRightHui" size="18" ml="8" mode=""></app-image>
                                    </view>
                                </view>

                                <view class="flex-shrink-0 w-80">
                                    <app-image :src="my.iconWodejifen" size="80" mode=""></app-image>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="w-full border-rd-20 bg-#fff px-30 py-35 mt-20">
                        <view class="flex items-center justify-between">
                            <view class="text-32 text-#292929 font-bold">商品订单</view>

                            <view class="flex items-center text-26 text-#999999" @click="goToOrder('all')">
                                <view class="">全部订单</view>

                                <app-image :src="common.iconRightHui" size="20" mode=""></app-image>
                            </view>
                        </view>

                        <!-- 订单状态按钮区域 -->
                        <view class="grid grid-cols-5 mt-35">
                            <view
                                v-for="item in orderStatusConfig"
                                :key="item.type"
                                class="flex flex-col flex-center ac-op"
                                @click="goToOrder(item.type)"
                            >
                                <app-image :src="item.icon" size="54" mode=""></app-image>
                                <view class="text-26 mt-18">{{ item.label }}</view>
                            </view>
                        </view>
                    </view>

                    <!-- 用户资产信息卡片 -->
                    <view class="w-full border-rd-20 bg-#fff px-30 py-35 mt-20">
                        <view class="grid grid-cols-5">
                            <view
                                v-for="item in userAssetsConfig"
                                :key="item.key"
                                class="flex flex-col flex-center ac-op"
                                @click="checkLoginAndExecute(() => route(item.route))"
                            >
                                <!-- 显示图标或数值 -->
                                <app-image v-if="item.showIcon" :src="item.icon" size="54" mode=""></app-image>
                                <view v-else class="text-40 font-500">{{ getAssetValue(item.key, item.transform) }}</view>

                                <view class="text-26 mt-18">{{ item.label }}</view>
                            </view>
                        </view>
                    </view>

                    <!-- 功能菜单区域 -->
                    <view class="w-full border-rd-20 bg-#fff px-30 py-35 mt-20">
                        <view class="grid grid-cols-4 gap-y-42rpx">
                            <!-- 前三个固定菜单 -->
                            <view
                                v-for="item in menuConfig.slice(0, 3)"
                                :key="item.label"
                                class="flex flex-col flex-center ac-op"
                                @click="handleMenuClick(item)"
                            >
                                <app-image :src="item.icon" size="68" mode=""></app-image>
                                <view class="text-26 mt-12">{{ item.label }}</view>
                            </view>

                            <!-- 合伙人相关功能 -->
                            <template v-if="userData.userPerson.isPartner">
                                <view
                                    class="flex flex-col flex-center ac-op"
                                    @click="checkLoginAndExecute(() => route('/pages/my/pages/teamCenter/teamCenter'))"
                                >
                                    <app-image :src="my.iconWodeyaoqing" size="68" mode=""></app-image>
                                    <view class="text-26 mt-12">我的邀请</view>
                                </view>
                            </template>
                            <template v-else>
                                <view class="flex flex-col flex-center ac-op" @click="checkLoginAndExecute(() => showActivatePartnersModal())">
                                    <app-image :src="my.iconHehuoren" size="68" mode=""></app-image>
                                    <view class="text-26 mt-12">激活合伙人</view>
                                </view>
                            </template>

                            <!-- 其余菜单项 -->
                            <view
                                v-for="item in menuConfig.slice(3)"
                                :key="item.label"
                                class="flex flex-col flex-center ac-op"
                                @click="handleMenuClick(item)"
                            >
                                <app-image :src="item.icon" size="68" mode=""></app-image>
                                <view class="text-26 mt-12">{{ item.label }}</view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <my-uv-modal
                ref="activatePartnersModalRef"
                content=""
                width="600rpx"
                title="激活码"
                :titleStyle="{ fontWeight: 'bold', fontSize: '32rpx' }"
                :closeOnClickOverlay="false"
                showCancelButton
            >
                <view class="w-full mt-20">
                    <input
                        class="bg-#F2F4F8 h-105 px-40 border-rd-53 text-36 text-#222222 font-bold text-center"
                        v-model="activateCode"
                        placeholder="请输入合伙人激活码"
                        placeholder-class="text-32 text-#A7ACB7 font-500"
                    />
                </view>
                <template #confirmButton>
                    <view class="w-full flex items-center justify-between px-41 pb-44">
                        <view class="w-246">
                            <uv-button
                                color="#F2F3F6"
                                text="取消"
                                class="w-full mt-0 flex-center"
                                custom-style="height: 90rpx;"
                                customTextStyle="font-size: 30rpx; font-weight: bold; color: #828D9C;"
                                shape="circle"
                                loadingMode="circle"
                                @click="closeActivatePartnersModal"
                            ></uv-button>
                        </view>
                        <view class="w-246">
                            <uv-button
                                color="#00B496"
                                text="确认激活"
                                class="w-full mt-0 flex-center"
                                custom-style="height: 90rpx;"
                                customTextStyle="font-size: 30rpx; font-weight: bold;"
                                shape="circle"
                                loadingMode="circle"
                                :loading="activateLoading"
                                :disabled="!activateCode"
                                @click="submitActivatePartners()"
                            ></uv-button>
                        </view>
                    </view>
                </template>
            </my-uv-modal>
        </z-paging>
    </app-layout>
</template>
