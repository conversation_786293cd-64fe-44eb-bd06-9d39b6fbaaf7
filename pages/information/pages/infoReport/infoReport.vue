<template>
  <app-layout>
    <uv-form labelPosition="top" :model="form" :rules="rules" ref="formRef" class="p-30">
      <uv-form-item label="请选择你要举报的类型" prop="type" labelWidth="auto" class="title">
        <view class="w-full grid grid-cols-3 gap-15 color-#323232 font-size-27 font-bold text-center mt-20rpx">
          <view class="type-item px-10 py-20 bg-#F2F3F6 rounded-6" :class="{ active: reportName === item }"
            v-for="(item, index) in typeList" :key="index" @click="reportName = item">
            <text>{{ item }}</text>
          </view>
        </view>
      </uv-form-item>

      <uv-form-item label="请填写举报信息" prop="desc" labelWidth="auto" class="title">
        <uv-textarea v-model="form.desc" placeholder="请详细描述出现的问题，上传相关页面的截图，以便我们提供更好的帮助" count height="125"
          class="mt-20rpx" :maxlength="200" :customStyle="{
            background: '#F2F4F8',
            borderRadius: '20rpx',
            border: 'none',
          }" :countStyle="{
            background: 'none',
          }"></uv-textarea>
      </uv-form-item>

      <uv-form-item :label="`请上传举报图片(${imageList.length}/3)`" labelWidth="auto" class="title">
        <uv-upload :fileList="imageList" name="file" multiple :maxCount="3" :previewFullImage="true" class="mt-20rpx"
          @afterRead="afterRead" @delete="deletePic" uploadIcon="plus"></uv-upload>
      </uv-form-item>
      <uv-form-item label="联系电话" prop="phone" labelWidth="auto" class="title">
        <uv-input placeholder="请输入手机号便于联系" border="bottom" v-model="form.phone" class="mt-20rpx"></uv-input>
      </uv-form-item>

      <view class="fixed bottom-0 left-0 h-142 w-full px-30 pt-15 border-t-solid border-1 border-#E9EDF1">
        <uv-button class="w-full" type="primary" size="large" text="提交" shape="circle" @click="submit"
          :disabled="!isCompleted || s"></uv-button>
      </view>
    </uv-form>
  </app-layout>
</template>

<script setup>
import { computed, ref, reactive } from "vue";
import { upload, reportArticle } from "@/server/api";
import { useUserStore } from "@/store";
import { useRoute } from "vue-router";


// 举报类型数据
const typeList = ref([
  "违法违规",
  "色情低俗",
  "诈骗信息",
  "虚假广告",
  "搬运抄袭",
  "谩骂攻击",
  "不实信息",
  "引人不适",
  "疑似自杀",
  "侵权投诉",
  "关于未成年人",
  "其他",
]);
const phoneReg = /^1[3-9]\d{9}$/
const formRef = ref(null);
const form = reactive({
  desc: "",
  phone: "",
});

const rules = {
  desc: {
    required: true,
    message: "请填写举报信息",
    trigger: ["blur", 'change'],
  },
  phone: {
    required: true,
    pattern: phoneReg,
    message: "请填写正确的手机号",
    trigger: ["blur", 'change'],
  },
};
// 选中的举报类型
const reportName = ref("");
// 已上传图片列表
const imageList = ref([]);

const isCompleted = computed(() => {
  return reportName.value && form.desc && form.phone && phoneReg.exec(form.phone) ? true : false;

});

// 新增图片
async function afterRead(event) {
  // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
  let lists = [].concat(event.file);
  let fileListLen = imageList.value.length;
  lists.map((item) => {
    imageList.value.push({
      ...item,
      status: "uploading",
      message: "上传中",
    });
  });
  for (let i = 0; i < lists.length; i++) {
    let status = "failed";
    let fileUrl = lists[i].url;
    try {
      const result = await upload(lists[i].url);
      if (result.apiStatus) {
        status = "success";
        fileUrl = result.data;
      }
    } catch (error) {
      console.error("上传失败", error);
    }
    let item = imageList.value[fileListLen];
    imageList.value.splice(
      fileListLen,
      1,
      Object.assign(item, {
        status,
        message: status === "failed" ? "上传失败" : "",
        url: fileUrl,
      })
    );
    fileListLen += 1;

    form.reportImage = imageList.value.filter(item => item.status === 'success').map(item => item.url).join("|")
  }
}

// 删除图片
function deletePic(event) {
  imageList.value.splice(event.index, 1);
}
// 路由传参 - 资讯id
// #ifdef H5
let { id } = useRoute().query;
// #endif
// #ifndef H5
let id = "";
// #endif

const isSubmitting = ref(false);
const submit = async () => {
  try {
    console.log('提交');
    const isValid = await formRef.value.validate()
    if (isValid) {
      isSubmitting.value = true;
      const userId = useUserStore().userData.userId || '';
      const params = {
        userId,
        articleId: id,
        reportInfo: form.desc,
        reportName: reportName.value,
        reportImage: form.reportImage,
        phone: form.phone,
      }
      const { apiStatus } = await reportArticle(params)
      if (apiStatus) {
        uni.showToast({
          title: '举报成功！',
          icon: 'success',
          duration: 2000,
        });
        setTimeout(() => {
          uni.navigateBack({
            delta: 1,
          });
        }, 1000)

      }
    }
  } catch (error) {
    uni.showToast({
      title: '举报失败，请稍后再试',
      icon: 'error',
      duration: 2000,
    });
  } finally {
    isSubmitting.value = false;

  }


}
</script>

<style lang="scss" scoped>
.title {
  font-size: 30rpx;
  font-weight: bold;
  color: #222222;
}

.type-item {
  border: 1px solid #e9edf1;
  border-radius: 6rpx;

  &.active {
    color: #00b496;
    border: 1px solid #66c5b5;
    background: #f0fcfc;
  }
}
</style>
