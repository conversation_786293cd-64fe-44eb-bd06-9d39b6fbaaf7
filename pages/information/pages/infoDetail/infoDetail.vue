<template>
    <app-layout>
        <view>
            <app-navBar
                :back="true"
                :fixed="true"
                :fixed-placeholder="true"
                :backIcon="common.iconLeftHei"
                backIconSize="40rpx"
                bgColor="#fff"
                :showRight="true"
            >
                <!-- #ifndef MP-WEIXIN -->
                <template v-slot:right>
                    <view class="pr-20">
                        <app-image :src="information.moreBlack" width="40rpx" mode="widthFix" @click="showActionSheet"></app-image>
                    </view>
                </template>
                <!-- #endif -->

                <!-- </template> -->
            </app-navBar>
            <view class="infoDetail w-full h-full bg-#fff relative pb-180">
                <!-- 标题 -->
                <view class="py-20 border-b-solid border-1 border-#F1F3F8 bg-#fff px-50" ref="headerRef">
                    <view class="font-600 text-44 color-#1A1A1A lh-8"> {{ infoDetail?.title }}</view>
                    <view class="flex justify-between items-center mt-20 font-500 text-28 color-#777">
                        <view>
                            <text class="mr-15 color-#00B698">{{ infoDetail?.categoryName }}</text>
                            <text>{{ getRelativeTime(infoDetail?.updateTime) }}</text>
                        </view>
                        <text> {{ salesVolumeToStr(infoDetail?.views) }}阅读 </text>
                    </view>
                </view>

                <!-- 正文 -->
                <div v-if="!loading && infoDetail?.content" class="content mt-30 px-50">
                    <!-- 渲染解析后的内容节点 -->
                    <view v-for="(node, index) in parsedNodes" :key="index" class="content-node">
                        <!-- 视频节点 -->
                        <video
                            v-if="node.type === 'video'"
                            :src="node.src"
                            :poster="node.poster"
                            :controls="true"
                            width="100%"
                            height="auto"
                            class="video"
                        ></video>
                        <!-- 其他节点 -->
                        <rich-text v-else :nodes="node.content"></rich-text>
                    </view>
                </div>

                <!-- 加载状态 -->
                <view v-if="loading" class="flex justify-center items-center py-100">
                    <uv-loading-icon mode="flower"></uv-loading-icon>
                </view>

                <!-- 错误状态 -->
                <view v-if="error && !loading" class="flex flex-col items-center py-100">
                    <text class="text-28 color-#999 mb-20">{{ error }}</text>
                    <uv-button type="primary" size="small" @click="retry">重试</uv-button>
                </view>
                <!-- 底部 -->
                <view class="fixed bottom-0 left-0 w-full bg-#fff border-t-solid border-1 border-#F2F2F2 px-40">
                    <view class="flex item-center justify-between py-20 bg-#fff">
                        <view class="flex items-center flex-start bg-#F1F4F8 pr-30 rounded-50">
                            <app-image :src="getUserAvatar" size="80" rd="50%" mr="26" mode=""></app-image>
                            <text class="font-bold text-28 color-#1A1A1A max-w-180 whitespace-nowrap overflow-hidden text-ellipsis">{{
                                userStore.userData.nickname || '游客'
                            }}</text>
                        </view>
                        <view class="flex gap-30 items-center">
                            <view class="flex items-center gap-10" :class="{ 'opacity-50': isThumbing }">
                                <app-image
                                    :src="isThumbed ? information.liked : information.like"
                                    width="50rpx"
                                    mode="widthFix"
                                    @click="thumbUp"
                                    :class="{ 'pointer-events-none': isThumbing }"
                                ></app-image>
                                <view>
                                    {{ salesVolumeToStr(infoDetail?.thumbs) }}
                                </view>
                            </view>
                            <!-- #ifdef MP-WEIXIN -->
                            <button open-type="share" plain="true" style="border: none; padding: 0">
                                <app-image :src="information.share" width="50rpx" mode="widthFix"></app-image>
                            </button>
                            <!-- #endif -->
                        </view>
                    </view>
                    <uv-safe-bottom></uv-safe-bottom>
                </view>
            </view>
        </view>
        <!-- 分享弹窗 -->
        <PageInfoSharePopup ref="shareActionRef" :shareData="shareData" @handleReport="handleReport" />
    </app-layout>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad, onReady, onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
import { common, information, logo } from '@/common/images'
import { useUserStore, useAppStore } from '@/store'
import { getRelativeTime, parseRichText } from '@/utils'
import { salesVolumeToStr } from '@/common/utils'
import { useRoute } from 'vue-router'
import { getArticleDetail, thumbUpArticle, addArticleViews } from '@/server/api'
import { toast } from '@/uni_modules/uv-ui-tools/libs/function/index.js'
import { debounce } from '@/utils/debounce'
import PageInfoSharePopup from '@/components/page-components/page-info-share-popup/page-info-share-popup.vue'

const userStore = useUserStore() // 用户状态管理：获取用户信息、登录状态检查
const appStore = useAppStore() // 应用状态管理：控制登录弹窗显示

const headerRef = ref(null)
const infoDetail = ref(null) // 文章详情数据：存储从API获取的文章完整信息
const parsedNodes = ref([])
const loading = ref(false) // 加载状态：控制加载动画显示/隐藏
const error = ref(null) // 错误信息：存储API请求失败时的错误消息
const isThumbed = ref(false) // 点赞状态：标识当前用户是否已点赞该文章
const isThumbing = ref(false) // 点赞进行中：防止重复点击，显示点赞动画

// 路由传参 - 资讯id：从URL参数或页面跳转参数中获取文章ID
// #ifdef H5
let { id } = useRoute().query // H5环境：从路由查询参数获取文章ID
// #endif
// #ifndef H5
let id = '' // 非H5环境：通过onLoad回调获取文章ID
// #endif

// 计算属性 - 用户头像：优先使用用户自定义头像，否则使用默认logo
const getUserAvatar = computed(() => {
    return userStore.userData?.avatar || logo
})

// 计算属性 - 分享数据：动态生成当前文章的分享信息，用于分享组件
const shareData = computed(() => {
    if (!infoDetail.value) return {} // 文章数据未加载时返回空对象

    return {
        title: infoDetail.value.title, // 分享标题：使用文章标题
        path: `/pages/information/pages/infoDetail/infoDetail?id=${id}`, // 分享路径：当前文章详情页
        imageUrl: infoDetail.value.cover, // 分享图片：使用文章封面图
        summary: infoDetail.value.summary || '健康生活，从这里开始', // 分享描述：优先使用文章摘要
    }
})

// 获取文章详情
const fetchData = async () => {
    if (!id) {
        error.value = '文章ID不能为空'
        return
    }

    try {
        loading.value = true
        error.value = null

        const { data, code } = await getArticleDetail({ id })

        if (code === 200 && data) {
            infoDetail.value = data
            // 从服务器数据中获取点赞状态
            isThumbed.value = data.isThumbed || false
            parsedNodes.value = parseRichText(data.content)
        } else {
            throw new Error('获取文章详情失败')
        }
    } catch (err) {
        error.value = err.message || '获取文章详情失败'
        toast('获取文章详情失败，请稍后重试')
    } finally {
        loading.value = false
    }
}

// 点赞功能 - 带防抖处理
const thumbUp = debounce(async () => {
    // 防止重复点赞
    if (isThumbing.value || isThumbed.value) {
        return
    }

    try {
        isThumbing.value = true

        await thumbUpArticle({ id })

        // 更新本地状态
        isThumbed.value = true
        if (infoDetail.value) {
            infoDetail.value.thumbs = (infoDetail.value.thumbs || 0) + 1
        }

        toast('点赞成功')
    } catch (err) {
        toast('点赞失败，请稍后重试')
    } finally {
        isThumbing.value = false
    }
}, 500)

// 增加浏览量
const addViews = async () => {
    if (!id) return

    try {
        await addArticleViews({ id })
    } catch (err) {
        console.error('增加阅读量失败:', err)
    }
}

// 重试获取数据
const retry = () => {
    fetchData()
}

onLoad((option) => {
    id = option.id
    fetchData()
    addViews()
})

const shareActionRef = ref() // 分享弹窗组件引用：用于控制分享弹窗的显示/隐藏

// 打开分享面板：点击导航栏更多按钮时调用
const showActionSheet = () => {
    shareActionRef.value.open()
}

// 举报文章：处理用户举报操作，需要登录后才能举报
const handleReport = () => {
    if (userStore.checkLogin) {
        // 跳转到举报页面，传递文章ID
        uni.navigateTo({
            url: `/pages/information/pages/infoReport/infoReport?id=${infoDetail.value?.id}`,
        })
    } else {
        // 未登录时显示登录弹窗
        appStore.changeShowLoginModal(true)
    }
}

// 页面准备完毕后的回调：设置微信小程序分享配置
onReady(() => {
    // #ifdef MP-WEIXIN
    // 微信小程序分享给好友：用户点击右上角分享按钮时的配置
    onShareAppMessage(() => {
        return {
            title: shareData.value.title || '和家无忧', // 分享标题：优先使用文章标题
            path: shareData.value.path || `/pages/information/pages/infoDetail/infoDetail?id=${id}`, // 分享页面路径
            imageUrl: shareData.value.imageUrl, // 分享图片：使用文章封面
        }
    })
    // 微信小程序分享到朋友圈：用户分享到朋友圈时的配置
    onShareTimeline(() => {
        return {
            title: shareData.value.title || '和家无忧', // 朋友圈标题
            query: `id=${id}`, // 朋友圈分享参数：文章ID
            imageUrl: shareData.value.imageUrl, // 朋友圈分享图片
        }
    })
    // #endif
})
</script>

<style lang="scss" scoped>
.infoDetail {
    .content {
        overflow-x: hidden;
        .video {
            width: 100% !important;
        }
    }
}
</style>
