<script setup>
import { onLoad, onReady, onPageScroll, onReachBottom } from "@dcloudio/uni-app";

import { ref, reactive, computed, nextTick, watch } from "vue";

import { useAppStore } from "@/store";

const appStore = useAppStore();

import { canENV, goArticlePage } from "@/common/utils";
import { logo } from "@/common/images";
</script>

<template>
	<app-layout>
		<view class="about-us bg-page-fff flex flex-col">
			<view class="pt-178 w-full h-full flex flex-col flex-1">
				<view class="w-full flex flex-center">
					<app-image :src="logo" class="" width="340rpx" mode="widthFix"></app-image>
				</view>
				<view class="w-full px-30 mt-85">
					<view class="w-full py-10 px-30 bg-#fff border-rd-20 info-box">
						<uv-list>
							<uv-list-item title="" :clickable="true" @click="goArticlePage('gywm')">
								<view class="w-full flex items-center justify-between py-30">
									<view class="text-28 font-bold">公司信息</view>
									<uv-icon name="arrow-right" color="#828D9C" size="26rpx"></uv-icon>
								</view>
							</uv-list-item>
							<!-- <uv-list-item title="" :clickable="true" @click="goArticlePage('ptgy')" border>
								<view class="w-full flex items-center justify-between py-30">
									<view class="text-28 font-bold">平台公约</view>
									<uv-icon name="arrow-right" color="#828D9C" size="26rpx"></uv-icon>
								</view>
							</uv-list-item> -->
							<uv-list-item title="" :clickable="true" @click="goArticlePage('fwxy')" border>
								<view class="w-full flex items-center justify-between py-30">
									<view class="text-28 font-bold">服务协议</view>
									<uv-icon name="arrow-right" color="#828D9C" size="26rpx"></uv-icon>
								</view>
							</uv-list-item>
							<uv-list-item title="" :clickable="true" @click="goArticlePage('yszc')" border>
								<view class="w-full flex items-center justify-between py-30">
									<view class="text-28 font-bold">隐私政策</view>
									<uv-icon name="arrow-right" color="#828D9C" size="26rpx"></uv-icon>
								</view>
							</uv-list-item>
							<!-- <uv-list-item title="" :clickable="false">
								<view class="w-full flex items-center justify-between py-30">
									<view class="text-28 font-bold">检查更新</view>
									<uv-icon name="arrow-right" color="#828D9C" size="26rpx"></uv-icon>
								</view>
							</uv-list-item>
							<uv-list-item title="" :clickable="false">
								<view class="w-full flex items-center justify-between py-30">
									<view class="text-28 font-bold">给我们打分</view>
									<uv-icon name="arrow-right" color="#828D9C" size="26rpx"></uv-icon>
								</view>
							</uv-list-item> -->
						</uv-list>
					</view>
				</view>
				<view class="w-full flex-1"></view>
				<view class="w-full pb-66">
					<view class="text-19 font-500 text-#777777 text-center">{{ appStore.appPublicSetting.RECORDER_INFO }}</view>
					<view class="text-22 font-500 text-#777777 mt-10 text-center">苏州悠康智能科技有限公司 版权所有</view>
					<view class="text-19 font-500 text-#777777 mt-10 text-center">{{ appStore.appPublicSetting.COPYRIGHT_INFO }}</view>
				</view>
			</view>
		</view>
	</app-layout>
</template>

<style lang="scss" scoped>
.about-us {
	width: 100%;
	min-height: calc(100vh - var(--window-bottom) - var(--window-top));
}

.info-box {
	box-shadow: 0rpx 0rpx 10rpx 0rpx rgba(220, 227, 236, 0.2);
}
</style>
