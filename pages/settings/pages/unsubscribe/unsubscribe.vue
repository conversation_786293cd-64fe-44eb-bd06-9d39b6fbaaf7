<script setup>
import { onLoad, onReady, onPageScroll, onReachBottom } from '@dcloudio/uni-app';
import { ref, reactive, computed, nextTick, watch } from 'vue';
import { useUserStore, useLoginStore } from '@/store';
import { canENV, route, goArticlePage } from '@/common/utils';

const userStore = useUserStore();
const loginStore = useLoginStore();

const cancelAccountModal = ref();

function cancelAccount(verify = false) {
	if (!verify) {
		cancelAccountModal.value.open();
		return;
	}

	loginStore.userUnsubscrib();
	// loginStore.logOut();
}
</script>

<template>
	<app-layout>
		<view class="w-full pt-80 flex-col">
			<view class="w-full px-50">
				<view class="text-50 font-bold">注销当前账号</view>

				<!-- 	<view class="text-28 font-500 text-#4B4B54 mt-30">
					<text>很遗憾无法继续为您服务，在注销之前请您仔细阅读</text>
					<text class="text-main" @click="goArticlePage('zxxz')">《注销须知》</text>
					<text>。</text>
				</view> -->

				<view class="text-28 font-500 text-#4B4B54 mt-35">
					<text>注销代表您同意放弃账号内所有虚拟资产</text>
				</view>
			</view>

			<view class="w-full bg-#fff mt-440">
				<view class="px-55 bg-#fff">
					<uv-button
						type="primary"
						text="注销账号"
						class="w-full flex-center font-bold"
						custom-style="height: 88rpx; border-radius: 44rpx;"
						customTextStyle="font-size: 30rpx;"
						@click="cancelAccount(false)"
					></uv-button>
				</view>
			</view>

			<my-uv-modal
				ref="cancelAccountModal"
				confirmText="确认"
				:confirmColor="'#E74353'"
				:lineColor="'#F2F6FB'"
				cancelText="取消"
				:duration="200"
				width="580rpx"
				showCancelButton
				@confirm="cancelAccount(true)"
				:asyncClose="true"
				:closeOnClickOverlay="false"
				:buttonReverse="true"
			>
				<view class="flex flex-col text-center line-height-35">
					<view class="text-34 font-bold">确定注销吗？</view>
					<view class="text-26 font-500 mt-17">注销后将不能再登录</view>
				</view>
			</my-uv-modal>
		</view>
	</app-layout>
</template>

<style lang="scss" scoped>
.footer-box-box {
	position: fixed;
	bottom: 0;
}

.footer-box {
	width: 100%;
	height: 130rpx;
	border-top: solid 1rpx #edeef0;
	padding: 13rpx 30rpx 40rpx 30rpx;
}
</style>
