<script setup>
import { reactive, ref } from 'vue';

import { onLoad } from '@dcloudio/uni-app';

import { upload, feedbackAdd } from '@/server/api';

import { useUserStore } from '@/store';

import { REGEX_MOBILE } from '@/common/test';

const userStore = useUserStore();

const pageData = reactive({
	formData: {
		image: [], // 相关截图
		content: '', // 问题内容
		mobile: '' // 联系电话
	},
	rules: {
		content: {
			rules: [
				{
					required: true,
					errorMessage: '请输入问题详情'
				}
			]
		}
	}
});

const form = ref(null);

onLoad((options) => {
	init();
});

function init() {}

function chooseImage() {
	uni.chooseImage({
		count: 3 - pageData.formData.image.length,
		sizeType: ['original', 'compressed'],
		sourceType: ['album'],
		success: (res) => {
			for (let i in res.tempFilePaths) {
				uni.showLoading({
					title: '上传中...',
					mask: true
				});
				upload(res.tempFilePaths[i], { formData: res.tempFilePaths })
					.then((resFile) => {
						uni.hideLoading();
						if (resFile.apiStatus) {
							pageData.formData.image.push(resFile.data);
						}
					})
					.catch((err) => {
						uni.hideLoading();
					});
			}
		}
	});
}

function submit() {
	form.value
		.validate()
		.then(() => {
			if (pageData.formData.image.length === 0) {
				uni.showToast({
					title: '请上传图片',
					icon: 'none'
				});
				return;
			}

			if (!pageData.formData.mobile) {
				uni.showToast({
					title: '请输入联系电话',
					icon: 'none'
				});
				return;
			}

			if (!pageData.formData.mobile) {
				uni.showToast({
					title: '请输入联系电话',
					icon: 'none'
				});
				return;
			}

			if (!REGEX_MOBILE(pageData.formData.mobile)) {
				uni.showToast({
					title: '请输入正确的联系电话',
					icon: 'none'
				});
				return;
			}

			const params = JSON.parse(JSON.stringify(pageData.formData));
			params.image = params.image.join(',');
			uni.showLoading({
				title: '加载中...'
			});
			feedbackAdd({
				userId: userStore.userData.userId,
				suggestion: pageData.formData.content,
				pictures: pageData.formData.image,
				phone: pageData.formData.mobile,
				platform: 1,
				type: 'feedback'
			})
				.then((res) => {
					console.log(res);
					uni.hideLoading();
					if (res.apiStatus) {
						uni.showToast({
							title: '感谢您的反馈',
							icon: 'success'
						});
						pageData.formData.image = [];
						pageData.formData.content = '';
						pageData.formData.mobile = '';
					}
				})
				.catch((err) => {
					uni.hideLoading();
					console.log(err);
				});
		})
		.catch((err) => {
			console.log('err', err);
		});
}

function delImage(index) {
	pageData.formData.image.splice(index, 1);
}

function goBack() {
	uni.navigateBack({
		delta: 1
	});
}
</script>
<template>
	<app-layout>
		<view class="report">
			<!-- <app-navBar class="bg-#fff" back>
				<template #content>
					<view class="text-34 text-center font-bold">
						意见反馈
					</view>
				</template>
			</app-navBar> -->
			<uv-navbar title="意见反馈" titleStyle="font-size: 30rpx; font-weight: bold;" placeholder autoBack></uv-navbar>
			<uni-forms ref="form" label-position="top" :modelValue="pageData.formData" :rules="pageData.rules" err-show-type="toast">
				<uni-forms-item name="content">
					<template #label>
						<view class="px-40">
							<view class="text-30 text-#16202A font-bold">
								<text class="text-red">*</text>
								<text>问题详情</text>
							</view>
						</view>
					</template>
					<template #default>
						<view class="px-40 py-30">
							<view class="p-20 bg-#F2F4F8 border-rd-20 mt-0">
								<uv-textarea
									v-model="pageData.formData.content"
									border="none"
									count
									height="200rpx"
									placeholder="请详细描述出现的问题，上传相关页面的截图，以便我们提供更好的帮助"
									maxlength="200"
									:textStyle="{ color: '#222', fontSize: '28rpx' }"
									:customStyle="{ padding: '0', background: '#F2F4F8' }"
								></uv-textarea>
								<!-- <view class="grid grid-cols-4 mt-16">
									<view class="relative w-140 h-140 mt-10" v-for="(item,index) in pageData.formData.image" :key="index"
									>
										<app-image class="w-140 h-140 border-rd-10 overflow-hidden" :src="item"
											mode="aspectFill"></app-image>
										<image class="absolute top-0 right-0 w-42 h-42" @click="delImage(index)"
											src="@/pages/dynamics/static/del2.png" mode=""></image>
									</view>
									<image v-if="pageData.formData.image.length < 3" class="w-140 h-140 mt-10"
										src="@/pages/dynamics/static/upload3.png" mode="" @click="chooseImage"></image>
								</view> -->
							</view>
						</view>
					</template>
				</uni-forms-item>

				<uni-forms-item name="content">
					<template #label>
						<view class="px-40">
							<view class="text-30 text-#16202A font-bold">
								<text class="text-red">*</text>
								<text>上传图片({{ pageData.formData.image.length }}/3)</text>
							</view>
						</view>
					</template>
					<template #default>
						<view class="px-40 py-15">
							<view class="p-0 border-rd-20 mt-0">
								<view class="grid grid-cols-3 gap-20 mt-16">
									<template v-for="(item, index) in pageData.formData.image" :key="index">
										<view class="relative w-200 h-200 mt-10 border-rd-20 overflow-hidden">
											<app-image size="200rpx" :src="item" mode="aspectFill"></app-image>
											<view @click="delImage(index)" class="w-42 h-42 absolute top-0 right-0 flex flex-center border-rd-5 bg-[rgba(0,0,0,0.4)]">
												<uv-icon name="trash" color="#fff"></uv-icon>
											</view>
										</view>
									</template>

									<template v-if="pageData.formData.image.length < 3">
										<view class="w-200 h-200 bg-#F2F4F8 flex flex-center border-rd-20 ac-op" @click="chooseImage">
											<uv-icon name="plus" size="38rpx" bold color="#B0B5BC"></uv-icon>
										</view>
									</template>
								</view>
							</view>
						</view>
					</template>
				</uni-forms-item>
			</uni-forms>
			<view class="px-40 py-30">
				<view class="text-30 text-#16202A font-bold">
					<text class="text-red">*</text>
					<text>联系电话</text>
				</view>
				<view class="mt-20">
					<uv-input placeholder="请输入手机号便于联系" border="none" v-model="pageData.formData.mobile" :customStyle="{ paddingLeft: 0 }"></uv-input>
				</view>
				<view class="w-full h-1 bg-#E9EDF1 mt-20"></view>
			</view>
			<view class="w-full h-142"></view>
			<view class="w-full px-30 pt-14 bg-#fff fixed left-0 bottom-0 border-t-1 border-t-solid border-t-#EDEEF0">
				<uv-button
					@click="submit()"
					color="#00B496"
					text="提交"
					class="w-690 h-88"
					custom-style="border-radius: 44rpx;"
					customTextStyle="font-size: 30rpx; color: #fff; font-weight: bold;"
				></uv-button>
				<app-safeAreaBottom></app-safeAreaBottom>
			</view>
		</view>
	</app-layout>
</template>
<style lang="scss" scoped></style>
