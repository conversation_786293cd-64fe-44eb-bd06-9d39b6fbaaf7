<script setup>
import { onLoad } from '@dcloudio/uni-app';

import { ref, reactive, computed } from 'vue';

import { useAppStore, useUserStore } from '@/store';

import { canENV, route, goArticlePage, revocationPrivacyAgreement } from '@/common/utils';

const userStore = useUserStore();

const pageData = reactive({});

const personalized = computed({
	get: () => {
		return userStore.checkLogin && userStore.personalizedOpen;
	},
	set: (val) => {
		if (userStore.checkLogin) {
			userStore.setPersonalizedOpen(val);
		} else {
			useAppStore().changeShowLoginModal(true);
		}
	}
});

const revocationPrivacyAgreementRef = ref();

function revocationPrivacyAgreementFun(verify = false) {
	if (!verify) {
		revocationPrivacyAgreementRef.value.open();
		return;
	}

	revocationPrivacyAgreement();
	revocationPrivacyAgreementRef.value.close();
}

function changeItem() {
	setData();
}

function setData() {}

function loadData() {}

onLoad((options) => {
	loadData();
});
</script>

<template>
	<app-layout>
		<view class="privacy-settings page-main bg-page-main">
			<view class="w-full px-0 py-10">
				<view class="w-full info-box mt-30" v-if="false">
					<view class="info-title-box text-93939C text-24 font-500 pl-16 pb-18">
						<view class="">系统管理</view>
					</view>
					<view class="info-content-box w-full bg-#fff border-rd-20 mb-24 overflow-hidden">
						<view class="flex px-30 w-full active:bg-card-ac-bg" @click="route('/pages/settings/pages/permissionManage/permissionManage')">
							<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3 b-b-0">
								<view class="flex flex-col flex-1 py-35 pl-6">
									<view class="font-bold text-30">系统权限管理</view>
									<view class="text-24 mt-10 flex items-center">
										<view class="text-93939C">管理您已授权在APP使用的系统权限</view>
									</view>
								</view>
								<view class="flex items-center">
									<view class="font-500 text-7A7A8A text-26"></view>
									<uv-icon name="arrow-right" :color="'#828D9C'" size="26	rpx" class="ml-13" bold></uv-icon>
								</view>
							</view>
						</view>
					</view>
				</view>

				<view class="w-full info-box">
					<view class="info-title-box text-93939C text-24 font-500 pl-30 py-18 bg-#F1F3F7">
						<view class="">隐私设置</view>
					</view>
					<view class="info-content-box w-full bg-#fff border-rd-20 mb-0 overflow-hidden">
						<view class="flex px-30 w-full active:bg-card-ac-bg">
							<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3 b-b-0">
								<view class="flex flex-col flex-1 py-35 pl-6">
									<view class="font-bold text-30">个性化推荐</view>
								</view>
								<view class="flex items-center">
									<view class="font-500 text-7A7A8A text-26"></view>
									<uv-switch v-model="personalized" size="32rpx" :activeColor="'#38B597'"></uv-switch>
								</view>
							</view>
						</view>
					</view>
				</view>

				<view class="w-full info-box">
					<view class="info-title-box text-93939C text-24 font-500 pl-30 py-18 bg-#F1F3F7">
						<view class="">隐私说明</view>
					</view>
					<view class="info-content-box w-full bg-#fff border-rd-20 mb-24 overflow-hidden">
						<view class="flex px-30 w-full active:bg-card-ac-bg">
							<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3 b-b-1" @click="goArticlePage('yszc')">
								<view class="flex flex-col flex-1 py-35 pl-6">
									<view class="font-bold text-30">隐私协议</view>
								</view>
								<view class="flex items-center">
									<view class="font-500 text-7A7A8A text-26"></view>
									<uv-icon name="arrow-right" :color="'#828D9C'" size="26	rpx" class="ml-13" bold></uv-icon>
								</view>
							</view>
						</view>

						<view class="flex px-30 w-full active:bg-card-ac-bg">
							<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3 b-b-1" @click="goArticlePage('fwxy')">
								<view class="flex flex-col flex-1 py-35 pl-6">
									<view class="font-bold text-30">用户服务协议</view>
								</view>
								<view class="flex items-center">
									<view class="font-500 text-7A7A8A text-26"></view>
									<uv-icon name="arrow-right" :color="'#828D9C'" size="26	rpx" class="ml-13" bold></uv-icon>
								</view>
							</view>
						</view>

						<!-- <view class="flex px-30 w-full active:bg-card-ac-bg">
							<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3  b-b-1">
								<view class="flex flex-col flex-1 py-35 pl-6">
									<view class="font-bold text-30">
										法律协议
									</view>
								</view>
								<view class="flex items-center">
									<view class="font-500 text-7A7A8A text-26"></view>
									<uv-icon name="arrow-right" :color="'#828D9C'" size="26	rpx"
										class="ml-13" bold></uv-icon>
								</view>
							</view>
						</view> -->

						<view class="flex px-30 w-full active:bg-card-ac-bg">
							<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3 b-b-1" @click="goArticlePage('wcnrysxy')">
								<view class="flex flex-col flex-1 py-35 pl-6">
									<view class="font-bold text-30">未成年人隐私协议</view>
								</view>
								<view class="flex items-center">
									<view class="font-500 text-7A7A8A text-26"></view>
									<uv-icon name="arrow-right" :color="'#828D9C'" size="26	rpx" class="ml-13" bold></uv-icon>
								</view>
							</view>
						</view>

						<view class="flex px-30 w-full active:bg-card-ac-bg">
							<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3 b-b-1" @click="goArticlePage('dsfsdk')">
								<view class="flex flex-col flex-1 py-35 pl-6">
									<view class="font-bold text-30">第三方信息共享清单</view>
								</view>
								<view class="flex items-center">
									<view class="font-500 text-7A7A8A text-26"></view>
									<uv-icon name="arrow-right" :color="'#828D9C'" size="26	rpx" class="ml-13" bold></uv-icon>
								</view>
							</view>
						</view>

						<view class="flex px-30 w-full active:bg-card-ac-bg">
							<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3 b-b-1" @click="goArticlePage('grxxqd')">
								<view class="flex flex-col flex-1 py-35 pl-6">
									<view class="font-bold text-30">个人信息收集清单</view>
								</view>
								<view class="flex items-center">
									<view class="font-500 text-7A7A8A text-26"></view>
									<uv-icon name="arrow-right" :color="'#828D9C'" size="26	rpx" class="ml-13" bold></uv-icon>
								</view>
							</view>
						</view>

						<view class="flex px-30 w-full active:bg-card-ac-bg">
							<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3 b-b-1" @click="goArticlePage('qxjytsm')">
								<view class="flex flex-col flex-1 py-35 pl-6">
									<view class="font-bold text-30">应用权限及用途说明</view>
								</view>
								<view class="flex items-center">
									<view class="font-500 text-7A7A8A text-26"></view>
									<uv-icon name="arrow-right" :color="'#828D9C'" size="26	rpx" class="ml-13" bold></uv-icon>
								</view>
							</view>
						</view>

						<!-- <view class="flex px-30 w-full active:bg-card-ac-bg">
							<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3  b-b-1"
								@click="goArticlePage('smrzfuxy')">
								<view class="flex flex-col flex-1 py-35 pl-6">
									<view class="font-bold text-30">
										实名认证服务协议
									</view>
								</view>
								<view class="flex items-center">
									<view class="font-500 text-7A7A8A text-26"></view>
									<uv-icon name="arrow-right" :color="'#828D9C'" size="26	rpx" class="ml-13" bold></uv-icon>
								</view>
							</view>
						</view> -->

						<view class="flex px-30 w-full active:bg-card-ac-bg" v-if="false">
							<!-- #ifdef APP -->
							<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3 b-b-1" @click="goArticlePage('rlclgz')">
								<!-- #endif -->
								<!-- #ifndef APP -->
								<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3 b-b-0" @click="goArticlePage('rlclgz')">
									<!-- #endif -->
									<view class="flex flex-col flex-1 py-35 pl-6">
										<view class="font-bold text-30">人脸处理规则</view>
									</view>
									<view class="flex items-center">
										<view class="font-500 text-7A7A8A text-26"></view>
										<uv-icon name="arrow-right" :color="'#828D9C'" size="26	rpx" class="ml-13" bold></uv-icon>
									</view>
								</view>
							</view>

							<!-- #ifdef APP -->
							<view class="flex px-30 w-full active:bg-card-ac-bg" @click="revocationPrivacyAgreementFun(false)">
								<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3 b-b-0">
									<view class="flex flex-col flex-1 py-35 pl-6">
										<view class="font-bold text-30">撤回隐私协议授权</view>
										<view class="text-24 mt-10 flex items-center">
											<view class="text-93939C">若您撤回隐私协议授权，将无法体验应用的全部功能</view>
										</view>
									</view>
									<view class="flex items-center">
										<view class="font-500 text-7A7A8A text-26"></view>
										<uv-icon name="arrow-right" :color="'#828D9C'" size="26	rpx" class="ml-13" bold></uv-icon>
									</view>
								</view>
							</view>
							<!-- #endif -->
						</view>
					</view>

					<!-- #ifdef APP -->
				</view>
				<!-- #endif -->
				<!-- #ifndef APP -->
			</view>
			<!-- #endif -->

			<my-uv-modal
				ref="revocationPrivacyAgreementRef"
				confirmText="确认撤回"
				:confirmColor="'#38B597'"
				:lineColor="'#F2F6FB'"
				cancelText="暂不撤回"
				:duration="200"
				width="580rpx"
				showCancelButton
				@confirm="revocationPrivacyAgreementFun(true)"
				:asyncClose="true"
				:closeOnClickOverlay="false"
			>
				<view class="flex-center h-100">
					<view class="text-28 font-bold">若您撤回隐私协议授权，将无法体验应用的全部内容，确定要撤回吗？</view>
				</view>
			</my-uv-modal>
		</view>
	</app-layout>
</template>

<style scoped lang="scss">
.privacy-settings {
}
</style>
