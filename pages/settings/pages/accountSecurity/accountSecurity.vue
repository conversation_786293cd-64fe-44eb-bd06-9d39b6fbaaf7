<script setup>
import { onLoad, onReady, onPageScroll, onReachBottom } from '@dcloudio/uni-app';

import { ref, reactive, computed, nextTick, watch } from 'vue';

import { useUserStore, useAppStore, useLoginStore } from '@/store';

import { canENV, route } from '@/common/utils';

const userStore = useUserStore();
// const userInfo = computed(() => userStore.userInfo)
const userData = computed(() => userStore.userData);
const appStore = useAppStore();

function changePassword() {
	if (userData.value.phone === '') {
		route({
			url: '/pages/login/pages/setPassword/setPassword',
			type: 'navigateTo'
		});
	}
	if (userData.value.phone !== '') {
		route({
			url: '/pages/login/pages/changePassword/changePassword',
			type: 'navigateTo'
		});
	}
}

function weixinBind() {
	if (!userStore.userData.appOpenid) {
		if (!appStore.configInfo.wxLogin) {
			uni.showToast({
				title: '当前设备未安装微信或微信版本过低',
				icon: 'none',
				position: 'bottom'
			});
			return;
		}
		useLoginStore().weiXinBind(true);
	} else {
		useLoginStore().weiXinBind(false);
	}
}

function appleBind() {
	if (userInfo.value.verification.wechat === 0) {
		if (!appStore.configInfo.wxLogin) {
			return;
		}
		userStore.appleBind(true);
	}
	if (userInfo.value.verification.wechat === 1) {
		userStore.appleBind(false);
	}
}

onLoad(() => {});
</script>

<template>
	<app-layout>
		<view class="settings bg-#fff">
			<view class="w-full">
				<view class="w-full h-24 bg-#F1F3F7"></view>
				<view class="w-full info-box">
					<!-- <view class="info-title-box text-93939C text-24 font-500 pl-16 pb-18">
						<view class=""></view>
					</view> -->
					<view class="info-content-box w-full bg-#fff border-rd-0 mb-0 overflow-hidden">
						<!-- <view class="flex px-30 w-full active:bg-card-ac-bg"
							@click="route('/pages/settings/pages/changeMobile/changeMobile')">
							<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3  b-b-1">
								<view class="flex flex-1 items-center py-35 font-bold text-30">
									绑定手机
								</view>
								<view class="flex items-center">
									<view class="font-500 text-7A7A8A text-26">
										<uv-text mode="phone" format="encrypt" size="28rpx" :text="userData.phone" :call="false"></uv-text>
									</view>
									<uv-icon name="arrow-right" :color="'#828D9C'" size="26	rpx" class="ml-13" bold></uv-icon>
								</view>
							</view>
						</view> -->

						<view class="flex px-30 w-full active:bg-card-ac-bg">
							<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3 b-b-1">
								<view class="flex flex-1 items-center py-35 font-bold text-30">绑定手机</view>
								<view class="flex items-center">
									<view class="font-500 text-7A7A8A text-26">
										<uv-text mode="phone" format="encrypt" size="28rpx" :text="userData.phone" :call="false"></uv-text>
									</view>
								</view>
							</view>
						</view>

						<!-- <view class="flex px-30 w-full active:bg-card-ac-bg">
							<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3  b-b-[0rpx]"
								@click="changePassword">
								<view class="flex flex-1 items-center py-35 font-bold text-30">
									登录密码
								</view>
								<view class="flex items-center">
									<template v-if="userInfo.verification.password === 1">
										<view class="font-500 text-26">去修改</view>
									</template>
									<template v-if="userInfo.verification.password === 0">
										<view class="font-500 text-26">去设置</view>
									</template>
									<uv-icon name="arrow-right" :color="'#828D9C'" size="26rpx" class="ml-13" bold></uv-icon>
								</view>
							</view>
						</view> -->
					</view>
				</view>

				<template v-if="appStore.configInfo.wxLogin">
					<view class="w-full py-16 px-30 text-24 font-500 text-828D9C bg-#F1F3F7">第三方账号绑定</view>

					<view class="w-full info-box">
						<view class="info-content-box w-full bg-#fff border-rd-0 mb-0 overflow-hidden">
							<view class="flex px-30 w-full active:bg-card-ac-bg">
								<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3 b-b-0">
									<!-- <app-image src="@/static/common/icon_wx.png" size="48rpx" mr="15rpx"></app-image> -->
									<view class="flex flex-1 items-center py-35 font-bold text-30">微信</view>
									<view class="flex items-center">
										<template v-if="userStore.userData.appOpenid">
											<uv-button
												text="解绑"
												class="w-90"
												:customStyle="{ background: 'rgba(255, 255, 255, 0)', height: '50rpx' }"
												:customTextStyle="{ fontSize: '24rpx', fontWeight: '500' }"
												@click="weixinBind"
												plain
											></uv-button>
										</template>
										<template v-else>
											<uv-button
												type="primary"
												text="绑定"
												class="w-90"
												:customStyle="{ background: 'rgba(255, 255, 255, 0)', height: '50rpx' }"
												:customTextStyle="{ fontSize: '24rpx', fontWeight: '500' }"
												@click="weixinBind"
												plain
											></uv-button>
										</template>
									</view>
								</view>
							</view>
						</view>
					</view>
				</template>
				<!-- <template v-if="configStore.checkWeiXinLogin">
					<view class="w-full py-16 px-30 text-24 font-500 text-828D9C bg-#F5F6F7">
						第三方账号绑定
					</view>

					<view class="w-full info-box">
						<view class="info-content-box w-full bg-#fff border-rd-0 mb-0 overflow-hidden">

							<view class="flex px-30 w-full active:bg-card-ac-bg" v-if="configStore.checkWeiXinLogin">
								<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3  b-b-[0rpx]">
									<app-image src="@/static/common/icon_wx.png" size="48rpx" mr="15rpx"></app-image>
									<view class="flex flex-1 items-center py-35 font-bold text-30">
										微信
									</view>
									<view class="flex items-center">
										<template v-if="userInfo.verification.wechat === 1">
											<uv-button text="解绑" class="w-90"
												:customStyle="{background: 'rgba(255, 255, 255, 0)', height: '50rpx'}"
												:customTextStyle="{fontSize: '24rpx', fontWeight: '500'}" @click="weixinBind" plain></uv-button>
										</template>
										<template v-if="userInfo.verification.wechat === 0">
											<uv-button type="primary" text="绑定" class="w-90"
												:customStyle="{background: 'rgba(255, 255, 255, 0)', height: '50rpx'}"
												:customTextStyle="{fontSize: '24rpx', fontWeight: '500'}" @click="weixinBind" plain></uv-button>
										</template>
									</view>
								</view>
							</view>

							<view class="flex px-30 w-full active:bg-card-ac-bg" v-if="configStore.checkiPhoneLogin && false">
								<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3  b-b-[0rpx]">
									<app-image src="@/static/common/icon_iphone.png" size="48rpx" mr="15rpx"></app-image>
									<view class="flex flex-1 items-center py-35 font-bold text-30">
										Apple
									</view>
									<view class="flex items-center">
										<template v-if="userInfo.verification.apple === 1">
											<uv-button text="解绑" class="w-90"
												:customStyle="{background: 'rgba(255, 255, 255, 0)', height: '50rpx'}"
												:customTextStyle="{fontSize: '24rpx', fontWeight: '500'}" plain></uv-button>
										</template>
										<template v-if="userInfo.verification.apple === 0">
											<uv-button type="primary" text="绑定" class="w-90"
												:customStyle="{background: 'rgba(255, 255, 255, 0)', height: '50rpx'}"
												:customTextStyle="{fontSize: '24rpx', fontWeight: '500'}" plain></uv-button>
										</template>
									</view>
								</view>
							</view>
						</view>
					</view>
				</template> -->

				<view class="w-full h-14 bg-#F1F3F7"></view>

				<view class="w-full info-box">
					<!-- <view class="info-title-box text-93939C text-24 font-500 pl-16 pb-18">
						<view class=""></view>
					</view> -->
					<view class="info-content-box w-full bg-#fff border-rd-0 mb-24 overflow-hidden">
						<view class="flex px-30 w-full active:bg-card-ac-bg">
							<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3 b-b-0" @click="route('/pages/settings/pages/unsubscribe/unsubscribe')">
								<view class="flex flex-1 items-center py-35 font-bold text-30">账户注销</view>
								<view class="flex items-center">
									<uv-icon name="arrow-right" :color="'#828D9C'" size="26rpx" class="ml-13" bold></uv-icon>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</app-layout>
</template>

<style lang="scss" scoped>
.settings {
	width: 100%;
	min-height: calc(100vh - var(--window-bottom) - var(--window-top));
}
</style>
