<script setup>
	import {
		onLoad,
		onReady,
		onPageScroll,
		onReachBottom
	} from '@dcloudio/uni-app';

	import {
		ref,
		reactive,
		computed,
		nextTick,
		watch
	} from 'vue';

	import {
		useUserStore
	} from '@/store';

	import {
		canENV,
		route
	} from '@/common/utils'

	const userStore = useUserStore();
</script>

<template>
	<app-layout>
		<view class="w-full pt-145 flex flex-center flex-col page-main">
			<app-image src="@/pages/settings/static/changeMobile/icon_genghuanshoujihao.png" size="200rpx"></app-image>
			<view class="mt-75 text-30 font-500 text-#7B7B7B">
				已绑定手机号
			</view>
			<view class="text-48 font-bold mt-30">
				<uv-text mode="phone" format="encrypt" size="48rpx" bold :text="userStore.userData.phone"
					:call="false"></uv-text>
			</view>

			<view class="w-full footer-box-box bg-#fff">
				<view class="footer-box bg-#fff">
					<uv-button type="primary" text="更换手机号" class=" w-full flex-center font-bold"
						custom-style="height: 88rpx; border-radius: 44rpx;" customTextStyle="font-size: 30rpx;"
						@click="route('/pages/login/pages/verifyOlMobile/verifyOlMobile')"></uv-button>
				</view>
			</view>
		</view>
	</app-layout>
</template>

<style lang="scss" scoped>
	.footer-box-box {
		position: fixed;
		bottom: 0;
	}

	.footer-box {
		width: 100%;
		height: 130rpx;
		border-top: solid 1rpx #EDEEF0;
		padding: 13rpx 30rpx 40rpx 30rpx;
	}
</style>