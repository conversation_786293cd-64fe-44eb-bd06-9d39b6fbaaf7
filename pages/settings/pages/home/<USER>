<script setup>
import { onLoad } from '@dcloudio/uni-app';

import { ref, reactive, computed, nextTick, watch } from 'vue';

import { useUserStore, useLoginStore, useAppStore } from '@/store';

import { canENV, getAppCacheSize, appClearCache, route, goArticlePage, goLogin } from '@/common/utils';

const userStore = useUserStore();
const loginStore = useLoginStore();

const loginOutModal = ref();
const cancelAccountModal = ref();
const clearCacheModal = ref();

const appCacheSize = ref('');

function cancelAccount(verify = false) {
	if (!verify) {
		cancelAccountModal.value.open();
		return;
	}
}

function loginOut(verify = false) {
	if (!verify) {
		loginOutModal.value.open();
		return;
	}
	loginStore.logOut();
}

async function loadData() {
	appCacheSize.value = await getAppCacheSize();
}

function clearAppCache(verify = false) {
	if (parseInt(appCacheSize.value) === 0) {
		uni.showToast({
			title: '暂无缓存~'
		});
	} else {
		if (verify) {
			appClearCache().then(async (res) => {
				clearCacheModal.value.close();
				uni.showToast({
					title: '缓存清理完成!',
					shock: true
				});
				appCacheSize.value = await getAppCacheSize();
			});
		} else {
			clearCacheModal.value.open();
		}
	}
}

function canICallBack(cb, type = '') {
	if (userStore.checkLogin) {
		if (typeof cb === 'function') cb();
	} else {
		// goLogin();
		useAppStore().changeShowLoginModal(true);
	}
}

onLoad(() => {
	loadData();
});
</script>

<template>
	<app-layout>
		<view class="settings bg-page-main">
			<view class="w-full h-24 bg-#F1F3F7"></view>
			<view class="w-full px-0 py-0">
				<view class="w-full info-box px-5">
					<!-- <view class="info-title-box text-93939C text-24 font-500 pl-16 pb-18">
						<view class=""></view>
					</view> -->
					<view class="info-content-box w-full bg-#fff border-rd-20 mb-0 overflow-hidden">
						<view class="flex px-30 w-full ac-op" @click="canICallBack(() => route('/pages/settings/pages/accountSecurity/accountSecurity'))">
							<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3 b-b-1">
								<view class="flex flex-1 items-center py-35 font-bold text-30">账户与安全</view>
								<view class="flex items-center">
									<view class="font-500 text-7A7A8A text-26"></view>
									<uv-icon name="arrow-right" :color="'#828D9C'" size="26rpx" class="ml-13" bold></uv-icon>
								</view>
							</view>
						</view>

						<!-- <view class="flex px-30 w-full active:bg-card-ac-bg"
							@click="route('/pages/settings/pages/notificationSettings/notificationSettings')">
							<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3  b-b-1">
								<view class="flex flex-1 items-center py-35 font-bold text-30">
									通知设置
								</view>
								<view class="flex items-center">
									<view class="font-500 text-7A7A8A text-26"></view>
									<uv-icon name="arrow-right" :color="'#828D9C'" size="26rpx" class="ml-13" bold></uv-icon>
								</view>
							</view>
						</view> -->

						<view class="flex px-30 w-full active:bg-card-ac-bg" @click="route('/pages/settings/pages/privacySettings/privacySettings')">
							<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3 b-b-0">
								<view class="flex flex-1 items-center py-35 font-bold text-30">隐私设置</view>
								<view class="flex items-center">
									<view class="font-500 text-7A7A8A text-26"></view>
									<uv-icon name="arrow-right" :color="'#828D9C'" size="26rpx" class="ml-13" bold></uv-icon>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- #ifdef APP -->
				<view class="w-full h-24 bg-#F1F3F7"></view>

				<view class="w-full info-box px-5">
					<!-- <view class="info-title-box text-93939C text-24 font-500 pl-16 pb-18">
						<view class=""></view>
					</view> -->
					<view class="info-content-box w-full bg-#fff border-rd-20 mb-0 overflow-hidden">
						<view class="flex px-30 w-full active:bg-card-ac-bg" @click="clearAppCache(false)">
							<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3 b-b-0">
								<view class="flex flex-1 items-center py-35 font-bold text-30">清理缓存</view>
								<view class="flex items-center">
									<view class="font-500 text-7A7A8A text-26">{{ appCacheSize }}</view>
									<uv-icon name="arrow-right" :color="'#828D9C'" size="26rpx" class="ml-13" bold></uv-icon>
								</view>
							</view>
						</view>
					</view>
				</view>
				<!-- #endif -->

				<view class="w-full h-24 bg-#F1F3F7"></view>

				<view class="w-full info-box">
					<!-- <view class="info-title-box text-93939C text-24 font-500 pl-16 pb-18">
						<view class=""></view>
					</view> -->
					<view class="info-content-box w-full bg-#fff border-rd-20 mb-24 overflow-hidden">
						<view class="flex px-30 w-full active:bg-card-ac-bg">
							<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3 b-b-1" @click="canICallBack(() => route('/pages/settings/pages/feedback/feedback'))">
								<view class="flex flex-1 items-center py-35 font-bold text-30">意见反馈</view>
								<view class="flex items-center">
									<view class="font-500 text-7A7A8A text-26"></view>
									<uv-icon name="arrow-right" :color="'#828D9C'" size="26rpx" class="ml-13" bold></uv-icon>
								</view>
							</view>
						</view>

						<!-- <view class="flex px-30 w-full active:bg-card-ac-bg">
							<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3  b-b-1"
								@click="goArticlePage('lxwm')">
								<view class="flex flex-1 items-center py-35 font-bold text-30">
									联系我们
								</view>
								<view class="flex items-center">
									<view class="font-500 text-7A7A8A text-26"></view>
									<uv-icon name="arrow-right" :color="'#828D9C'" size="26rpx" class="ml-13" bold></uv-icon>
								</view>
							</view>
						</view> -->

						<view class="flex px-30 w-full active:bg-card-ac-bg">
							<view class="flex w-full items-center justify-between b-b-solid border-#EAEDF3 b-b-0" @click="route('/pages/settings/pages/aboutUs/aboutUs')">
								<view class="flex flex-1 items-center py-35 font-bold text-30">关于我们</view>
								<view class="flex items-center">
									<view class="font-500 text-7A7A8A text-26"></view>
									<uv-icon name="arrow-right" :color="'#828D9C'" size="26rpx" class="ml-13" bold></uv-icon>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- <view class="w-full py-10 px-30 bg-#fff border-rd-20 info-box mb-20">
					<uv-list>
						<uv-list-item title="" :clickable="true" @click="cancelAccount(false)">
							<view class="w-full flex items-center justify-between py-30">
								<view class="text-28 font-bold">注销账号</view>
								<uv-icon name="arrow-right" color="#828D9C" size="26rpx"></uv-icon>
							</view>
						</uv-list-item>
					</uv-list>
				</view> -->

				<view class="w-full mt-46 px-36">
					<template v-if="userStore.checkLogin">
						<uv-button
							text="退出账户"
							loadingMode="circle"
							class="w-full flex-center"
							custom-style="height: 98rpx; border-radius: 98rpx; border: none; box-shadow: 0rpx 0rpx 10rpx 0rpx rgba(220,227,236,0.2); background: #EFEFEF;"
							:customTextStyle="{ fontWeight: 'bold', fontSize: '30rpx', color: true ? '#323232' : '#E74353' }"
							@click="loginOut(false)"
						></uv-button>
					</template>
					<template v-else>
						<uv-button
							text="前往登录"
							loadingMode="circle"
							class="w-full flex-center"
							custom-style="height: 98rpx; border-radius: 98rpx; border: none; box-shadow: 0rpx 0rpx 10rpx 0rpx rgba(220,227,236,0.2); background: #EFEFEF;"
							:customTextStyle="{ fontWeight: 'bold', fontSize: '30rpx', color: true ? '#323232' : '#E74353' }"
							@click="goLogin(true)"
						></uv-button>
					</template>
				</view>
			</view>

			<my-uv-modal
				ref="cancelAccountModal"
				confirmText="确认"
				:confirmColor="'#E74353'"
				:lineColor="'#F2F6FB'"
				cancelText="取消"
				:duration="200"
				width="580rpx"
				showCancelButton
				@confirm="cancelAccount(true)"
				:asyncClose="true"
				:closeOnClickOverlay="false"
			>
				<view class="flex flex-col text-center line-height-35">
					<view class="text-34 font-bold">确定注销吗？</view>
					<view class="text-26 font-500 mt-17">注销后将不能再登录</view>
				</view>
			</my-uv-modal>

			<my-uv-modal
				ref="loginOutModal"
				confirmText="确认"
				:confirmColor="'#38B597'"
				:lineColor="'#F2F6FB'"
				cancelText="取消"
				:duration="200"
				width="580rpx"
				showCancelButton
				@confirm="loginOut(true)"
				:asyncClose="true"
				:closeOnClickOverlay="false"
			>
				<view class="flex-center h-100">
					<view class="text-34 font-bold">确定退出吗？</view>
				</view>
			</my-uv-modal>

			<my-uv-modal
				ref="clearCacheModal"
				confirmText="确认清理"
				:confirmColor="'#38B597'"
				:lineColor="'#F2F6FB'"
				cancelText="暂不清理"
				:duration="200"
				width="580rpx"
				showCancelButton
				@confirm="clearAppCache(true)"
				:asyncClose="true"
				:closeOnClickOverlay="false"
			>
				<view class="flex-center h-100">
					<view class="text-34 font-bold">确定清理所有缓存吗？</view>
				</view>
			</my-uv-modal>
		</view>
	</app-layout>
</template>

<style lang="scss" scoped>
.settings {
	width: 100%;
	min-height: calc(100vh - var(--window-bottom) - var(--window-top));
}
</style>
