<template>
	<view class="permission-manage page-main bg-page-main">

		<view class="w-full px-30 py-30">
			<view class="w-full px-30 py-40">
				<view class="info-title-box text-93939C text-24 font-500">
					<view class="">
						为了向您提供更好的使用体验，我们会在特定场景下向您申请以下手机系统权限
					</view>
				</view>
			</view>

			<view class="w-full info-box mt-0">
				<!-- <view class="info-title-box text-93939C text-24 font-500 pl-16 pb-18">
					<view class="">
						系统管理
					</view>
				</view> -->
				<view class="info-content-box w-full bg-#fff border-rd-20 mb-24 overflow-hidden">

					<view class="flex px-30 w-full active:bg-card-ac-bg">
						<view class=" flex w-full items-center justify-between b-b-solid border-#EAEDF3  b-b-0">
							<view class="flex flex-col flex-1 py-35 pl-6">
								<view class="font-bold text-30">
									相机权限
								</view>
								<view class="text-24 mt-10 flex items-center">
									<view class="text-93939C">
										开启后，可实现扫描、拍照、录视频等功能
									</view>
								</view>
							</view>
							<view class="flex items-center">
								<view class="font-500 text-7A7A8A text-26">未开启</view>
								<uv-icon name="arrow-right" :color="'#828D9C'" size="26rpx" class="ml-13" bold></uv-icon>
							</view>
						</view>
					</view>

				</view>
			</view>
		</view>

	</view>
</template>

<script setup>
	import {
		onLoad
	} from '@dcloudio/uni-app';

	import {
		ref
	} from 'vue';

	import {
		canENV
	} from '@/common/utils';

	onLoad((options) => {

	})
</script>

<style scoped lang="scss">
	.permission-manage {}
</style>