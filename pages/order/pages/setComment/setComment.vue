<script setup>
import { onLoad, onReady } from '@dcloudio/uni-app';

import { ref, reactive, computed, unref } from 'vue';

import { useUserStore } from '@/store';

import { Decimal } from 'decimal.js';

import useConvert from '@/common/useConvert';

import { getShopOrderItem, upload, submitAfsOrder, submitBuyersEvaluation, getServerOrderInfo, getOrderEvaluateWordList } from '@/server/api';

import { aRefundWhy } from '@/pages/order/common/types/order';

import { canENV, getPriceInfo } from '@/common/utils';

import { useOrderStore } from '@/store';

import { deepClone, sleep } from '@/uni_modules/uv-ui-tools/libs/function/index.js';

const { divTenThousand, mulTenThousand } = useConvert();

const userStore = useUserStore();

const _props = defineProps({});

const _data = reactive({
	serviceStaffEvaluationType: 0, // -1->不满意 1->满意
	serviceStaffEvaluationTextList: [],
	serviceStaffEvaluationTextIndex: 0,
	serviceStaffEvaluationTextIndexs: [],
	serviceStaffEvaluationTextItems: []
});

const formData = ref([
	{
		comment: '',
		rate: 5,
		medias: []
	}
]);

const formRules = {
	comment: [
		{
			required: true,
			message: '请填写评价',
			trigger: ['blur']
		}
	]
};

const pageFormRef = ref();

const orderInfo = ref();
const goodsInfoList = ref();

const orderDetail = ref();

const imageList = ref([]);

const isServerGoods = computed(() => {
	return Array.isArray(goodsInfoList.value) && goodsInfoList.value.length > 0 && goodsInfoList.value[0].serveGoods === 1;
});

const submitDisabled = computed(() => {
	let disabled = false;
	if (Array.isArray(goodsInfoList.value) && goodsInfoList.value.length > 0) {
		for (let item of formData.value) {
			if (!item.comment || item.rate === 0) {
				disabled = true;
				break;
			}
		}
	} else {
		disabled = true;
	}
	return disabled;
});

async function submit() {
	if (submitDisabled.value) return;

	if (isServerGoods.value) {
		if (_data.serviceStaffEvaluationType === 0) {
			return uni.showToast({
				icon: 'error',
				title: '请评价服务人员'
			});
		}
	}

	const masterComment = {
		masterId: 0,
		masterType: '',
		masterWords: []
	};

	if (isServerGoods.value) {
		masterComment.masterId = orderDetail.value.masterId;
		masterComment.masterType = _data.serviceStaffEvaluationType === -1 ? 'bad' : 'good';
		masterComment.masterWords = _data.serviceStaffEvaluationTextItems.map((i) => i.evaluateWord);
	}

	imageList.value.map((images, index) => {
		formData.value[index].medias = [];
		images.map((i) => {
			if (i.status === 'success') {
				formData.value[index].medias.push(i.url);
			}
		});
	});

	const { shopId, orderNo } = orderInfo.value;
	const items = goodsInfoList.value.map((order, index) => {
		const { productId, skuId, specs } = order;
		const { comment, medias, rate } = formData.value[index];
		const key = { productId, skuId };
		return { key, comment, medias, rate, specs, ...masterComment };
	});

	let params = deepClone({ shopId, orderNo, items });

	if (isServerGoods.value) {
		// params = {
		// 	...params,
		// 	...masterComment
		// };
	}

	uni.showLoading({
		title: '评价中...'
	});

	const res = await submitBuyersEvaluation(params, {
		custom: {
			toast: false
		}
	});
	uni.hideLoading();
	if (res && res.apiStatus) {
		uni.navigateBack();
	} else {
		if (res.code === 30050) {
			uni.showModal({
				showCancel: false,
				title: '提示',
				content: `抱歉给您带来不便！系统检测到[账号身份异常]，为保护资金安全，将临时冻结评价等功能，您可正常浏览商品、联系客服等，请联系客服人员快速解冻！`
			});
		} else {
			uni.showToast({
				icon: 'none',
				title: res.msg
			});
		}
	}
}

// 删除图片
function deletePic(event, index = 0) {
	imageList.value[index].splice(event.index, 1);
}

// 新增图片
async function afterRead(event, index = 0) {
	// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
	let lists = [].concat(event.file);
	let fileListLen = imageList.value[index].length;
	lists.map((item) => {
		imageList.value[index].push({
			...item,
			status: 'uploading',
			message: '上传中'
		});
	});
	for (let i = 0; i < lists.length; i++) {
		let status = 'failed';
		let fileUrl = lists[i].url;
		try {
			const result = await upload(lists[i].url);
			if (result.apiStatus) {
				status = 'success';
				fileUrl = result.data;
			}
		} catch (error) {
			//TODO handle the exception
		}
		let item = imageList.value[index][fileListLen];
		imageList.value[index].splice(
			fileListLen,
			1,
			Object.assign(item, {
				status,
				message: status === 'failed' ? '上传失败' : '',
				url: fileUrl
			})
		);
		fileListLen += 1;
	}
}

async function loadData() {
	goodsInfoList.value = useOrderStore().evaluateGoodsInfoList;
	orderInfo.value = useOrderStore().orderInfoItem;

	formData.value = [];
	imageList.value = [];

	_data.serviceStaffEvaluationTextList = [];
	_data.serviceStaffEvaluationTextIndex = 0;
	_data.serviceStaffEvaluationTextIndexs = [];
	_data.serviceStaffEvaluationType = 0;

	canENV(() => {
		console.log(goodsInfoList.value);
		console.log(orderInfo.value);
	});

	goodsInfoList.value.map((i) => {
		let formDataItem = {
			comment: '',
			rate: 0,
			medias: []
		};

		formData.value.push(formDataItem);
		imageList.value.push([]);
	});

	if (isServerGoods.value) {
		initOrderData();
	}
}

async function changeServiceStaffEvaluationType(type) {
	if (_data.serviceStaffEvaluationType === type) return;
	_data.serviceStaffEvaluationType = type;

	_data.serviceStaffEvaluationTextIndex = 0;
	_data.serviceStaffEvaluationTextIndexs = [];

	getOrderEvaluateWordList(
		{
			type: _data.serviceStaffEvaluationType === -1 ? 'bad' : 'good'
		},
		{
			custom: {
				loading: {
					awaitTime: 100
				}
			}
		}
	).then((res) => {
		if (res.apiStatus) {
			_data.serviceStaffEvaluationTextList = res.data;
		}
	});
}

function clickServiceStaffEvaluationText(index, item) {
	const findIndex = _data.serviceStaffEvaluationTextIndexs.findIndex((i) => i === index);

	if (findIndex < 0) {
		_data.serviceStaffEvaluationTextIndexs.push(index);
		_data.serviceStaffEvaluationTextItems.push(item);
	} else {
		_data.serviceStaffEvaluationTextIndexs.splice(findIndex, 1);
		_data.serviceStaffEvaluationTextItems.splice(findIndex, 1);
	}
}

async function initOrderData() {
	try {
		const res = await getServerOrderInfo({
			orderNo: orderInfo.value.orderNo,
			shopId: orderInfo.value.shopId,
			usePackage: false
		});
		if (res.apiStatus) {
			orderDetail.value = res.data;
		}
	} catch (error) {
		//TODO handle the exception
	}
}

onLoad(() => {
	loadData();
});

onReady(() => {
	if (pageFormRef.value) pageFormRef.value.setRules(formRules);
});

const formStyle = ref({
	labelStyle: {
		fontSize: '30rpx',
		fontWeight: 'bold'
	},
	inputStyle: {
		height: '98rpx',
		padding: '0rpx 0rpx',
		borderBottom: `solid 1rpx #E9EDF1`
	},
	labelStyle2: {
		fontSize: '26rpx',
		fontWeight: 'bold',
		color: '#1152D7'
	},
	inputStyle2: {
		height: '88rpx',
		borderRadius: '10rpx',
		background: '#fff',
		padding: '0rpx 30rpx'
	},
	inputStyle3: {
		borderRadius: '16rpx',
		background: '#F2F6FB',
		padding: '25rpx 30rpx'
	},
	inputStyle4: {
		borderRadius: '16rpx',
		background: '#fff',
		padding: '25rpx 30rpx'
	},
	inputStyle5: {
		height: '105rpx',
		borderRadius: '16rpx',
		background: '#F2F6FB',
		padding: '0rpx 30rpx'
	},
	labelStyle6: {
		fontSize: '28rpx',
		fontWeight: '500',
		color: '#1A1A1B'
	},
	inputStyle6: {
		fontWeight: 'bold',
		fontSize: '28rpx'
	},
	labelStyle7: {
		fontSize: '28rpx',
		fontWeight: '500',
		color: '#1A1A1B'
	},
	inputStyle7: {
		fontWeight: '500',
		fontSize: '30rpx',
		padding: 0
	},
	inputStyle8: {
		// borderRadius: '16rpx',
		background: '#F3F4F7',
		padding: '25rpx 30rpx',
		borderRadius: '53rpx'
	},
	inputPlaceholderStyle: `font-size: 30rpx; font-weight: 500; color: #AEAEB4;`,
	inputPlaceholderStyle3: `font-size: 26rpx; font-weight: 500; color: #B1B6BC;`,
	inputPlaceholderStyle4: `font-size: 30rpx; font-weight: 500; color: #B1B6BC;`,
	inputPlaceholderStyle6: `font-size: 28rpx; font-weight: 500; color: #A7ACB7;`,
	inputPlaceholderStyle7: `font-size: 30rpx; font-weight: 500; color: #93939C;`
});
</script>

<template>
	<view class="apply-after-sales page-main bg-#F0F3F7 pb-150">
		<view class="w-full py-30 px-20">
			<template v-if="isServerGoods && orderDetail">
				<view class="w-full border-rd-20 bg-#FFFFFF px-30 py-30 mb-20">
					<view class="w-full flex items-center justify-between">
						<view class="text-32 font-bold">对服务人员满意吗？</view>
						<!-- <view class="text-#8D9196 text-24 font-500">已匿名</view> -->
					</view>
					<view class="w-full h-1 bg-#EAEDF3 mt-30 mb-30"></view>

					<view class="w-full flex">
						<view class="w-70">
							<app-image :src="orderDetail.masterVO.avatar" size="70" rd="14" mode=""></app-image>
						</view>

						<view class="flex-1 ml-24">
							<view class="flex items-center">
								<view class="text-30 font-bold">{{ orderDetail.masterVO.name }}</view>
								<view class="ml-20 text-30 font-500">{{ orderDetail.masterVO.mobile }}</view>
							</view>
							<view class="flex items-center mt-12">
								<view class="h-30 flex flex-center text-22 text-#fff font-500 px-8 border-rd-6" style="background: linear-gradient(-38deg, #19d492 0%, #32e0a3 100%)">
									{{ { nurse: '护理', attend: '陪诊', homemaking: '家政' }[orderDetail.serveTypeFename] }}
								</view>
								<view class="text-#777 font-26 font-500 ml-18">{{ { MALE: '男', FEMALE: '女' }[orderDetail.masterVO.sex] }}</view>
								<view class="ml-18 w-1 h-20 bg-#DFDFDF"></view>
								<view class="text-#777 font-26 font-500 ml-18">{{ orderDetail.masterVO.age }}岁</view>
							</view>
						</view>
					</view>

					<view class="w-full evaluation-type-box" :class="[_data.serviceStaffEvaluationType === 1 ? 'manyyi' : _data.serviceStaffEvaluationType === -1 ? 'bumanyi' : '']">
						<view class="w-full">
							<view class="w-full flex items-center mt-35">
								<view class="evaluation-type-item ac-op flex-1 mr-8" @click="changeServiceStaffEvaluationType(-1)">
									<template v-if="_data.serviceStaffEvaluationType === -1">
										<app-image src="@/pages/order/static/icon_sfpj_bumanyi_active.png" size="64" mr="6" ml="6" mode=""></app-image>
									</template>
									<template v-else>
										<app-image src="@/pages/order/static/icon_sfpj_bumanyi.png" size="48" mr="14" ml="14" mode=""></app-image>
									</template>

									<view>不满意</view>
								</view>

								<view class="evaluation-type-item ac-op flex-1 ml-8" @click="changeServiceStaffEvaluationType(1)">
									<template v-if="_data.serviceStaffEvaluationType === 1">
										<app-image src="@/pages/order/static/icon_sfpj_manyi_active.png" size="64" mr="6" ml="6" mode=""></app-image>
									</template>
									<template v-else>
										<app-image src="@/pages/order/static/icon_sfpj_manyi.png" size="48" mr="14" ml="14" mode=""></app-image>
									</template>
									<view class="">满意</view>
								</view>
							</view>
							<template v-if="_data.serviceStaffEvaluationTextList.length > 0">
								<view class="w-full mt-26">
									<scroll-view scroll-x="true">
										<view class="flex">
											<template v-for="(item, index) in _data.serviceStaffEvaluationTextList" :key="index">
												<view
													class="evaluation-item-box ac-op"
													:class="[_data.serviceStaffEvaluationTextIndexs.includes(index) && 'active']"
													@click="clickServiceStaffEvaluationText(index, item)"
												>
													{{ item.evaluateWord }}
												</view>
											</template>
										</view>
									</scroll-view>
								</view>
							</template>
						</view>
					</view>
				</view>
			</template>

			<template v-for="(goods, index) in goodsInfoList" :key="index">
				<view class="mt-0 mb-20">
					<view class="w-full border-rd-20 bg-#FFFFFF px-30 py-30 mb-0 mt-0">
						<view class="flex justify-start items-center">
							<app-image :src="orderInfo.shopLogo" size="42" class="w-42 h-42 border-1/2" mode=""></app-image>
							<text class="text-32 text-#222222 ml-12 mr-6 font-bold">{{ orderInfo.shopName }}</text>
							<!-- <uv-icon size="12" name="arrow-right" color="#999999" :bold="true"></uv-icon> -->
						</view>
						<view class="mt-30">
							<view class="w-full flex">
								<image :src="goods.image" class="w-164 h-164 min-w-164 border-solid border-1 border-#F5F5F5 border-rd-16" mode=""></image>
								<view class="flex-1 flex flex-col justify-between ml-20">
									<view class="">
										<view class="text-30 text-#222222 font-bold uv-line-1">{{ goods.productName }}</view>
										<view class="text-26 text-#555555 font-500 mt-10 flex justify-between items-center">
											<text>{{ goods.specs.join(' ') }}</text>
											<text>x{{ goods.num }}</text>
										</view>
									</view>
									<view class="text-#222222 font-bold">
										<view class="flex items-end">
											<view class="text-30 pb-2">¥</view>
											<view class="text-36">
												{{ getPriceInfo(goods.salePrice).integer }}
											</view>
											<view class="text-30 pb-2">.{{ getPriceInfo(goods.salePrice).decimalText }}</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
					<view class="px-20">
						<view class="w-full border-t-2 border-t-#fff border-t-dashed"></view>
					</view>
					<view class="w-full border-rd-20 bg-#FFFFFF px-30 py-30 mb-0 mt-0">
						<view class="w-full flex items-center">
							<view class="text-26 font-bold">总体评价：</view>
							<view class="ml-20 flex-1">
								<uv-rate :count="5" v-model="formData[index].rate" active-color="#F8BC35" inactive-color="#E2E2E2" inactiveIcon="star-fill" size="36rpx" gutter="0"></uv-rate>
							</view>
						</view>
						<view class="w-full mt-30">
							<textarea
								v-model="formData[index].comment"
								cols="30"
								rows="5"
								confirm-type="done"
								:maxlength="200"
								auto-height
								placeholder="写出你的评价，给大家参考"
								class="min-h-200 w-full text-28"
								placeholderStyle="color: #A7ACB7; fontSize: 28rpx"
							></textarea>

							<view class="w-full">
								<uv-upload
									:fileList="imageList[index]"
									name="file"
									multiple
									:maxCount="6"
									:previewFullImage="true"
									@afterRead="afterRead($event, index)"
									@delete="deletePic($event, index)"
								></uv-upload>
							</view>
						</view>
					</view>
				</view>
			</template>
		</view>

		<view class="w-full px-30 pt-15 bg-#fff fixed left-0 bottom-0 border-t-1 border-t-solid border-t-#EDEEF0 flex items-center">
			<view class="flex-1">
				<uv-button
					@click="submit()"
					color="#00B496"
					text="提交"
					:disabled="submitDisabled"
					class="flex-1 h-88"
					custom-style="border-radius: 44rpx;"
					customTextStyle="font-size: 30rpx; color: #fff; font-weight: bold;"
				></uv-button>

				<app-safeAreaBottom></app-safeAreaBottom>
			</view>
		</view>
	</view>
</template>

<style lang="scss" scoped>
.apply-after-sales {
}

.evaluation-type-box {
	&.manyyi {
		.evaluation-type-item {
			&.active {
				background: #fff5ee;
				color: #ff7e00;
			}
		}
		.evaluation-item-box {
			&.active {
				background: #fff5ee;
				color: #ff7e00;
			}
		}
	}
	&.bumanyi {
		.evaluation-type-item {
			&.active {
				background: #ffeceb;
				color: #fc3f33;
			}
		}
		.evaluation-item-box {
			&.active {
				background: #ffeceb;
				color: #fc3f33;
			}
		}
	}
}
.evaluation-type-item {
	background: #f5f5f7;
	border-radius: 10rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	// padding: 14rpx 0rpx;
	height: 76rpx;
	font-size: 26rpx;
	font-weight: 500;
}
.evaluation-item-box {
	min-width: 126rpx;
	height: 48rpx;
	background: #f5f5f7;
	border-radius: 6rpx;
	font-weight: 500;
	font-size: 24rpx;
	color: #777777;
	padding: 0rpx 15rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
	margin-right: 20rpx;
}
</style>
