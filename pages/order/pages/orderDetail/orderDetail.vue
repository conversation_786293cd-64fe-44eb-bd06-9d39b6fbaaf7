<script setup>
import { ref, reactive, computed, watch, nextTick, unref } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";

import { Decimal } from "decimal.js";

import dayjs from "dayjs";

import { useOrderStore, useMessageStore, useUserStore } from "@/store";

import { canENV, getPriceInfo, route, goShopHome } from "@/common/utils";

import { isUnpaidOrder, orderStatusPlus, orderStatusPlusInfo, isCompleted, isComment } from "@/pages/order/common/utils";

import { REGEX_HTTP_URL } from "@/common/test";

import { getAfsStatusCn, getAsfsStatusBtnCn } from "@/pages/order/common/types/order";

import { ORDERPAYMENT, PAY_TYPE, discountTypeConf, usePaymentCn } from "@/common/types/goods";

import { getOrder, getServerOrder, closeOrderByOrderNo, putOrderReceiver, getUserBalance, getOrderPayPage, getOrderIsPaySuccess, confirmGoods, getOrderInfo, getServerOrderInfo, queryConfigByModule, getOrderPayPageAgain, closeOrderServerGoods, getServerOrderContract, addOrderNumberServer, getInvoiceStatus } from "@/server/api";

import { getIntegralOrderDetail, closeIntegralOrderByOrderNo, putIntegralOrderReceiver } from "@/server/api";

import { createIntegralOrderCreate, getIntegralOrderCreateConditions, getUserIntegralSystemtotal, submitIntegralOrderPay, getIntegralOrderIsPaySuccess, confirmIntegralGoods } from "@/server/api";

import useConvert from "@/common/useConvert";

import { deepClone, sleep } from "@/uni_modules/uv-ui-tools/libs/function/index.js";

import storage from "@/common/storage";
import { common } from "@/common/images";

const { mulTenThousand, divTenThousand } = useConvert();

const _props = defineProps({
	orderNo: {
		type: String,
		required: true,
	},
	shopId: {
		type: String,
		default: "",
	},
	orderType: {
		type: String,
		default: "shop",
	},
	packageId: {
		type: String,
		default: "",
	},
});

const _data = reactive({
	btnItemStyle: {
		height: "64rpx",
		borderRadius: "40rpx",
		fontSize: "26rpx",
	},
});

const goodsOrder = computed(() => {
	return _props.orderType === "shop" || _props.orderType === "integral";
});

const isIntegral = computed(() => {
	const typeItem = _props.orderType;
	if (typeItem) {
		return typeItem === "integral";
	}
	return false;
});

const fastGoodsInfo = computed(() => {
	const goodsArr = Array.from(orderMapComputed(shopOrder.value.shopOrderItems).values());
	return goodsArr.length > 0 ? goodsArr[0] : null;
});

const pagingRef = ref(null);

const paySelectRef = ref();
const showPay = ref(false);
const payLoading = ref(false);
const payOrderType = ref("order");
const payInfo = reactive({
	price: 0,
	balanceTotalShow: true,
	balanceTotal: 0,
	timeout: 0,

	integralPriceShow: false,
	integralPrice: 0,
	integralTotalShow: true,
	integralTotal: 0,
});

const orderNumber = ref("");
const payExtra = ref();
const payFrom = ref(PAY_TYPE.ORDER);

const info = ref();

const shopOrder = ref({ shopOrderItems: [] });

//优惠统计
const statistics = ref({
	//总优惠
	totalDiscount: new Decimal(0),
	// 店铺优惠
	shopDiscount: new Decimal(0),
	// 其它优惠项
	otherDiscount: [],
});

// 订单备注信息
const remark = ref({});
//运费
const freightPrice = ref(new Decimal(0));
//是否有使用支付
const isUseRebate = ref(false);
//折扣
const discounts = ref([]);
//计算店铺总折扣
const shopDiscount = computed(() => {
	return discounts.value.filter((item) => item.isShopDiscount).reduce((pre, item) => pre.add(item.price), new Decimal(0));
});
const logistics = ref();
// 是否为无需物流发货 true 是 false 不是
const logisticsTypeIsWithout = computed(() => {
	return !!(logistics.value && (logistics.value.type === "VIRTUAL" || logistics.value.type === "WITHOUT"));
});
// 商品总个数
const totalNum = computed(() => (info.value ? info.value.shopOrders.reduce((pre, item) => pre + item.shopOrderItems.reduce((accumulator, currentValue) => accumulator + currentValue.num, 0), 0) : 0));

watch(
	discounts,
	() => {
		let totalDiscount = new Decimal(0);
		let shopDiscount = new Decimal(0);
		const otherDiscount = [];
		discounts.value.forEach((item) => {
			totalDiscount = totalDiscount.add(item.price);
			if (item.isShopDiscount) {
				shopDiscount = shopDiscount.add(item.price);
			} else {
				otherDiscount.push(item);
			}
		});
		statistics.value = {
			totalDiscount,
			shopDiscount,
			otherDiscount: otherDiscount.sort((a, b) => a.sort - b.sort),
		};
	},
	{
		immediate: true,
	}
);

/**
 * 优惠后总价格计算
 * @param {*} info
 * @param {*} fixPrice 除不尽的时候展示的价格
 */
const payAmountComputed = computed(() => {
	if (!info.value || !Array.isArray(info.value.shopOrders)) {
		return new Decimal(0);
	}
	const shopOrder = info.value.shopOrders.flatMap((shopOrder) => shopOrder.shopOrderItems);
	return shopOrder.reduce((p, item) => {
		return p.add(new Decimal(item.dealPrice).mul(new Decimal(item.num)).add(item.fixPrice));
	}, new Decimal(0));
});
/**
 * 总价格计算
 */
const paysalePriceAmountComputed = computed(() => {
	if (!info.value || !Array.isArray(info.value.shopOrders)) {
		return new Decimal(0);
	}
	const shopOrder = info.value.shopOrders.flatMap((shopOrder) => shopOrder.shopOrderItems);
	return shopOrder.reduce((p, item) => {
		return p.add(new Decimal(item.salePrice).mul(new Decimal(item.num)));
	}, new Decimal(0));
});
/**
 * 优惠后的实付金额
 */
const amountRealPay = computed(() => {
	if (info.value) {
		const amount = payAmountComputed.value;
		const totalPrice = new Decimal(amount);
		const totalPrice_ = totalPrice.toNumber() < 0 ? new Decimal(0) : totalPrice; // 运费不参与优惠计算 最后相加
		return totalPrice_.add(freightPrice.value);
	}
	return new Decimal(0);
});

function getIntegralOrderStatus(status) {
	const statusInfo = {
		UNPAID: "待支付",
		SERVER_UN_DELIVERY: "待接单",
		PAID: "待接单",
		SERVEROK: "待确认",
		SERVERPAYED: "等待分配服务人员",
		SERVERMASTERAWAIT: "等待服务人员接单",
		SERVERMASTEROK: "待服务",
		SERVERMASTERNO: "等待再次分配服务人员",
		SERVERGO: "服务中",
		UN_COMMENT: "待评价",
		COMPLETED: "服务完成",
		SERVEREND: "服务完成",
		BUYER_CLOSED: "服务关闭",
		SYSTEM_CLOSED: "服务关闭",
		SELLER_CLOSED: "服务关闭",
	};

	return statusInfo[status] || "";
}

function goShopChat(order) {
	if (!isIntegral.value) {
		if (order.shopOrders[0]) {
			const { shopLogo, shopName, shopId, totalAmount } = order.shopOrders[0];
			const { productName, image, productId } = order.shopOrders[0].shopOrderItems[0];
			const params = {
				id: productId,
				name: productName,
				salePrices: totalAmount,
				pic: image,
				no: order.no,
				amountRealPay: divTenThousand(amountRealPay.value).toFixed(2),
				afsStatu: {
					desc: orderStatusPlus(order, order.shopOrders[0]).desc,
				},
				commodityList: order.shopOrders[0].shopOrderItems.map((text) => {
					return {
						...text,
						pic: text.image,
						name: text.productName,
						amountRealPay: divTenThousand(text.dealPrice)?.toFixed(2),
						dealPrice: divTenThousand(text.dealPrice)?.toFixed(2),
					};
				}),
			};

			useMessageStore().setImUserInfo({
				shopId,
				shopLogo,
				shopName,
				showOrder: true,
				showOrderInfo: {
					...params,
					my: true,
					h5: true,
				},
			});

			route("/pages/message/pages/im/im");
		}
	} else {
		let amountRealPay = ``;

		amountRealPay += `${Number(order.price).toFixed(2)}积分`;

		if (order.salePrice + order.freightPrice > 0) {
			amountRealPay += `+￥${divTenThousand(order.salePrice + order.freightPrice).toFixed(2)}`;
		}

		const { productName, image, productId, salePrice } = order;
		const params = {
			id: productId,
			name: productName,
			salePrices: salePrice,
			pic: image,
			no: order.no,
			amountRealPay: amountRealPay,
			afsStatu: {
				desc: getIntegralOrderStatus(order.status),
			},
			commodityList: [
				{
					pic: image,
					name: productName,
				},
			],
		};

		useMessageStore().setImUserInfo({
			shopId: "0",
			shopLogo: "",
			shopName: "平台客服",
			showOrder: true,
			showOrderInfo: {
				...params,
				my: true,
				h5: true,
			},
		});

		useMessageStore().setImUserInfo({
			shopId: "0",
			shopLogo: "",
			shopName: "平台客服",
			showOrder: true,
			showOrderInfo: {
				...params,
				my: true,
				h5: true,
			},
		});

		route("/pages/message/pages/im/im");
	}
}

function getOrderPayTime(order) {
	const timeout = order.timeout.payTimeout * 1000 - (dayjs().valueOf() - dayjs(order.createTime).valueOf());

	return timeout;
}

function getOrderReceiveTime(order) {
	const timeout = order.timeout.confirmTimeout * 1000 - (dayjs().valueOf() - dayjs(shopOrder.value.extra?.deliverTime).valueOf());

	return timeout;
}

function getOrderCommentTime(order) {
	const timeout = order.timeout.commentTimeout * 1000 - (dayjs().valueOf() - dayjs(shopOrder.value.extra?.receiveTime).valueOf());

	return timeout;
}

function mergeItemsArr(pre, current) {
	pre.num += current.num;
	pre.fixPrice = new Decimal(pre.fixPrice).plus(current.fixPrice).toString();
	pre.freightPrice = new Decimal(pre.freightPrice).plus(current.freightPrice).toString();
	//当前是关闭状态 下一个是未关闭状态 则设置为正常状态
	const changeCloseStatus = pre.status === "CLOSED" && current.status === "OK";
	//判断是否设置为正常状态
	if (changeCloseStatus) {
		//部分关闭 说明有未关闭的 合并后的状态设置为正常
		pre.status = current.status;
		pre.packageStatus = current.packageStatus;
	}
	if (pre.afsStatus === "NONE" && current.afsStatus !== "NONE") {
		pre.afsStatus = current.afsStatus;
	}
	//部分发货 设置为待发货状态
	if (!!pre.packageId && !current.packageId) {
		pre.packageId = current.packageId;
	}
	if (pre.packageStatus !== "WAITING_FOR_DELIVER" && current.packageStatus === "WAITING_FOR_DELIVER") {
		pre.packageStatus = current.packageStatus;
	}
	//如果

	pre.merged = true;
	return pre;
}

function orderMapComputed(shopOrderItems = []) {
	const shopOrderItemsMap = shopOrderItems.reduce((pre, item) => {
		const id = `${item.productId}${item.skuId}${item.specs}`;
		const currentItem = pre.get(id);
		if (currentItem) {
			const current = { ...item };
			currentItem.merged = mergeItemsArr(currentItem.merged, current);
			currentItem.items.push(current);
		} else {
			pre.set(id, {
				items: [{ ...item }],
				merged: { ...item },
			});
		}
		return pre;
	}, new Map());

	return shopOrderItemsMap;
}

function computeOrderPrice(data) {
	if (data.id) {
		return data.shopOrderItems.reduce((pre, cur) => {
			return pre.add(new Decimal(cur.num).mul(cur.dealPrice).add(cur.fixPrice).add(cur.freightPrice));
		}, new Decimal(0));
	}
	return info.valuehopOrders.reduce((pre, item) => pre.add(new Decimal(item.dealPrice).mul(item.num).add(item.freightPrice).add(item.fixPrice)), new Decimal(0));
}

async function reloadData() {
	// if (pagingRef.value) pagingRef.value.reload();
	await initOrderData();
}

function countDownFinish(e) {
	reloadData();
}

async function initUserBalance() {
	const { code, msg, data, apiStatus } = await getUserBalance();
	payInfo.balanceTotalShow = apiStatus;
	payInfo.balanceTotal = apiStatus ? `${getPriceInfo(data).integer}.${getPriceInfo(data).decimalText}` : "";
}

async function initUserIntegral() {
	const { code, msg, data, apiStatus } = await getUserIntegralSystemtotal();
	payInfo.integralTotalShow = apiStatus;
	payInfo.integralTotal = apiStatus ? `${getPriceInfo(mulTenThousand(data)).integer}.${getPriceInfo(mulTenThousand(data)).decimalText}` : "";
}

// 取消订单
function cancelOrder(order) {
	uni.showModal({
		title: "请确认",
		content: "是否需要取消？",
		success: async ({ confirm }) => {
			if (!confirm) return;
			const { code, data, msg, apiStatus } = await closeOrderByOrderNo({ orderNo: order.no });
			// if (!apiStatus) return uni.showToast({ icon: 'none', title: `${msg ? msg : '取消订单失败'}` });
			if (!apiStatus) return;
			uni.showToast({ icon: "none", title: `订单已取消` });
			reloadData();
		},
	});
}

// 取消订单
function cancelOrderIntegral(order) {
	uni.showModal({
		title: "请确认",
		content: "是否需要取消？",
		success: async ({ confirm }) => {
			if (!confirm) return;
			const { code, data, msg, apiStatus } = await closeIntegralOrderByOrderNo({ orderNo: order.no });
			// if (!apiStatus) return uni.showToast({ icon: 'none', title: `${msg ? msg : '取消订单失败'}` });
			if (!apiStatus) return;
			uni.showToast({ icon: "none", title: `订单已取消` });
			reloadData();
		},
	});
}

// 更换地址
function changeOrderAddress(order) {
	route({
		type: "to",
		url: "/pages/my/pages/address/addressGoods",
		params: {
			select: 1,
		},
		events: {
			async selectAddress(data) {
				uni.$once("order-item-detail-show", () => {
					uni.showModal({
						title: "请确认",
						content: `是否更改地址为${data.address}？`,
						success: async ({ confirm }) => {
							if (!confirm) return;
							const { apiStatus } = await putOrderReceiver({ orderNo: order.no, ...data });
							// if (!apiStatus) return uni.showToast({ icon: 'none', title: `${msg ? msg : '修改失败'}` });
							if (!apiStatus) return;
							uni.showToast({ icon: "none", title: `修改成功` });
							reloadData();
						},
					});
				});
			},
		},
	});
}

// 更换地址
function changeOrderAddressIntegral(order) {
	route({
		type: "to",
		url: "/pages/my/pages/address/addressGoods",
		params: {
			select: 1,
		},
		events: {
			async selectAddress(data) {
				uni.$once("order-item-detail-show", () => {
					uni.showModal({
						title: "请确认",
						content: `是否更改地址为${data.address}？`,
						success: async ({ confirm }) => {
							if (!confirm) return;
							const { apiStatus } = await putIntegralOrderReceiver({ orderNo: order.no, ...data, id: order.id });
							// if (!apiStatus) return uni.showToast({ icon: 'none', title: `${msg ? msg : '修改失败'}` });
							if (!apiStatus) return;
							uni.showToast({ icon: "none", title: `修改成功` });
							reloadData();
						},
					});
				});
			},
		},
	});
}

// 催
async function urge(type, order) {
	if (!type || !order) return;

	const typeMap = {
		shipment: {
			name: "催发货",
			msg: "已提醒商家尽快发货,请您耐心等待~",
		},
		take: {
			name: "催接单",
			msg: "已提醒商家尽快接单,请您耐心等待~",
		},
		allocation: {
			name: "催分配",
			msg: "已提醒商家尽快分配服务人员,请您耐心等待~",
		},
		takeMaster: {
			name: "催接单",
			msg: "已提醒服务人员尽快接单,请您耐心等待~",
		},
	};

	const item = typeMap[type];

	if (!item) return;

	uni.showLoading({
		title: "加载中...",
	});
	await sleep(500);
	uni.hideLoading();
	uni.showToast({
		icon: "none",
		title: item.msg,
	});
}

// 查看物流
function viewLogistics(order) {
	route("/pages/order/pages/logisticsOrderMore/logisticsOrderMore", {
		orderNo: order.no,
		shopOrderNo: shopOrder.value.no,
		shopId: shopOrder.value.shopId,
	});
	useOrderStore().setOrderInfoItem({
		orderNo: _props.orderNo,
		...shopOrder.value,
	});
}

// 查看物流
function viewLogisticsSingle(order) {
	console.log(order);

	useOrderStore().setOrderInfoItem({
		...order,
	});

	route("/pages/order/pages/logisticsOrderSingle/logisticsOrderSingle", {
		expressNo: order.expressNo,
		companyCode: order.expressCompanyName,
		recipientsPhone: order.integralOrderReceiverVO?.mobile || order.integralOrderReceiver?.mobile,
		orderType: "integral",
	});
}

// 确认收货
function confirmReceive(order) {
	const showModalProps = {
		content: "是否确认收货",
		showClose: true,
		isSubmit: true,
	};

	// 通过每一个商品的售后得到 可以改变包状态的数组
	const shop = shopOrder.value;
	if (!shop.shopOrderItems.every((item) => getAfsStatusCn(item.afsStatus).canChangePackageStatus)) {
		showModalProps.content = "该订单中存在退款宝贝，等待商家确认收货";
		showModalProps.isSubmit = false;
		showModalProps.showClose = false;
	} else if (!shop.shopOrderItems.every((item) => !getAfsStatusCn(item.afsStatus).canChangePackageStatusText)) {
		showModalProps.content = "该订单中存在退款宝贝，确认收货将关闭退款";
	}
	// 该订单中存在退款宝贝，等待商家确认收货  该订单中存在退款宝贝，确认收货将关闭退款
	uni.showModal({
		// title: '提示',
		confirmColor: "#f12f22",
		content: `${showModalProps.content}`,
		showCancel: showModalProps.showClose,
		success: async (res) => {
			if (res.cancel || !showModalProps.isSubmit) return;
			const { packageId } = shop.shopOrderItems[0];
			const { code, msg, apiStatus } = await confirmGoods({ orderNo: order.no, shopId: shop.shopId });
			// if (!apiStatus) return uni.showToast({ title: `${msg ? msg : '确认收货失败'}`, icon: 'none' });
			if (!apiStatus) return;
			uni.showToast({ icon: "none", title: `收货成功` });
			reloadData();
		},
	});
}

// 确认收货
function confirmReceiveIntegral(order) {
	const showModalProps = {
		content: "是否确认收货",
		showClose: true,
		isSubmit: true,
	};

	uni.showModal({
		// title: '提示',
		confirmColor: "#f12f22",
		content: `${showModalProps.content}`,
		showCancel: showModalProps.showClose,
		success: async (res) => {
			const { code, msg, apiStatus } = await confirmIntegralGoods({ orderNo: order.no });
			// if (!apiStatus) return uni.showToast({ title: `${msg ? msg : '确认收货失败'}`, icon: 'none' });
			if (!apiStatus) return;
			uni.showToast({ icon: "none", title: `收货成功` });
			reloadData();
		},
	});
}

// 删除订单
function delOrder(order) {
	const showModalProps = {
		content: "是否删除订单",
		showClose: true,
		isSubmit: true,
	};

	// 通过每一个商品的售后得到 可以改变包状态的数组
	// const shop = shopOrder.value;
	// if (!shop.shopOrderItems.every((item) => getAfsStatusCn(item.afsStatus).canChangePackageStatus)) {
	// 	showModalProps.content = '该订单中存在退款宝贝，等待商家确认收货';
	// 	showModalProps.isSubmit = false;
	// 	showModalProps.showClose = false;
	// } else if (!shop.shopOrderItems.every((item) => !getAfsStatusCn(item.afsStatus).canChangePackageStatusText)) {
	// 	showModalProps.content = '该订单中存在退款宝贝，确认收货将关闭退款';
	// }
	// 该订单中存在退款宝贝，等待商家确认收货  该订单中存在退款宝贝，确认收货将关闭退款
	uni.showModal({
		title: "提示",
		confirmColor: "#f12f22",
		content: `${showModalProps.content}`,
		showCancel: showModalProps.showClose,
		success: async (res) => {
			if (res.cancel || !showModalProps.isSubmit) return;
			// const { packageId } = shop.shopOrderItems[0];
			// const { code, msg, apiStatus } = await confirmGoods({ orderNo: order.no, shopId: shop.shopId });
			// // if (!apiStatus) return uni.showToast({ title: `${msg ? msg : '确认收货失败'}`, icon: 'none' });
			// if (!apiStatus) return;
			// uni.showToast({ icon: 'none', title: `收货成功` });
			// reloadData();
		},
	});
}

// 商品详情
function goGoodsDetails(goods) {
	route("/pages/goods/pages/productDetails/productDetails", {
		productId: goods.productId,
		shopId: goods.shopId,
	});
}

// 商品详情
function goGoodsDetailsIntegralc(goods) {
	route("/pages/goods/pages/productDetailsIntegral/productDetailsIntegral", {
		productId: goods.productId,
	});
}

async function paySubmit(e) {
	if (!goodsOrder.value) {
		// #ifdef  MP-WEIXIN
		// 调起客户端小程序订阅消息界面，返回用户订阅消息的操作结果。当用户勾选了订阅面板中的“总是保持以上选择，不再询问”时，模板消息会被添加到用户的小程序设置页。
		try {
			const res = await uni.requestSubscribeMessage({
				tmplIds: [
					// 'HA8G6APZuXamC-W8WUeFJNyEGaSH8og0Oxb38MprqiQ', // 发货
					// 'Yjtuq-FfzGWD9SR_XNWQWIhEXW34TVXAJABa-aHavzo' // 完成

					// 'IjhUqEhCGqqpYQ6i_8suidaGqNYrwHMD5Nk-fl7VTac', // 订单确认

					"IjhUqEhCGqqpYQ6i_8suieMstG7SoJAOXp3LHnGA9_Y", // 价格变化
					"fS1bkoRJpogM86SxOte7XZGN_YSWYqg4QeY_opB0LUA", // 分配人员
				],
			});
		} catch (error) {
			//TODO handle the exception
			console.log("订阅失败 => ", error);
		}
		// #endif
	}

	if (payOrderType.value === "subOrder") {
		paySubmitAgain(e);
		return;
	}

	if (e.type === "balance") {
		let res = null;
		if (!isIntegral.value) {
			res = await getOrderPayPage({
				orderNo: unref(orderNumber),
				payType: ORDERPAYMENT.BALANCE,
				rebate: false,
			}).catch((e) => {
				payLoading.value = false;
			});
		} else {
			res = await submitIntegralOrderPay({
				integralOrderNo: unref(orderNumber),
				payType: ORDERPAYMENT.BALANCE,
			}).catch((e) => {
				payLoading.value = false;
			});
		}

		const { code, data, msg, apiStatus } = res;

		if (!apiStatus) {
			payError(unref(orderNumber), payFrom.value);

			return;
		}

		orderNumber.value = data.outTradeNo;
		loopCheckPayStatus();
	}

	if (e.type === "wechat") {
		let payRes = null;
		if (!isIntegral.value) {
			payRes = await getOrderPayPage({
				orderNo: unref(orderNumber),
				payType: ORDERPAYMENT.WECHAT,
				rebate: false,
			}).catch((e) => {
				payLoading.value = false;
			});
		} else {
			payRes = await submitIntegralOrderPay({
				integralOrderNo: unref(orderNumber),
				payType: ORDERPAYMENT.WECHAT,
			}).catch((e) => {
				payLoading.value = false;
			});
		}

		canENV(() => {
			console.log(payRes);
		});

		if (!payRes || !payRes.apiStatus) {
			payError(unref(orderNumber), payFrom.value);
			return;
		}

		orderNumber.value = payRes.data.outTradeNo;

		requestPayment(e.payItem.payment, payRes.data.data);
	}

	if (e.type === "alipay") {
		let payRes = null;
		if (!isIntegral.value) {
			payRes = await getOrderPayPage({
				orderNo: unref(orderNumber),
				payType: ORDERPAYMENT.ALIPAY,
				rebate: false,
			}).catch((e) => {
				payLoading.value = false;
			});
		} else {
			payRes = await submitIntegralOrderPay({
				integralOrderNo: unref(orderNumber),
				payType: ORDERPAYMENT.ALIPAY,
			}).catch((e) => {
				payLoading.value = false;
			});
		}

		canENV(() => {
			console.log(payRes);
		});

		if (!payRes || !payRes.apiStatus) {
			payError(unref(orderNumber), payFrom.value);
			return;
		}

		orderNumber.value = payRes.data.outTradeNo;

		requestPayment(e.payItem.payment, payRes.data.data);
	}
}

/**
 * uni requestPayment 支付
 * @param {*} data
 */
function requestPayment(provider, data) {
	uni.requestPayment({
		provider,
		orderInfo: data,
		...data,
		success: () => {
			loopCheckPayStatus();
		},
		fail: () => {
			closeLoopCheck(false, true);
		},
	});
}

let time = null;

/**
 * 循环检查支付状态
 */
function loopCheckPayStatus() {
	uni.showLoading({
		title: "支付校验中...",
		mask: true,
	});
	let count = 1;
	checkPayStatus(false);
	try {
		//先清除一次定时器
		time && clearInterval(time);
		// 开启轮训
		time = setInterval(() => checkPayStatus(++count > 10), 800);
	} catch (error) {
		uni.hideLoading();
		time && clearInterval(time);
	}
	// payBtnLoading.value = false;
}

/**
 * 定时器循环体
 */
function checkPayStatus(skipLoop) {
	if (skipLoop) {
		return closeLoopCheck();
	}
	if (!isIntegral.value) {
		getOrderIsPaySuccess({
			outTradeNo: unref(orderNumber),
		}).then((res) => {
			if (res.apiStatus) {
				closeLoopCheck(true, true);
			}
		});
	} else {
		getIntegralOrderIsPaySuccess({
			outTradeNo: unref(orderNumber),
		}).then((res) => {
			if (res.apiStatus) {
				closeLoopCheck(true, true);
			}
		});
	}
}

/**
 * 取消轮训检查
 */
function closeLoopCheck(state = false, isPayStatus = false) {
	time && clearInterval(time);
	uni.hideLoading();
	if (isPayStatus) {
		if (state) {
			paySuccess(unref(orderNumber), payFrom.value);
		} else {
			payError(unref(orderNumber), payFrom.value);
		}
	} else {
		payLoading.value = false;
		showPay.value = false;
		// reloadData();
	}
}

async function paySuccess(orderType, payFrom) {
	// uni.hideLoading();
	payLoading.value = false;
	showPay.value = false;
	uni.showToast({
		icon: "success",
		title: "支付成功",
		duration: 1000,
		mask: true,
	});

	reloadData();
}

async function payError(orderType, payFrom) {
	// uni.hideLoading();
	payLoading.value = false;
	showPay.value = false;
	uni.showToast({
		icon: "error",
		title: "支付失败",
		duration: 1500,
		mask: true,
	});

	reloadData();
}

function paySelectChange(e) {
	if (!e.show) {
		closeLoopCheck(false, false);
	}
}

// 再次支付
async function openPay(order) {
	uni.showLoading({
		title: "加载中...",
	});

	orderNumber.value = order.no;

	await initUserBalance();

	if (!isIntegral.value) {
		payInfo.timeout = order.timeout.payTimeout * 1000 - (dayjs().valueOf() - dayjs(order.createTime).valueOf());
		payInfo.price = `${getPriceInfo(order.orderPayment.payAmount).integer}.${getPriceInfo(order.orderPayment.payAmount).decimalText}`;
		payFrom.value = PAY_TYPE.ORDER;
		payOrderType.value = "order";
	} else {
		payInfo.integralPriceShow = true;
		payFrom.value = PAY_TYPE.INTEGRAL;

		await initUserIntegral();

		payInfo.timeout = order.timeout.payTimeout * 1000 - (dayjs().valueOf() - dayjs(order.createTime).valueOf());
		payInfo.price = `${getPriceInfo(order.salePrice + order.freightPrice).integer}.${getPriceInfo(order.salePrice + order.freightPrice).decimalText}`;
		payInfo.integralPrice = `${getPriceInfo(mulTenThousand(order.price)).integer}.${getPriceInfo(mulTenThousand(order.price)).decimalText}`;

		payOrderType.value = "integral";
	}

	showPay.value = true;
	uni.hideLoading();
}

// 申请售后
function applyAfterSale(goods) {
	const unMerged = !goods.merged.merged;

	const shopOrderItem = goods.merged;

	const { status, afsStatus, packageStatus } = shopOrderItem;

	//是否未发货
	const notDelivery = packageStatus === "WAITING_FOR_DELIVER";
	//检查售后状态 确定是否已申请过售后
	//是否未申请过售后
	const nonAfs = afsStatus === "NONE";
	//订单是否已关闭
	const closed = status !== "OK";

	//如果未合并 则需要计算 可申请售后价格 价格不大于 0 无法申请售后
	if (unMerged) {
		//计算 可售后退款价格
		const { dealPrice, num, fixPrice, freightPrice } = shopOrderItem;
		const price = new Decimal(dealPrice).div(num).add(fixPrice);
		//未发货需要加上运费
		if (packageStatus && notDelivery) {
			price.add(freightPrice);
		}

		//如果总费用小于 0 则点击售后按钮提示 实付款金额为0 不支持售后
		if (price.lte(0)) {
			uni.showToast({
				title: "实付款金额为0，不支持售后",
				icon: "none",
			});
			return;
		}
	}

	if (nonAfs) {
		route("/pages/order/pages/applyAfterSales/applyAfterSales", {
			orderNo: info.value?.no,
			itemId: shopOrderItem.id,
		});
	} else {
		if (unMerged && shopOrderItem.afsNo) {
			route("/pages/order/pages/afterSaleDetail/afterSaleDetail", {
				afsNo: shopOrderItem.afsNo,
				packageId: shopOrderItem.packageId || "",
			});
		}
	}
}

function afsButtonController(goods) {
	const result = {
		show: false,
		text: "",
		click: () => {},
	};

	if (!goods) return result;

	//如果未支付 则不需要显示
	if (orderStatusPlusInfo(info.value, shopOrder.value).status === "UNPAID") {
		return result;
	}

	const shopOrderItem = goods.merged;

	const { status, afsStatus, packageStatus } = shopOrderItem;

	//是否未发货
	const notDelivery = packageStatus === "WAITING_FOR_DELIVER";
	//检查售后状态 确定是否已申请过售后
	//是否未申请过售后
	const nonAfs = afsStatus === "NONE";
	//订单是否已关闭
	const closed = status !== "OK";

	//未申请过售后
	if (nonAfs) {
		// 订单项已关闭 或订单已完成 无需申请售后
		if (closed || isCompleted(packageStatus) || orderStatusPlusInfo(info.value, shopOrder.value).status === "CLOSED") {
			return result;
		}

		const unMerged = !goods.merged.merged;

		//如果未合并 则需要计算 可申请售后价格 价格不大于 0 无法申请售后
		if (unMerged) {
			//计算 可售后退款价格
			const { dealPrice, num, fixPrice, freightPrice } = shopOrderItem;
			const price = new Decimal(dealPrice).div(num).add(fixPrice);
			//未发货需要加上运费
			if (packageStatus && notDelivery) {
				price.add(freightPrice);
			}

			//如果总费用小于 0 则点击售后按钮提示 实付款金额为0 不支持售后
			if (price.lte(0)) {
				result.show = true;
				result.text = "申请售后";
				result.click = () => {
					uni.showToast({
						title: "实付款金额为0，不支持售后",
						icon: "none",
					});
				};
				return result;
			}
		}

		//未申请过  订单项状态正常 可以申请
		result.show = true;
		result.text = "申请售后";

		if (info.value?.extra.distributionMode === "INTRA_CITY_DISTRIBUTION" && !["", "DELIVERED"].includes(info.value?.icStatus || "")) {
			result.show = false;
			result.text = "";
		}

		//已发货弹出 售后类型选择器 否则 直接使用默认类型
		result.click = applyAfterSale;
		return result;
	}

	result.show = true;
	result.text = getAsfsStatusBtnCn(afsStatus);
	result.click = applyAfterSale;
	return result;
}

function goComment(goods) {
	if (!goods) return;

	const shopOrderItem = goods.merged;

	const packageStatus = shopOrderItem.packageStatus;

	const canComment = isComment(packageStatus);

	const goodsInfo = {
		...goods.merged,
		serveGoods: Number(!goodsOrder.value),
	};

	useOrderStore().setEvaluateGoodsInfoList([goodsInfo]);

	useOrderStore().setOrderInfoItem({
		orderNo: _props.orderNo,
		...shopOrder.value,
	});

	if (canComment) {
		// console.log('去评价');
		route("/pages/order/pages/setComment/setComment");
	} else {
		// console.log('查看评价');
		route("/pages/my/pages/evaluationDetails/evaluationDetails", {
			orderNo: _props.orderNo,
		});
	}
}

function goCommentDetails(info) {
	// console.log('查看评价');
}

//评价按钮控制器
function evaluateButtonController(goods) {
	const result = {
		show: false,
		text: "",
		click: () => {},
	};

	const shopOrderItem = goods.merged;

	const packageStatus = shopOrderItem.packageStatus;

	const canComment = isComment(packageStatus);

	//不能评价 且 未评价 则不显示
	if (!canComment && !isCompleted(packageStatus)) {
		return result;
	}

	const afsButtonControllerInfo = afsButtonController(goods);

	if (afsButtonControllerInfo.show && ["待商家审核", "退款成功"].includes(afsButtonControllerInfo.text)) {
		result.show = false;
		return result;
	}

	result.show = true;
	result.text = canComment ? "评价" : "已评价";
	// result.click = canComment ? () => goComment(shopOrderItem) : () => goCommentDetails(shopOrderItem);

	return result;
}

// 申请开票
function applyInvoice(info) {
	canENV(() => {
		console.log(info);
	});
	getInvoiceStatus({
		invoiceOwnerType: "USER",
		applicantId: useUserStore().userData.userId,
		orderNo: _props.orderNo,
		applicantShopId: _props.shopId,
	}).then((res) => {
		if (res.apiStatus && res.data) {
			const { data } = res;
			if (data.invoiceStatus === "REQUEST_HAS_EXPIRED") {
				uni.showToast({
					icon: "error",
					title: "已过开票时间",
				});
				return;
			}
			if (data.invoiceStatus === "SERVER_NOT_SUPPORTED") {
				uni.showToast({
					icon: "error",
					title: "该店铺暂不提供开票服务",
				});
				return;
			}
			useOrderStore().setInvoiceGoodsInfoList([
				{
					shopOrders: info.shopOrders,
					orderNo: info.no,
					invoiceAmount: data.billMoney,
					isDetail: data.invoiceStatus !== "ALLOWED_INVOICING",
					id: data.id,
				},
			]);

			if (data.invoiceStatus === "ALLOWED_INVOICING") {
				route("/pages/order/pages/invoice/applyInvoice");
			} else {
				route("/pages/order/pages/invoice/applyInvoiceDetails");
			}
		}
	});
}

const orderInvoiceStatus = ref("");

function loadData() {
	initOrderData();
}

function initRemark(params) {
	if (!params) {
		remark.value = {};
		return;
	}
	remark.value = JSON.parse(params);
}

function previewDocument(url) {
	if (!url) {
		uni.showToast({
			icon: "error",
			title: "查看失败",
		});
		return;
	}
	uni.showLoading({
		title: "加载中...",
	});
	uni.downloadFile({
		url,
		success: (res) => {
			const filePath = res.tempFilePath;
			uni.openDocument({
				filePath: filePath,
				showMenu: true,
				success: (res2) => {},
				fail: (err) => {
					uni.showToast({
						icon: "error",
						title: "查看失败",
					});
				},
				complete: () => {
					uni.hideLoading();
				},
			});
		},
		fail: (err) => {
			uni.showToast({
				icon: "error",
				title: "查看失败",
			});
		},
	});
}
function isImage(str) {
	return /.(gif|jpg|jpeg|png|gif|jpg|png|webp|avif|jfif)$/i.test(str);
}
function openImage(images) {
	if (!images) {
		uni.showToast({
			icon: "error",
			title: "查看失败",
		});
		return;
	}
	const imageUrlArr = images.split(",");
	const imagesArr = [];
	for (let url of imageUrlArr) {
		if (isImage(url)) {
			imagesArr.push(url);
		}
	}
	if (imagesArr.length > 0) {
		previewImage(imagesArr);
	} else {
		uni.showToast({
			icon: "error",
			title: "查看失败",
		});
	}
}
function previewImage(url) {
	if (!url) return;
	uni.previewImage({
		urls: Array.isArray(url) ? url : [url],
		count: Array.isArray(url) ? url[0] : url,
	});
}
function onCopy(text) {
	uni.setClipboardData({
		data: String(text),
		showToast: true,
	});
}
function callTel(tel) {
	if (!tel) return;

	uni.makePhoneCall({
		phoneNumber: String(tel),
		complete: (res) => {
			// console.log(res);
		},
	});
}

const pageLoading = ref(false);
async function initOrderData() {
	// shopOrder.value = { shopOrderItems: [] };
	pageLoading.value = true;
	if (goodsOrder.value) {
		if (!isIntegral.value) {
			try {
				const res = await getOrderInfo({
					orderNo: _props.orderNo,
					shopId: _props.shopId,
					usePackage: false,
				});

				if (res.apiStatus) {
					const order = res.data;

					discounts.value = [];

					order.orderDiscounts.forEach((item) => {
						if (item.sourceType === "CONSUMPTION_REBATE") {
							isUseRebate.value = true;
						}
						discounts.value.push({
							...discountTypeConf[item.sourceType],
							price: new Decimal(item.sourceAmount),
						});
					});

					freightPrice.value = order.shopOrders
						.flatMap((shopOrder) => shopOrder.shopOrderItems)
						.map((shopOrderItem) => new Decimal(shopOrderItem.freightPrice))
						.reduce((pre, current) => current.add(pre));

					info.value = order;

					canENV(() => {
						console.log(info.value);
					});

					shopOrder.value = info.value.shopOrders[0];
					if (orderStatusPlusInfo(info.value, shopOrder.value).status === "COMPLETED") {
						getInvoiceStatus({
							invoiceOwnerType: "USER",
							applicantId: useUserStore().userData.userId,
							orderNo: _props.orderNo,
							applicantShopId: _props.shopId,
						}).then((res) => {
							if (res.apiStatus) {
								orderInvoiceStatus.value = res.data.invoiceStatus;
							}
						});
					}

					initRemark(order.shopOrders[0].remark);
				}
			} catch (error) {
				//TODO handle the exception
			}
		} else {
			try {
				const res = await getIntegralOrderDetail({
					orderNo: _props.orderNo,
				});
				if (res.apiStatus) {
					const order = res.data;

					discounts.value = [];

					freightPrice.value = new Decimal(order.freightPrice);

					info.value = order;

					canENV(() => {
						console.log(info.value);
					});

					shopOrder.value = {
						extra: {
							deliveryTime: info.value.deliveryTime,
							receivableTime: info.value.receivableTime,
							receiveTime: info.value.receiveTime,
						},
						...info.value,
					};

					// initRemark(order.buyerRemark);
				}
			} catch (error) {
				//TODO handle the exception
			}
		}
	} else {
		try {
			const res = await getServerOrderInfo({
				orderNo: _props.orderNo,
				shopId: _props.shopId,
				usePackage: false,
			});
			if (res.apiStatus) {
				const order = res.data;

				discounts.value = [];

				order.orderDiscounts.forEach((item) => {
					if (item.sourceType === "CONSUMPTION_REBATE") {
						isUseRebate.value = true;
					}
					discounts.value.push({
						...discountTypeConf[item.sourceType],
						price: new Decimal(item.sourceAmount),
					});
				});

				freightPrice.value = order.shopOrders
					.flatMap((shopOrder) => shopOrder.shopOrderItems)
					.map((shopOrderItem) => new Decimal(shopOrderItem.freightPrice))
					.reduce((pre, current) => current.add(pre));

				info.value = order;

				canENV(() => {
					console.log(info.value);
				});

				shopOrder.value = info.value.shopOrders[0];

				initRemark(order.shopOrders[0].remark);
			}
		} catch (error) {
			//TODO handle the exception
		}
	}
	pageLoading.value = false;
}

onLoad((options) => {
	// loadData();
});

onShow(() => {
	// if (pagingRef.value) {
	// 	pagingRef.value.reload();
	// } else {
	// 	loadData();
	// }
	loadData();
	uni.$emit("order-item-detail-show");
});

async function onRefresh() {
	await initOrderData();
	if (pagingRef.value) pagingRef.value.complete();
}
</script>

<template>
	<view class="page-main bg-#F4F5F8">
		<app-layout>
			<app-image src="@/pages/order/static/bg_order_details_header_bg.png" mode="widthFix"></app-image>
			<z-paging
				ref="pagingRef"
				:paging-style="{
					backgroundRepeat: 'no-repeat',
					backgroundSize: '100% 450rpx',
				}"
				refresher-theme-style="white"
				refresher-only
				@onRefresh="onRefresh">
				<template #top>
					<app-navBar back :backIcon="common.iconLeftBai" :showRight="false">
						<template #content>
							<view class="w-full flex justify-between items-center h-full">
								<view class="flex justify-start items-center"></view>
								<view class="flex items-center">
									<app-image src="@/pages/order/static/icon_kefu_bai.png" class="mr-24" size="40" mode="" v-if="!isIntegral" @click="goShopChat(info)"></app-image>
									<!-- <app-image src="@/pages/order/static/icon_caidan_bai.png" class="mr-24" size="36" mode=""></app-image> -->
								</view>
							</view>
						</template>
					</app-navBar>
				</template>

				<view class="px-20">
					<!-- 商城订单 -->
					<template v-if="goodsOrder">
						<template v-if="!isIntegral">
							<!-- 待付款 -->
							<template v-if="orderStatusPlusInfo(info, shopOrder).status === 'UNPAID'">
								<view class="px-26 mt-20">
									<view class="flex justify-between items-center">
										<view class="text-38 text-#fff font-bold">
											<text>{{ orderStatusPlus(info, shopOrder).desc }}</text>
										</view>
										<view class="text-36 text-#fff font-bold">
											<my-uv-count-down
												:time="Number(getOrderPayTime(info))"
												format="HH时mm分ss秒"
												ref="payCountDown"
												:autoStart="true"
												:textStyle="{
													fontSize: '36rpx',
													color: '#fff',
													fontWeight: 'bold',
												}"
												@finish="reloadData()"></my-uv-count-down>
										</view>
									</view>
									<view class="text-28 text-#fff font-500 mt-10">
										<text>请在规定时间内未付款，订单超时自动取消</text>
									</view>
								</view>
							</template>

							<!-- 待发货 -->
							<template v-if="orderStatusPlusInfo(info, shopOrder).status === 'UN_DELIVERY'">
								<view class="px-26 mt-20">
									<view class="flex justify-between items-center">
										<view class="text-38 text-#fff font-bold">
											<text>{{ orderStatusPlus(info, shopOrder).desc }}</text>
										</view>
										<!-- <view class="text-36 text-#fff font-bold">0分0秒</view> -->
									</view>
									<view class="text-28 text-#fff font-500 mt-10">
										<text>订单已付款，会尽快安排发货</text>
									</view>
								</view>
							</template>

							<!-- 待收货 -->
							<template v-if="orderStatusPlusInfo(info, shopOrder).status === 'UN_RECEIVE'">
								<view class="px-26 mt-20">
									<view class="flex justify-between items-center">
										<view class="text-38 text-#fff font-bold">
											<text>{{ orderStatusPlus(info, shopOrder).desc }}</text>
										</view>
										<!-- <view class="text-36 text-#fff font-bold">0分0秒</view> -->
									</view>
									<view class="text-28 text-#fff font-500 mt-10 flex">
										<view class="">订单已发出，还剩</view>
										<my-uv-count-down
											:time="Number(getOrderReceiveTime(info))"
											format="DD天HH时mm分ss秒"
											ref="payCountDown2"
											:autoStart="true"
											:textStyle="{
												fontSize: '28rpx',
												color: '#fff',
												fontWeight: '500',
											}"
											@finish="reloadData()"></my-uv-count-down>
										<view class="">自动确认</view>
									</view>
								</view>
							</template>

							<!-- 待评价 -->
							<template v-if="orderStatusPlusInfo(info, shopOrder).status === 'UN_COMMENT'">
								<view class="px-26 mt-20">
									<view class="flex justify-between items-center">
										<view class="text-38 text-#fff font-bold">
											<text>{{ orderStatusPlus(info, shopOrder).desc }}</text>
										</view>
										<!-- <view class="text-36 text-#fff font-bold">0分0秒</view> -->
									</view>
									<view class="text-28 text-#fff font-500 mt-10 flex">
										<view class="">订单已签收，还剩</view>
										<my-uv-count-down
											:time="Number(getOrderCommentTime(info))"
											format="DD天HH时mm分ss秒"
											ref="payCountDown3"
											:autoStart="true"
											:textStyle="{
												fontSize: '28rpx',
												color: '#fff',
												fontWeight: '500',
											}"
											@finish="reloadData()"></my-uv-count-down>
										<view class="">自动评价</view>
									</view>
								</view>
							</template>

							<!-- 已完成 -->
							<template v-if="orderStatusPlusInfo(info, shopOrder).status === 'COMPLETED'">
								<view class="px-26 mt-20">
									<view class="flex justify-between items-center">
										<view class="text-38 text-#fff font-bold">
											<text>交易完成</text>
										</view>
										<!-- <view class="text-36 text-#fff font-bold">0分0秒</view> -->
									</view>
									<view class="text-28 text-#fff font-500 mt-10">
										<text>订单已签收，期待您的下次购买</text>
									</view>
								</view>
							</template>

							<!-- 已关闭 -->
							<template v-if="orderStatusPlusInfo(info, shopOrder).status === 'CLOSED'">
								<view class="px-26 mt-20">
									<view class="flex justify-between items-center">
										<view class="text-38 text-#fff font-bold">
											<text>交易关闭</text>
										</view>
										<!-- <view class="text-36 text-#fff font-bold">0分0秒</view> -->
									</view>
									<view class="text-28 text-#fff font-500 mt-10">
										<text>
											{{ { BUYER_CLOSED: "当前订单已关闭", SYSTEM_CLOSED: "订单超时，系统已自动关闭", SELLER_CLOSED: "卖家已关闭此订单" }[shopOrder.status] }}
										</text>
									</view>
								</view>
							</template>
						</template>
						<template v-else-if="info">
							<!-- 待付款 -->
							<template v-if="info.status === 'UNPAID'">
								<view class="px-26 mt-20">
									<view class="flex justify-between items-center">
										<view class="text-38 text-#fff font-bold">
											<text>待付款</text>
										</view>
										<view class="text-36 text-#fff font-bold">
											<my-uv-count-down
												:time="Number(getOrderPayTime(info))"
												format="HH时mm分ss秒"
												ref="payCountDown"
												:autoStart="true"
												:textStyle="{
													fontSize: '36rpx',
													color: '#fff',
													fontWeight: 'bold',
												}"
												@finish="reloadData()"></my-uv-count-down>
										</view>
									</view>
									<view class="text-28 text-#fff font-500 mt-10">
										<text>请在规定时间内未付款，订单超时自动取消</text>
									</view>
								</view>
							</template>

							<!-- 待发货 -->
							<template v-if="info.status === 'PAID'">
								<view class="px-26 mt-20">
									<view class="flex justify-between items-center">
										<view class="text-38 text-#fff font-bold">
											<text>待发货</text>
										</view>
										<!-- <view class="text-36 text-#fff font-bold">0分0秒</view> -->
									</view>
									<view class="text-28 text-#fff font-500 mt-10">
										<text>订单已付款，会尽快安排发货</text>
									</view>
								</view>
							</template>

							<!-- 待收货 -->
							<template v-if="info.status === 'ON_DELIVERY'">
								<view class="px-26 mt-20">
									<view class="flex justify-between items-center">
										<view class="text-38 text-#fff font-bold">
											<text>待收货</text>
										</view>
										<!-- <view class="text-36 text-#fff font-bold">0分0秒</view> -->
									</view>
									<view class="text-28 text-#fff font-500 mt-10 flex">
										<view class="">订单已发出，还剩</view>
										<my-uv-count-down
											:time="Number(getOrderReceiveTime(info))"
											format="DD天HH时mm分ss秒"
											ref="payCountDown2"
											:autoStart="true"
											:textStyle="{
												fontSize: '28rpx',
												color: '#fff',
												fontWeight: '500',
											}"
											@finish="reloadData()"></my-uv-count-down>
										<view class="">自动确认</view>
									</view>
								</view>
							</template>

							<!-- 已完成 -->
							<template v-if="info.status === 'ACCOMPLISH'">
								<view class="px-26 mt-20">
									<view class="flex justify-between items-center">
										<view class="text-38 text-#fff font-bold">
											<text>交易完成</text>
										</view>
										<!-- <view class="text-36 text-#fff font-bold">0分0秒</view> -->
									</view>
									<view class="text-28 text-#fff font-500 mt-10">
										<text>订单已签收，期待您的下次购买</text>
									</view>
								</view>
							</template>

							<!-- 已关闭 -->
							<template v-if="info.status === 'SYSTEM_CLOSED'">
								<view class="px-26 mt-20">
									<view class="flex justify-between items-center">
										<view class="text-38 text-#fff font-bold">
											<text>交易关闭</text>
										</view>
										<!-- <view class="text-36 text-#fff font-bold">0分0秒</view> -->
									</view>
									<view class="text-28 text-#fff font-500 mt-10">
										<text>当前订单已关闭</text>
									</view>
								</view>
							</template>
						</template>
					</template>
					<!-- 服务订单 -->
					<template v-if="!goodsOrder">
						<view></view>
					</template>

					<template v-if="goodsOrder && info && info.orderReceiver">
						<view class="px-30 bg-#fff border-rd-20 mt-20 py-32">
							<view class="flex justify-start mt-0">
								<app-image src="@/pages/order/static/icon_dingwei_lv.png" class="mr-20" size="30" mode=""></app-image>
								<view class="flex-1">
									<view class="text-30 text-#222 font-bold line-height-30">{{ info.orderReceiver.address }}</view>
									<view class="text-26 text-#555 font-500 mt-10">{{ info.orderReceiver.name }} {{ info.orderReceiver.mobile }}</view>
								</view>
							</view>
						</view>
					</template>

					<template v-if="isIntegral && info && info.integralOrderReceiver">
						<view class="px-30 bg-#fff border-rd-20 mt-20 py-32">
							<view class="flex justify-start mt-0">
								<app-image src="@/pages/order/static/icon_dingwei_lv.png" class="mr-20" size="30" mode=""></app-image>
								<view class="flex-1">
									<view class="text-30 text-#222 font-bold line-height-30">{{ info.integralOrderReceiver.address }}</view>
									<view class="text-26 text-#555 font-500 mt-10">{{ info.integralOrderReceiver.name }} {{ info.integralOrderReceiver.mobile }}</view>
								</view>
							</view>
						</view>
					</template>

					<view class="px-26 bg-#fff border-rd-20 mt-20 pt-40" v-if="false">
						<view class="text-#121212 text-32 font-bold">服务信息</view>
						<view class="flex justify-start mt-20">
							<app-image src="@/pages/order/static/icon_dingwei_lv.png" class="mr-20" size="30" mode=""></app-image>
							<view class="flex-1">
								<view class="text-30 text-#222 font-bold line-height-30">南天成路333号高融大厦1102</view>
								<view class="text-26 text-#555 font-500 mt-10">张三 15666668888</view>
							</view>
						</view>
						<view class="border-t-solid border-1 border-#EAEDF3 text-28 font-500 py-30 mt-30 px-4">
							<text class="text-#222">客户备注:</text>
							<text class="text-#00B496 ml-10">尽快发货</text>
						</view>
					</view>

					<view class="px-26 bg-#fff border-rd-20 mt-20 pt-40 pb-30" v-if="false">
						<view class="text-#121212 text-32 font-bold">客户信息</view>
						<view class="flex justify-between items-center mt-20">
							<view class="text-28 text-#323232 font-500">客户ID</view>
							<view class="flex justify-end items-center">
								<image src="/static/logo.png" class="w-42 h-42 mr-12" mode=""></image>
								<text class="text-28 text-#222 font-500">张大雷</text>
							</view>
						</view>
						<view class="flex justify-between items-center mt-20">
							<view class="text-28 text-#323232 font-500">客户手机号</view>
							<view class="flex justify-end items-center">
								<text class="text-28 text-#222 font-500">159****2655</text>
								<view class="w-1 h-20 bg-#DFDFDF mx-12"></view>
								<image src="/static/order/phone.png" class="w-26 h-26" mode=""></image>
							</view>
						</view>
					</view>

					<view class="px-26 bg-#fff border-rd-20 mt-20 pt-30 pb-30">
						<template v-if="shopOrder.shopId">
							<view class="flex justify-start items-center" @click="goShopHome(shopOrder.shopId)">
								<app-image :src="shopOrder.shopLogo" size="42" class="w-42 h-42 border-1/2" mode=""></app-image>
								<text class="text-32 text-#222222 ml-12 mr-6 font-bold">{{ shopOrder.shopName }}</text>
								<uv-icon size="12" name="arrow-right" color="#999999" :bold="true"></uv-icon>
							</view>
						</template>

						<template v-if="goodsOrder">
							<template v-if="!isIntegral">
								<template v-for="(goods, index) in Array.from(orderMapComputed(shopOrder.shopOrderItems).values())" :key="index">
									<view class="mt-30">
										<view class="w-full flex" @click="goGoodsDetails(goods.merged)">
											<image :src="goods.merged.image" class="w-164 h-164 min-w-164 border-solid border-1 border-#F5F5F5 border-rd-16" mode=""></image>
											<view class="flex-1 flex flex-col justify-between ml-20">
												<view class="">
													<view class="text-30 text-#222222 font-bold uv-line-1">{{ goods.merged.productName }}</view>
													<view class="text-26 text-#555555 font-500 mt-10 flex justify-between items-center">
														<text>{{ goods.merged.specs ? goods.merged.specs?.join(" ") : "默认规格" }}</text>
														<text>x{{ goods.merged.num }}</text>
													</view>
												</view>
												<view class="text-#222222 font-bold">
													<view class="flex items-end">
														<view class="text-30 pb-2">¥</view>
														<view class="text-36">
															{{ getPriceInfo(goods.merged.salePrice).integer }}
														</view>
														<view class="text-30 pb-2">.{{ getPriceInfo(goods.merged.salePrice).decimalText }}</view>
													</view>
												</view>
											</view>
										</view>
										<view class="w-full flex justify-between items-center">
											<view class=""></view>
											<view class="flex items-center">
												<template v-if="afsButtonController(goods).show && !pageLoading">
													<view class="flex ml-10 flex-shrink-0">
														<uv-button :custom-style="{ fontSize: '24rpx', height: '60rpx', borderRadius: '60rpx' }" color="#D5D5D5" plain @click="applyAfterSale(goods)">
															<view class="btn-text text-#323232 flex-shrink-0 uv-line-1">{{ afsButtonController(goods).text }}</view>
														</uv-button>
													</view>
												</template>

												<template v-if="evaluateButtonController(goods).show && !pageLoading">
													<view class="flex ml-10">
														<uv-button :custom-style="{ fontSize: '24rpx', height: '60rpx', borderRadius: '60rpx' }" color="#FF950A" plain @click="goComment(goods)">
															<view class="btn-text text-#FF950A">{{ evaluateButtonController(goods).text }}</view>
														</uv-button>
													</view>
												</template>
											</view>
										</view>
									</view>
								</template>
							</template>
							<template v-else-if="info">
								<view class="mt-0">
									<view class="w-full flex" @click="goGoodsDetailsIntegralc(info)">
										<image :src="info.image" class="w-164 h-164 min-w-164 border-solid border-1 border-#F5F5F5 border-rd-16" mode=""></image>
										<view class="flex-1 flex flex-col justify-between ml-20">
											<view class="">
												<view class="text-30 text-#222222 font-bold uv-line-1">{{ info.productName }}</view>
												<view class="text-26 text-#555555 font-500 mt-10 flex justify-between items-center">
													<text>{{ info.specs ? info.specs?.join(" ") : "默认规格" }}</text>
													<text>x{{ info.num }}</text>
												</view>
											</view>
											<view class="text-#222222 font-bold">
												<view class="flex">
													<view class="flex items-end">
														<view class="text-36">
															{{ getPriceInfo(mulTenThousand(info.price)).integer }}
														</view>
														<view class="text-30 pb-2">.{{ getPriceInfo(mulTenThousand(info.price)).decimalText }}</view>
														<view class="text-30 pb-2">积分</view>
													</view>
													<view class="flex items-end" v-if="info.salePrice > 0">
														<view class="text-30 pb-2 px-5">+</view>
														<view class="text-30 pb-2">¥</view>
														<view class="text-36">
															{{ getPriceInfo(info.salePrice).integer }}
														</view>
														<view class="text-30 pb-2">.{{ getPriceInfo(info.salePrice).decimalText }}</view>
													</view>
												</view>
											</view>
										</view>
									</view>
									<view class="w-full flex justify-between items-center">
										<view class=""></view>
										<view class="flex items-center"></view>
									</view>
								</view>
							</template>
						</template>
						<template v-if="!goodsOrder"></template>

						<view class="w-full h-1 bg-#EAEDF3 mt-30 mb-15"></view>
						<template v-if="goodsOrder">
							<view class="py-15 flex justify-between items-center text-28 text-#323232 font-500">
								<view class="">运费</view>
								<view class="">{{ freightPrice?.cmp(0) > 0 ? `￥${getPriceInfo(freightPrice).integer}.${getPriceInfo(freightPrice).decimalText}` : `包邮` }}</view>
							</view>
						</template>
						<!-- <view class="py-15 flex justify-between items-center text-28 text-#323232 font-500">
							<view class="">优惠劵</view>
							<view class="text-#FC3F33">-￥20</view>
						</view> -->
						<template v-if="!isIntegral">
							<template v-if="statistics.shopDiscount">
								<view class="py-15 flex justify-between items-center text-28 text-#323232 font-500">
									<view class="">其他优惠</view>
									<view class="text-#FC3F33 flex items-center">
										<view class="">-</view>
										<view class="">￥</view>
										<view class="">{{ getPriceInfo(statistics.shopDiscount).integer }}.{{ getPriceInfo(statistics.shopDiscount).decimalText }}</view>
									</view>
								</view>
							</template>
							<template v-for="(item, idx) in statistics.otherDiscount" :key="idx">
								<view class="py-15 flex justify-between items-center text-28 text-#323232 font-500">
									<view class="">{{ item.name }}</view>
									<view class="text-#FC3F33 flex items-center">
										<view class="">-</view>
										<view class="">￥</view>
										<view class="">{{ getPriceInfo(item.price).integer }}.{{ getPriceInfo(item.price).decimalText }}</view>
									</view>
								</view>
							</template>
						</template>
						<!-- <template v-if="statistics.totalDiscount">
							<view class="py-15 flex justify-between items-center text-28 text-#323232 font-500">
								<view class="">总优惠</view>
								<view class="text-#FC3F33 flex items-center">
									<view class="">-</view>
									<view class="">￥</view>
									<view class="">{{ getPriceInfo(statistics.totalDiscount).integer }}.{{ getPriceInfo(statistics.totalDiscount).decimalText }}</view>
								</view>
							</view>
						</template> -->
						<template v-if="goodsOrder">
							<template v-if="!isIntegral">
								<view class="py-15 flex justify-between items-center text-28 text-#323232 font-500">
									<view class="">{{ info && info.status === "PAID" ? "实付款" : "应付款" }}</view>
									<view class="font-bold flex items-center">
										<view class="">￥</view>
										<view class="">{{ getPriceInfo(amountRealPay).integer }}.{{ getPriceInfo(amountRealPay).decimalText }}</view>
									</view>
								</view>
							</template>
							<template v-else-if="info">
								<view class="py-15 flex justify-between items-center text-28 text-#323232 font-500">
									<view class="">{{ info && info.status === "PAID" ? "实付款" : "应付款" }}</view>
									<view class="font-bold flex items-center">
										<view class="">{{ getPriceInfo(mulTenThousand(info.price)).integer }}.{{ getPriceInfo(mulTenThousand(info.price)).decimalText }}</view>
										<view class="">积分</view>
										<template v-if="info.salePrice + info.freightPrice > 0">
											<view class="px-5">+</view>
											<view class="">￥</view>
											<view class="">{{ getPriceInfo(info.salePrice + info.freightPrice).integer }}.{{ getPriceInfo(info.salePrice + info.freightPrice).decimalText }}</view>
										</template>
									</view>
								</view>
							</template>
						</template>
						<view class="w-full h-1 bg-#EAEDF3 mt-30"></view>
						<template v-if="info">
							<view class="pt-30 pb-15 flex justify-between items-center text-28 text-#323232 font-500">
								<view class="">订单编号</view>
								<view class="flex justify-end items-center">
									<view class="text-#616161">{{ info.no }}</view>
									<view class="flex items-center ml-10">
										<uv-button :customStyle="{ height: '30rpx', borderRadius: '30rpx', padding: '0rpx 12rpx' }" color="#F0F0F0" @click="onCopy(info.no)">
											<view class="text-20 font-500 text-#888888">复制</view>
										</uv-button>
									</view>
								</view>
							</view>
							<view class="py-10 flex justify-between items-center text-28 text-#323232 font-500">
								<view class="">创建时间</view>
								<view class="text-#616161">{{ info.createTime }}</view>
							</view>

							<template v-if="!isIntegral">
								<template v-if="orderStatusPlusInfo(info, shopOrder).status !== 'UNPAID' && orderStatusPlusInfo(info, shopOrder).status !== 'CLOSED'">
									<view class="py-10 flex justify-between items-center text-28 text-#323232 font-500">
										<view class="">支付方式</view>
										<view class="text-#616161">{{ usePaymentCn(info?.orderPayment?.type) }}</view>
									</view>
									<view class="py-10 flex justify-between items-center text-28 text-#323232 font-500">
										<view class="">支付时间</view>
										<view class="text-#616161">{{ info?.orderPayment?.payTime }}</view>
									</view>
								</template>

								<template v-if="goodsOrder && shopOrder.extra && shopOrder.extra.deliverTime">
									<view class="py-10 flex justify-between items-center text-28 text-#323232 font-500">
										<view class="">发货时间</view>
										<view class="text-#616161">{{ shopOrder.extra.deliverTime }}</view>
									</view>
								</template>

								<template v-if="goodsOrder && shopOrder.extra && shopOrder.extra.receiveTime">
									<view class="py-10 flex justify-between items-center text-28 text-#323232 font-500">
										<view class="">收货时间</view>
										<view class="text-#616161">{{ shopOrder.extra.receiveTime }}</view>
									</view>
								</template>

								<template v-if="orderStatusPlusInfo(info, shopOrder).isClosed && orderStatusPlusInfo(info, shopOrder).closeTime">
									<view class="py-10 flex justify-between items-center text-28 text-#323232 font-500">
										<view class="">取消时间</view>
										<view class="text-#616161">{{ orderStatusPlusInfo(info, shopOrder).closeTime }}</view>
									</view>
								</template>
							</template>
							<template v-else>
								<template v-if="info && info.payTime">
									<view class="py-10 flex justify-between items-center text-28 text-#323232 font-500">
										<view class="">支付时间</view>
										<view class="text-#616161">{{ info.payTime }}</view>
									</view>
								</template>

								<template v-if="goodsOrder && shopOrder.extra && shopOrder.extra.deliveryTime">
									<view class="py-10 flex justify-between items-center text-28 text-#323232 font-500">
										<view class="">发货时间</view>
										<view class="text-#616161">{{ shopOrder.extra.deliveryTime }}</view>
									</view>
								</template>

								<template v-if="goodsOrder && shopOrder.extra && shopOrder.extra.receiveTime">
									<view class="py-10 flex justify-between items-center text-28 text-#323232 font-500">
										<view class="">收货时间</view>
										<view class="text-#616161">{{ shopOrder.extra.receiveTime }}</view>
									</view>
								</template>
							</template>
						</template>
						<template v-if="goodsOrder && !isIntegral">
							<view class="w-full h-1 bg-#EAEDF3 mt-30 mb-15"></view>
							<template v-for="(val, key, index) in remark" :key="index">
								<template v-if="!['newOrderNotify', 'remark'].includes(key)">
									<view class="py-10 flex justify-between items-center text-28 text-#323232 font-500">
										<view class="flex-shrink-0 mr-20">{{ key }}</view>
										<template v-if="isImage(val)">
											<view>
												<image :src="val" class="h-80" mode="heightFix" @click="previewImage(val)"></image>
											</view>
										</template>
										<template v-else>
											<view class="text-#616161">{{ val }}</view>
										</template>
									</view>
								</template>
							</template>
						</template>
					</view>
					<view class="h-20"></view>
				</view>
				<template #bottom>
					<view class="w-full">
						<view class="w-full">
							<!-- 商城订单 -->
							<template v-if="goodsOrder && info && shopOrder">
								<template v-if="!isIntegral">
									<!-- 待付款 -->
									<template v-if="orderStatusPlusInfo(info, shopOrder).status === 'UNPAID'">
										<view class="py-14 px-20 border-t-solid border-1 border-#F2F2F2 bg-#fff">
											<view class="flex items-center justify-between">
												<view class=""></view>

												<view class="flex items-center">
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="cancelOrder(info)">
															<view class="btn-text text-#323232">取消订单</view>
														</uv-button>
													</view>

													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#00B496" plain @click="changeOrderAddress(info)">
															<view class="btn-text text-#00B496">更换地址</view>
														</uv-button>
													</view>

													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#66C5B5" @click="openPay(info)">
															<view class="btn-text text-#fff">立即支付</view>
														</uv-button>
													</view>
												</view>
											</view>
										</view>
									</template>

									<!-- 待发货 -->
									<template v-if="orderStatusPlusInfo(info, shopOrder).status === 'UN_DELIVERY'">
										<view class="py-14 px-50 border-t-solid border-1 border-#F2F2F2 bg-#fff">
											<view class="flex items-center justify-between">
												<view class=""></view>

												<view class="flex items-center">
													<!-- <view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="cancelOrder(info)">
															<view class="btn-text text-#323232">取消订单</view>
														</uv-button>
													</view> -->
													<!-- <view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="applyAfterSale(info)">
															<view class="btn-text text-#323232">申请售后</view>
														</uv-button>
													</view> -->
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#00B496" plain @click="urge('shipment', info)">
															<view class="btn-text text-#00B496">催发货</view>
														</uv-button>
													</view>
												</view>
											</view>
										</view>
									</template>

									<!-- 待收货 -->
									<template v-if="orderStatusPlusInfo(info, shopOrder).status === 'UN_RECEIVE'">
										<view class="py-14 px-50 border-t-solid border-1 border-#F2F2F2 bg-#fff">
											<view class="flex items-center justify-between">
												<view class=""></view>

												<view class="flex items-center">
													<!-- <view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="applyAfterSale(info)">
															<view class="btn-text text-#323232">申请售后</view>
														</uv-button>
													</view> -->
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#00B496" plain @click="viewLogistics(info)">
															<view class="btn-text text-#00B496">查看物流</view>
														</uv-button>
													</view>
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#66C5B5" @click="confirmReceive(info)">
															<view class="btn-text text-#fff">确认收货</view>
														</uv-button>
													</view>
												</view>
											</view>
										</view>
									</template>

									<!-- 待评价 -->
									<template v-if="orderStatusPlusInfo(info, shopOrder).status === 'UN_COMMENT'">
										<view class="py-14 px-50 border-t-solid border-1 border-#F2F2F2 bg-#fff">
											<view class="flex items-center justify-between">
												<view class=""></view>

												<view class="flex items-center">
													<!-- <view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="delOrder(info)">
															<view class="btn-text text-#323232">删除订单</view>
														</uv-button>
													</view> -->
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="viewLogistics(info)">
															<view class="btn-text text-#323232">查看物流</view>
														</uv-button>
													</view>
													<!-- <view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#FF950A" @click="goGoodsDetails(shopOrder.shopOrderItems[0])" plain>
															<view class="btn-text text-#FF950A">去评价</view>
														</uv-button>
													</view> -->
												</view>
											</view>
										</view>
									</template>

									<!-- 已完成 -->
									<template v-if="orderStatusPlusInfo(info, shopOrder).status === 'COMPLETED'">
										<view class="py-14 px-50 border-t-solid border-1 border-#F2F2F2 bg-#fff">
											<view class="flex items-center justify-between">
												<view class=""></view>

												<view class="flex items-center">
													<!-- <view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="delOrder(info)">
															<view class="btn-text text-#323232">删除订单</view>
														</uv-button>
													</view> -->
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="viewLogistics(info)">
															<view class="btn-text text-#323232">查看物流</view>
														</uv-button>
													</view>
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" @click="goGoodsDetails(shopOrder.shopOrderItems[0])" plain>
															<view class="btn-text text-#323232">再来一单</view>
														</uv-button>
													</view>
													<template v-if="['', 'ALLOWED_INVOICING', 'REQUEST_HAS_EXPIRED', 'SERVER_NOT_SUPPORTED'].includes(orderInvoiceStatus)">
														<view class="btn-item-box">
															<uv-button :custom-style="_data.btnItemStyle" color="#00B496" plain @click="applyInvoice(info)">
																<view class="btn-text text-#00B496">申请开票</view>
															</uv-button>
														</view>
													</template>

													<template v-if="orderInvoiceStatus === 'REQUEST_IN_PROCESS'">
														<view class="btn-item-box">
															<uv-button :custom-style="_data.btnItemStyle" color="#00B496" plain @click="applyInvoice(info)">
																<view class="btn-text text-#00B496">开票中</view>
															</uv-button>
														</view>
													</template>

													<template v-if="orderInvoiceStatus === 'SUCCESSFULLY_INVOICED'">
														<view class="btn-item-box">
															<uv-button :custom-style="_data.btnItemStyle" color="#00B496" plain @click="applyInvoice(info)">
																<view class="btn-text text-#00B496">开票完成</view>
															</uv-button>
														</view>
													</template>

													<template v-if="orderInvoiceStatus === 'FAILED_INVOICE_REQUEST'">
														<view class="btn-item-box">
															<uv-button :custom-style="_data.btnItemStyle" color="#00B496" plain @click="applyInvoice(info)">
																<view class="btn-text text-#00B496">开票失败</view>
															</uv-button>
														</view>
													</template>
												</view>
											</view>
										</view>
									</template>

									<!-- 已关闭 -->
									<template v-if="orderStatusPlusInfo(info, shopOrder).status === 'CLOSED'">
										<view class="py-14 px-50 border-t-solid border-1 border-#F2F2F2 bg-#fff">
											<view class="flex items-center justify-between">
												<view class=""></view>

												<view class="flex items-center">
													<!-- <view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="delOrder(info)">
															<view class="btn-text text-#323232">删除订单</view>
														</uv-button>
													</view> -->
													<!-- <view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="viewLogistics(info)">
															<view class="btn-text text-#323232">查看物流</view>
														</uv-button>
													</view> -->
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" @click="goGoodsDetails(shopOrder.shopOrderItems[0])" plain>
															<view class="btn-text text-#323232">再来一单</view>
														</uv-button>
													</view>
												</view>
											</view>
										</view>
									</template>
								</template>
								<template v-else>
									<!-- 待付款 -->
									<template v-if="info.status === 'UNPAID'">
										<view class="py-14 px-20 border-t-solid border-1 border-#F2F2F2 bg-#fff">
											<view class="flex items-center justify-between">
												<view class=""></view>

												<view class="flex items-center">
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="cancelOrderIntegral(info)">
															<view class="btn-text text-#323232">取消订单</view>
														</uv-button>
													</view>

													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#00B496" plain @click="changeOrderAddressIntegral(info)">
															<view class="btn-text text-#00B496">更换地址</view>
														</uv-button>
													</view>

													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#66C5B5" @click="openPay(info)">
															<view class="btn-text text-#fff">立即支付</view>
														</uv-button>
													</view>
												</view>
											</view>
										</view>
									</template>

									<!-- 待发货 -->
									<template v-if="info.status === 'PAID'">
										<view class="py-14 px-50 border-t-solid border-1 border-#F2F2F2 bg-#fff">
											<view class="flex items-center justify-between">
												<view class=""></view>

												<view class="flex items-center">
													<!-- <view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="cancelOrder(info)">
															<view class="btn-text text-#323232">取消订单</view>
														</uv-button>
													</view> -->
													<!-- <view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="applyAfterSale(info)">
															<view class="btn-text text-#323232">申请售后</view>
														</uv-button>
													</view> -->
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#00B496" plain @click="urge('shipment', info)">
															<view class="btn-text text-#00B496">催发货</view>
														</uv-button>
													</view>
												</view>
											</view>
										</view>
									</template>

									<!-- 待收货 -->
									<template v-if="info.status === 'ON_DELIVERY'">
										<view class="py-14 px-50 border-t-solid border-1 border-#F2F2F2 bg-#fff">
											<view class="flex items-center justify-between">
												<view class=""></view>

												<view class="flex items-center">
													<!-- <view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="applyAfterSale(info)">
															<view class="btn-text text-#323232">申请售后</view>
														</uv-button>
													</view> -->
													<template v-if="info.expressNo">
														<view class="btn-item-box">
															<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="viewLogisticsSingle(info)">
																<view class="btn-text text-#323232">查看物流</view>
															</uv-button>
														</view>
													</template>
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#66C5B5" @click="confirmReceiveIntegral(info)">
															<view class="btn-text text-#fff">确认收货</view>
														</uv-button>
													</view>
												</view>
											</view>
										</view>
									</template>

									<!-- 已完成 -->
									<template v-if="info.status === 'ACCOMPLISH'">
										<view class="py-14 px-50 border-t-solid border-1 border-#F2F2F2 bg-#fff">
											<view class="flex items-center justify-between">
												<view class=""></view>

												<view class="flex items-center">
													<!-- <view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="delOrder(info)">
															<view class="btn-text text-#323232">删除订单</view>
														</uv-button>
													</view> -->
													<template v-if="info.expressNo">
														<view class="btn-item-box">
															<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="viewLogisticsSingle(info)">
																<view class="btn-text text-#323232">查看物流</view>
															</uv-button>
														</view>
													</template>
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" @click="goGoodsDetailsIntegralc(info)" plain>
															<view class="btn-text text-#323232">再来一单</view>
														</uv-button>
													</view>
												</view>
											</view>
										</view>
									</template>

									<!-- 已关闭 -->
									<template v-if="info.status === 'SYSTEM_CLOSED'">
										<view class="py-14 px-50 border-t-solid border-1 border-#F2F2F2 bg-#fff">
											<view class="flex items-center justify-between">
												<view class=""></view>

												<view class="flex items-center">
													<!-- <view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="delOrder(info)">
															<view class="btn-text text-#323232">删除订单</view>
														</uv-button>
													</view> -->
													<!-- <view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="viewLogistics(info)">
															<view class="btn-text text-#323232">查看物流</view>
														</uv-button>
													</view> -->
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" @click="goGoodsDetailsIntegralc(info)" plain>
															<view class="btn-text text-#323232">再来一单</view>
														</uv-button>
													</view>
												</view>
											</view>
										</view>
									</template>
								</template>
							</template>
							<!-- 服务订单 -->
							<template v-if="!goodsOrder"></template>
						</view>
						<app-safeAreaBottom></app-safeAreaBottom>
					</view>
				</template>
			</z-paging>

			<app-pay-select
				ref="paySelectRef"
				v-model:show="showPay"
				v-model:btnLoading="payLoading"
				:price="payInfo.price"
				:balanceTotal="payInfo.balanceTotal"
				:balanceTotalShow="payInfo.balanceTotalShow"
				:integralPriceShow="payInfo.integralPriceShow"
				:integralPrice="payInfo.integralPrice"
				:integralTotalShow="payInfo.integralTotalShow"
				:integralTotal="payInfo.integralTotal"
				:timeout="payInfo.timeout"
				@submit="paySubmit"
				@change="paySelectChange"></app-pay-select>
		</app-layout>
	</view>
</template>

<style lang="scss" scoped>
.btn-item-box {
	height: 64rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 0rpx 0 14rpx;
}
.btn-text {
	font-size: 26rpx;
	font-weight: 500;
}
.btn-item {
	height: 64rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 26rpx;
	margin: 0 14rpx 0 0;
	border-radius: 40rpx;
	border-style: solid;
	border-width: 1rpx;
	font-size: 26rpx;
	font-weight: 500;
}
</style>
