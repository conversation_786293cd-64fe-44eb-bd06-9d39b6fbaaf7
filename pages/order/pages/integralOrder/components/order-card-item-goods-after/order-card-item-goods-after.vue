<script setup>
import { reactive, ref, watch, nextTick, computed } from 'vue';

import { Decimal } from 'decimal.js';

import dayjs from 'dayjs';

import { getOrder, closeOrderByOrderNo, putOrderReceiver, getUserBalance, confirmGoods } from '@/server/api';

import { useOrderStore } from '@/store';

import { getPriceInfo, route, goShopHome } from '@/common/utils';

// import { isUnpaidOrder, orderStatusPlus, orderStatusPlusInfo } from '@/pages/order/common/utils';

// import { getAfsStatusCn } from '@/pages/order/common/types/order';

import { isUnpaidOrder, orderStatusPlus, orderStatusPlusInfo } from '@/common/order';

import { getAfsStatusCn } from '@/common/types/order';

import { deepClone, sleep } from '@/uni_modules/uv-ui-tools/libs/function/index.js';

const $props = defineProps({
	index: {
		type: Number,
		default: 0
	},
	item: {
		type: Object,
		default: () => ({})
	},
	orderIndex: {
		type: Number,
		default: 0
	},
	order: {
		type: Object,
		default: () => ({})
	},
	orderType: {
		type: String,
		default: 'shop'
	}
});

const $emit = defineEmits(['reloadList', 'orderOperation']);

const $data = reactive({
	btnItemStyle: {
		height: '64rpx',
		borderRadius: '40rpx',
		fontSize: '26rpx'
	}
});

// 商品详情
function goGoodsDetails(goods) {
	route('/pages/goods/pages/productDetails/productDetails', {
		productId: goods.productId,
		shopId: goods.shopId
	});
}

// 售后详情
function goAfterSaleDetail(order) {
	route('/pages/order/pages/afterSaleDetail/afterSaleDetail', {
		afsNo: order.afsNo,
		packageId: order.packageId || ''
	});
}

const tabIndex = ref(1);

defineExpose({});
</script>
<template>
	<view class="w-full">
		<view class="px-0 bg-#fff-0 border-rd-0">
			<view class="h-102 flex justify-between items-center">
				<view class="flex justify-start items-center" @click="goShopHome(item.shopId)">
					<app-image :src="item.shopLogo" size="42" class="w-42 h-42 border-1/2" mode=""></app-image>
					<text class="text-28 text-#222222 ml-12 mr-6">{{ item.shopName }}</text>
					<uv-icon size="12" name="arrow-right" color="#999999" :bold="true"></uv-icon>
				</view>

				<view class="text-#888888 text-26 font-500">{{ getAfsStatusCn(item.status).list }}</view>
			</view>
			<view class="w-full h-1 bg-#F1F3F8"></view>
			<view
				class="flex mt-25"
				@click="
					goAfterSaleDetail({
						afsNo: item.no,
						packageId: item.packageId
					})
				"
			>
				<image :src="item.afsOrderItem.image" class="w-164 h-164 min-w-164 border-solid border-1 border-#F5F5F5 border-rd-16" mode=""></image>
				<view class="flex-1 flex flex-col justify-between ml-20">
					<view class="">
						<view class="text-30 text-#222222 font-bold uv-line-1">{{ item.afsOrderItem.productName }}</view>
						<view class="text-26 text-#555555 font-500 mt-10 flex justify-between items-center">
							<text>{{ item.afsOrderItem.specs.join(' ') }}</text>
							<text>x{{ item.afsOrderItem.num }}</text>
						</view>
					</view>
					<view class="text-#222222 font-bold">
						<view class="flex items-end">
							<view class="text-30 pb-4">¥</view>
							<view class="text-36">
								{{ getPriceInfo(item.afsOrderItem.salePrice).integer }}
							</view>
							<view class="text-30 pb-4">.{{ getPriceInfo(item.afsOrderItem.salePrice).decimalText }}</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 已关闭 -->
			<template v-if="orderStatusPlusInfo(order, item).status === 'CLOSED'">
				<view class="w-full h-1 bg-#F1F3F8 mt-24 mb-24"></view>
				<view class="w-full flex justify-between items-center">
					<view class="text-#888888 text-28 font-500">
						<!-- 更多 -->
					</view>

					<view class="flex justify-end items-center">
						<view class="btn-item-box">
							<uv-button
								:custom-style="$data.btnItemStyle"
								color="#D5D5D5"
								@click="
									goGoodsDetails({
										productId: item.afsOrderItem.productId,
										shopId: item.shopId
									})
								"
								plain
							>
								<view class="btn-text text-#323232">再来一单</view>
							</uv-button>
						</view>
					</view>
				</view>
			</template>
		</view>
	</view>
</template>
<style lang="scss" scoped>
.btn-item-box {
	height: 64rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 0rpx 0 14rpx;
}
.btn-text {
	font-size: 26rpx;
	font-weight: 500;
}
.btn-item {
	height: 64rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 26rpx;
	margin: 0 14rpx 0 0;
	border-radius: 40rpx;
	border-style: solid;
	border-width: 1rpx;
	font-size: 26rpx;
	font-weight: 500;
}
</style>
