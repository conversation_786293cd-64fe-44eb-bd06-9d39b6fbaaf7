<script setup>
import { reactive, ref, watch, nextTick, computed } from "vue";

import { Decimal } from "decimal.js";

import dayjs from "dayjs";

import { getOrder, closeOrderByOrderNo, putOrderReceiver, getUserBalance, confirmGoods } from "@/server/api";

import { closeIntegralOrderByOrderNo, putIntegralOrderReceiver } from "@/server/api";

import { createIntegralOrderCreate, getIntegralOrderCreateConditions, getUserIntegralSystemtotal, submitIntegralOrderPay, getIntegralOrderIsPaySuccess, confirmIntegralGoods } from "@/server/api";

import { useOrderStore } from "@/store";

import { getPriceInfo, route, goShopHome } from "@/common/utils";

// import { isUnpaidOrder, orderStatusPlus, orderStatusPlusInfo } from '@/pages/order/common/utils';

// import { getAfsStatusCn } from '@/pages/order/common/types/order';

import { isUnpaidOrder, orderStatusPlus, orderStatusPlusInfo } from "@/common/order";

import { getAfsStatusCn } from "@/common/types/order";

import useConvert from "@/common/useConvert";

import { deepClone, sleep } from "@/uni_modules/uv-ui-tools/libs/function/index.js";

const $props = defineProps({
	index: {
		type: Number,
		default: 0,
	},
	item: {
		type: Object,
		default: () => ({}),
	},
	orderIndex: {
		type: Number,
		default: 0,
	},
	order: {
		type: Object,
		default: () => ({}),
	},
	orderType: {
		type: String,
		default: "shop",
	},
});

const $emit = defineEmits(["reloadList", "orderOperation"]);

const { mulTenThousand } = useConvert();

const isIntegral = computed(() => {
	const typeItem = $props.orderType;
	if (typeItem) {
		return typeItem === "integral";
	}
	return false;
});

const integralcOrderStatus = {
	UNPAID: "未支付",
	PAID: "待发货",
	ON_DELIVERY: "待收货",
	ACCOMPLISH: "已完成",
	SYSTEM_CLOSED: "已关闭",
};

function mergeItemsArr(pre, current) {
	pre.num += current.num;
	pre.fixPrice = new Decimal(pre.fixPrice).plus(current.fixPrice).toString();
	pre.freightPrice = new Decimal(pre.freightPrice).plus(current.freightPrice).toString();
	//当前是关闭状态 下一个是未关闭状态 则设置为正常状态
	const changeCloseStatus = pre.status === "CLOSED" && current.status === "OK";
	//判断是否设置为正常状态
	if (changeCloseStatus) {
		//部分关闭 说明有未关闭的 合并后的状态设置为正常
		pre.status = current.status;
		pre.packageStatus = current.packageStatus;
	}
	if (pre.afsStatus === "NONE" && current.afsStatus !== "NONE") {
		pre.afsStatus = current.afsStatus;
	}
	//部分发货 设置为待发货状态
	if (!!pre.packageId && !current.packageId) {
		pre.packageId = current.packageId;
	}
	if (pre.packageStatus !== "WAITING_FOR_DELIVER" && current.packageStatus === "WAITING_FOR_DELIVER") {
		pre.packageStatus = current.packageStatus;
	}
	//如果

	pre.merged = true;
	return pre;
}

function orderMapComputed(shopOrderItems = []) {
	const shopOrderItemsMap = shopOrderItems.reduce((pre, item) => {
		const id = `${item.productId}${item.skuId}${item.specs}`;
		const currentItem = pre.get(id);
		if (currentItem) {
			const current = { ...item };
			currentItem.merged = mergeItemsArr(currentItem.merged, current);
			currentItem.items.push(current);
		} else {
			pre.set(id, {
				items: [{ ...item }],
				merged: { ...item },
			});
		}
		return pre;
	}, new Map());

	return shopOrderItemsMap;
}

function computeOrderPrice(data) {
	if (data.id) {
		return data.shopOrderItems.reduce((pre, cur) => {
			return pre.add(new Decimal(cur.num).mul(cur.dealPrice).add(cur.fixPrice).add(cur.freightPrice));
		}, new Decimal(0));
	}
	return $props.order.shopOrders.reduce((pre, item) => pre.add(new Decimal(item.dealPrice).mul(item.num).add(item.freightPrice).add(item.fixPrice)), new Decimal(0));
}

function reloadList() {
	$emit("reloadList");
}

function countDownFinish(e) {
	reloadList();
}

const $data = reactive({
	btnItemStyle: {
		height: "64rpx",
		borderRadius: "40rpx",
		fontSize: "26rpx",
	},
});

const paySelectRef = ref();
const showPay = ref(false);
const payLoading = ref(false);
const payInfo = reactive({
	price: 0,
	balanceTotalShow: true,
	balanceTotal: 0,
	timeout: 0,

	integralPriceShow: false,
	integralPrice: 0,
	integralTotalShow: true,
	integralTotal: 0,
});

function getOrderTime(order) {
	const timeout = order.timeout.payTimeout * 1000 - (dayjs().valueOf() - dayjs(order.createTime).valueOf());

	return timeout;
}

async function initUserBalance() {
	const { code, msg, data, apiStatus } = await getUserBalance();
	payInfo.balanceTotalShow = apiStatus;
	payInfo.balanceTotal = apiStatus ? `${getPriceInfo(data).integer}.${getPriceInfo(data).decimalText}` : "";
}

// 取消订单
function cancelOrder(order) {
	uni.showModal({
		title: "请确认",
		content: "是否需要取消？",
		success: async ({ confirm }) => {
			if (!confirm) return;
			const { code, data, msg, apiStatus } = await closeOrderByOrderNo({ orderNo: order.no });
			// if (!apiStatus) return uni.showToast({ icon: 'none', title: `${msg ? msg : '取消订单失败'}` });
			if (!apiStatus) return;
			uni.showToast({ icon: "none", title: `订单已取消` });
			reloadList();
		},
	});
}

// 取消订单
function cancelOrderIntegral(order) {
	uni.showModal({
		title: "请确认",
		content: "是否需要取消？",
		success: async ({ confirm }) => {
			if (!confirm) return;
			const { code, data, msg, apiStatus } = await closeIntegralOrderByOrderNo({ orderNo: order.no });
			// if (!apiStatus) return uni.showToast({ icon: 'none', title: `${msg ? msg : '取消订单失败'}` });
			if (!apiStatus) return;
			uni.showToast({ icon: "none", title: `订单已取消` });
			reloadList();
		},
	});
}

// 更换地址
function changeOrderAddress(order) {
	route({
		type: "to",
		url: "/pages/my/pages/address/addressGoods",
		params: {
			select: 1,
		},
		events: {
			async selectAddress(data) {
				await sleep(150);
				uni.showModal({
					title: "请确认",
					content: `是否更改地址为${data.address}？`,
					success: async ({ confirm }) => {
						if (!confirm) return;
						const { apiStatus } = await putOrderReceiver({ orderNo: order.no, ...data });
						// if (!apiStatus) return uni.showToast({ icon: 'none', title: `${msg ? msg : '修改失败'}` });
						if (!apiStatus) return;
						uni.showToast({ icon: "none", title: `修改成功` });
						reloadList();
					},
				});
			},
		},
	});
}

// 更换地址
function changeOrderAddressIntegral(order) {
	route({
		type: "to",
		url: "/pages/my/pages/address/addressGoods",
		params: {
			select: 1,
		},
		events: {
			async selectAddress(data) {
				await sleep(150);
				uni.showModal({
					title: "请确认",
					content: `是否更改地址为${data.address}？`,
					success: async ({ confirm }) => {
						if (!confirm) return;
						const { apiStatus } = await putIntegralOrderReceiver({ orderNo: order.no, ...data, id: order.id });
						// if (!apiStatus) return uni.showToast({ icon: 'none', title: `${msg ? msg : '修改失败'}` });
						if (!apiStatus) return;
						uni.showToast({ icon: "none", title: `修改成功` });
						reloadList();
					},
				});
			},
		},
	});
}

// 再次支付
async function openPay(order) {
	// uni.showLoading({
	// 	title: '加载中...'
	// });
	// await initUserBalance();
	// payInfo.timeout = order.timeout.payTimeout * 1000 - (dayjs().valueOf() - dayjs(order.createTime).valueOf());
	// payInfo.price = `${getPriceInfo(order.orderPayment.payAmount).integer}.${getPriceInfo(order.orderPayment.payAmount).decimalText}`;
	// showPay.value = true;
	// uni.hideLoading();

	$emit("orderOperation", {
		type: "pay",
		order,
	});
}

// 催
async function urge(type, order) {
	if (!type || !order) return;

	const typeMap = {
		shipment: {
			name: "催发货",
			msg: "已提醒商家尽快发货,请您耐心等待~",
		},
		take: {
			name: "催接单",
			msg: "已提醒商家尽快接单,请您耐心等待~",
		},
		allocation: {
			name: "催分配",
			msg: "已提醒商家尽快分配服务人员,请您耐心等待~",
		},
		takeMaster: {
			name: "催接单",
			msg: "已提醒服务人员尽快接单,请您耐心等待~",
		},
	};

	const item = typeMap[type];

	if (!item) return;

	uni.showLoading({
		title: "加载中...",
	});
	await sleep(500);
	uni.hideLoading();
	uni.showToast({
		icon: "none",
		title: item.msg,
	});
}

// 查看物流
function viewLogistics(order) {
	route("/pages/order/pages/logisticsOrderMore/logisticsOrderMore", {
		orderNo: order.no,
		shopOrderNo: $props.item.no,
		shopId: $props.item.shopId,
	});
	useOrderStore().setOrderInfoItem({
		...$props.item,
	});
}

// 查看物流
function viewLogisticsSingle(order) {
	useOrderStore().setOrderInfoItem({
		...order,
	});

	route("/pages/order/pages/logisticsOrderSingle/logisticsOrderSingle", {
		expressNo: order.expressNo,
		companyCode: order.expressCompanyName,
		recipientsPhone: order.integralOrderReceiverVO?.mobile || order.integralOrderReceiver?.mobile,
		orderType: "integral",
	});
}

// 确认收货
function confirmReceive(order) {
	const showModalProps = {
		content: "是否确认收货",
		showClose: true,
		isSubmit: true,
	};

	// 通过每一个商品的售后得到 可以改变包状态的数组
	const shop = $props.item;
	if (!shop.shopOrderItems.every((item) => getAfsStatusCn(item.afsStatus).canChangePackageStatus)) {
		showModalProps.content = "该订单中存在退款宝贝，等待商家确认收货";
		showModalProps.isSubmit = false;
		showModalProps.showClose = false;
	} else if (!shop.shopOrderItems.every((item) => !getAfsStatusCn(item.afsStatus).canChangePackageStatusText)) {
		showModalProps.content = "该订单中存在退款宝贝，确认收货将关闭退款";
	}
	// 该订单中存在退款宝贝，等待商家确认收货  该订单中存在退款宝贝，确认收货将关闭退款
	uni.showModal({
		// title: '提示',
		confirmColor: "#f12f22",
		content: `${showModalProps.content}`,
		showCancel: showModalProps.showClose,
		success: async (res) => {
			if (res.cancel || !showModalProps.isSubmit) return;
			const { packageId } = shop.shopOrderItems[0];
			const { code, msg, apiStatus } = await confirmGoods({ orderNo: order.no, shopId: shop.shopId });
			// if (!apiStatus) return uni.showToast({ title: `${msg ? msg : '确认收货失败'}`, icon: 'none' });
			if (!apiStatus) return;
			uni.showToast({ icon: "none", title: `收货成功` });
			reloadList();
		},
	});
}

// 确认收货
function confirmReceiveIntegral(order) {
	const showModalProps = {
		content: "是否确认收货",
		showClose: true,
		isSubmit: true,
	};

	uni.showModal({
		// title: '提示',
		confirmColor: "#f12f22",
		content: `${showModalProps.content}`,
		showCancel: showModalProps.showClose,
		success: async (res) => {
			const { code, msg, apiStatus } = await confirmIntegralGoods({ orderNo: order.no });
			// if (!apiStatus) return uni.showToast({ title: `${msg ? msg : '确认收货失败'}`, icon: 'none' });
			if (!apiStatus) return;
			uni.showToast({ icon: "none", title: `收货成功` });
			reloadList();
		},
	});
}

// 删除订单
function delOrder(order) {
	const showModalProps = {
		content: "是否删除订单",
		showClose: true,
		isSubmit: true,
	};

	// 通过每一个商品的售后得到 可以改变包状态的数组
	// const shop = $props.item;
	// if (!shop.shopOrderItems.every((item) => getAfsStatusCn(item.afsStatus).canChangePackageStatus)) {
	// 	showModalProps.content = '该订单中存在退款宝贝，等待商家确认收货';
	// 	showModalProps.isSubmit = false;
	// 	showModalProps.showClose = false;
	// } else if (!shop.shopOrderItems.every((item) => !getAfsStatusCn(item.afsStatus).canChangePackageStatusText)) {
	// 	showModalProps.content = '该订单中存在退款宝贝，确认收货将关闭退款';
	// }
	// 该订单中存在退款宝贝，等待商家确认收货  该订单中存在退款宝贝，确认收货将关闭退款
	uni.showModal({
		title: "提示",
		confirmColor: "#f12f22",
		content: `${showModalProps.content}`,
		showCancel: showModalProps.showClose,
		success: async (res) => {
			if (res.cancel || !showModalProps.isSubmit) return;
			// const { packageId } = shop.shopOrderItems[0];
			// const { code, msg, apiStatus } = await confirmGoods({ orderNo: order.no, shopId: shop.shopId });
			// // if (!apiStatus) return uni.showToast({ title: `${msg ? msg : '确认收货失败'}`, icon: 'none' });
			// if (!apiStatus) return;
			// uni.showToast({ icon: 'none', title: `收货成功` });
			// reloadList();
		},
	});
}

// 申请售后
function applyAfterSale(order) {}

// 商品详情
function goGoodsDetails(goods) {
	route("/pages/goods/pages/productDetails/productDetails", {
		productId: goods.productId,
		shopId: goods.shopId,
	});
}

// 商品详情
function goGoodsDetailsIntegralc(goods) {
	route("/pages/goods/pages/productDetailsIntegral/productDetailsIntegral", {
		productId: goods.productId,
	});
}

// 订单详情
function goOrderDetails(order) {
	const shop = $props.item;
	route("/pages/order/pages/orderDetail/orderDetail", {
		orderNo: order.no,
		shopId: shop.shopId,
		orderType: $props.orderType,
		packageId: shop.shopOrderItems[0].packageId || "",
	});
}

// 订单详情
function goIntegralcOrderDetails(order) {
	route("/pages/order/pages/orderDetail/orderDetail", {
		orderNo: order.no,
		orderType: $props.orderType,
	});
}

const tabIndex = ref(1);

defineExpose({});
</script>
<template>
	<view class="w-full">
		<view class="px-0 bg-#fff-0 border-rd-0">
			<view class="h-102 flex justify-between items-center">
				<template v-if="item.shopId">
					<view class="flex justify-start items-center" @click="goShopHome(item.shopId)">
						<app-image :src="item.shopLogo" size="42" class="w-42 h-42 border-1/2" mode=""></app-image>
						<text class="text-28 text-#222222 ml-12 mr-6">{{ item.shopName }}</text>
						<uv-icon size="12" name="arrow-right" color="#999999" :bold="true"></uv-icon>
					</view>
				</template>
				<template v-else>
					<view class="flex justify-start items-center">
						<view class="flex justify-start items-center">
							<text class="text-26 text-#222222 ml-0 mr-6">订单号：{{ order.no }}</text>
						</view>
					</view>
				</template>

				<template v-if="!isIntegral">
					<!-- 待付款 -->
					<template v-if="orderStatusPlusInfo(order, item).status === 'UNPAID'">
						<text class="text-#F94B4A text-26 font-500">待付款</text>
					</template>

					<!-- 待发货 -->
					<template v-if="orderStatusPlusInfo(order, item).status === 'UN_DELIVERY'">
						<view class="flex justify-end items-center">
							<!-- <text class="text-#888888 text-22">{{ order.createTime }}下单</text>
							<view class="w-1 h-20 bg-#EEEEEE mx-14"></view> -->
							<text class="text-#00B496 text-26 font-500">{{ orderStatusPlus(order, item).desc }}</text>
						</view>
					</template>

					<!-- 待收货 -->
					<template v-if="orderStatusPlusInfo(order, item).status === 'UN_RECEIVE'">
						<view class="flex justify-end items-center">
							<!-- <text class="text-#888888 text-22">{{ order.createTime }}下单</text>
							<view class="w-1 h-20 bg-#EEEEEE mx-14"></view> -->
							<text class="text-#00B496 text-26 font-500">{{ orderStatusPlus(order, item).desc }}</text>
						</view>
					</template>

					<!-- 待评价 -->
					<template v-if="orderStatusPlusInfo(order, item).status === 'UN_COMMENT'">
						<view class="flex justify-end items-center">
							<!-- <text class="text-#888888 text-22">{{ order.createTime }}下单</text>
							<view class="w-1 h-20 bg-#EEEEEE mx-14"></view> -->
							<text class="text-#FF950A text-26 font-500">{{ orderStatusPlus(order, item).desc }}</text>
						</view>
					</template>

					<!-- 已完成 -->
					<template v-if="orderStatusPlusInfo(order, item).status === 'COMPLETED'">
						<view class="flex justify-end items-center">
							<!-- <text class="text-#888888 text-22">{{ order.createTime }}下单</text>
							<view class="w-1 h-20 bg-#EEEEEE mx-14"></view> -->
							<text class="text-#888888 text-26 font-500">{{ orderStatusPlus(order, item).desc }}</text>
						</view>
					</template>

					<!-- 已关闭 -->
					<template v-if="orderStatusPlusInfo(order, item).status === 'CLOSED'">
						<view class="flex justify-end items-center">
							<!-- <text class="text-#888888 text-22">{{ order.createTime }}下单</text>
							<view class="w-1 h-20 bg-#EEEEEE mx-14"></view> -->
							<text class="text-#888888 text-26 font-500">{{ orderStatusPlus(order, item).desc }}</text>
						</view>
					</template>
				</template>
				<template v-else>
					<!-- 待付款 -->
					<template v-if="order.status === 'UNPAID'">
						<text class="text-#F94B4A text-26 font-500">待付款</text>
					</template>

					<!-- 待发货 -->
					<template v-if="order.status === 'PAID'">
						<view class="flex justify-end items-center">
							<!-- <text class="text-#888888 text-22">{{ order.createTime }}下单</text>
							<view class="w-1 h-20 bg-#EEEEEE mx-14"></view> -->
							<text class="text-#00B496 text-26 font-500">待发货</text>
						</view>
					</template>

					<!-- 待收货 -->
					<template v-if="order.status === 'ON_DELIVERY'">
						<view class="flex justify-end items-center">
							<!-- <text class="text-#888888 text-22">{{ order.createTime }}下单</text>
							<view class="w-1 h-20 bg-#EEEEEE mx-14"></view> -->
							<text class="text-#00B496 text-26 font-500">待收货</text>
						</view>
					</template>

					<!-- 已完成 -->
					<template v-if="order.status === 'ACCOMPLISH'">
						<view class="flex justify-end items-center">
							<!-- <text class="text-#888888 text-22">{{ order.createTime }}下单</text>
							<view class="w-1 h-20 bg-#EEEEEE mx-14"></view> -->
							<text class="text-#888888 text-26 font-500">已完成</text>
						</view>
					</template>

					<!-- 已关闭 -->
					<template v-if="order.status === 'SYSTEM_CLOSED'">
						<view class="flex justify-end items-center">
							<!-- <text class="text-#888888 text-22">{{ order.createTime }}下单</text>
							<view class="w-1 h-20 bg-#EEEEEE mx-14"></view> -->
							<text class="text-#888888 text-26 font-500">已关闭</text>
						</view>
					</template>
				</template>
			</view>
			<view class="w-full h-1 bg-#F1F3F8"></view>

			<template v-if="!isIntegral">
				<template v-for="(goods, index) in Array.from(orderMapComputed(item.shopOrderItems).values())" :key="index">
					<view class="flex mt-25" @click="goOrderDetails(order)">
						<image :src="goods.merged.image" class="w-164 h-164 min-w-164 border-solid border-1 border-#F5F5F5 border-rd-16" mode=""></image>
						<view class="flex-1 flex flex-col justify-between ml-20">
							<view class="">
								<view class="text-30 text-#222222 font-bold uv-line-1">{{ goods.merged.productName }}</view>
								<view class="text-26 text-#555555 font-500 mt-10 flex justify-between items-center">
									<text>{{ goods.merged.specs.join(" ") }}</text>
									<text>x{{ goods.merged.num }}</text>
								</view>
							</view>
							<view class="text-#222222 font-bold">
								<view class="flex items-end">
									<view class="text-30 pb-4">¥</view>
									<view class="text-36">
										{{ getPriceInfo(goods.merged.salePrice).integer }}
									</view>
									<view class="text-30 pb-4">.{{ getPriceInfo(goods.merged.salePrice).decimalText }}</view>
								</view>
							</view>
						</view>
					</view>
				</template>
				<template v-if="orderStatusPlusInfo(order, item).status === 'UNPAID'">
					<view class="text-26 flex justify-end items-center py-24">
						<template v-if="getOrderTime(order) > 0">
							<view class="flex items-center mr-20">
								<view class="text-#222222">剩余:</view>
								<view class="text-#FC3F33 font-bold flex items-end">
									<view class="text-30 pb-0">
										<my-uv-count-down
											:time="Number(getOrderTime(order))"
											format="HH:mm:ss"
											ref="payCountDown"
											:autoStart="true"
											:textStyle="{
												fontSize: '30rpx',
												color: '#F85D5D',
												fontWeight: 'bold',
											}"
											@finish="countDownFinish"></my-uv-count-down>
									</view>
								</view>
							</view>
						</template>

						<view class="flex items-center">
							<view class="text-#222222">待付款:</view>
							<view class="text-#FC3F33 font-bold flex items-end">
								<view class="text-30 pb-4">¥</view>
								<view class="text-36">
									{{ getPriceInfo(computeOrderPrice(item)).integer }}
								</view>
								<view class="text-30 pb-4">.{{ getPriceInfo(computeOrderPrice(item)).decimalText }}</view>
							</view>
						</view>
					</view>
				</template>
			</template>
			<template v-else>
				<view class="flex mt-25" @click="goIntegralcOrderDetails(order)">
					<image :src="order.image" class="w-164 h-164 min-w-164 border-solid border-1 border-#F5F5F5 border-rd-16" mode=""></image>
					<view class="flex-1 flex flex-col justify-between ml-20">
						<view class="">
							<view class="text-30 text-#222222 font-bold uv-line-1">{{ order.productName }}</view>
							<view class="text-26 text-#555555 font-500 mt-10 flex justify-between items-center">
								<text></text>
								<text>x{{ order.num }}</text>
							</view>
						</view>
						<view class="text-#222222 font-bold">
							<view class="flex items-center">
								<view class="flex items-end">
									<view class="text-36">
										{{ getPriceInfo(mulTenThousand(order.price)).integer }}
									</view>
									<view class="text-30 pb-4">.{{ getPriceInfo(mulTenThousand(order.price)).decimalText }}</view>
									<view class="text-30 pb-4">积分</view>
								</view>
								<template v-if="order.salePrice > 0">
									<view class="flex items-end">
										<view class="text-30 pb-4 mx-5">+</view>
										<view class="text-30 pb-4">¥</view>
										<view class="text-36">
											{{ getPriceInfo(order.salePrice).integer }}
										</view>
										<view class="text-30 pb-4">.{{ getPriceInfo(order.salePrice).decimalText }}</view>
									</view>
								</template>
							</view>
						</view>
					</view>
				</view>

				<template v-if="order.status === 'UNPAID'">
					<view class="text-26 flex justify-end items-center py-24">
						<template v-if="getOrderTime(order) > 0">
							<view class="flex items-center mr-20">
								<view class="text-#222222">剩余:</view>
								<view class="text-#FC3F33 font-bold flex items-end">
									<view class="text-30 pb-0">
										<my-uv-count-down
											:time="Number(getOrderTime(order))"
											format="HH:mm:ss"
											ref="payCountDown"
											:autoStart="true"
											:textStyle="{
												fontSize: '30rpx',
												color: '#F85D5D',
												fontWeight: 'bold',
											}"
											@finish="countDownFinish"></my-uv-count-down>
									</view>
								</view>
							</view>
						</template>

						<view class="flex items-center">
							<view class="text-#222222">待付款:</view>
							<view class="text-#FC3F33 font-bold flex items-end">
								<view class="text-30">
									{{ getPriceInfo(mulTenThousand(order.price)).integer }}
								</view>
								<view class="text-26 pb-4">.{{ getPriceInfo(mulTenThousand(order.price)).decimalText }}</view>
								<view class="text-26 pb-4">积分</view>
							</view>
							<template v-if="order.salePrice + order.freightPrice > 0">
								<view class="text-#FC3F33 font-bold flex items-end">
									<view class="text-26 pb-4 px-5">+</view>
									<view class="text-26 pb-4">¥</view>
									<view class="text-30">
										{{ getPriceInfo(order.salePrice + order.freightPrice).integer }}
									</view>
									<view class="text-26 pb-4">.{{ getPriceInfo(order.salePrice + order.freightPrice).decimalText }}</view>
								</view>
							</template>
						</view>
					</view>
				</template>
			</template>

			<view class="bg-#F6F6F6 p-26 border-rd-16" v-if="false">
				<view class="flex justify-between">
					<view class="text-28 text-#222 font-500 min-w-180">地址</view>
					<view class="text-28 text-#222 font-500 text-right flex-1">张三 15666668888 xxx</view>
					<image :src="common.iconCopyBai" class="w-20 h-20 min-w-20 mt-8 ml-10" mode=""></image>
				</view>
				<view class="flex justify-between mt-20">
					<view class="text-28 text-#222 font-500 min-w-180">订单编号</view>
					<view class="text-28 text-#888 font-500 text-right flex-1">xxxx</view>
					<image :src="common.iconCopyBai" class="w-20 h-20 min-w-20 mt-8 ml-10" mode=""></image>
				</view>
			</view>

			<template v-if="!isIntegral">
				<!-- 待付款 -->
				<template v-if="orderStatusPlusInfo(order, item).status === 'UNPAID'">
					<view class="w-full h-1 bg-#F1F3F8 mt-24 mb-24"></view>
					<view class="w-full flex justify-between items-center">
						<view class="text-#888888 text-28 font-500">
							<!-- 更多 -->
						</view>

						<view class="flex justify-end items-center">
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="cancelOrder(order)">
									<view class="btn-text text-#323232">取消订单</view>
								</uv-button>
							</view>

							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#00B496" plain @click="changeOrderAddress(order)">
									<view class="btn-text text-#00B496">更换地址</view>
								</uv-button>
							</view>

							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#66C5B5" @click="openPay(order)">
									<view class="btn-text text-#fff">立即支付</view>
								</uv-button>
							</view>
						</view>
					</view>
				</template>

				<!-- 待发货 -->
				<template v-if="orderStatusPlusInfo(order, item).status === 'UN_DELIVERY'">
					<view class="w-full h-1 bg-#F1F3F8 mt-24 mb-24"></view>
					<view class="w-full flex justify-between items-center">
						<view class="text-#888888 text-28 font-500">
							<!-- 更多 -->
						</view>

						<view class="flex justify-end items-center">
							<!-- <view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="cancelOrder(order)">
									<view class="btn-text text-#323232">取消订单</view>
								</uv-button>
							</view> -->
							<!-- <view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="applyAfterSale(order)">
									<view class="btn-text text-#323232">申请售后</view>
								</uv-button>
							</view> -->
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#00B496" plain @click="urge('shipment', order)">
									<view class="btn-text text-#00B496">催发货</view>
								</uv-button>
							</view>
						</view>
					</view>
				</template>

				<!-- 待收货 -->
				<template v-if="orderStatusPlusInfo(order, item).status === 'UN_RECEIVE'">
					<view class="w-full h-1 bg-#F1F3F8 mt-24 mb-24"></view>
					<view class="w-full flex justify-between items-center">
						<view class="text-#888888 text-28 font-500">
							<!-- 更多 -->
						</view>

						<view class="flex justify-end items-center">
							<!-- <view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="applyAfterSale(order)">
									<view class="btn-text text-#323232">申请售后</view>
								</uv-button>
							</view> -->
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#00B496" plain @click="viewLogistics(order)">
									<view class="btn-text text-#00B496">查看物流</view>
								</uv-button>
							</view>
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#66C5B5" @click="confirmReceive(order)">
									<view class="btn-text text-#fff">确认收货</view>
								</uv-button>
							</view>
						</view>
					</view>
				</template>

				<!-- 待评价 -->
				<template v-if="orderStatusPlusInfo(order, item).status === 'UN_COMMENT'">
					<view class="w-full h-1 bg-#F1F3F8 mt-24 mb-24"></view>
					<view class="w-full flex justify-between items-center">
						<view class="text-#888888 text-28 font-500">
							<!-- 更多 -->
						</view>

						<view class="flex justify-end items-center">
							<!-- <view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="delOrder(order)">
									<view class="btn-text text-#323232">删除订单</view>
								</uv-button>
							</view> -->
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="viewLogistics(order)">
									<view class="btn-text text-#323232">查看物流</view>
								</uv-button>
							</view>
							<!-- <view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#FF950A" @click="goGoodsDetails(item.shopOrderItems[0])" plain>
									<view class="btn-text text-#FF950A">去评价</view>
								</uv-button>
							</view> -->
						</view>
					</view>
				</template>

				<!-- 已完成 -->
				<template v-if="orderStatusPlusInfo(order, item).status === 'COMPLETED'">
					<view class="w-full h-1 bg-#F1F3F8 mt-24 mb-24"></view>
					<view class="w-full flex justify-between items-center">
						<view class="text-#888888 text-28 font-500">
							<!-- 更多 -->
						</view>

						<view class="flex justify-end items-center">
							<!-- <view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="delOrder(order)">
									<view class="btn-text text-#323232">删除订单</view>
								</uv-button>
							</view> -->
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="viewLogistics(order)">
									<view class="btn-text text-#323232">查看物流</view>
								</uv-button>
							</view>
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" @click="goGoodsDetails(item.shopOrderItems[0])" plain>
									<view class="btn-text text-#323232">再来一单</view>
								</uv-button>
							</view>
						</view>
					</view>
				</template>

				<!-- 已关闭 -->
				<template v-if="orderStatusPlusInfo(order, item).status === 'CLOSED'">
					<view class="w-full h-1 bg-#F1F3F8 mt-24 mb-24"></view>
					<view class="w-full flex justify-between items-center">
						<view class="text-#888888 text-28 font-500">
							<!-- 更多 -->
						</view>

						<view class="flex justify-end items-center">
							<!-- <view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="delOrder(order)">
									<view class="btn-text text-#323232">删除订单</view>
								</uv-button>
							</view> -->
							<!-- <view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="viewLogistics(order)">
									<view class="btn-text text-#323232">查看物流</view>
								</uv-button>
							</view> -->
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" @click="goGoodsDetails(item.shopOrderItems[0])" plain>
									<view class="btn-text text-#323232">再来一单</view>
								</uv-button>
							</view>
						</view>
					</view>
				</template>
			</template>

			<template v-else>
				<!-- 待付款 -->
				<template v-if="order.status === 'UNPAID'">
					<view class="w-full h-1 bg-#F1F3F8 mt-24 mb-24"></view>
					<view class="w-full flex justify-between items-center">
						<view class="text-#888888 text-28 font-500">
							<!-- 更多 -->
						</view>

						<view class="flex justify-end items-center">
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="cancelOrderIntegral(order)">
									<view class="btn-text text-#323232">取消订单</view>
								</uv-button>
							</view>

							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#00B496" plain @click="changeOrderAddressIntegral(order)">
									<view class="btn-text text-#00B496">更换地址</view>
								</uv-button>
							</view>

							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#66C5B5" @click="openPay(order)">
									<view class="btn-text text-#fff">立即支付</view>
								</uv-button>
							</view>
						</view>
					</view>
				</template>

				<!-- 待发货 -->
				<template v-if="order.status === 'PAID'">
					<view class="w-full h-1 bg-#F1F3F8 mt-24 mb-24"></view>
					<view class="w-full flex justify-between items-center">
						<view class="text-#888888 text-28 font-500">
							<!-- 更多 -->
						</view>

						<view class="flex justify-end items-center">
							<!-- <view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="cancelOrder(order)">
									<view class="btn-text text-#323232">取消订单</view>
								</uv-button>
							</view> -->
							<!-- <view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="applyAfterSale(order)">
									<view class="btn-text text-#323232">申请售后</view>
								</uv-button>
							</view> -->
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#00B496" plain @click="urge('shipment', order)">
									<view class="btn-text text-#00B496">催发货</view>
								</uv-button>
							</view>
						</view>
					</view>
				</template>

				<!-- 待收货 -->
				<template v-if="order.status === 'ON_DELIVERY'">
					<view class="w-full h-1 bg-#F1F3F8 mt-24 mb-24"></view>
					<view class="w-full flex justify-between items-center">
						<view class="text-#888888 text-28 font-500">
							<!-- 更多 -->
						</view>

						<view class="flex justify-end items-center">
							<!-- <view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="applyAfterSale(order)">
									<view class="btn-text text-#323232">申请售后</view>
								</uv-button>
							</view> -->
							<template v-if="order.expressNo">
								<view class="btn-item-box">
									<uv-button :custom-style="$data.btnItemStyle" color="#00B496" plain @click="viewLogisticsSingle(order)">
										<view class="btn-text text-#00B496">查看物流</view>
									</uv-button>
								</view>
							</template>
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#66C5B5" @click="confirmReceiveIntegral(order)">
									<view class="btn-text text-#fff">确认收货</view>
								</uv-button>
							</view>
						</view>
					</view>
				</template>

				<!-- 已完成 -->
				<template v-if="order.status === 'ACCOMPLISH'">
					<view class="w-full h-1 bg-#F1F3F8 mt-24 mb-24"></view>
					<view class="w-full flex justify-between items-center">
						<view class="text-#888888 text-28 font-500">
							<!-- 更多 -->
						</view>

						<view class="flex justify-end items-center">
							<!-- <view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="delOrder(order)">
									<view class="btn-text text-#323232">删除订单</view>
								</uv-button>
							</view> -->
							<template v-if="order.expressNo">
								<view class="btn-item-box">
									<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="viewLogisticsSingle(order)">
										<view class="btn-text text-#323232">查看物流</view>
									</uv-button>
								</view>
							</template>
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" @click="goGoodsDetailsIntegralc(order)" plain>
									<view class="btn-text text-#323232">再来一单</view>
								</uv-button>
							</view>
						</view>
					</view>
				</template>

				<!-- 已关闭 -->
				<template v-if="order.status === 'SYSTEM_CLOSED'">
					<view class="w-full h-1 bg-#F1F3F8 mt-24 mb-24"></view>
					<view class="w-full flex justify-between items-center">
						<view class="text-#888888 text-28 font-500">
							<!-- 更多 -->
						</view>

						<view class="flex justify-end items-center">
							<!-- <view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="delOrder(order)">
									<view class="btn-text text-#323232">删除订单</view>
								</uv-button>
							</view> -->
							<!-- <view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" plain @click="viewLogistics(order)">
									<view class="btn-text text-#323232">查看物流</view>
								</uv-button>
							</view> -->
							<view class="btn-item-box">
								<uv-button :custom-style="$data.btnItemStyle" color="#D5D5D5" @click="goIntegralcOrderDetails(order)" plain>
									<view class="btn-text text-#323232">再来一单</view>
								</uv-button>
							</view>
						</view>
					</view>
				</template>
			</template>
		</view>
	</view>
</template>
<style lang="scss" scoped>
.btn-item-box {
	height: 64rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 0rpx 0 14rpx;
}
.btn-text {
	font-size: 26rpx;
	font-weight: 500;
}
.btn-item {
	height: 64rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 26rpx;
	margin: 0 14rpx 0 0;
	border-radius: 40rpx;
	border-style: solid;
	border-width: 1rpx;
	font-size: 26rpx;
	font-weight: 500;
}
</style>
