<script setup>
import { ref, reactive, computed, nextTick, watch } from "vue";

import { onLoad, onReady, onShow, onHide } from "@dcloudio/uni-app";

import { usePublicStore, useAppStore } from "@/store";

import swiperItemList from "./components/swiper-item-list/swiper-item-list.vue";

const publicStore = usePublicStore();

const $data = reactive({
	typeList: [
		{
			name: "积分订单",
			type: "integral",
			id: "0",
			value: 0,
		},
		{
			name: "护理服务",
			type: "nurse",
			id: "1904049800822345728",
			value: 3,
		},
		{
			name: "陪诊服务",
			type: "attend",
			id: "1904111595356377088",
			value: 2,
		},
		{
			name: "家政服务",
			type: "homemaking",
			id: "1904111619062583296",
			value: 1,
		},
		{
			name: "商品订单",
			type: "shop",
			id: "0",
			value: 0,
		},
	],
	typeCurrent: 0,

	stateList: [
		{
			goodsStateName: "全部",
			serveStateName: "全部",
			goodsStateValue: "",
			serveStateValue: "",
			goodsStateShow: true,
			serveStateShow: true,

			integralStateName: "全部",
			integralStateValue: "",
			integralStateShow: true,
		},
		{
			goodsStateName: "待付款",
			serveStateName: "待付款",
			goodsStateValue: "UNPAID",
			serveStateValue: "UNPAID",
			goodsStateShow: true,
			serveStateShow: true,

			integralStateName: "待付款",
			integralStateValue: "UNPAID",
			integralStateShow: true,
		},
		{
			goodsStateName: "待发货",
			serveStateName: "待确定",
			goodsStateValue: "UN_DELIVERY",
			serveStateValue: "SERVER_UN_DELIVERY",
			goodsStateShow: true,
			serveStateShow: true,

			integralStateName: "待发货",
			integralStateValue: "PAID",
			integralStateShow: true,
		},
		{
			goodsStateName: "待收货",
			serveStateName: "待服务",
			goodsStateValue: "UN_RECEIVE",
			serveStateValue: "TOBE_SERVED",
			goodsStateShow: true,
			serveStateShow: true,

			integralStateName: "待收货",
			integralStateValue: "ON_DELIVERY",
			integralStateShow: true,
		},
		{
			goodsStateName: "",
			serveStateName: "服务中",
			goodsStateValue: "",
			serveStateValue: "SERVER_GO",
			goodsStateShow: false,
			serveStateShow: true,

			integralStateName: "",
			integralStateValue: "",
			integralStateShow: false,
		},
		{
			goodsStateName: "待评价",
			serveStateName: "待评价",
			goodsStateValue: "UN_COMMENT",
			serveStateValue: "SERVER_MSG",
			goodsStateShow: true,
			serveStateShow: true,

			integralStateName: "",
			integralStateValue: "",
			integralStateShow: false,
		},
		{
			goodsStateName: "已完成",
			serveStateName: "已完成",
			goodsStateValue: "COMPLETED",
			serveStateValue: "COMPLETED",
			goodsStateShow: true,
			serveStateShow: true,

			integralStateName: "已完成",
			integralStateValue: "ACCOMPLISH",
			integralStateShow: true,
		},
		{
			goodsStateName: "已关闭",
			serveStateName: "已关闭",
			goodsStateValue: "CLOSED",
			serveStateValue: "CLOSED",
			goodsStateShow: true,
			serveStateShow: true,

			integralStateName: "已关闭",
			integralStateValue: "",
			integralStateShow: false,
		},
		{
			goodsStateName: "售后",
			serveStateName: "售后",
			goodsStateValue: "AFTER_SALE",
			serveStateValue: "SERVER_BAD",
			goodsStateShow: true,
			serveStateShow: true,

			integralStateName: "",
			integralStateValue: "",
			integralStateShow: false,
		},
	],
	stateCurrent: 0,

	swiperList: [],
});

const isIntegral = ref(true);

$data.typeList.map((item, index) => {
	$data.typeList[index].value = Number(useAppStore().goodsServerTypeList[item.type].id || 0);
	$data.typeList[index].id = useAppStore().goodsServerTypeList[item.type].trueId;
});

const goodsOrder = computed(() => {
	return $data.typeList[$data.typeCurrent].id === "0";
});

const pagingRef = ref(null);
const swiperItems = ref([]);
const swiperHeight = ref(0);

const zTabsRef = ref();

const listComp = ref([]);

const {
	stop: watchListCompStop,
	pause: watchListCompPause,
	resume: watchListCompResume,
} = watch(
	listComp,
	(nv, ov) => {
		if (nv.length > 0) {
			nextTick(() => {
				loadCompShowHide($data.stateCurrent, 1, false, true);
			});
		}
	},
	{ deep: true, immediate: true }
);

function changeSwiperList() {
	$data.stateCurrent = 0;
	$data.swiperList = [];

	nextTick(() => {
		let list = [];
		$data.stateList.map((i) => {
			if (goodsOrder.value) {
				if (!isIntegral.value && i.goodsStateShow) {
					list.push(i);
				}

				if (isIntegral.value && i.integralStateShow) {
					list.push(i);
				}
			}
			if (!goodsOrder.value && i.serveStateShow) {
				list.push(i);
			}
		});

		$data.swiperList = list;

		nextTick(() => {
			// loadCompShowHide($data.stateCurrent, 1, false, true);
		});
	});
}

function changeStateCurrent(e) {
	if ($data.stateCurrent === e) return;

	loadCompShowHide($data.stateCurrent, -1);

	$data.stateCurrent = e;

	loadCompShowHide($data.stateCurrent, 1);
}

function swiperAnimationfinish(e) {
	changeStateCurrent(e.detail.current);

	if (zTabsRef.value) zTabsRef.value.unlockDx();
}

function swiperTransition(e) {
	if (zTabsRef.value) zTabsRef.value.setDx(e.detail.dx);
}

function tabsChange(e) {
	if ($data.stateCurrent === e) return;
	changeStateCurrent(e);
}

function typeChange(e) {
	if ($data.typeCurrent === e) return;
	$data.typeCurrent = e;

	changeSwiperList();
}

function loadData() {
	changeSwiperList(true);
}

function loadCompShowHide(index = -1, status = 0, pageFun = false, reset = false) {
	// console.log(index, status, pageFun, reset, listComp.value.length);
	if (index >= 0) {
		if (Array.isArray(listComp.value)) {
			for (let key in listComp.value) {
				if (listComp.value.hasOwnProperty(key)) {
					const item = listComp.value[key];
					if (item.compIndex === index) {
						if (status === 1) {
							if (typeof item.onShow === "function") {
								item.onShow(reset, pageFun);
							}
						}
						if (status === -1) {
							if (typeof item.onHide === "function") {
								item.onHide(reset, pageFun);
							}
						}
					}
				}
			}
		}
	}
}

onLoad(() => {
	loadData();
});

onReady(() => {
	// nextTick(() => {
	// 	loadData();
	// });
});

onShow(() => {
	loadCompShowHide($data.stateCurrent, 1, true);
});

onHide(() => {
	loadCompShowHide($data.stateCurrent, -1, true);
});
</script>
<template>
	<app-layout>
		<z-paging-swiper ref="pagingRef">
			<template #top>
				<view class="">
					<app-navBar back>
						<template #content>
							<view class="w-full h-full flex justify-between items-center px-36">
								<text class="text-38 text-#000 font-bold">订单列表</text>
								<!-- <view class="flex justify-start items-center w-208 h-66 border-rd-33 bg-#F6F6F6 px-6">
									<app-image class="w-26 h-26 ml-20" :src="common.iconSousuoHui" mode=""></app-image>
									<text class="text-#999999 text-28 font-500 ml-10">搜索</text>
								</view> -->
							</view>
						</template>
					</app-navBar>

					<template v-if="!isIntegral">
						<view class="px-20">
							<!-- <my-uv-subsection
								:list="$data.typeList"
								:current="$data.typeCurrent"
								@change="typeChange"
								bgColor="#72BFB9"
								fontSize="26rpx"
								inactiveColor="#FFFFFF"
								activeColor="#222222"
								:custom-style="{
									borderRadius: '32rpx',
									height: '64rpx'
								}"
								:customItemStyle="{
									width: '171rpx',
									height: '56rpx',
									background: '#FFFFFF',
									borderRadius: '28rpx'
								}"
							></my-uv-subsection> -->
							<my-uv-subsection
								:list="$data.typeList"
								:current="$data.typeCurrent"
								@change="typeChange"
								bgColor="#72BFB9"
								fontSize="26rpx"
								inactiveColor="#FFFFFF"
								activeColor="#222222"
								:custom-style="{
									borderRadius: '32rpx',
									height: '64rpx',
								}"
								:customItemStyle="{
									background: '#FFFFFF',
									borderRadius: '28rpx',
								}"></my-uv-subsection>
						</view>
					</template>

					<view class="px-6">
						<z-tabs
							:list="$data.swiperList"
							:current="$data.stateCurrent"
							:keyName="!goodsOrder ? 'serveStateName' : !isIntegral ? 'goodsStateName' : 'integralStateName'"
							:nameKey="!goodsOrder ? 'serveStateName' : !isIntegral ? 'goodsStateName' : 'integralStateName'"
							@change="tabsChange"
							lineColor="#198B6B"
							activeColor="#198B6B"
							lineWidth="50rpx"
							barWidth="50rpx"
							:activeStyle="{
								color: '#222222',
								fontSize: '30rpx',
								fontWeight: 'bold',
							}"
							:inactiveStyle="{
								color: '#767676',
								fontSize: '28rpx',
								fontWeight: '500',
							}"
							ref="zTabsRef"></z-tabs>
					</view>
				</view>
			</template>

			<view class="bg-#F0F3F7 w-full h-full">
				<!-- 	<view class="flex justify-start items-center h-70">
					<view class="flex justify-start items-center ml-30">
						<text class="text-26 text-#00B496 font-500">近三个月订单</text>
						<image src="/static/common/down.png" class="w-20 h-20" mode=""></image>
					</view>
					<view class="flex justify-start items-center ml-30">
						<text class="text-26 text-#888888 font-500">备注标记</text>
						<image src="/static/common/down.png" class="w-20 h-20" mode=""></image>
					</view>
				</view> -->

				<!-- <view class="w-full h-25"></view> -->
				<swiper class="swiper" :current="$data.stateCurrent" @transition="swiperTransition" @animationfinish="swiperAnimationfinish" :duration="300">
					<template v-for="(item, index) in $data.swiperList" :key="index">
						<swiper-item class="swiper-item">
							<swiperItemList :index="index" :item="item" :tabIndex="$data.stateCurrent" :tabList="$data.swiperList" :typeIndex="$data.typeCurrent" :typeList="$data.typeList" ref="listComp"></swiperItemList>
						</swiper-item>
					</template>
				</swiper>
			</view>
		</z-paging-swiper>
	</app-layout>
</template>
<style lang="scss" scoped>
.swiper {
	height: 100%;
}
.swiper-item {
	width: 100%;
	background: #f0f3f7;
}
</style>
