<script setup>
import { ref, reactive, computed } from 'vue';

import { onLoad, onReady } from '@dcloudio/uni-app';

import { getAfssInfo, setReturnedByAfsNo } from '@/server/api';

import { REGEX_MOBILE } from '@/common/test';

import expressList from '@/pages/order/common/data/expressList.data';

const props = defineProps({
	afsNo: {
		type: String,
		required: true
	}
});

const pageData = reactive({
	expressList: [],
	expressIndexs: [[]],
	expressValues: [[]]
});

const pageForm = ref({
	clerk: '',
	mobile: '',
	remark: ''
});

const pageFormRules = {
	clerk: [
		{
			required: true,
			message: '请输入处理人姓名',
			trigger: 'blur'
		}
	],
	mobile: [
		{
			required: true,
			message: '请输入手机号',
			trigger: 'blur'
		},
		{
			validator: (rule, value, callback) => {
				if (!REGEX_MOBILE(value)) {
					callback(new Error('请输入正确的手机号'));
				} else {
					callback();
				}
			},
			trigger: 'blur'
		}
	],
	remark: [{ max: 100, message: '退货说明不能超过100字', trigger: 'blur' }]
};

const pageFormRef = ref();

const afsOrderInfo = ref();

onLoad((options) => {
	initData();
});

function initData() {
	uni.showLoading({
		title: '加载中...',
		mask: true
	});

	pageData.expressList = [expressList];

	getAfssInfo({ afsNo: props.afsNo })
		.then((res) => {
			if (res.apiStatus) {
				afsOrderInfo.value = res.data;
				uni.hideLoading();
			} else {
				uni.hideLoading();
			}
		})
		.catch(() => {
			uni.hideLoading();
		});
}

async function submit() {
	pageFormRef.value.validate().then(async (res) => {
		const { clerk, mobile, remark } = pageForm.value;
		const params = { deliverType: 'WITHOUT', goStoreRefund: { explain: remark, mobile, shopAssistantName: clerk } };
		const { code, data, msg, apiStatus } = await setReturnedByAfsNo({ afsNo: props.afsNo, type: 'GO_STORE_REFUND', expressRefund: params });
		if (!apiStatus) return;
		uni.showToast({
			title: `提交成功`,
			icon: 'none',
			duration: 1000,
			success: () => {
				uni.navigateBack();
			}
		});
	});
}

onReady(() => {
	if (pageFormRef.value) {
		pageFormRef.value.setRules(pageFormRules);
	}
});

const formStyle = ref({
	labelStyle: {
		fontSize: '30rpx',
		fontWeight: 'bold'
	},
	inputStyle: {
		height: '98rpx',
		padding: '0rpx 0rpx',
		borderBottom: `solid 1rpx #E9EDF1`
	},
	labelStyle2: {
		fontSize: '26rpx',
		fontWeight: 'bold',
		color: '#1152D7'
	},
	inputStyle2: {
		height: '88rpx',
		borderRadius: '10rpx',
		background: '#fff',
		padding: '0rpx 30rpx'
	},
	labelStyle3: {
		fontSize: '24rpx',
		fontWeight: '500',
		color: '#323232'
	},
	inputStyle3: {
		borderRadius: '16rpx',
		background: '#F2F6FB',
		padding: '25rpx 30rpx'
	},
	inputStyle4: {
		borderRadius: '16rpx',
		background: '#fff',
		padding: '25rpx 30rpx'
	},
	inputStyle5: {
		height: '105rpx',
		borderRadius: '16rpx',
		background: '#F2F6FB',
		padding: '0rpx 30rpx'
	},
	labelStyle6: {
		fontSize: '28rpx',
		fontWeight: '500',
		color: '#1A1A1B'
	},
	inputStyle6: {
		fontWeight: 'bold',
		fontSize: '28rpx'
	},
	labelStyle7: {
		fontSize: '28rpx',
		fontWeight: '500',
		color: '#1A1A1B'
	},
	inputStyle7: {
		fontWeight: '500',
		fontSize: '30rpx',
		padding: 0
	},
	inputStyle8: {
		// borderRadius: '16rpx',
		background: '#F3F4F7',
		padding: '25rpx 30rpx',
		borderRadius: '53rpx'
	},
	inputPlaceholderStyle: `font-size: 30rpx; font-weight: 500; color: #AEAEB4;`,
	inputPlaceholderStyle3: `font-size: 26rpx; font-weight: 500; color: #B1B6BC;`,
	inputPlaceholderStyle4: `font-size: 30rpx; font-weight: 500; color: #B1B6BC;`,
	inputPlaceholderStyle6: `font-size: 28rpx; font-weight: 500; color: #939398;`,
	inputPlaceholderStyle7: `font-size: 30rpx; font-weight: 500; color: #93939C;`
});

const labelStyle = {
	fontSize: '26rpx',
	fontWeight: 'bold'
};
</script>

<template>
	<app-layout>
		<z-paging>
			<view class="px-50 pt-50">
				<view class="w-full">
					<uv-form labelPosition="top" labelWidth="auto" :labelStyle="formStyle.labelStyle3" :model="pageForm" :rules="pageFormRules" ref="pageFormRef" errorType="toast">
						<uv-form-item label="处理人" prop="clerk">
							<uv-input
								v-model="pageForm.clerk"
								placeholder="请输入处理人姓名"
								class="mt-10 text-32"
								:placeholderStyle="formStyle.inputPlaceholderStyle"
								:customStyle="formStyle.inputStyle8"
								fontSize="32rpx"
								border="none"
								clearable
							></uv-input>
						</uv-form-item>
						<uv-form-item label="手机号码" prop="mobile">
							<uv-input
								v-model="pageForm.mobile"
								placeholder="请输入手机号"
								class="mt-10 text-32"
								:placeholderStyle="formStyle.inputPlaceholderStyle"
								:customStyle="formStyle.inputStyle8"
								fontSize="32rpx"
								border="none"
								clearable
							></uv-input>
						</uv-form-item>
						<uv-form-item label="退货说明" prop="remark">
							<uv-input
								v-model="pageForm.remark"
								placeholder="请输入退货说明"
								class="mt-10 text-32"
								:placeholderStyle="formStyle.inputPlaceholderStyle"
								:customStyle="formStyle.inputStyle8"
								fontSize="32rpx"
								border="none"
								clearable
							></uv-input>
						</uv-form-item>
					</uv-form>
				</view>
			</view>
			<template #bottom>
				<view class="w-full px-30 pt-15 bg-#fff fixed left-0 bottom-0 border-t-1 border-t-solid border-t-#EDEEF0 flex items-center">
					<view class="flex-1">
						<uv-button
							@click="submit()"
							color="#00B496"
							text="提交"
							class="flex-1 h-88"
							custom-style="border-radius: 44rpx;"
							customTextStyle="font-size: 30rpx; color: #fff; font-weight: bold;"
						></uv-button>
						<app-safeAreaBottom></app-safeAreaBottom>
					</view>
				</view>
			</template>
		</z-paging>
	</app-layout>
</template>

<style lang="scss" scoped></style>
