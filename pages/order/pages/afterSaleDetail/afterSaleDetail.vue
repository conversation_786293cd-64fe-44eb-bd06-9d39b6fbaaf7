<script setup>
import { ref, reactive, computed, watch } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";

import { Decimal } from "decimal.js";

import dayjs from "dayjs";

import { useOrderStore } from "@/store";

import { canENV, getPriceInfo, route, goShopHome } from "@/common/utils";

import { getAfsStatusCn } from "@/pages/order/common/types/order";

import useConvert from "@/common/useConvert";

import { getAfssInfo, getLogisticsTrajectoryByWaybillNo, closeAfssCancel, getOrderInfo, getAfssHistory } from "@/server/api";
import { common } from "@/common/images";

const { divTenThousand } = useConvert();

const _props = defineProps({
	afsNo: {
		type: String,
		required: true,
	},
	packageId: {
		type: String,
		default: "",
	},
});

const _data = reactive({
	btnItemStyle: {
		height: "64rpx",
		borderRadius: "40rpx",
		fontSize: "26rpx",
	},

	logisticsList: [],
	lastLogistics: null,

	consultationHistoryList: [],
	lastConsultationHistory: null,
});

const pagingRef = ref(null);

const afsOrderInfo = ref();

const goods = ref();

const orderAfsStatus = computed(() => {
	if (!afsOrderInfo.value) return;
	return getAfsStatusCn(afsOrderInfo.value.status);
});

const tatolPirce = computed(() => {
	if (!afsOrderInfo.value) return 0;
	const { dealPrice, num } = afsOrderInfo.value.afsOrderItem;

	return divTenThousand(dealPrice).mul(num).toString();
});

async function loadData() {
	await initAfssInfo();
}

async function reloadData() {
	await initAfssInfo();
}

function onCopy(text) {
	uni.setClipboardData({
		data: text,
		showToast: true,
	});
}

function isImage(str) {
	return /.(gif|jpg|jpeg|png|gif|jpg|png|webp|avif|jfif)$/i.test(str);
}

function previewImage(url) {
	uni.previewImage({
		urls: [url],
		count: url,
	});
}

function getTime(timeoutTime = 0, startTime = "") {
	const timeout = timeoutTime * 1000 - (dayjs().valueOf() - dayjs(startTime).valueOf());

	return timeout;
}

function viewLogistics() {
	route("/pages/order/pages/logisticsAfterSale/logisticsAfterSale", {
		afsNo: _props.afsNo,
	});
}

function logisticsTrajectory(expressCompanyCode, expressNo, shopId) {
	getLogisticsTrajectoryByWaybillNo({
		companyCode: expressCompanyCode,
		waybillNo: expressNo,
		recipientsPhone: "",
		shopId: shopId,
	})
		.then((res) => {
			if (res.code == 200) {
				_data.logisticsList = res.data.data
					.map((elem) => {
						return {
							title: elem.status,
							desc: elem.context,
							time: elem.time,
							...elem,
						};
					})
					.reverse();

				if (_data.logisticsList.length > 0) {
					_data.lastLogistics = _data.logisticsList[0];
				}
			}
		})
		.catch(() => {
			uni.hideLoading();
		});
}

async function initAfssInfo() {
	if (!_props.afsNo) return;

	try {
		const res = await getAfssInfo({
			afsNo: _props.afsNo,
		});

		if (res.apiStatus) {
			if (res.data?.afsPackage?.buyerReturnedInfo?.expressRefund?.expressCompany?.expressCompanyCode) {
				const { expressCompanyCode, expressNo, shopId } = res.data?.afsPackage?.buyerReturnedInfo?.expressRefund?.expressCompany;
				logisticsTrajectory(expressCompanyCode, expressNo, shopId);
			}

			afsOrderInfo.value = res.data;

			if (orderAfsStatus.value && orderAfsStatus.value.isConsumer) {
				getConsultationHistory();
			}

			if (afsOrderInfo.value.afsOrderItem) {
				goods.value = afsOrderInfo.value.afsOrderItem;
			}

			canENV(() => {
				console.log(afsOrderInfo.value);
			});
		}
	} catch (error) {
		//TODO handle the exception
	}
}

function getUserAddressInfo() {
	let info = ``;
	const packageInfo = afsOrderInfo.value.afsPackage;

	if (packageInfo) {
		info = `收件人：${packageInfo.receiverName} \n收件人手机号：${packageInfo.receiverMobile} \n详细地址：${packageInfo.receiverAddress}`;
	}

	return info;
}

function cancelAfssCancel() {
	uni.showModal({
		title: "请确认",
		content: "是否撤销申请？",
		success: async ({ confirm }) => {
			if (!confirm) return;
			if (!afsOrderInfo.value) return;
			const { code, msg, apiStatus } = await closeAfssCancel({ afsNo: afsOrderInfo.value?.no });
			if (!apiStatus) return;
			uni.showToast({ title: "撤销成功", icon: "none" });
			reloadData();
		},
	});
}

function applyAfterSales() {
	if (!afsOrderInfo.value) return;
	route({
		url: "/pages/order/pages/applyAfterSales/applyAfterSales",
		type: "redirectTo",
		params: {
			orderNo: afsOrderInfo.value?.orderNo,
			itemId: afsOrderInfo.value?.shopOrderItemId,
		},
	});
}

function addTrackingNumber() {
	route("/pages/order/pages/trackingNumber/trackingNumber", {
		afsNo: _props.afsNo,
	});
}

function addStoreReturn() {
	route("/pages/order/pages/storeReturn/storeReturn", {
		afsNo: _props.afsNo,
	});
}

function getConsultationHistory() {
	getAfssHistory({
		afsNo: _props.afsNo,
	}).then((res) => {
		if (res.apiStatus && Array.isArray(res.data) && res.data.length > 0) {
			if (Array.isArray(res.data[0].histories) && res.data[0].histories.length > 0) {
				_data.consultationHistoryList = res.data[0].histories;
				_data.lastConsultationHistory = _data.consultationHistoryList[0];
			}
		}
	});
}

// 商品详情
function goGoodsDetails(goods) {
	route("/pages/goods/pages/productDetails/productDetails", {
		productId: goods.productId,
		shopId: goods.shopId,
	});
}

onLoad((options) => {
	// loadData();
});

onShow(() => {
	loadData();
});

async function onRefresh() {
	await reloadData();
	if (pagingRef.value) pagingRef.value.complete();
}
</script>

<template>
	<view class="page-main bg-#F4F5F8">
		<app-layout>
			<!-- <app-image src="@/pages/order/static/bg_order_details_header_bg.png" mode="widthFix"></app-image> -->
			<z-paging
				ref="pagingRef"
				:paging-style="{
					backgroundRepeat: 'no-repeat',
					backgroundSize: '100% 450rpx',
				}"
				refresher-theme-style="black"
				refresher-only
				@onRefresh="onRefresh">
				<template #top>
					<app-navBar back :showRight="false">
						<template #content>
							<view class="w-full flex justify-between items-center h-full">
								<view class="flex justify-start items-center"></view>
								<view class="flex items-center">
									<!-- <app-image src="@/pages/order/static/icon_kefu_bai.png" class="mr-24" size="40" mode=""></app-image> -->
									<app-image src="@/pages/order/static/icon_caidan_hui.png" class="mr-24" size="36" mode=""></app-image>
								</view>
							</view>
						</template>
					</app-navBar>
				</template>

				<view class="px-20">
					<template v-if="afsOrderInfo">
						<!-- 暂未申请售后 -->
						<template v-if="afsOrderInfo.status === 'NONE'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-36 text-#222 font-bold" :class="[orderAfsStatus.typeStatus === -1 && 'text-FC3F33', orderAfsStatus.typeStatus === 1 && 'text-00B496']">
										<text>{{ orderAfsStatus.title }}</text>
									</view>
									<!-- <view class="text-28 text-#222 font-bold flex">
										<my-uv-count-down
											:time="Number(getTime(afsOrderInfo.keyNodeTimeout.confirmReturnedTimeout, afsOrderInfo.afsPackage.createTime))"
											format="DD天HH时mm分ss秒"
											ref="payCountDown"
											:autoStart="true"
											:textStyle="{
												fontSize: '28rpx',
												color: '#222',
												fontWeight: 'bold'
											}"
											@finish="reloadData()"
										></my-uv-count-down>
										<view class="">后自动收货</view>
									</view> -->
								</view>
								<view class="text-28 text-#888888 font-500 mt-10">
									<text>{{ orderAfsStatus.desc }}</text>
								</view>
							</view>
						</template>

						<!-- 等待商家处理 - 退款 -->
						<template v-if="afsOrderInfo.status === 'REFUND_REQUEST'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-36 text-#222 font-bold" :class="[orderAfsStatus.typeStatus === -1 && 'text-FC3F33', orderAfsStatus.typeStatus === 1 && 'text-00B496']">
										<text>{{ orderAfsStatus.title }}</text>
									</view>
									<view class="text-28 text-#222 font-bold flex">
										<my-uv-count-down
											:time="Number(getTime(afsOrderInfo.keyNodeTimeout.requestAgreeTimeout, afsOrderInfo.createTime))"
											format="DD天HH时mm分ss秒"
											ref="payCountDown"
											:autoStart="true"
											:textStyle="{
												fontSize: '28rpx',
												color: '#222',
												fontWeight: 'bold',
											}"
											@finish="reloadData()"></my-uv-count-down>
									</view>
								</view>
								<view class="text-28 text-#888888 font-500 mt-10">
									<text>{{ orderAfsStatus.desc }}</text>
								</view>
							</view>
						</template>

						<!-- 退款中 - 商家同意 -->
						<template v-if="afsOrderInfo.status === 'REFUND_AGREE'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-36 text-#222 font-bold" :class="[orderAfsStatus.typeStatus === -1 && 'text-FC3F33', orderAfsStatus.typeStatus === 1 && 'text-00B496']">
										<text>{{ orderAfsStatus.title }}</text>
									</view>
								</view>
								<view class="text-28 text-#888888 font-500 mt-10">
									<text>{{ orderAfsStatus.desc }}</text>
								</view>
							</view>
						</template>

						<!-- 退款中 - 系统同意 -->
						<template v-if="afsOrderInfo.status === 'SYSTEM_REFUND_AGREE'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-36 text-#222 font-bold" :class="[orderAfsStatus.typeStatus === -1 && 'text-FC3F33', orderAfsStatus.typeStatus === 1 && 'text-00B496']">
										<text>{{ orderAfsStatus.title }}</text>
									</view>
								</view>
								<view class="text-28 text-#888888 font-500 mt-10">
									<text>{{ orderAfsStatus.desc }}</text>
								</view>
							</view>
						</template>

						<!-- 退款关闭 - 商家拒绝 -->
						<template v-if="afsOrderInfo.status === 'REFUND_REJECT'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-36 text-#222 font-bold" :class="[orderAfsStatus.typeStatus === -1 && 'text-FC3F33', orderAfsStatus.typeStatus === 1 && 'text-00B496']">
										<text>{{ orderAfsStatus.title }}</text>
									</view>
								</view>
								<view class="text-28 text-#888888 font-500 mt-10">
									<text>{{ afsOrderInfo.createTime }}</text>
								</view>
								<view class="text-30 text-#222 font-500 mt-30">
									<text>拒绝原因：{{ _data.lastConsultationHistory?.remark }}</text>
								</view>
							</view>
						</template>

						<!-- 退款成功 -->
						<template v-if="afsOrderInfo.status === 'REFUNDED'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-36 text-#222 font-bold" :class="[orderAfsStatus.typeStatus === -1 && 'text-FC3F33', orderAfsStatus.typeStatus === 1 && 'text-00B496']">
										<text>{{ orderAfsStatus.title }}</text>
									</view>
								</view>
								<view class="text-28 text-#888888 font-500 mt-10">
									<text>{{ afsOrderInfo.createTime }}</text>
								</view>
							</view>
						</template>

						<!-- 等待商家处理 - 退货退款 -->
						<template v-if="afsOrderInfo.status === 'RETURN_REFUND_REQUEST'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-36 text-#222 font-bold" :class="[orderAfsStatus.typeStatus === -1 && 'text-FC3F33', orderAfsStatus.typeStatus === 1 && 'text-00B496']">
										<text>{{ orderAfsStatus.title }}</text>
									</view>
									<view class="text-28 text-#222 font-bold flex">
										<my-uv-count-down
											:time="Number(getTime(afsOrderInfo.keyNodeTimeout.requestAgreeTimeout, afsOrderInfo.createTime))"
											format="DD天HH时mm分ss秒"
											ref="payCountDown"
											:autoStart="true"
											:textStyle="{
												fontSize: '28rpx',
												color: '#222',
												fontWeight: 'bold',
											}"
											@finish="reloadData()"></my-uv-count-down>
									</view>
								</view>
								<view class="text-28 text-#888888 font-500 mt-10">
									<text>{{ orderAfsStatus.desc }}</text>
								</view>
							</view>
						</template>

						<!-- 等待卖家寄回 - 商家同意 -->
						<template v-if="afsOrderInfo.status === 'RETURN_REFUND_AGREE'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-36 text-#222 font-bold" :class="[orderAfsStatus.typeStatus === -1 && 'text-FC3F33', orderAfsStatus.typeStatus === 1 && 'text-00B496']">
										<text>{{ orderAfsStatus.title }}</text>
									</view>
									<view class="text-28 text-#222 font-bold flex">
										<my-uv-count-down
											:time="Number(getTime(afsOrderInfo.keyNodeTimeout.returnedTimeout, afsOrderInfo.createTime))"
											format="DD天HH时mm分ss秒"
											ref="payCountDown"
											:autoStart="true"
											:textStyle="{
												fontSize: '28rpx',
												color: '#222',
												fontWeight: 'bold',
											}"
											@finish="reloadData()"></my-uv-count-down>
									</view>
								</view>
								<view class="text-28 text-#888888 font-500 mt-10">
									<text>{{ orderAfsStatus.desc }}</text>
								</view>
							</view>
						</template>

						<!-- 等待卖家寄回 - 系统同意 -->
						<template v-if="afsOrderInfo.status === 'SYSTEM_RETURN_REFUND_AGREE'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-36 text-#222 font-bold" :class="[orderAfsStatus.typeStatus === -1 && 'text-FC3F33', orderAfsStatus.typeStatus === 1 && 'text-00B496']">
										<text>{{ orderAfsStatus.title }}</text>
									</view>
									<view class="text-28 text-#222 font-bold flex">
										<my-uv-count-down
											:time="Number(getTime(afsOrderInfo.keyNodeTimeout.returnedTimeout, afsOrderInfo.createTime))"
											format="DD天HH时mm分ss秒"
											ref="payCountDown"
											:autoStart="true"
											:textStyle="{
												fontSize: '28rpx',
												color: '#222',
												fontWeight: 'bold',
											}"
											@finish="reloadData()"></my-uv-count-down>
									</view>
								</view>
								<view class="text-28 text-#888888 font-500 mt-10">
									<text>{{ orderAfsStatus.desc }}</text>
								</view>
							</view>
						</template>

						<!-- 退款关闭 - 商家拒绝 -->
						<template v-if="afsOrderInfo.status === 'RETURN_REFUND_REJECT'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-36 text-#222 font-bold" :class="[orderAfsStatus.typeStatus === -1 && 'text-FC3F33', orderAfsStatus.typeStatus === 1 && 'text-00B496']">
										<text>{{ orderAfsStatus.title }}</text>
									</view>
								</view>
								<view class="text-28 text-#888888 font-500 mt-10">
									<text>{{ afsOrderInfo.createTime }}</text>
								</view>
								<view class="text-30 text-#222 font-500 mt-30">
									<text>拒绝原因：{{ _data.lastConsultationHistory?.remark }}</text>
								</view>
							</view>
						</template>

						<!-- 等待商家收货 -->
						<template v-if="afsOrderInfo.status === 'RETURNED_REFUND'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-36 text-#222 font-bold" :class="[orderAfsStatus.typeStatus === -1 && 'text-FC3F33', orderAfsStatus.typeStatus === 1 && 'text-00B496']">
										<text>{{ orderAfsStatus.title }}</text>
									</view>
									<view class="text-28 text-#222 font-bold flex">
										<my-uv-count-down
											:time="Number(getTime(afsOrderInfo.keyNodeTimeout.confirmReturnedTimeout, afsOrderInfo.afsPackage.createTime))"
											format="DD天HH时mm分ss秒"
											ref="payCountDown"
											:autoStart="true"
											:textStyle="{
												fontSize: '28rpx',
												color: '#222',
												fontWeight: 'bold',
											}"
											@finish="reloadData()"></my-uv-count-down>
									</view>
								</view>
								<view class="text-28 text-#888888 font-500 mt-10">
									<text>{{ orderAfsStatus.desc }}</text>
								</view>
							</view>
						</template>

						<!-- 商家已收货 -->
						<template v-if="afsOrderInfo.status === 'RETURNED_REFUND_CONFIRM'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-36 text-#222 font-bold" :class="[orderAfsStatus.typeStatus === -1 && 'text-FC3F33', orderAfsStatus.typeStatus === 1 && 'text-00B496']">
										<text>{{ orderAfsStatus.title }}</text>
									</view>
								</view>
								<view class="text-28 text-#888888 font-500 mt-10">
									<text>{{ orderAfsStatus.desc }}</text>
								</view>
							</view>
						</template>

						<!-- 系统自动收货 -->
						<template v-if="afsOrderInfo.status === 'SYSTEM_RETURNED_REFUND_CONFIRM'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-36 text-#222 font-bold" :class="[orderAfsStatus.typeStatus === -1 && 'text-FC3F33', orderAfsStatus.typeStatus === 1 && 'text-00B496']">
										<text>{{ orderAfsStatus.title }}</text>
									</view>
								</view>
								<view class="text-28 text-#888888 font-500 mt-10">
									<text>{{ orderAfsStatus.desc }}</text>
								</view>
							</view>
						</template>

						<!-- 退款关闭 - 商家拒绝收货 -->
						<template v-if="afsOrderInfo.status === 'RETURNED_REFUND_REJECT'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-36 text-#222 font-bold" :class="[orderAfsStatus.typeStatus === -1 && 'text-FC3F33', orderAfsStatus.typeStatus === 1 && 'text-00B496']">
										<text>{{ orderAfsStatus.title }}</text>
									</view>
								</view>
								<view class="text-28 text-#888888 font-500 mt-10">
									<text>{{ afsOrderInfo.createTime }}</text>
								</view>
								<view class="text-30 text-#222 font-500 mt-30">
									<text>拒绝原因：{{ _data.lastConsultationHistory?.remark }}</text>
								</view>
							</view>
						</template>

						<!-- 退款成功 -->
						<template v-if="afsOrderInfo.status === 'RETURNED_REFUNDED'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-36 text-#222 font-bold" :class="[orderAfsStatus.typeStatus === -1 && 'text-FC3F33', orderAfsStatus.typeStatus === 1 && 'text-00B496']">
										<text>{{ orderAfsStatus.title }}</text>
									</view>
								</view>
								<view class="text-28 text-#888888 font-500 mt-10">
									<text>{{ afsOrderInfo.createTime }}</text>
								</view>
							</view>
						</template>

						<!-- 撤销售后 -->
						<template v-if="afsOrderInfo.status === 'BUYER_CLOSED'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-36 text-#222 font-bold" :class="[orderAfsStatus.typeStatus === -1 && 'text-FC3F33', orderAfsStatus.typeStatus === 1 && 'text-00B496']">
										<text>{{ orderAfsStatus.title }}</text>
									</view>
								</view>
								<view class="text-28 text-#888888 font-500 mt-10">
									<text>{{ orderAfsStatus.desc }}</text>
								</view>
							</view>
						</template>

						<!-- 售后关闭 -->
						<template v-if="afsOrderInfo.status === 'SYSTEM_CLOSED'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-36 text-#222 font-bold" :class="[orderAfsStatus.typeStatus === -1 && 'text-FC3F33', orderAfsStatus.typeStatus === 1 && 'text-00B496']">
										<text>{{ orderAfsStatus.title }}</text>
									</view>
								</view>
								<view class="text-28 text-#888888 font-500 mt-10">
									<text>{{ orderAfsStatus.desc }}</text>
								</view>
							</view>
						</template>
					</template>

					<template v-if="afsOrderInfo && afsOrderInfo.afsPackage">
						<view class="px-30 bg-#fff border-rd-20 mt-20 py-32">
							<view class="flex justify-start mt-0">
								<app-image src="@/pages/order/static/icon_dingwei_lv.png" class="mr-20" size="30" mode=""></app-image>
								<view class="flex-1">
									<view class="flex">
										<view class="text-30 text-#222 font-bold line-height-30">{{ afsOrderInfo.afsPackage.receiverAddress }}</view>
										<view class="flex items-center ml-10 flex-shrink-0">
											<uv-button :customStyle="{ height: '30rpx', borderRadius: '30rpx', padding: '0rpx 12rpx' }" color="#F0F0F0" @click="onCopy(getUserAddressInfo())">
												<view class="text-20 font-500 text-#888888">复制</view>
											</uv-button>
										</view>
									</view>
									<view class="text-26 text-#555 font-500 mt-10">{{ afsOrderInfo.afsPackage.receiverName }} {{ afsOrderInfo.afsPackage.receiverMobile }}</view>
								</view>
							</view>
							<view class="w-full h-1 bg-#EAEDF3 my-30"></view>
							<template v-if="afsOrderInfo.afsPackage.buyerReturnedInfo">
								<view class="flex justify-start mt-0" @click="viewLogistics">
									<app-image src="@/pages/order/static/icon_wuliuche_lv.png" class="mr-20" width="30" mode="widthFix"></app-image>
									<view class="flex-1">
										<view class="flex">
											<view class="text-30 text-#222 font-bold line-height-30">{{ _data.lastLogistics ? `快递已${_data.lastLogistics.status}` : "快递已寄出" }}</view>
										</view>
										<view class="text-26 text-#555 font-500 mt-10">{{ _data.lastLogistics ? _data.lastLogistics.time : afsOrderInfo.afsPackage.createTime }}</view>
									</view>
									<view class="flex-shrink-0 flex flex-center">
										<app-image :src="common.iconRightHui" size="22" mode=""></app-image>
									</view>
								</view>
							</template>
							<template v-else>
								<template v-if="true">
									<view class="flex justify-between items-center mt-0">
										<view class="">
											<view class="text-30 text-#222 font-bold line-height-30">填写单号</view>
										</view>
										<view class="flex-shrink-0 flex flex-center">
											<view class="btn-item-box">
												<uv-button :custom-style="_data.btnItemStyle" color="#00B496" plain @click="addTrackingNumber">
													<view class="btn-text text-#00B496">填写单号</view>
												</uv-button>
											</view>
										</view>
									</view>
								</template>
								<template v-else>
									<view class="flex justify-between items-center mt-0">
										<view class="">
											<view class="text-30 text-#222 font-bold line-height-30">到店退货</view>
										</view>
										<view class="flex-shrink-0 flex flex-center">
											<view class="btn-item-box">
												<uv-button :custom-style="_data.btnItemStyle" color="#00B496" plain @click="addStoreReturn">
													<view class="btn-text text-#00B496">到店退货</view>
												</uv-button>
											</view>
										</view>
									</view>
								</template>
							</template>
						</view>
					</template>

					<view class="px-26 bg-#fff border-rd-20 mt-20 pt-30 pb-30">
						<template v-if="afsOrderInfo">
							<view class="flex justify-start items-center" @click="goShopHome(afsOrderInfo.shopId)">
								<app-image :src="afsOrderInfo.shopLogo" size="42" class="w-42 h-42 border-1/2" mode=""></app-image>
								<text class="text-32 text-#222222 ml-12 mr-6 font-bold">{{ afsOrderInfo.shopName }}</text>
								<uv-icon size="12" name="arrow-right" color="#999999" :bold="true"></uv-icon>
							</view>
						</template>

						<template v-if="goods">
							<view class="mt-30">
								<view class="w-full flex" @click="goGoodsDetails({ ...goods, shopId: afsOrderInfo.shopId })">
									<image :src="goods.image" class="w-164 h-164 min-w-164 border-solid border-1 border-#F5F5F5 border-rd-16" mode=""></image>
									<view class="flex-1 flex flex-col justify-between ml-20">
										<view class="">
											<view class="text-30 text-#222222 font-bold uv-line-1">{{ goods.productName }}</view>
											<view class="text-26 text-#555555 font-500 mt-10 flex justify-between items-center">
												<text>{{ goods.specs.join(" ") }}</text>
												<text>x{{ goods.num }}</text>
											</view>
										</view>
										<view class="text-#222222 font-bold">
											<view class="flex items-end">
												<view class="text-30 pb-2">¥</view>
												<view class="text-36">
													{{ getPriceInfo(goods.salePrice).integer }}
												</view>
												<view class="text-30 pb-2">.{{ getPriceInfo(goods.salePrice).decimalText }}</view>
											</view>
										</view>
									</view>
								</view>
							</view>
						</template>

						<view class="w-full h-1 bg-#EAEDF3 mt-30 mb-15"></view>

						<template v-if="afsOrderInfo">
							<view class="py-10 flex justify-between items-center text-28 text-#323232 font-500">
								<view class="">退款说明</view>
								<view class="text-#616161">{{ afsOrderInfo.explain }}</view>
							</view>

							<view class="py-15 flex justify-between items-center text-28 text-#323232 font-500">
								<view class="">退款金额</view>
								<view class="font-bold flex items-center">
									<view class="">￥</view>
									<view class="">{{ tatolPirce }}</view>
									<!-- <view class="">{{ getPriceInfo(tatolPirce).integer }}.{{ getPriceInfo(tatolPirce).decimalText }}</view> -->
								</view>
							</view>

							<view class="pt-30 pb-15 flex justify-between items-center text-28 text-#323232 font-500">
								<view class="">订单编号</view>
								<view class="flex justify-end items-center">
									<view class="text-#616161">{{ afsOrderInfo.orderNo }}</view>
									<view class="flex items-center ml-10">
										<uv-button :customStyle="{ height: '30rpx', borderRadius: '30rpx', padding: '0rpx 12rpx' }" color="#F0F0F0" @click="onCopy(afsOrderInfo.orderNo)">
											<view class="text-20 font-500 text-#888888">复制</view>
										</uv-button>
									</view>
								</view>
							</view>

							<view class="pt-30 pb-15 flex justify-between items-center text-28 text-#323232 font-500">
								<view class="">售后编号</view>
								<view class="flex justify-end items-center">
									<view class="text-#616161">{{ afsOrderInfo.no }}</view>
									<view class="flex items-center ml-10">
										<uv-button :customStyle="{ height: '30rpx', borderRadius: '30rpx', padding: '0rpx 12rpx' }" color="#F0F0F0" @click="onCopy(afsOrderInfo.no)">
											<view class="text-20 font-500 text-#888888">复制</view>
										</uv-button>
									</view>
								</view>
							</view>

							<view class="pt-30 pb-15 flex justify-between items-center text-28 text-#323232 font-500">
								<view class="">申请时间</view>
								<view class="flex justify-end items-center">
									<view class="text-#616161">{{ afsOrderInfo.updateTime }}</view>
								</view>
							</view>
						</template>
					</view>
					<view class="h-20"></view>
				</view>
				<template #bottom>
					<view class="w-full">
						<view class="w-full">
							<template v-if="orderAfsStatus">
								<view class="py-14 px-20 border-t-solid border-1 border-#F2F2F2 bg-#fff">
									<view class="flex items-center justify-between">
										<view class=""></view>

										<view class="flex items-center">
											<template v-if="orderAfsStatus.applyForAgain">
												<view class="btn-item-box">
													<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="applyAfterSales()">
														<view class="btn-text text-#323232">再次申请</view>
													</uv-button>
												</view>
											</template>

											<template v-if="orderAfsStatus.closable">
												<view class="btn-item-box">
													<uv-button :custom-style="_data.btnItemStyle" color="#66C5B5" @click="cancelAfssCancel()">
														<view class="btn-text text-#fff">撤销申请</view>
													</uv-button>
												</view>
											</template>
										</view>
									</view>
								</view>
							</template>
						</view>
						<app-safeAreaBottom></app-safeAreaBottom>
					</view>
				</template>
			</z-paging>
		</app-layout>
	</view>
</template>

<style lang="scss" scoped>
.btn-item-box {
	height: 64rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 0rpx 0 14rpx;
}
.btn-text {
	font-size: 26rpx;
	font-weight: 500;
}
.btn-item {
	height: 64rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 26rpx;
	margin: 0 14rpx 0 0;
	border-radius: 40rpx;
	border-style: solid;
	border-width: 1rpx;
	font-size: 26rpx;
	font-weight: 500;
}

.text-FC3F33 {
	color: #fc3f33 !important;
}

.text-00B496 {
	color: #00b496 !important;
}
</style>
