<script setup>
import { ref, reactive, computed, watch, nextTick, unref } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";

import { Decimal } from "decimal.js";

import dayjs from "dayjs";

import { useOrderStore, useMessageStore, useAppStore, useUserStore } from "@/store";

import { canENV, getPriceInfo, route, goShopHome } from "@/common/utils";

import { isUnpaidOrder, orderStatusPlus, orderStatusPlusInfo, isCompleted, isComment } from "@/pages/order/common/utils";

import { REGEX_HTTP_URL } from "@/common/test";

import { getAfsStatusCn, getAsfsStatusBtnCn } from "@/pages/order/common/types/order";

import { ORDERPAYMENT, PAY_TYPE, discountTypeConf, usePaymentCn } from "@/common/types/goods";

import { getOrder, getServerOrder, closeOrderByOrderNo, closeOrderByOrderNoSub, putOrderReceiver, getUserBalance, getOrderPayPage, getOrderIsPaySuccess, confirmGoods, getOrderInfo, getServerOrderInfo, queryConfigByModule, getOrderPayPageAgain, closeOrderServerGoods, getServerOrderContract, addOrderNumberServer, getInvoiceStatus } from "@/server/api";

import useConvert from "@/common/useConvert";

import { deepClone, sleep } from "@/uni_modules/uv-ui-tools/libs/function/index.js";

import storage from "@/common/storage";
import { common } from "@/common/images";

const { mulTenThousand, divTenThousand } = useConvert();

const _props = defineProps({
	orderNo: {
		type: String,
		required: true,
	},
	shopId: {
		type: String,
		required: true,
	},
	orderType: {
		type: String,
		default: "shop",
	},
	packageId: {
		type: String,
		default: "",
	},
});

const _data = reactive({
	btnItemStyle: {
		height: "64rpx",
		borderRadius: "40rpx",
		fontSize: "26rpx",
	},
});

const insuranceEnterpriseName = ref("");

const goodsOrder = computed(() => {
	return _props.orderType === "shop";
});

const fastGoodsInfo = computed(() => {
	const goodsArr = Array.from(orderMapComputed(shopOrder.value.shopOrderItems).values());
	return goodsArr.length > 0 ? goodsArr[0] : null;
});

const pagingRef = ref(null);

const paySelectRef = ref();
const showPay = ref(false);
const payLoading = ref(false);
const payOrderType = ref("order");
const payInfo = reactive({
	price: 0,
	balanceTotalShow: true,
	balanceTotal: 0,
	timeout: 0,
});

const orderNumber = ref("");
const payExtra = ref();
const payFrom = ref(PAY_TYPE.ORDER);

const info = ref();

const shopOrder = ref({ shopOrderItems: [] });

//优惠统计
const statistics = ref({
	//总优惠
	totalDiscount: new Decimal(0),
	// 店铺优惠
	shopDiscount: new Decimal(0),
	// 其它优惠项
	otherDiscount: [],
});

// 订单备注信息
const remark = ref({});
//运费
const freightPrice = ref(new Decimal(0));
//是否有使用支付
const isUseRebate = ref(false);
//折扣
const discounts = ref([]);
//计算店铺总折扣
const shopDiscount = computed(() => {
	return discounts.value.filter((item) => item.isShopDiscount).reduce((pre, item) => pre.add(item.price), new Decimal(0));
});
const logistics = ref();
// 是否为无需物流发货 true 是 false 不是
const logisticsTypeIsWithout = computed(() => {
	return !!(logistics.value && (logistics.value.type === "VIRTUAL" || logistics.value.type === "WITHOUT"));
});
// 商品总个数
const totalNum = computed(() => (info.value ? info.value.shopOrders.reduce((pre, item) => pre + item.shopOrderItems.reduce((accumulator, currentValue) => accumulator + currentValue.num, 0), 0) : 0));

watch(
	discounts,
	() => {
		let totalDiscount = new Decimal(0);
		let shopDiscount = new Decimal(0);
		const otherDiscount = [];
		discounts.value.forEach((item) => {
			totalDiscount = totalDiscount.add(item.price);
			if (item.isShopDiscount) {
				shopDiscount = shopDiscount.add(item.price);
			} else {
				otherDiscount.push(item);
			}
		});
		statistics.value = {
			totalDiscount,
			shopDiscount,
			otherDiscount: otherDiscount.sort((a, b) => a.sort - b.sort),
		};
	},
	{
		immediate: true,
	}
);

/**
 * 优惠后总价格计算
 * @param {*} info
 * @param {*} fixPrice 除不尽的时候展示的价格
 */
const payAmountComputed = computed(() => {
	if (!info.value) {
		return new Decimal(0);
	}
	const shopOrder = info.value.shopOrders.flatMap((shopOrder) => shopOrder.shopOrderItems);
	if (goodsOrder.value) {
		return shopOrder.reduce((p, item) => {
			return p.add(new Decimal(item.dealPrice).mul(new Decimal(item.num)).add(item.fixPrice));
		}, new Decimal(0));
	} else {
		const item = shopOrder[0];
		const orderPayAmount = new Decimal(item.dealPrice).mul(new Decimal(item.num)).add(item.fixPrice);

		const additionOrderList = info.value.orderItems || [];

		const additionOrderPayAmount = additionOrderList.reduce((p, item) => {
			if (item.status !== "UNPAY") {
				return p.add(new Decimal(item.payment.payAmount));
			} else {
				return p;
			}
		}, new Decimal(0));

		return orderPayAmount.add(additionOrderPayAmount);

		console.log(additionOrderPayAmount.toString());
	}
});
/**
 * 总价格计算
 */
const paysalePriceAmountComputed = computed(() => {
	if (!info.value) {
		return new Decimal(0);
	}
	const shopOrder = info.value.shopOrders.flatMap((shopOrder) => shopOrder.shopOrderItems);
	return shopOrder.reduce((p, item) => {
		return p.add(new Decimal(item.salePrice).mul(new Decimal(item.num)));
	}, new Decimal(0));
});
/**
 * 优惠后的实付金额
 */
const amountRealPay = computed(() => {
	if (info.value) {
		const amount = payAmountComputed.value;
		const totalPrice = new Decimal(amount);
		const totalPrice_ = totalPrice.toNumber() < 0 ? new Decimal(0) : totalPrice; // 运费不参与优惠计算 最后相加
		return totalPrice_.add(freightPrice.value);
	}
	return new Decimal(0);
});

function getServerOrderStatus(status, order) {
	const statusInfo = {
		UNPAID: "待支付",
		SERVER_UN_DELIVERY: "待接单",
		PAID: "待接单",
		SERVEROK: "待确认",
		SERVERPAYED: "等待分配服务人员",
		SERVERMASTERAWAIT: "等待服务人员接单",
		SERVERMASTEROK: "待服务",
		SERVERMASTERNO: "等待再次分配服务人员",
		SERVERGO: "服务中",
		UN_COMMENT: "待评价",
		COMPLETED: "服务完成",
		SERVEREND: "服务完成",
		BUYER_CLOSED: "服务关闭",
		SYSTEM_CLOSED: "服务关闭",
		SELLER_CLOSED: "服务关闭",
	};

	return statusInfo[status] || "";
}

function goShopChat(order) {
	if (order.shopOrders[0]) {
		const { shopLogo, shopName, shopId, totalAmount } = order.shopOrders[0];
		const { productName, image, productId } = order.shopOrders[0].shopOrderItems[0];
		const params = {
			id: productId,
			name: productName,
			salePrices: totalAmount,
			pic: image,
			no: order.no,
			amountRealPay: divTenThousand(amountRealPay.value).toFixed(2),
			afsStatu: {
				desc: getServerOrderStatus(order.status, order),
			},
			commodityList: order.shopOrders[0].shopOrderItems.map((text) => {
				return {
					...text,
					pic: text.image,
					name: text.productName,
					amountRealPay: divTenThousand(text.dealPrice)?.toFixed(2),
					dealPrice: divTenThousand(text.dealPrice)?.toFixed(2),
				};
			}),
		};

		useMessageStore().setImUserInfo({
			shopId,
			shopLogo,
			shopName,
			showOrder: true,
			showOrderInfo: {
				...params,
				my: true,
				h5: true,
			},
		});

		route("/pages/message/pages/im/im");
	}
}

function getOrderPayTime(order) {
	const timeout = order.timeout.payTimeout * 1000 - (dayjs().valueOf() - dayjs(order.createTime).valueOf());

	return timeout;
}

function getOrderReceiveTime(order) {
	const timeout = order.timeout.confirmTimeout * 1000 - (dayjs().valueOf() - dayjs(shopOrder.extra?.deliverTime).valueOf());

	return timeout;
}

function getOrderCommentTime(order) {
	const timeout = order.timeout.commentTimeout * 1000 - (dayjs().valueOf() - dayjs(shopOrder.extra?.receiveTime).valueOf());

	return timeout;
}

function mergeItemsArr(pre, current) {
	pre.num += current.num;
	pre.fixPrice = new Decimal(pre.fixPrice).plus(current.fixPrice).toString();
	pre.freightPrice = new Decimal(pre.freightPrice).plus(current.freightPrice).toString();
	//当前是关闭状态 下一个是未关闭状态 则设置为正常状态
	const changeCloseStatus = pre.status === "CLOSED" && current.status === "OK";
	//判断是否设置为正常状态
	if (changeCloseStatus) {
		//部分关闭 说明有未关闭的 合并后的状态设置为正常
		pre.status = current.status;
		pre.packageStatus = current.packageStatus;
	}
	if (pre.afsStatus === "NONE" && current.afsStatus !== "NONE") {
		pre.afsStatus = current.afsStatus;
	}
	//部分发货 设置为待发货状态
	if (!!pre.packageId && !current.packageId) {
		pre.packageId = current.packageId;
	}
	if (pre.packageStatus !== "WAITING_FOR_DELIVER" && current.packageStatus === "WAITING_FOR_DELIVER") {
		pre.packageStatus = current.packageStatus;
	}
	//如果

	pre.merged = true;
	return pre;
}

function orderMapComputed(shopOrderItems = []) {
	const shopOrderItemsMap = shopOrderItems.reduce((pre, item) => {
		const id = `${item.productId}${item.skuId}${item.specs}`;
		const currentItem = pre.get(id);
		if (currentItem) {
			const current = { ...item };
			currentItem.merged = mergeItemsArr(currentItem.merged, current);
			currentItem.items.push(current);
		} else {
			pre.set(id, {
				items: [{ ...item }],
				merged: { ...item },
			});
		}
		return pre;
	}, new Map());

	return shopOrderItemsMap;
}

function computeOrderPrice(data) {
	if (data.id) {
		return data.shopOrderItems.reduce((pre, cur) => {
			return pre.add(new Decimal(cur.num).mul(cur.dealPrice).add(cur.fixPrice).add(cur.freightPrice));
		}, new Decimal(0));
	}
	return info.valuehopOrders.reduce((pre, item) => pre.add(new Decimal(item.dealPrice).mul(item.num).add(item.freightPrice).add(item.fixPrice)), new Decimal(0));
}

async function reloadData() {
	// if (pagingRef.value) pagingRef.value.reload();
	await initOrderData();
}

function countDownFinish(e) {
	reloadData();
}

async function initUserBalance() {
	const { code, msg, data, apiStatus } = await getUserBalance();
	payInfo.balanceTotalShow = apiStatus;
	payInfo.balanceTotal = apiStatus ? `${getPriceInfo(data).integer}.${getPriceInfo(data).decimalText}` : "";
}

// 取消订单
function cancelOrder(order) {
	uni.showModal({
		title: "请确认",
		content: "是否需要取消？",
		success: async ({ confirm }) => {
			if (!confirm) return;
			const { code, data, msg, apiStatus } = await closeOrderByOrderNo({ orderNo: order.no });
			// if (!apiStatus) return uni.showToast({ icon: 'none', title: `${msg ? msg : '取消订单失败'}` });
			if (!apiStatus) return;
			uni.showToast({ icon: "none", title: `订单已取消` });
			reloadData();
		},
	});
}

// 更换地址
function changeOrderAddressServer(order) {
	route({
		type: "to",
		url: "/pages/my/pages/address/addressServer",
		params: {
			select: 1,
		},
		events: {
			async selectAddress(data) {
				uni.$once("order-item-detail-show", () => {
					uni.showModal({
						title: "请确认",
						content: `是否更改地址为${data.address}？`,
						success: async ({ confirm }) => {
							if (!confirm) return;
							const { apiStatus } = await putOrderReceiver({ orderNo: order.no, ...data });
							// if (!apiStatus) return uni.showToast({ icon: 'none', title: `${msg ? msg : '修改失败'}` });
							if (!apiStatus) return;
							uni.showToast({ icon: "none", title: `修改成功` });
							reloadData();
						},
					});
				});
			},
		},
	});
}

// 催
async function urge(type, order) {
	if (!type || !order) return;

	const typeMap = {
		shipment: {
			name: "催发货",
			msg: "已提醒商家尽快发货,请您耐心等待~",
		},
		take: {
			name: "催接单",
			msg: "已提醒商家尽快接单,请您耐心等待~",
		},
		allocation: {
			name: "催分配",
			msg: "已提醒商家尽快分配服务人员,请您耐心等待~",
		},
		takeMaster: {
			name: "催接单",
			msg: "已提醒服务人员尽快接单,请您耐心等待~",
		},
	};

	const item = typeMap[type];

	if (!item) return;

	uni.showLoading({
		title: "加载中...",
	});
	await sleep(500);
	uni.hideLoading();
	uni.showToast({
		icon: "none",
		title: item.msg,
	});
}

// 查看合同
async function checkContract(order) {
	uni.showLoading({
		title: "加载中...",
	});

	try {
		const res = await getServerOrderContract(
			{ orderNo: order.no }
			// {
			// 	header: {
			// 		'Shop-Id': order.shopOrders[0].shopId
			// 	}
			// }
		);
		uni.hideLoading();
		openContract(res);
	} catch (error) {
		//TODO handle the exception
		uni.hideLoading();
		uni.showToast({
			icon: "error",
			title: "查看失败",
		});
	}
}
// 打开合同
async function openContract(res) {
	if (res.apiStatus) {
		if (res.data) {
			let arr = [];
			if (res.data.contractImg) {
				arr.push("查看合同图片");
			}
			if (res.data.contractPdf) {
				arr.push("查看合同文件");
			}
			if (arr.length > 1) {
				uni.showActionSheet({
					itemList: arr,
					success: (e) => {
						if (e.tapIndex === 0) {
							openImage(res.data.contractImg);
						}
						if (e.tapIndex === 1) {
							previewDocument(res.data.contractPdf);
						}
					},
				});
			} else if (arr.length === 1) {
				if (arr.includes("查看合同图片")) {
					openImage(res.data.contractImg);
				}
				if (arr.includes("查看合同文件")) {
					previewDocument(res.data.contractPdf);
				}
			}
		} else {
			uni.showToast({
				icon: "error",
				title: "暂未上传合同",
			});
		}
	} else {
		uni.showToast({
			icon: "error",
			title: "查看失败",
		});
	}
}

function canConfirmOrder(order) {
	let show = false;
	let obj = {
		show: false,
		order: null,
	};

	if (Array.isArray(order.orderItems)) {
		const findItem = order.orderItems.find((i) => (i.type === "T10001" || i.type === "T10003" || i.type === "T10004") && i.status === "UNPAY");
		if (findItem) {
			obj.show = true;
			obj.order = findItem;
		}
	}

	return obj;
}
function canSubOrderPay(order) {
	let show = false;
	let obj = {
		show: false,
		order: null,
	};

	if (Array.isArray(order.orderItems)) {
		const findItem = order.orderItems.find((i) => i.type === "T10002" && i.status === "UNPAY");
		if (findItem) {
			obj.show = true;
			obj.order = findItem;
		}
	}

	return obj;
}
// 确认订单
async function confirmOrder(order) {
	const state = canConfirmOrder(order);
	if (state.show && state.order) {
		openSubOrderPay(state.order);
	}
}
// 再次支付
async function subOrderPay(order) {
	const state = canSubOrderPay(order);
	if (state.show && state.order) {
		openSubOrderPay(state.order);
	}
}
// 取消再次支付
async function subOrderClose(order) {
	if (Array.isArray(order.orderItems)) {
		const findItem = order.orderItems.find((i) => i.type === "T10002" && i.status === "UNPAY");
		cancelOrderSub(findItem);
	}
}

const masterInfo = ref({
	masterId: "",
	masterMobile: "",
	masterName: "",
	masterAvatar: "",
	masterSex: "",
	masterAge: "",
});
const masterInfoRef = ref();
// 联系服务人员
function showMasterInfo(order) {
	if (!order.masterId) {
		uni.showToast({
			icon: "error",
			title: "暂无服务人员",
		});
	}

	masterInfo.value = {
		masterId: order.masterId || "",
		masterMobile: order.masterMobile || "",
		masterName: order.masterName || "",
		masterAvatar: order.masterVO?.avatar || "",
		masterSex: order.masterVO?.sex || "",
		masterAge: order.masterVO?.age || "",
	};
	if (masterInfoRef.value) masterInfoRef.value.open();
}

const orderNumAddRef = ref();
const addNumberOrder = ref({});
const addNumberNum = ref(1);
// 订单加时
function addOrderNumber(order) {
	addNumberOrder.value = order;
	addNumberNum.value = 1;
	if (orderNumAddRef.value) orderNumAddRef.value.open();
}
async function submitServerNum() {
	try {
		const res = await addOrderNumberServer({
			orderNo: addNumberOrder.value.no,
			addTime: addNumberNum.value,
		});

		if (orderNumAddRef.value) orderNumAddRef.value.close();
		if (res.apiStatus) {
			uni.showToast({ icon: "none", title: `加时成功` });
			reloadData();
		}
	} catch (error) {
		//TODO handle the exception
		if (orderNumAddRef.value) orderNumAddRef.value.close();
	}
}

// 结束服务
function confirmReceive(order) {
	const showModalProps = {
		content: "是否确认结束服务",
		showClose: true,
		isSubmit: true,
	};

	// 通过每一个商品的售后得到 可以改变包状态的数组
	const shop = shopOrder.value;

	if (!shop.shopOrderItems.every((item) => getAfsStatusCn(item.afsStatus).canChangePackageStatus)) {
		showModalProps.content = "该订单中存在退款服务，等待商家确认";
		showModalProps.isSubmit = false;
		showModalProps.showClose = false;
	} else if (!shop.shopOrderItems.every((item) => !getAfsStatusCn(item.afsStatus).canChangePackageStatusText)) {
		showModalProps.content = "该订单中存在退款服务，结束服务将关闭退款";
	}
	// 该订单中存在退款服务，等待商家确认  该订单中存在退款服务，结束服务将关闭退款
	uni.showModal({
		// title: '提示',
		confirmColor: "#f12f22",
		content: `${showModalProps.content}`,
		showCancel: showModalProps.showClose,
		success: async (res) => {
			if (res.cancel || !showModalProps.isSubmit) return;
			const { packageId } = shop.shopOrderItems[0];
			const { code, msg, apiStatus } = await closeOrderServerGoods({ orderNo: order.no, shopId: shop.shopId, status: "SERVEREND" });
			// if (!apiStatus) return uni.showToast({ title: `${msg ? msg : '确认收货失败'}`, icon: 'none' });
			if (!apiStatus) return;
			uni.showToast({ icon: "none", title: `结束成功` });
			reloadData();
		},
	});
}

// 删除订单
function delOrder(order) {
	const showModalProps = {
		content: "是否删除订单",
		showClose: true,
		isSubmit: true,
	};

	// 通过每一个商品的售后得到 可以改变包状态的数组
	// const shop = shopOrder.value;
	// if (!shop.shopOrderItems.every((item) => getAfsStatusCn(item.afsStatus).canChangePackageStatus)) {
	// 	showModalProps.content = '该订单中存在退款宝贝，等待商家确认收货';
	// 	showModalProps.isSubmit = false;
	// 	showModalProps.showClose = false;
	// } else if (!shop.shopOrderItems.every((item) => !getAfsStatusCn(item.afsStatus).canChangePackageStatusText)) {
	// 	showModalProps.content = '该订单中存在退款宝贝，确认收货将关闭退款';
	// }
	// 该订单中存在退款宝贝，等待商家确认收货  该订单中存在退款宝贝，确认收货将关闭退款
	uni.showModal({
		title: "提示",
		confirmColor: "#f12f22",
		content: `${showModalProps.content}`,
		showCancel: showModalProps.showClose,
		success: async (res) => {
			if (res.cancel || !showModalProps.isSubmit) return;
			// const { packageId } = shop.shopOrderItems[0];
			// const { code, msg, apiStatus } = await confirmGoods({ orderNo: order.no, shopId: shop.shopId });
			// // if (!apiStatus) return uni.showToast({ title: `${msg ? msg : '确认收货失败'}`, icon: 'none' });
			// if (!apiStatus) return;
			// uni.showToast({ icon: 'none', title: `收货成功` });
			// reloadData();
		},
	});
}

// 商品详情
function goGoodsDetails(goods) {
	route("/pages/goods/pages/productDetails/productDetails", {
		productId: goods.productId,
		shopId: goods.shopId,
	});
}

async function paySubmit(e) {
	if (!goodsOrder.value) {
		// #ifdef  MP-WEIXIN
		// 调起客户端小程序订阅消息界面，返回用户订阅消息的操作结果。当用户勾选了订阅面板中的“总是保持以上选择，不再询问”时，模板消息会被添加到用户的小程序设置页。
		try {
			const res = await uni.requestSubscribeMessage({
				tmplIds: [
					// 'HA8G6APZuXamC-W8WUeFJNyEGaSH8og0Oxb38MprqiQ', // 发货
					// 'Yjtuq-FfzGWD9SR_XNWQWIhEXW34TVXAJABa-aHavzo' // 完成

					// 'IjhUqEhCGqqpYQ6i_8suidaGqNYrwHMD5Nk-fl7VTac', // 订单确认

					"IjhUqEhCGqqpYQ6i_8suieMstG7SoJAOXp3LHnGA9_Y", // 价格变化
					"fS1bkoRJpogM86SxOte7XZGN_YSWYqg4QeY_opB0LUA", // 分配人员
				],
			});
		} catch (error) {
			//TODO handle the exception
			console.log("订阅失败 => ", error);
		}
		// #endif
	}

	if (payOrderType.value === "subOrder") {
		paySubmitAgain(e);
		return;
	}

	if (e.type === "balance") {
		const { code, data, msg, apiStatus } = await getOrderPayPage({
			orderNo: unref(orderNumber),
			payType: ORDERPAYMENT.BALANCE,
			rebate: false,
		}).catch((e) => {
			payLoading.value = false;
		});

		if (!apiStatus) {
			payError(unref(orderNumber), payFrom.value);

			return;
		}

		orderNumber.value = data.outTradeNo;
		loopCheckPayStatus();
	}

	if (e.type === "wechat") {
		const payRes = await getOrderPayPage({
			orderNo: unref(orderNumber),
			payType: ORDERPAYMENT.WECHAT,
			rebate: false,
		}).catch((e) => {
			payLoading.value = false;
		});

		canENV(() => {
			console.log(payRes);
		});

		if (!payRes || !payRes.apiStatus) {
			payError(unref(orderNumber), payFrom.value);
			return;
		}

		orderNumber.value = payRes.data.outTradeNo;

		requestPayment(e.payItem.payment, payRes.data.data);
	}

	if (e.type === "alipay") {
		const payRes = await getOrderPayPage({
			orderNo: unref(orderNumber),
			payType: ORDERPAYMENT.ALIPAY,
			rebate: false,
		}).catch((e) => {
			payLoading.value = false;
		});

		canENV(() => {
			console.log(payRes);
		});

		if (!payRes || !payRes.apiStatus) {
			payError(unref(orderNumber), payFrom.value);
			return;
		}

		orderNumber.value = payRes.data.outTradeNo;

		requestPayment(e.payItem.payment, payRes.data.data);
	}
}

/**
 * uni requestPayment 支付
 * @param {*} data
 */
function requestPayment(provider, data) {
	uni.requestPayment({
		provider,
		orderInfo: data,
		...data,
		success: () => {
			loopCheckPayStatus();
		},
		fail: () => {
			closeLoopCheck(false, true);
		},
	});
}

let time = null;

/**
 * 循环检查支付状态
 */
function loopCheckPayStatus() {
	uni.showLoading({
		title: "支付校验中...",
		mask: true,
	});
	let count = 1;
	checkPayStatus(false);
	try {
		//先清除一次定时器
		time && clearInterval(time);
		// 开启轮训
		time = setInterval(() => checkPayStatus(++count > 10), 800);
	} catch (error) {
		uni.hideLoading();
		time && clearInterval(time);
	}
	// payBtnLoading.value = false;
}

/**
 * 定时器循环体
 */
function checkPayStatus(skipLoop) {
	if (skipLoop) {
		return closeLoopCheck(false, true);
	}
	getOrderIsPaySuccess({
		outTradeNo: unref(orderNumber),
	}).then((res) => {
		if (res.apiStatus) {
			closeLoopCheck(true, true);
		}
	});
}

/**
 * 取消轮训检查
 */
function closeLoopCheck(state = false, isPayStatus = false) {
	time && clearInterval(time);
	uni.hideLoading();
	if (isPayStatus) {
		if (state) {
			paySuccess(unref(orderNumber), payFrom.value);
		} else {
			payError(unref(orderNumber), payFrom.value);
		}
	} else {
		payLoading.value = false;
		showPay.value = false;
		// reloadData();
	}
}

async function paySuccess(orderType, payFrom) {
	// uni.hideLoading();
	payLoading.value = false;
	showPay.value = false;
	uni.showToast({
		icon: "success",
		title: "支付成功",
		duration: 1000,
		mask: true,
	});

	reloadData();
}

async function payError(orderType, payFrom) {
	// uni.hideLoading();
	payLoading.value = false;
	showPay.value = false;
	uni.showToast({
		icon: "error",
		title: "支付失败",
		duration: 1500,
		mask: true,
	});

	reloadData();
}

function paySelectChange(e) {
	if (!e.show) {
		closeLoopCheck(false, false);
	}
}

// 再次支付
async function openPay(order) {
	uni.showLoading({
		title: "加载中...",
	});

	orderNumber.value = order.no;

	await initUserBalance();
	payInfo.timeout = order.timeout.payTimeout * 1000 - (dayjs().valueOf() - dayjs(order.createTime).valueOf());
	payInfo.price = `${getPriceInfo(order.orderPayment.payAmount).integer}.${getPriceInfo(order.orderPayment.payAmount).decimalText}`;
	payOrderType.value = "order";
	showPay.value = true;
	uni.hideLoading();
}

// 子订单支付
async function openSubOrderPay(order) {
	uni.showLoading({
		title: "加载中...",
	});

	orderNumber.value = order.no;

	if (Number(order.payment.payAmount) === 0) {
		paySubmit({ type: "balance" });
	} else {
		await initUserBalance();
		payInfo.timeout = 0;
		payInfo.price = `${getPriceInfo(order.payment.payAmount).integer}.${getPriceInfo(order.payment.payAmount).decimalText}`;
		payOrderType.value = "subOrder";
		showPay.value = true;
	}

	uni.hideLoading();
}

async function paySubmitAgain(e) {
	if (e.type === "balance") {
		const { code, data, msg, apiStatus } = await getOrderPayPageAgain({
			orderNo: unref(orderNumber),
			payType: ORDERPAYMENT.BALANCE,
			rebate: false,
		}).catch((e) => {
			payLoading.value = false;
		});

		if (!apiStatus) {
			payError(unref(orderNumber), payFrom.value);

			return;
		}

		orderNumber.value = data.outTradeNo;
		loopCheckPayStatus();
	}

	if (e.type === "wechat") {
		const payRes = await getOrderPayPageAgain({
			orderNo: unref(orderNumber),
			payType: ORDERPAYMENT.WECHAT,
			rebate: false,
		}).catch((e) => {
			payLoading.value = false;
		});

		canENV(() => {
			console.log(payRes);
		});

		if (!payRes || !payRes.apiStatus) {
			payError(unref(orderNumber), payFrom.value);
			return;
		}

		orderNumber.value = payRes.data.outTradeNo;

		requestPayment(e.payItem.payment, payRes.data.data);
	}

	if (e.type === "alipay") {
		const payRes = await getOrderPayPageAgain({
			orderNo: unref(orderNumber),
			payType: ORDERPAYMENT.ALIPAY,
			rebate: false,
		}).catch((e) => {
			payLoading.value = false;
		});

		canENV(() => {
			console.log(payRes);
		});

		if (!payRes || !payRes.apiStatus) {
			payError(unref(orderNumber), payFrom.value);
			return;
		}

		orderNumber.value = payRes.data.outTradeNo;

		requestPayment(e.payItem.payment, payRes.data.data);
	}
}

// 申请售后
function applyAfterSale(goods) {
	const unMerged = !goods.merged.merged;

	const shopOrderItem = goods.merged;

	const { status, afsStatus, packageStatus } = shopOrderItem;

	//是否未发货
	const notDelivery = packageStatus === "WAITING_FOR_DELIVER";
	//检查售后状态 确定是否已申请过售后
	//是否未申请过售后
	const nonAfs = afsStatus === "NONE";
	//订单是否已关闭
	const closed = status !== "OK";

	//如果未合并 则需要计算 可申请售后价格 价格不大于 0 无法申请售后
	if (unMerged) {
		//计算 可售后退款价格
		const { dealPrice, num, fixPrice, freightPrice } = shopOrderItem;
		const price = new Decimal(dealPrice).div(num).add(fixPrice);
		//未发货需要加上运费
		if (packageStatus && notDelivery) {
			price.add(freightPrice);
		}

		//如果总费用小于 0 则点击售后按钮提示 实付款金额为0 不支持售后
		if (price.lte(0)) {
			uni.showToast({
				title: "实付款金额为0，不支持售后",
				icon: "none",
			});
			return;
		}
	}

	if (nonAfs) {
		route("/pages/order/pages/applyAfterSales/applyAfterSales", {
			orderNo: info.value?.no,
			itemId: shopOrderItem.id,
		});
	} else {
		if (unMerged && shopOrderItem.afsNo) {
			route("/pages/order/pages/afterSaleDetail/afterSaleDetail", {
				afsNo: shopOrderItem.afsNo,
				packageId: shopOrderItem.packageId || "",
			});
		}
	}
}

function afsButtonController(goods) {
	const result = {
		show: false,
		text: "",
		click: () => {},
	};

	if (!goodsOrder.value && info.value.status !== "SERVER_UN_DELIVERY" && info.value.status !== "PAID" && info.value.status !== "UN_COMMENT" && info.value.status !== "COMPLETED" && info.value.status !== "SERVEREND") {
		return result;
	}

	//如果未支付 则不需要显示
	if (orderStatusPlusInfo(info.value, shopOrder.value).status === "UNPAID") {
		return result;
	}

	const shopOrderItem = goods.merged;

	const { status, afsStatus, packageStatus } = shopOrderItem;

	//是否未发货
	const notDelivery = packageStatus === "WAITING_FOR_DELIVER";
	//检查售后状态 确定是否已申请过售后
	//是否未申请过售后
	const nonAfs = afsStatus === "NONE";
	//订单是否已关闭
	const closed = status !== "OK";

	//未申请过售后
	if (nonAfs) {
		// 订单项已关闭 或订单已完成 无需申请售后
		if (closed || isCompleted(packageStatus) || orderStatusPlusInfo(info.value, shopOrder.value).status === "CLOSED") {
			return result;
		}

		const unMerged = !goods.merged.merged;

		//如果未合并 则需要计算 可申请售后价格 价格不大于 0 无法申请售后
		if (unMerged) {
			//计算 可售后退款价格
			const { dealPrice, num, fixPrice, freightPrice } = shopOrderItem;
			const price = new Decimal(dealPrice).div(num).add(fixPrice);
			//未发货需要加上运费
			if (packageStatus && notDelivery) {
				price.add(freightPrice);
			}

			//如果总费用小于 0 则点击售后按钮提示 实付款金额为0 不支持售后
			if (price.lte(0)) {
				result.show = true;
				result.text = "申请售后";
				result.click = () => {
					uni.showToast({
						title: "实付款金额为0，不支持售后",
						icon: "none",
					});
				};
				return result;
			}
		}

		//未申请过  订单项状态正常 可以申请
		result.show = true;
		result.text = "申请售后";

		if (info.value?.extra.distributionMode === "INTRA_CITY_DISTRIBUTION" && !["", "DELIVERED"].includes(info.value?.icStatus || "")) {
			result.show = false;
			result.text = "";
		}

		//已发货弹出 售后类型选择器 否则 直接使用默认类型
		result.click = applyAfterSale;
		return result;
	}

	result.show = true;
	result.text = getAsfsStatusBtnCn(afsStatus);
	result.click = applyAfterSale;
	return result;
}

// 申请售后
function applyAfterSaleSub(goods) {
	const unMerged = false;

	const shopOrderItem = goods.shopOrderItem;

	// if (!shopOrderItem.packageStatus) {
	// 	shopOrderItem.packageStatus = 'WAITING_FOR_DELIVER';
	// }

	if (!shopOrderItem.status) {
		shopOrderItem.status = "OK";
	}

	const { status, afsStatus, packageStatus } = shopOrderItem;

	//是否未发货
	const notDelivery = packageStatus === "WAITING_FOR_DELIVER";
	//检查售后状态 确定是否已申请过售后
	//是否未申请过售后
	const nonAfs = afsStatus === "NONE";
	//订单是否已关闭
	const closed = status !== "OK";

	//如果未合并 则需要计算 可申请售后价格 价格不大于 0 无法申请售后
	if (unMerged) {
		//计算 可售后退款价格
		const { dealPrice, num, fixPrice, freightPrice } = shopOrderItem;
		const price = new Decimal(dealPrice).div(num).add(fixPrice);
		//未发货需要加上运费
		if (packageStatus && notDelivery) {
			price.add(freightPrice);
		}

		//如果总费用小于 0 则点击售后按钮提示 实付款金额为0 不支持售后
		if (price.lte(0)) {
			uni.showToast({
				title: "实付款金额为0，不支持售后",
				icon: "none",
			});
			return;
		}
	}

	if (nonAfs) {
		route("/pages/order/pages/applyAfterSales/applyAfterSales", {
			orderNo: goods.no,
			itemId: shopOrderItem.id,
		});
	} else {
		if (unMerged && shopOrderItem.afsNo) {
			route("/pages/order/pages/afterSaleDetail/afterSaleDetail", {
				afsNo: shopOrderItem.afsNo,
				packageId: shopOrderItem.packageId || "",
			});
		}
	}
}

function afsButtonControllerSub(goods) {
	const result = {
		show: false,
		text: "",
		click: () => {},
	};

	if (!goodsOrder.value && info.value.status !== "SERVER_UN_DELIVERY" && info.value.status !== "PAID" && info.value.status !== "UN_COMMENT" && info.value.status !== "COMPLETED" && info.value.status !== "SERVEREND") {
		return result;
	}

	const goodsList = Array.from(orderMapComputed(shopOrder.value.shopOrderItems).values());

	let canComment = false;

	if (goodsList.length > 0) {
		const goodsItem = goodsList[0];

		const shopOrderItem = goodsItem.merged;
		const packageStatus = shopOrderItem.packageStatus;

		canComment = isComment(packageStatus);
	}

	if (!canComment) {
		return result;
	}

	//如果未支付 则不需要显示
	if (orderStatusPlusInfo(info.value, shopOrder.value).status === "UNPAID") {
		return result;
	}

	//如果未支付 则不需要显示
	if (goods.status === "UNPAY") {
		return result;
	}

	const shopOrderItem = goods.shopOrderItem;

	// if (!shopOrderItem.packageStatus) {
	// 	shopOrderItem.packageStatus = 'WAITING_FOR_DELIVER';
	// }

	if (!shopOrderItem.status) {
		shopOrderItem.status = "OK";
	}

	const { status, afsStatus, packageStatus } = shopOrderItem;

	//是否未发货
	const notDelivery = packageStatus === "WAITING_FOR_DELIVER";
	//检查售后状态 确定是否已申请过售后
	//是否未申请过售后
	const nonAfs = afsStatus === "NONE";
	//订单是否已关闭
	const closed = status !== "OK";

	//未申请过售后
	if (nonAfs) {
		// 订单项已关闭 或订单已完成 无需申请售后
		if (closed || isCompleted(packageStatus) || orderStatusPlusInfo(info.value, shopOrder.value).status === "CLOSED") {
			return result;
		}

		const unMerged = false;

		//如果未合并 则需要计算 可申请售后价格 价格不大于 0 无法申请售后
		if (unMerged) {
			//计算 可售后退款价格
			const { dealPrice, num, fixPrice, freightPrice } = shopOrderItem;
			const price = new Decimal(dealPrice).div(num).add(fixPrice);
			//未发货需要加上运费
			if (packageStatus && notDelivery) {
				price.add(freightPrice);
			}

			//如果总费用小于 0 则点击售后按钮提示 实付款金额为0 不支持售后
			if (price.lte(0)) {
				result.show = true;
				result.text = "申请售后";
				result.click = () => {
					uni.showToast({
						title: "实付款金额为0，不支持售后",
						icon: "none",
					});
				};
				return result;
			}
		}

		//未申请过  订单项状态正常 可以申请
		result.show = true;
		result.text = "申请售后";

		if (info.value?.extra.distributionMode === "INTRA_CITY_DISTRIBUTION" && !["", "DELIVERED"].includes(info.value?.icStatus || "")) {
			result.show = false;
			result.text = "";
		}

		//已发货弹出 售后类型选择器 否则 直接使用默认类型
		result.click = applyAfterSale;
		return result;
	}

	result.show = true;
	result.text = getAsfsStatusBtnCn(afsStatus);
	result.click = applyAfterSale;
	return result;
}

// 取消订单 - 子订单
function cancelOrderSub(order) {
	uni.showModal({
		title: "请确认",
		content: "是否需要取消？",
		success: async ({ confirm }) => {
			if (!confirm) return;
			const { code, data, msg, apiStatus } = await closeOrderByOrderNoSub({ orderNo: order.no });
			// if (!apiStatus) return uni.showToast({ icon: 'none', title: `${msg ? msg : '取消订单失败'}` });
			if (!apiStatus) return;
			uni.showToast({ icon: "none", title: `订单已取消` });
			reloadData();
		},
	});
}

function cancelButtonControllerSub(goods) {
	const result = {
		show: false,
		text: "取消",
		click: () => {},
	};

	if (goods.type !== "T10002" || goods.status !== "UNPAY") {
		return result;
	}

	if (!goodsOrder.value && info.value.status !== "SERVEROK" && info.value.status !== "SERVERPAYED" && info.value.status !== "SERVERMASTERAWAIT" && info.value.status !== "SERVERMASTERNO" && info.value.status !== "SERVERGO") {
		return result;
	}

	result.show = true;
	result.text = "取消增加";
	result.click = cancelOrderSub;
	return result;
}

function goComment(goods) {
	if (!goods) return;
	const shopOrderItem = goods.merged;

	const packageStatus = shopOrderItem.packageStatus;

	const canComment = isComment(packageStatus);

	const goodsInfo = {
		...goods.merged,
		serveGoods: Number(!goodsOrder.value),
	};

	useOrderStore().setEvaluateGoodsInfoList([goodsInfo]);

	useOrderStore().setOrderInfoItem({
		orderNo: _props.orderNo,
		...shopOrder.value,
	});

	if (canComment) {
		// console.log('去评价');
		route("/pages/order/pages/setComment/setComment");
	} else {
		// console.log('查看评价');
		route("/pages/my/pages/evaluationDetails/evaluationDetails", {
			orderNo: _props.orderNo,
		});
	}
}

function goCommentDetails(info) {
	// console.log('查看评价');
}

//评价按钮控制器
function evaluateButtonController(goods) {
	const result = {
		show: false,
		text: "",
		click: () => {},
	};

	if (!goods) return result;

	const shopOrderItem = goods.merged;

	const packageStatus = shopOrderItem.packageStatus;

	const canComment = isComment(packageStatus);

	//不能评价 且 未评价 则不显示
	if (!canComment && !isCompleted(packageStatus)) {
		return result;
	}

	const afsButtonControllerInfo = afsButtonController(goods);

	if (afsButtonControllerInfo.show && ["待商家审核", "退款成功"].includes(afsButtonControllerInfo.text)) {
		result.show = false;
		return result;
	}

	result.show = true;
	result.text = canComment ? "评价" : "已评价";
	// result.click = canComment ? () => goComment(shopOrderItem) : () => goCommentDetails(shopOrderItem);

	return result;
}

// 申请开票
// 申请开票
function applyInvoice(info) {
	canENV(() => {
		console.log(info);
	});
	getInvoiceStatus({
		invoiceOwnerType: "USER",
		applicantId: useUserStore().userData.userId,
		orderNo: _props.orderNo,
		applicantShopId: _props.shopId,
	}).then((res) => {
		if (res.apiStatus && res.data) {
			const { data } = res;
			if (data.invoiceStatus === "REQUEST_HAS_EXPIRED") {
				uni.showToast({
					icon: "error",
					title: "已过开票时间",
				});
				return;
			}
			if (data.invoiceStatus === "SERVER_NOT_SUPPORTED") {
				uni.showToast({
					icon: "error",
					title: "该店铺暂不提供开票服务",
				});
				return;
			}
			useOrderStore().setInvoiceGoodsInfoList([
				{
					shopOrders: info.shopOrders,
					orderNo: info.no,
					invoiceAmount: data.billMoney,
					isDetail: data.invoiceStatus !== "ALLOWED_INVOICING",
					id: data.id,
				},
			]);

			if (data.invoiceStatus === "ALLOWED_INVOICING") {
				route("/pages/order/pages/invoice/applyInvoice");
			} else {
				route("/pages/order/pages/invoice/applyInvoiceDetails");
			}
		}
	});
}

const orderInvoiceStatus = ref("");

function loadData() {
	initOrderData();
}

function initRemark(params) {
	if (!params) {
		remark.value = {};
		return;
	}
	remark.value = JSON.parse(params);
}

function previewDocument(url) {
	if (!url) {
		uni.showToast({
			icon: "error",
			title: "查看失败",
		});
		return;
	}
	uni.showLoading({
		title: "加载中...",
	});
	uni.downloadFile({
		url,
		success: (res) => {
			const filePath = res.tempFilePath;
			uni.openDocument({
				filePath: filePath,
				showMenu: true,
				success: (res2) => {},
				fail: (err) => {
					uni.showToast({
						icon: "error",
						title: "查看失败",
					});
				},
				complete: () => {
					uni.hideLoading();
				},
			});
		},
		fail: (err) => {
			uni.showToast({
				icon: "error",
				title: "查看失败",
			});
		},
	});
}
function isImage(str) {
	return /.(gif|jpg|jpeg|png|gif|jpg|png|webp|avif|jfif)$/i.test(str);
}
function openImage(images) {
	if (!images) {
		uni.showToast({
			icon: "error",
			title: "查看失败",
		});
		return;
	}
	const imageUrlArr = images.split(",");
	const imagesArr = [];
	for (let url of imageUrlArr) {
		// if (isImage(url)) {
		imagesArr.push(url);
		// }
	}
	if (imagesArr.length > 0) {
		previewImage(imagesArr);
	} else {
		uni.showToast({
			icon: "error",
			title: "查看失败",
		});
	}
}
function previewImage(url) {
	if (!url) return;
	uni.previewImage({
		urls: Array.isArray(url) ? url : [url],
		count: Array.isArray(url) ? url[0] : url,
	});
}
function onCopy(text) {
	uni.setClipboardData({
		data: String(text),
		showToast: true,
	});
}
function callTel(tel) {
	if (!tel) return;

	uni.makePhoneCall({
		phoneNumber: String(tel),
		complete: (res) => {
			// console.log(res);
		},
	});
}

const pageLoading = ref(false);
async function initOrderData() {
	// shopOrder.value = { shopOrderItems: [] };

	// if (_props.orderType === 'nurse') {
	insuranceEnterpriseName.value = useAppStore().insuranceEnterpriseName;
	// }

	pageLoading.value = true;

	if (goodsOrder.value) {
	} else {
		try {
			const res = await getServerOrderInfo({
				orderNo: _props.orderNo,
				shopId: _props.shopId,
				usePackage: false,
			});
			if (res.apiStatus) {
				const order = res.data;

				discounts.value = [];

				order.orderDiscounts.forEach((item) => {
					if (item.sourceType === "CONSUMPTION_REBATE") {
						isUseRebate.value = true;
					}
					discounts.value.push({
						...discountTypeConf[item.sourceType],
						price: new Decimal(item.sourceAmount),
					});
				});

				freightPrice.value = order.shopOrders
					.flatMap((shopOrder) => shopOrder.shopOrderItems)
					.map((shopOrderItem) => new Decimal(shopOrderItem.freightPrice))
					.reduce((pre, current) => current.add(pre));

				info.value = order;

				let orderStatus = info.value.status;
				if (info.value.shopOrders[0] && info.value.shopOrders[0].status) {
					if (info.value.shopOrders[0].status !== "OK") {
						if (["BUYER_CLOSED", "SYSTEM_CLOSED", "SELLER_CLOSED"].includes(info.value.shopOrders[0].status)) {
							orderStatus = info.value.shopOrders[0].status;
						}
					} else if (orderStatus === "COMPLETED" || orderStatus === "SERVEREND") {
						const shopOrderItems = info.value.shopOrders[0].shopOrderItems;

						//检查是否全部关闭
						const deliverConfig = {
							okNum: 0,
							// 已评价
							evaluation: 0,
						};
						for (let shopOrderItem of shopOrderItems) {
							if (shopOrderItem.status === "OK") {
								deliverConfig.okNum += 1;
								deliverConfig.evaluation += ["BUYER_COMMENTED_COMPLETED", "SYSTEM_COMMENTED_COMPLETED"].includes(shopOrderItem.packageStatus) ? 1 : 0;
							}
						}
						if (deliverConfig.evaluation !== deliverConfig.okNum) {
							orderStatus = "UN_COMMENT";
						}
					}
				}

				info.value.status = orderStatus;

				if (info.value.status === "COMPLETED" || info.value.status === "SERVEREND") {
					getInvoiceStatus({
						invoiceOwnerType: "USER",
						applicantId: useUserStore().userData.userId,
						orderNo: _props.orderNo,
						applicantShopId: _props.shopId,
					}).then((res) => {
						if (res.apiStatus) {
							orderInvoiceStatus.value = res.data.invoiceStatus;
						}
					});
				}

				canENV(() => {
					console.log(info.value);
				});

				shopOrder.value = info.value.shopOrders[0];

				initRemark(order.shopOrders[0].remark);
			}
		} catch (error) {
			//TODO handle the exception
		}
	}

	pageLoading.value = false;
}

onLoad((options) => {
	// loadData();
});

onShow(() => {
	// if (pagingRef.value) {
	// 	pagingRef.value.reload();
	// } else {
	// 	loadData();
	// }
	loadData();
	uni.$emit("order-item-detail-show");
});

async function onRefresh() {
	await initOrderData();
	if (pagingRef.value) pagingRef.value.complete();
}
</script>

<template>
	<view class="page-main bg-#F4F5F8">
		<app-layout>
			<app-image src="@/pages/order/static/bg_order_details_header_bg.png" mode="widthFix"></app-image>
			<z-paging
				ref="pagingRef"
				:paging-style="{
					backgroundRepeat: 'no-repeat',
					backgroundSize: '100% 450rpx',
				}"
				refresher-theme-style="white"
				refresher-only
				@onRefresh="onRefresh">
				<template #top>
					<app-navBar back :backIcon="common.iconLeftBai" :showRight="false">
						<template #content>
							<view class="w-full flex justify-between items-center h-full">
								<view class="flex justify-start items-center"></view>
								<view class="flex items-center">
									<app-image src="@/pages/order/static/icon_kefu_bai.png" class="mr-24" size="40" mode="" @click="goShopChat(info)"></app-image>
									<!-- <app-image src="@/pages/order/static/icon_caidan_bai.png" class="mr-24" size="36" mode=""></app-image> -->
								</view>
							</view>
						</template>
					</app-navBar>
				</template>

				<view class="px-20">
					<!-- 商城订单 -->
					<template v-if="goodsOrder"></template>
					<!-- 服务订单 -->
					<template v-if="!goodsOrder && info">
						<!-- 待付款 -->
						<template v-if="info.status === 'UNPAID'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-38 text-#fff font-bold">
										<text>待支付</text>
									</view>
									<view class="text-36 text-#fff font-bold">
										<my-uv-count-down
											:time="Number(getOrderPayTime(info))"
											format="HH时mm分ss秒"
											ref="payCountDown"
											:autoStart="true"
											:textStyle="{
												fontSize: '36rpx',
												color: '#fff',
												fontWeight: 'bold',
											}"
											@finish="reloadData()"></my-uv-count-down>
									</view>
								</view>
								<view class="text-28 text-#fff font-500 mt-10">
									<text>请在规定时间内未付款，订单超时自动取消</text>
								</view>
							</view>
						</template>

						<!-- 待接单 -->
						<template v-if="info.status === 'SERVER_UN_DELIVERY' || info.status === 'PAID'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-38 text-#fff font-bold">
										<text>待接单</text>
									</view>
									<!-- <view class="text-36 text-#fff font-bold">0分0秒</view> -->
								</view>
								<view class="text-28 text-#fff font-500 mt-10">
									<text>订单已付款，等待商家接单</text>
								</view>
							</view>
						</template>

						<!-- 待确认 -->
						<template v-if="info.status === 'SERVEROK'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-38 text-#fff font-bold">
										<text>待确认</text>
									</view>
									<!-- <view class="text-36 text-#fff font-bold">0分0秒</view> -->
								</view>
								<view class="text-28 text-#fff font-500 mt-10">
									<text>订单已接单，请尽快确认合同与费用</text>
								</view>
							</view>
						</template>

						<!-- 等待分配服务人员 -->
						<template v-if="info.status === 'SERVERPAYED'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-38 text-#fff font-bold">
										<text>等待分配服务人员</text>
									</view>
									<!-- <view class="text-36 text-#fff font-bold">0分0秒</view> -->
								</view>
								<view class="text-28 text-#fff font-500 mt-10">
									<text>订单已付款，商家会尽快安排服务人员服务</text>
								</view>
							</view>
						</template>

						<!-- 等待服务人员接单 -->
						<template v-if="info.status === 'SERVERMASTERAWAIT'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-38 text-#fff font-bold">
										<text>等待服务人员接单</text>
									</view>
									<!-- <view class="text-36 text-#fff font-bold">0分0秒</view> -->
								</view>
								<view class="text-28 text-#fff font-500 mt-10">
									<text>商家已指派服务人员，等待服务人员确认接单</text>
								</view>
							</view>
						</template>

						<!-- 服务人员已接单 -->
						<template v-if="info.status === 'SERVERMASTEROK'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-38 text-#fff font-bold">
										<text>待服务</text>
									</view>
									<!-- <view class="text-36 text-#fff font-bold">0分0秒</view> -->
								</view>
								<view class="text-28 text-#fff font-500 mt-10">
									<text>服务人员已接单，等待服务人员服务</text>
								</view>
							</view>
						</template>

						<!-- 服务人员取消订单 -->
						<template v-if="info.status === 'SERVERMASTERNO'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-38 text-#fff font-bold">
										<text>等待再次分配服务人员</text>
									</view>
									<!-- <view class="text-36 text-#fff font-bold">0分0秒</view> -->
								</view>
								<view class="text-28 text-#fff font-500 mt-10">
									<text>服务人员无法接单，已通知商尽快再次安排服务人员</text>
								</view>
							</view>
						</template>

						<!-- 服务商服务中 -->
						<template v-if="info.status === 'SERVERGO'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-38 text-#fff font-bold">
										<text>服务中</text>
									</view>
									<!-- <view class="text-36 text-#fff font-bold">0分0秒</view> -->
								</view>
								<view class="text-28 text-#fff font-500 mt-10">
									<text>服务人员正在服务中</text>
								</view>
							</view>
						</template>

						<!-- 待评价 -->
						<template v-if="info.status === 'UN_COMMENT'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-38 text-#fff font-bold">
										<text>待评价</text>
									</view>
									<!-- <view class="text-36 text-#fff font-bold">0分0秒</view> -->
								</view>
								<view class="text-28 text-#fff font-500 mt-10 flex">
									<!-- <view class="">服务已结束，还剩</view>
									<my-uv-count-down
										:time="Number(getOrderCommentTime(info))"
										format="DD天HH时mm分ss秒"
										ref="payCountDown3"
										:autoStart="true"
										:textStyle="{
											fontSize: '28rpx',
											color: '#fff',
											fontWeight: '500',
										}"
										@finish="reloadData()"
									></my-uv-count-down> -->
									<view class="">服务已结束，超时将</view>
									<view class="">自动评价</view>
								</view>
							</view>
						</template>

						<!-- 已完成 -->
						<template v-if="info.status === 'COMPLETED' || info.status === 'SERVEREND'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-38 text-#fff font-bold">
										<text>服务完成</text>
									</view>
									<!-- <view class="text-36 text-#fff font-bold">0分0秒</view> -->
								</view>
								<view class="text-28 text-#fff font-500 mt-10">
									<text>服务已完成</text>
								</view>
							</view>
						</template>

						<!-- 已关闭 -->
						<template v-if="info.status === 'BUYER_CLOSED' || info.status === 'SYSTEM_CLOSED' || info.status === 'SELLER_CLOSED'">
							<view class="px-26 mt-20">
								<view class="flex justify-between items-center">
									<view class="text-38 text-#fff font-bold">
										<text>服务关闭</text>
									</view>
									<!-- <view class="text-36 text-#fff font-bold">0分0秒</view> -->
								</view>
								<view class="text-28 text-#fff font-500 mt-10">
									<text>{{ { BUYER_CLOSED: "当前订单已关闭", SYSTEM_CLOSED: "订单超时，系统已自动关闭", SELLER_CLOSED: "商家已关闭此订单" }[info.status] }}</text>
								</view>
							</view>
						</template>
					</template>

					<template v-if="goodsOrder && info && info.orderReceiver"></template>
					<template v-if="!goodsOrder && info && info.orderReceiver">
						<view class="px-26 bg-#fff border-rd-20 mt-20 py-30">
							<!-- 护理服务 -->
							<template v-if="info.serveTypeFename === 'nurse'">
								<view class="flex justify-start mt-0">
									<app-image src="@/pages/order/static/icon_dingwei_lv.png" class="mr-15" size="30" mode=""></app-image>
									<view class="flex-1">
										<view class="text-30 text-#222 font-bold line-height-30">{{ info.orderReceiver.address }}</view>
										<view class="text-26 text-#555 font-500 mt-10">{{ info.orderReceiver.name }} {{ info.orderReceiver.mobile }}</view>
									</view>
								</view>
								<view class="w-full h-1 bg-#EAEDF3 my-30"></view>
								<view class="text-28 font-500 py-0 mt-30 px-4">
									<view class="w-full flex items-center justify-between">
										<view class="w-55%">
											<view class="flex justify-start mt-0">
												<app-image src="@/pages/order/static/icon_yonghu_lv.png" class="mr-15" size="30" mode=""></app-image>
												<view class="flex-1">
													<view class="text-30 text-#222 font-bold line-height-30">{{ info.orderReceiver.caredUsername }}</view>
													<view class="text-26 text-#555 font-500 mt-10">被照护人</view>
												</view>
											</view>
										</view>
										<view class="w-41% ml-4%">
											<view class="flex justify-start mt-0">
												<app-image src="@/pages/order/static/icon_shijian_lv.png" class="mr-15" size="30" mode=""></app-image>
												<view class="flex-1">
													<view class="text-30 text-#222 font-bold line-height-30">{{ dayjs(info.orderReceiver.startTime).format("MM-DD HH:mm") }}</view>
													<view class="text-26 text-#555 font-500 mt-10">预约时间</view>
												</view>
											</view>
										</view>
									</view>
								</view>
								<template v-if="remark && Object.keys(remark).length > 0">
									<view class="w-full h-1 bg-#EAEDF3 my-30"></view>
								</template>
							</template>

							<!-- 陪诊服务 -->
							<template v-if="info.serveTypeFename === 'attend'">
								<!-- <view class="flex justify-start mt-0">
									<app-image src="@/pages/order/static/icon_dingwei_lv.png" class="mr-15" size="30" mode=""></app-image>
									<view class="flex-1">
										<view class="text-30 text-#222 font-bold line-height-30">{{ info.orderReceiver.address }}</view>
										<view class="text-26 text-#555 font-500 mt-10">{{ info.orderReceiver.name }} {{ info.orderReceiver.mobile }}</view>
									</view>
								</view> -->
								<view class="text-28 font-500 py-0 mt-30 px-4">
									<view class="w-full flex items-center justify-between">
										<view class="w-55%">
											<view class="flex justify-start mt-0">
												<app-image src="@/pages/order/static/icon_dingwei_lv.png" class="mr-15" size="30" mode=""></app-image>
												<view class="flex-1">
													<view class="text-30 text-#222 font-bold line-height-30">{{ info.orderReceiver.address }}</view>
													<view class="text-26 text-#555 font-500 mt-10">就诊医院</view>
												</view>
											</view>
										</view>
										<view class="w-41% ml-4%">
											<view class="flex justify-start mt-0">
												<app-image src="@/pages/order/static/icon_shijian_lv.png" class="mr-15" size="30" mode=""></app-image>
												<view class="flex-1">
													<view class="text-30 text-#222 font-bold line-height-30">{{ dayjs(info.orderReceiver.startTime).format("MM-DD HH:mm") }}</view>
													<view class="text-26 text-#555 font-500 mt-10">就诊时间</view>
												</view>
											</view>
										</view>
									</view>
								</view>
								<view class="w-full h-1 bg-#EAEDF3 my-30"></view>
								<view class="text-28 font-500 py-0 mt-30 px-4">
									<view class="w-full flex items-center justify-between">
										<view class="w-55%">
											<view class="flex justify-start mt-0">
												<app-image src="@/pages/order/static/icon_yonghu_lv.png" class="mr-15" size="30" mode=""></app-image>
												<view class="flex-1">
													<view class="text-30 text-#222 font-bold line-height-30">{{ info.orderReceiver.caredUsername }}</view>
													<view class="text-26 text-#555 font-500 mt-10">就诊人</view>
												</view>
											</view>
										</view>
										<view class="w-41% ml-4%">
											<view class="flex justify-start mt-0">
												<app-image src="@/pages/order/static/icon_shijian_lv.png" class="mr-15" size="30" mode=""></app-image>
												<view class="flex-1">
													<view class="text-30 text-#222 font-bold line-height-30">{{ info.orderReceiver.caredMobile }}</view>
													<view class="text-26 text-#555 font-500 mt-10">就诊人电话</view>
												</view>
											</view>
										</view>
									</view>
								</view>
								<template v-if="true">
									<view class="w-full h-1 bg-#EAEDF3 my-30"></view>
									<view class="py-10 flex items-center text-28 text-#323232 font-500 mb-30">
										<view class="flex-shrink-0 mr-10 text-#222">联系人：</view>
										<view class="text-"> {{ info.orderReceiver.contactUsername || info.orderReceiver.name }} {{ info.orderReceiver.contactMobile || info.orderReceiver.mobile }} </view>
									</view>
								</template>
							</template>

							<!-- 家政服务 -->
							<template v-if="info.serveTypeFename === 'homemaking'">
								<view class="flex justify-start mt-0">
									<app-image src="@/pages/order/static/icon_dingwei_lv.png" class="mr-15" size="30" mode=""></app-image>
									<view class="flex-1">
										<view class="text-30 text-#222 font-bold line-height-30">{{ info.orderReceiver.address }}</view>
										<view class="text-26 text-#555 font-500 mt-10">{{ info.orderReceiver.name }} {{ info.orderReceiver.mobile }}</view>
									</view>
								</view>
								<view class="w-full h-1 bg-#EAEDF3 my-30"></view>
								<view class="flex justify-start mt-0">
									<app-image src="@/pages/order/static/icon_dingwei_lv.png" class="mr-15" size="30" mode=""></app-image>
									<view class="flex-1">
										<view class="text-30 text-#222 font-bold line-height-30">{{ info.orderReceiver.startTime }}</view>
										<view class="text-26 text-#555 font-500 mt-10">上门时间</view>
									</view>
								</view>
								<template v-if="remark && Object.keys(remark).length > 0">
									<view class="w-full h-1 bg-#EAEDF3 my-30"></view>
								</template>
							</template>

							<view class="text-28 font-500 py-0 mt-0 px-4">
								<template v-for="(val, key, index) in remark" :key="index">
									<template v-if="!['newOrderNotify', 'remark'].includes(key)">
										<view class="py-10 flex items-center text-28 text-#323232 font-500">
											<view class="flex-shrink-0 mr-10 text-#222">{{ key }}：</view>
											<template v-if="isImage(val)">
												<view>
													<image :src="val" class="h-80" mode="heightFix" @click="previewImage(val)"></image>
												</view>
											</template>
											<template v-else>
												<view class="text-#00B496">{{ val }}</view>
											</template>
										</view>
									</template>
								</template>
							</view>
						</view>
					</template>

					<view class="px-26 bg-#fff border-rd-20 mt-20 pt-30 pb-30">
						<view class="flex justify-start items-center" @click="goShopHome(shopOrder.shopId)">
							<app-image :src="shopOrder.shopLogo" size="42" class="w-42 h-42 border-1/2" mode=""></app-image>
							<text class="text-32 text-#222222 ml-12 mr-6 font-bold">{{ shopOrder.shopName }}</text>
							<uv-icon size="12" name="arrow-right" color="#999999" :bold="true"></uv-icon>
						</view>

						<template v-if="goodsOrder"></template>
						<template v-if="!goodsOrder">
							<template v-for="(goods, index) in Array.from(orderMapComputed(shopOrder.shopOrderItems).values())" :key="index">
								<template v-if="index === 0">
									<view class="mt-30">
										<view class="w-full flex" @click="goGoodsDetails(goods.merged)">
											<image :src="goods.merged.image" class="w-164 h-164 min-w-164 border-solid border-1 border-#F5F5F5 border-rd-16" mode=""></image>
											<view class="flex-1 flex flex-col justify-between ml-20">
												<view class="">
													<view class="text-30 text-#222222 font-bold uv-line-1">{{ goods.merged.productName }}</view>
													<view class="text-26 text-#555555 font-500 mt-10 flex justify-between items-center">
														<text>{{ goods.merged.specs ? goods.merged.specs?.join(" ") : "默认规格" }}</text>
														<text>x{{ goods.merged.num }}</text>
													</view>
												</view>
												<view class="text-#222222 font-bold">
													<view class="flex items-end">
														<view class="text-30 pb-2">¥</view>
														<view class="text-36">
															{{ getPriceInfo(goods.merged.salePrice).integer }}
														</view>
														<view class="text-30 pb-2">.{{ getPriceInfo(goods.merged.salePrice).decimalText }}</view>
													</view>
												</view>
											</view>
										</view>
										<view class="w-full flex justify-between items-center">
											<view class=""></view>
											<view class="flex items-center">
												<template v-if="afsButtonController(goods).show && !pageLoading">
													<view class="flex ml-10">
														<uv-button :custom-style="{ fontSize: '24rpx', height: '60rpx', borderRadius: '60rpx' }" color="#D5D5D5" plain @click="applyAfterSale(goods)">
															<text class="btn-text text-#323232">{{ afsButtonController(goods).text }}</text>
														</uv-button>
													</view>
												</template>

												<template v-if="evaluateButtonController(goods).show && !pageLoading">
													<view class="flex ml-10">
														<uv-button :custom-style="{ fontSize: '24rpx', height: '60rpx', borderRadius: '60rpx' }" color="#FF950A" plain @click="goComment(goods)">
															<view class="btn-text text-#FF950A">{{ evaluateButtonController(goods).text }}</view>
														</uv-button>
													</view>
												</template>
											</view>
										</view>
									</view>
								</template>
								<template v-if="index > 0"></template>
							</template>
						</template>

						<view class="w-full h-1 bg-#EAEDF3 mt-30 mb-15"></view>
						<!-- 子订单支付 -->
						<template v-if="!goodsOrder">
							<template v-if="info && Array.isArray(info.orderItems) && info.orderItems.length > 0">
								<template v-for="(payItem, payIndex) in info.orderItems" :key="payIndex">
									<template
										v-if="
											info.status === 'SERVEROK' ||
											info.status === 'SERVERPAYED' ||
											info.status === 'SERVERMASTERAWAIT' ||
											info.status === 'SERVERMASTERNO' ||
											info.status === 'SERVERGO' ||
											(info.status !== 'SERVEROK' && info.status !== 'SERVERPAYED' && info.status !== 'SERVERMASTERAWAIT' && info.status !== 'SERVERMASTERNO' && info.status !== 'SERVERGO' && payItem.status !== 'UNPAY')
										">
										<view class="">
											<view class="py-15 flex justify-between items-center text-28 text-#323232 font-500">
												<view class="">{{ { T10001: "签约附加金", T10002: "服务附加金", T10003: "签约附加金", T10004: "签约附加金" }[payItem.type] }}</view>
												<view class="flex items-center">
													<view class="">+</view>
													<view class="">￥</view>
													<view class="">{{ getPriceInfo(payItem.payment.payAmount).integer }}.{{ getPriceInfo(payItem.payment.payAmount).decimalText }}</view>
												</view>
											</view>
											<view class="w-full flex justify-between items-center">
												<view class=""></view>
												<view class="flex items-center">
													<template v-if="afsButtonControllerSub(payItem).show && !pageLoading">
														<view class="flex ml-10">
															<uv-button :custom-style="{ fontSize: '22rpx', height: '50rpx', borderRadius: '50rpx' }" color="#D5D5D5" plain @click="applyAfterSaleSub(payItem)">
																<view class="btn-text text-#323232">{{ afsButtonControllerSub(payItem).text }}</view>
															</uv-button>
														</view>
													</template>

													<template v-if="cancelButtonControllerSub(payItem).show && !pageLoading">
														<view class="flex ml-10">
															<uv-button :custom-style="{ fontSize: '22rpx', height: '50rpx', borderRadius: '50rpx' }" color="#D5D5D5" plain @click="cancelOrderSub(payItem)">
																<view class="btn-text text-#323232">{{ cancelButtonControllerSub(payItem).text }}</view>
															</uv-button>
														</view>
													</template>
												</view>
											</view>
										</view>
									</template>
								</template>
							</template>
						</template>

						<template v-if="statistics.shopDiscount">
							<view class="py-15 flex justify-between items-center text-28 text-#323232 font-500">
								<view class="">其他优惠</view>
								<view class="text-#FC3F33 flex items-center">
									<view class="">-</view>
									<view class="">￥</view>
									<view class="">{{ getPriceInfo(statistics.shopDiscount).integer }}.{{ getPriceInfo(statistics.shopDiscount).decimalText }}</view>
								</view>
							</view>
						</template>
						<template v-for="(item, idx) in statistics.otherDiscount" :key="idx">
							<view class="py-15 flex justify-between items-center text-28 text-#323232 font-500">
								<view class="">{{ item.name }}</view>
								<view class="text-#FC3F33 flex items-center">
									<view class="">+</view>
									<view class="">￥</view>
									<view class="">{{ getPriceInfo(item.price).integer }}.{{ getPriceInfo(item.price).decimalText }}</view>
								</view>
							</view>
						</template>
						<template v-if="goodsOrder"></template>
						<template v-else>
							<view class="py-15 flex justify-between items-center text-28 text-#323232 font-500">
								<view class="">{{ info && info.status !== "UNPAID" ? "实付款" : "应付款" }}</view>
								<view class="font-bold flex items-center">
									<view class="">￥</view>
									<view class="">{{ getPriceInfo(amountRealPay).integer }}.{{ getPriceInfo(amountRealPay).decimalText }}</view>
								</view>
							</view>
						</template>

						<template v-if="insuranceEnterpriseName">
							<view class="w-full h-1 bg-#EAEDF3 mt-30"></view>
							<view class="pt-30 pb-15 flex justify-between items-center text-28 text-#323232 font-500">
								<view class="">保险保障</view>
								<view class="flex justify-end items-center">
									<view class="text-#616161">{{ insuranceEnterpriseName }}</view>
								</view>
							</view>
						</template>

						<view class="w-full h-1 bg-#EAEDF3 mt-30"></view>
						<template v-if="info">
							<template v-if="!goodsOrder && info">
								<template v-if="info.status === 'SERVERMASTEROK' || info.status === 'SERVERGO'">
									<view class="py-10 flex justify-between items-center text-28 text-#323232 font-500">
										<view class="">服务人员</view>
										<view class="text-#00B496 flex-1 ml-30 text-right" @click="showMasterInfo(info)">{{ info.masterName }}</view>
									</view>
								</template>
							</template>

							<view class="pt-30 pb-15 flex justify-between items-center text-28 text-#323232 font-500">
								<view class="">订单编号</view>
								<view class="flex justify-end items-center">
									<view class="text-#616161">{{ info.no }}</view>
									<view class="flex items-center ml-10">
										<uv-button :customStyle="{ height: '30rpx', borderRadius: '30rpx', padding: '0rpx 12rpx' }" color="#F0F0F0" @click="onCopy(info.no)">
											<view class="text-20 font-500 text-#888888">复制</view>
										</uv-button>
									</view>
								</view>
							</view>

							<view class="py-10 flex justify-between items-center text-28 text-#323232 font-500">
								<view class="">创建时间</view>
								<view class="text-#616161">{{ info.createTime }}</view>
							</view>

							<template v-if="(info.status !== 'UNPAID' && info.status !== 'BUYER_CLOSED') || info.status !== 'SYSTEM_CLOSED' || info.status !== 'SELLER_CLOSED'">
								<view class="py-10 flex justify-between items-center text-28 text-#323232 font-500">
									<view class="">支付方式</view>
									<view class="text-#616161">{{ usePaymentCn(info?.orderPayment?.type) }}</view>
								</view>
								<view class="py-10 flex justify-between items-center text-28 text-#323232 font-500">
									<view class="">支付时间</view>
									<view class="text-#616161">{{ info?.orderPayment?.payTime || info?.orderPayment?.createTime }}</view>
								</view>
							</template>

							<!-- <template v-if="orderStatusPlusInfo(info, shopOrder).isClosed && orderStatusPlusInfo(info, shopOrder).closeTime">
								<view class="py-10 flex justify-between items-center text-28 text-#323232 font-500">
									<view class="">取消时间</view>
									<view class="text-#616161">{{ orderStatusPlusInfo(info, shopOrder).closeTime }}</view>
								</view>
							</template> -->
						</template>
						<template v-if="goodsOrder"></template>
					</view>
					<view class="h-20"></view>
				</view>
				<template #bottom>
					<view class="w-full">
						<view class="w-full">
							<!-- 商城订单 -->
							<template v-if="goodsOrder && info && shopOrder"></template>
							<!-- 服务订单 -->
							<template v-if="!goodsOrder && info">
								<!-- 待付款 -->
								<template v-if="info.status === 'UNPAID'">
									<view class="py-14 px-20 border-t-solid border-1 border-#F2F2F2 bg-#fff">
										<view class="flex items-center justify-between">
											<view class=""></view>

											<view class="flex items-center">
												<view class="btn-item-box">
													<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="cancelOrder(info)">
														<view class="btn-text text-#323232">取消订单</view>
													</uv-button>
												</view>

												<view class="btn-item-box">
													<uv-button :custom-style="_data.btnItemStyle" color="#00B496" plain @click="changeOrderAddressServer(info)">
														<view class="btn-text text-#00B496">更换地址</view>
													</uv-button>
												</view>

												<view class="btn-item-box">
													<uv-button :custom-style="_data.btnItemStyle" color="#66C5B5" @click="openPay(info)">
														<view class="btn-text text-#fff">立即支付</view>
													</uv-button>
												</view>
											</view>
										</view>
									</view>
								</template>

								<!-- 待接单 -->
								<template v-if="info.status === 'SERVER_UN_DELIVERY' || info.status === 'PAID'">
									<view class="py-14 px-20 border-t-solid border-1 border-#F2F2F2 bg-#fff">
										<view class="flex items-center justify-between">
											<view class=""></view>

											<view class="flex items-center">
												<template v-if="afsButtonController(fastGoodsInfo).show">
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="applyAfterSale(fastGoodsInfo)">
															<view class="btn-text text-#323232">{{ afsButtonController(fastGoodsInfo).text }}</view>
														</uv-button>
													</view>
												</template>

												<view class="btn-item-box">
													<uv-button :custom-style="_data.btnItemStyle" color="#00B496" plain @click="urge('take', info)">
														<view class="btn-text text-#00B496">催接单</view>
													</uv-button>
												</view>
											</view>
										</view>
									</view>
								</template>

								<!-- 待确认 -->
								<template v-if="info.status === 'SERVEROK'">
									<view class="py-14 px-20 border-t-solid border-1 border-#F2F2F2 bg-#fff">
										<view class="flex items-center justify-between">
											<view class=""></view>

											<view class="flex items-center">
												<template v-if="info.contractPdf || info.contractImg">
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#00B496" plain @click="openContract({ apiStatus: true, data: { contractPdf: info.contractPdf || '', contractImg: info.contractImg || '' } })">
															<view class="btn-text text-#00B496">查看合同</view>
														</uv-button>
													</view>
												</template>
												<template v-if="canConfirmOrder(info).show">
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#00B496" @click="confirmOrder(info)">
															<view class="btn-text text-#fff">确认费用</view>
														</uv-button>
													</view>
												</template>
												<template v-if="canSubOrderPay(info).show">
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#00B496" @click="subOrderPay(info)">
															<view class="btn-text text-#fff">支付费用</view>
														</uv-button>

														<view class="btn-item-box">
															<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="subOrderClose(info)">
																<view class="btn-text text-#323232">取消增加</view>
															</uv-button>
														</view>
													</view>
												</template>
											</view>
										</view>
									</view>
								</template>

								<!-- 等待分配服务人员 -->
								<template v-if="info.status === 'SERVERPAYED'">
									<view class="py-14 px-20 border-t-solid border-1 border-#F2F2F2 bg-#fff">
										<view class="flex items-center justify-between">
											<view class=""></view>

											<view class="flex items-center">
												<template v-if="info.contractPdf || info.contractImg">
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#00B496" plain @click="openContract({ apiStatus: true, data: { contractPdf: info.contractPdf || '', contractImg: info.contractImg || '' } })">
															<view class="btn-text text-#00B496">查看合同</view>
														</uv-button>
													</view>
												</template>
												<view class="btn-item-box">
													<uv-button :custom-style="_data.btnItemStyle" color="#00B496" @click="urge('allocation', info)">
														<view class="btn-text text-#fff">催分配</view>
													</uv-button>
												</view>
												<template v-if="canSubOrderPay(info).show">
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#00B496" @click="subOrderPay(info)">
															<view class="btn-text text-#fff">支付费用</view>
														</uv-button>

														<view class="btn-item-box">
															<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="subOrderClose(info)">
																<view class="btn-text text-#323232">取消增加</view>
															</uv-button>
														</view>
													</view>
												</template>
											</view>
										</view>
									</view>
								</template>

								<!-- 等待服务人员接单 -->
								<template v-if="info.status === 'SERVERMASTERAWAIT'">
									<view class="py-14 px-20 border-t-solid border-1 border-#F2F2F2 bg-#fff">
										<view class="flex items-center justify-between">
											<view class=""></view>

											<view class="flex items-center">
												<template v-if="info.contractPdf || info.contractImg">
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#00B496" plain @click="openContract({ apiStatus: true, data: { contractPdf: info.contractPdf || '', contractImg: info.contractImg || '' } })">
															<view class="btn-text text-#00B496">查看合同</view>
														</uv-button>
													</view>
												</template>
												<view class="btn-item-box">
													<uv-button :custom-style="_data.btnItemStyle" color="#00B496" @click="urge('takeMaster', info)">
														<view class="btn-text text-#fff">催接单</view>
													</uv-button>
												</view>
												<template v-if="canSubOrderPay(info).show">
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#00B496" @click="subOrderPay(info)">
															<view class="btn-text text-#fff">支付费用</view>
														</uv-button>
													</view>

													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="subOrderClose(info)">
															<view class="btn-text text-#323232">取消增加</view>
														</uv-button>
													</view>
												</template>
											</view>
										</view>
									</view>
								</template>

								<!-- 服务人员已接单 -->
								<template v-if="info.status === 'SERVERMASTEROK'">
									<view class="py-14 px-20 border-t-solid border-1 border-#F2F2F2 bg-#fff">
										<view class="flex items-center justify-between">
											<view class=""></view>

											<view class="flex items-center">
												<template v-if="info.contractPdf || info.contractImg">
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#00B496" plain @click="openContract({ apiStatus: true, data: { contractPdf: info.contractPdf || '', contractImg: info.contractImg || '' } })">
															<view class="btn-text text-#00B496">查看合同</view>
														</uv-button>
													</view>
												</template>

												<view class="btn-item-box">
													<uv-button :custom-style="_data.btnItemStyle" color="#00B496" @click="showMasterInfo(info)">
														<view class="btn-text text-#fff">联系服务人员</view>
													</uv-button>
												</view>

												<template v-if="canSubOrderPay(info).show">
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#00B496" @click="subOrderPay(info)">
															<view class="btn-text text-#fff">支付费用</view>
														</uv-button>
													</view>

													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="subOrderClose(info)">
															<view class="btn-text text-#323232">取消增加</view>
														</uv-button>
													</view>
												</template>
											</view>
										</view>
									</view>
								</template>

								<!-- 服务人员取消订单 -->
								<template v-if="info.status === 'SERVERMASTERNO'">
									<view class="py-14 px-20 border-t-solid border-1 border-#F2F2F2 bg-#fff">
										<view class="flex items-center justify-between">
											<view class=""></view>

											<view class="flex items-center">
												<template v-if="info.contractPdf || info.contractImg">
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#00B496" plain @click="openContract({ apiStatus: true, data: { contractPdf: info.contractPdf || '', contractImg: info.contractImg || '' } })">
															<view class="btn-text text-#00B496">查看合同</view>
														</uv-button>
													</view>
												</template>
												<view class="btn-item-box">
													<uv-button :custom-style="_data.btnItemStyle" color="#00B496" @click="urge('allocation', info)">
														<view class="btn-text text-#fff">催分配</view>
													</uv-button>
												</view>
												<template v-if="canSubOrderPay(info).show">
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#00B496" @click="subOrderPay(info)">
															<view class="btn-text text-#fff">支付费用</view>
														</uv-button>
													</view>

													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="subOrderClose(info)">
															<view class="btn-text text-#323232">取消增加</view>
														</uv-button>
													</view>
												</template>
											</view>
										</view>
									</view>
								</template>

								<!-- 服务中 -->
								<template v-if="info.status === 'SERVERGO'">
									<view class="py-14 px-20 border-t-solid border-1 border-#F2F2F2 bg-#fff">
										<view class="flex items-center justify-between">
											<view class=""></view>

											<view class="flex items-center">
												<template v-if="info.contractPdf || info.contractImg">
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#00B496" plain @click="openContract({ apiStatus: true, data: { contractPdf: info.contractPdf || '', contractImg: info.contractImg || '' } })">
															<view class="btn-text text-#00B496">查看合同</view>
														</uv-button>
													</view>
												</template>

												<!-- 	<view class="btn-item-box">
													<uv-button :custom-style="_data.btnItemStyle" color="#00B496" @click="showMasterInfo(info)">
														<view class="btn-text text-#fff">联系服务人员</view>
													</uv-button>
												</view> -->

												<template v-if="canSubOrderPay(info).show">
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#00B496" @click="subOrderPay(info)">
															<view class="btn-text text-#fff">支付费用</view>
														</uv-button>
													</view>

													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="subOrderClose(info)">
															<view class="btn-text text-#323232">取消增加</view>
														</uv-button>
													</view>
												</template>
												<template v-else>
													<template v-if="info.serveTypeFename !== 'nurse'">
														<view class="btn-item-box">
															<uv-button :custom-style="_data.btnItemStyle" color="#00B496" plain @click="addOrderNumber(info)">
																<view class="btn-text text-#00B496">增加服务</view>
															</uv-button>
														</view>
													</template>

													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#66C5B5" @click="confirmReceive(info)">
															<view class="btn-text text-#fff">结束服务</view>
														</uv-button>
													</view>
												</template>
											</view>
										</view>
									</view>
								</template>

								<!-- 待评价 -->
								<template v-if="info.status === 'UN_COMMENT'">
									<view class="py-14 px-20 border-t-solid border-1 border-#F2F2F2 bg-#fff">
										<view class="flex items-center justify-between">
											<view class=""></view>

											<view class="flex items-center">
												<!-- <view class="btn-item-box">
													<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" plain @click="delOrder(order)">
														<view class="btn-text text-#323232">删除订单</view>
													</uv-button>
												</view> -->
												<template v-if="evaluateButtonController(fastGoodsInfo).show">
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#FF950A" plain @click="goComment(fastGoodsInfo)">
															<view class="btn-text text-#FF950A">{{ evaluateButtonController(fastGoodsInfo).text }}</view>
														</uv-button>
													</view>
												</template>
											</view>
										</view>
									</view>
								</template>

								<!-- 已完成 -->
								<template v-if="info.status === 'COMPLETED' || info.status === 'SERVEREND'">
									<view class="py-14 px-20 border-t-solid border-1 border-#F2F2F2 bg-#fff">
										<view class="flex items-center justify-between">
											<view class=""></view>

											<view class="flex items-center">
												<view class="btn-item-box">
													<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" @click="goGoodsDetails(shopOrder.shopOrderItems[0])" plain>
														<view class="btn-text text-#323232">再来一单</view>
													</uv-button>
												</view>

												<template v-if="['', 'ALLOWED_INVOICING', 'REQUEST_HAS_EXPIRED', 'SERVER_NOT_SUPPORTED'].includes(orderInvoiceStatus)">
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#00B496" plain @click="applyInvoice(info)">
															<view class="btn-text text-#00B496">申请开票</view>
														</uv-button>
													</view>
												</template>

												<template v-if="orderInvoiceStatus === 'REQUEST_IN_PROCESS'">
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#00B496" plain @click="applyInvoice(info)">
															<view class="btn-text text-#00B496">开票中</view>
														</uv-button>
													</view>
												</template>

												<template v-if="orderInvoiceStatus === 'SUCCESSFULLY_INVOICED'">
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#00B496" plain @click="applyInvoice(info)">
															<view class="btn-text text-#00B496">开票完成</view>
														</uv-button>
													</view>
												</template>

												<template v-if="orderInvoiceStatus === 'FAILED_INVOICE_REQUEST'">
													<view class="btn-item-box">
														<uv-button :custom-style="_data.btnItemStyle" color="#00B496" plain @click="applyInvoice(info)">
															<view class="btn-text text-#00B496">开票失败</view>
														</uv-button>
													</view>
												</template>
											</view>
										</view>
									</view>
								</template>

								<!-- 已完成 -->
								<template v-if="info.status === 'BUYER_CLOSED' || info.status === 'SYSTEM_CLOSED' || info.status === 'SELLER_CLOSED'">
									<view class="py-14 px-20 border-t-solid border-1 border-#F2F2F2 bg-#fff">
										<view class="flex items-center justify-between">
											<view class=""></view>

											<view class="flex items-center">
												<view class="btn-item-box">
													<uv-button :custom-style="_data.btnItemStyle" color="#D5D5D5" @click="goGoodsDetails(shopOrder.shopOrderItems[0])" plain>
														<view class="btn-text text-#323232">再来一单</view>
													</uv-button>
												</view>
											</view>
										</view>
									</view>
								</template>
							</template>
						</view>
						<app-safeAreaBottom></app-safeAreaBottom>
					</view>
				</template>
			</z-paging>

			<app-pay-select ref="paySelectRef" v-model:show="showPay" v-model:btnLoading="payLoading" :price="payInfo.price" :balanceTotal="payInfo.balanceTotal" :balanceTotalShow="payInfo.balanceTotalShow" :timeout="payInfo.timeout" @submit="paySubmit" @change="paySelectChange"></app-pay-select>

			<my-uv-modal ref="masterInfoRef" title="服务人员信息" confirmText="立即呼叫" @confirm="callTel(masterInfo.masterMobile || '')" showCancelButton>
				<view class="w-full">
					<template v-if="masterInfo.masterAvatar && masterInfo.masterSex && masterInfo.masterAge">
						<view class="bg-#F6F6F6 px-15 py-15 border-rd-16 mt-20">
							<view class="flex items-center">
								<view class="w-100 h-100">
									<app-image :src="masterInfo.masterAvatar" size="100" rd="10" mode=""></app-image>
								</view>
								<view class="flex-1 ml-20">
									<view class="flex">
										<view class="text-30 text-#222 font-bold">{{ masterInfo.masterName }}</view>
										<view class="text-30 text-#222 font-500 ml-20">{{ masterInfo.masterMobile }}</view>
									</view>
									<view class="flex text-#777777 text-26 line-height-26 font-500 items-center mt-10">
										<view class="">{{ { MALE: "男", FEMALE: "女" }[masterInfo.masterSex] }}</view>
										<view class="w-1 h-20 bg-#DFDFDF mx-15"></view>
										<view class="">{{ masterInfo.masterAge }}岁</view>
									</view>
								</view>
							</view>
						</view>
					</template>
					<template v-else>
						<view class="bg-#F6F6F6 px-26 py-10 border-rd-16 mt-20">
							<view class="flex justify-between my-20">
								<view class="flex-shrink-0">
									<view class="text-30 text-#222 font-500 min-w-150">姓名</view>
								</view>

								<view class="flex items-center">
									<view class="text-30 text-#222 font-500 text-right flex-1">
										{{ masterInfo.masterName }}
									</view>
									<image :src="common.iconCopyBai" class="w-20 h-20 min-w-20 mt-8 ml-10" mode="" @click="onCopy(`${order.orderReceiver.area[1]}${order.orderReceiver.area[2]}${order.orderReceiver.address}`)"></image>
								</view>
							</view>

							<view class="flex justify-between my-20">
								<view class="flex-shrink-0">
									<view class="text-30 text-#222 font-500 min-w-150">手机号</view>
								</view>

								<view class="flex items-center">
									<view class="text-30 font-500 text-right flex-1 text-#00B496">
										{{ masterInfo.masterMobile }}
									</view>
								</view>
							</view>
						</view>
					</template>
				</view>
			</my-uv-modal>

			<my-uv-modal ref="orderNumAddRef" title="增加服务" @confirm="submitServerNum" :closeOnClickOverlay="false" showCancelButton asyncClose>
				<view class="w-full">
					<view class="bg-#fff p-26 border-rd-16 mt-20">
						<view class="w-full flex justify-center">
							<my-uv-number-box v-model="addNumberNum" :min="1" :max="10" bgColor="#F3F4F6" color="#111010" buttonSize="90rpx" inputWidth="120rpx" inputFontSize="42rpx" :inputCustomStyle="{ fontWeight: 500 }" integer></my-uv-number-box>
						</view>

						<template v-if="addNumberOrder && addNumberOrder.shopOrders">
							<view class="mt-20 text-24 text-#00B496 font-500"> 提示：￥{{ getPriceInfo(addNumberOrder.shopOrders[0].shopOrderItems[0].salePrice).integer }}.{{ getPriceInfo(addNumberOrder.shopOrders[0].shopOrderItems[0].salePrice).decimalText }}/次，按整次起加 </view>
						</template>
					</view>
				</view>
			</my-uv-modal>
		</app-layout>
	</view>
</template>

<style lang="scss" scoped>
.btn-item-box {
	height: 64rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 0rpx 0 14rpx;
}
.btn-text {
	// font-size: 26rpx;
	font-weight: 500;
}
.btn-item {
	height: 64rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 26rpx;
	margin: 0 14rpx 0 0;
	border-radius: 40rpx;
	border-style: solid;
	border-width: 1rpx;
	font-size: 26rpx;
	font-weight: 500;
}
</style>
