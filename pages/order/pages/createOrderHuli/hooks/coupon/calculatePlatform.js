import CalculateHandler from '../common/calculate';
import Decimal from 'decimal.js';

export default class CalculateForPlatformCoupon extends CalculateHandler {
	couponRule = {};
	constructor(process) {
		super(process.productInfoArr || []);
		this.couponRule = process.rule;
	}

	handle(params = {}) {
		let disPrice = new Decimal('0');
		const filterProduct = this.productInfo;

		// 计算商品总价
		const totalPrice = filterProduct.reduce((acc, cur) => {
			return new Decimal(acc).add(new Decimal(cur.productPrice).mul(cur.quantity));
		}, new Decimal('0'));

		// 处理不同优惠券类型
		if (['PRICE_REDUCE', 'REQUIRED_PRICE_REDUCE'].includes(this.couponRule.type)) {
			const couponAmount = new Decimal(this.couponRule.amount || '0');
			disPrice = totalPrice.lessThanOrEqualTo(couponAmount) ? totalPrice : couponAmount;
		} else {
			const discountRate = new Decimal(10).minus(this.couponRule.discount || '0').div(10);
			disPrice = totalPrice.mul(discountRate);
		}

		// 构建结果对象
		const result = {
			shopId: '0',
			totalPrice: params.totalPrice || this.totalPrice,
			discountPrice: params.discountPrice ?
				disPrice.add(new Decimal(params.discountPrice)).toString() : disPrice.toString(),
			preferTreatment: {
				...(params.preferTreatment || {}),
				COUPON: {
					discountId: this.couponRule.couponUserId,
					discount: disPrice.toString()
				}
			}
		};

		return super.handle({
			...params,
			...result
		});
	}
}