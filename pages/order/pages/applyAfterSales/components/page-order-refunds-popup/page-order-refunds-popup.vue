<script setup>
import { onMounted, ref, watch, computed, nextTick } from 'vue';

import { canENV } from '@/common/utils';

import { consumerCollectCoupon, getOrderShopCouponPage } from '@/server/api';

import { useGoodsStore } from '@/store';

import useConvert from '@/common/useConvert';

const { divTenThousand } = useConvert();

const props = defineProps({
	title: {
		type: String,
		default: '退款原因'
	},
	refundsList: {
		type: Array,
		default: () => []
	}
});

const emit = defineEmits(['confirm']);

const selectBtnPopup = ref();

const pageFormSend = ref(true);
const pageLoading = ref(false);

const groupValue = ref('');

function openPopup() {
	selectBtnPopup.value.open();
}

function closePopup() {
	selectBtnPopup.value.close();
}

function submit() {
	const coupon = props.refundsList.find((item) => item.key === groupValue.value);
	emit('confirm', coupon);
	closePopup();
}

function clickPopupCentent() {
	openPopup();
}

onMounted(() => {});

defineExpose({
	openPopup,
	closePopup
});
</script>

<template>
	<view class="page-select-btn w-full" @touchmove.stop.prevent>
		<view class="w-full" @click="clickPopupCentent">
			<slot></slot>
		</view>
		<my-uv-popup mode="bottom" ref="selectBtnPopup" bgColor="#fff" round="20rpx" class="" closeable close-on-click-overlay>
			<view class="w-full bg-#fff border-top-left-rd-20">
				<template v-if="title">
					<view class="w-full py-40 text-center text-34 font-bold">
						{{ title }}
					</view>
				</template>
				<view class="px-26 pb-50">
					<view class="w-full">
						<template v-if="refundsList.length > 0 || true">
							<view class="w-full">
								<scroll-view scroll-y="true" class="w-full h-500">
									<uv-radio-group v-model="groupValue" placement="column" iconPlacement="right" activeColor="#00B496">
										<template v-for="(item, index) in refundsList" :key="index">
											<view class="w-full mb-30">
												<uv-radio :name="item.key">
													<view class="w-full flex items-center justify-between pr-30">
														<view class="text-28 font-bold">{{ item.name }}</view>
													</view>
												</uv-radio>
											</view>
										</template>
									</uv-radio-group>
								</scroll-view>
							</view>
						</template>
						<template v-else>
							<view class="w-full">
								<uv-empty mode="coupon"></uv-empty>
							</view>
						</template>
					</view>

					<view class="mt-60">
						<uv-button
							color="#00B496"
							text="确认"
							loadingText="设置中..."
							class="w-full mt-0 flex-center"
							custom-style="height: 90rpx;"
							customTextStyle="font-size: 30rpx; font-weight: bold;"
							shape="circle"
							loadingMode="circle"
							:loading="pageLoading"
							:disabled="!pageFormSend"
							@click="submit"
						></uv-button>
					</view>
				</view>
			</view>
		</my-uv-popup>
	</view>
</template>

<style scoped lang="scss">
.page-select-btn {
	.select-btn-box {
		width: 216rpx;
		height: 80rpx;
		border-radius: 30rpx;
		font-weight: 500;
		font-size: 28rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		margin: 0 8rpx 24rpx 8rpx;
		background: #f7f7f7;
		color: #1a1a1b;

		.select-icon {
			display: none;
			position: absolute;
			top: 0;
			right: 0;
		}

		&.active {
			background: #eee8ff;
			color: #5933e9;

			.select-icon {
				display: block;
			}
		}
	}
}
</style>
