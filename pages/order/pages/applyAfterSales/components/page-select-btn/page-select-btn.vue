<script setup>
import { onMounted, ref, watch } from 'vue';

import { canENV } from '@/common/utils';

const props = defineProps({
	dataList: {
		type: Array,
		default: () => []
	},
	maxLength: {
		type: Number,
		default: 1
	},
	minLength: {
		type: Number,
		default: 1
	},
	textKey: {
		type: String,
		default: 'name'
	},
	valueKey: {
		type: String,
		default: 'value'
	},
	title: {
		type: String,
		default: ''
	},
	clickAutoOpen: {
		type: Boolean,
		default: true
	}
});

const selectBtnPopup = ref();

const pageFormSend = ref(false);
const pageLoading = ref(false);

const emit = defineEmits(['confirm']);

const indexs = defineModel('indexs', {
	default: []
});
const items = defineModel('items', {
	default: []
});

function selectItem(item, index) {
	const findIndex = indexs.value.findIndex((i) => i === index);
	if (findIndex >= 0) {
		if (props.minLength === 1 && indexs.value.length === 1) return;
		indexs.value.splice(findIndex, 1);
		items.value.splice(findIndex, 1);
	} else {
		if (indexs.value.length < props.maxLength) {
			indexs.value.push(index);
			items.value.push(item);
		} else if (indexs.value.length === props.maxLength && props.maxLength === 1) {
			indexs.value.splice(0, 1);
			items.value.splice(0, 1);
			indexs.value.push(index);
			items.value.push(item);
		}
	}

	if (indexs.value.length >= props.minLength && indexs.value.length <= props.maxLength) {
		pageFormSend.value = true;
	} else {
		pageFormSend.value = false;
	}
}

function clickPopupCentent() {
	if (props.clickAutoOpen) {
		openPopup();
	}
}

function openPopup() {
	if (indexs.value.length >= props.minLength && indexs.value.length <= props.maxLength) {
		pageFormSend.value = true;
	} else {
		pageFormSend.value = false;
	}

	selectBtnPopup.value.open();
}

function closePopup() {
	selectBtnPopup.value.close();
}

function submit() {
	emit('confirm', {
		indexs: indexs.value,
		items: items.value,
		values: items.value.map((i) => i[props.valueKey]),
		texts: items.value.map((i) => i[props.textKey]),
		textsText: items.value.map((i) => i[props.textKey]).join(','),
		valuesText: items.value.map((i) => i[props.valueKey]).join(',')
	});
	closePopup();
}

watch(
	() => props.dataList,
	() => {
		// if (props.dataList.length > 0 && props.maxLength === 1 && indexs.value.length === 0) {
		// 	indexs.value.push(0)
		// 	items.value.push(props.dataList[0])
		// 	pageFormSend.value = true
		// }
	},
	{
		immediate: true
	}
);

onMounted(() => {});

defineExpose({
	openPopup,
	closePopup
});
</script>

<template>
	<view class="page-select-btn w-full">
		<view class="w-full" @click="clickPopupCentent">
			<slot></slot>
		</view>
		<my-uv-popup mode="bottom" ref="selectBtnPopup" bgColor="#fff" round="20rpx" class="" close-on-click-overlay>
			<view class="w-full bg-#fff border-top-left-rd-20">
				<template v-if="title">
					<view class="w-full py-40 text-center text-34 font-bold">
						{{ title }}
					</view>
				</template>
				<view class="px-26 pb-50">
					<view class="flex flex-wrap items-center">
						<template v-for="(item, index) in dataList">
							<view class="select-btn-box" :class="indexs.findIndex((i) => i === index) >= 0 && 'active'" @click="selectItem(item, index)">
								<view class="">
									{{ item[textKey] }}
								</view>
								<!-- <app-image src="@/static/common/icon_btn_xuanze.png" mode="widthFix" class="select-icon"
									size="28rpx"></app-image> -->
							</view>
						</template>
					</view>

					<!-- <view class="font-500 text-26 text-#00B496">已选（{{ indexs.length }}/{{ maxLength }}）</view> -->

					<view class="mt-60">
						<uv-button
							color="#00B496"
							text="确认"
							loadingText="设置中..."
							class="w-full mt-0 flex-center"
							custom-style="height: 90rpx;"
							customTextStyle="font-size: 30rpx; font-weight: bold;"
							shape="circle"
							loadingMode="circle"
							:loading="pageLoading"
							:disabled="!pageFormSend"
							@click="submit"
						></uv-button>
					</view>
				</view>
			</view>
		</my-uv-popup>
	</view>
</template>

<style scoped lang="scss">
.page-select-btn {
	.select-btn-box {
		width: 216rpx;
		height: 80rpx;
		border-radius: 80rpx;
		font-weight: 500;
		font-size: 28rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		margin: 0 8rpx 24rpx 8rpx;
		background: #f2f3f6;
		color: #323232;
		border: solid 1rpx;
		border-color: #f2f3f6;

		.select-icon {
			display: none;
			position: absolute;
			top: 0;
			right: 0;
		}

		&.active {
			background: #f0fcfc;
			color: #00b496;
			border-color: #00b496;

			.select-icon {
				display: block;
			}
		}
	}
}
</style>
