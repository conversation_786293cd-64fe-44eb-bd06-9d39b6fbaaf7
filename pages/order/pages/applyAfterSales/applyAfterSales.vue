<script setup>
import { onLoad, onReady } from "@dcloudio/uni-app";

import { ref, reactive, computed, unref } from "vue";

import { useUserStore } from "@/store";

import { Decimal } from "decimal.js";

import useConvert from "@/common/useConvert";

import { getShopOrderItem, upload, submitAfsOrder } from "@/server/api";

import { aRefundWhy } from "@/pages/order/common/types/order";

import { canENV, getPriceInfo } from "@/common/utils";

import pageSelectBtn from "./components/page-select-btn/page-select-btn.vue";
import pageOrderRefundsPopup from "./components/page-order-refunds-popup/page-order-refunds-popup";
import { common } from "@/common/images";

const { divTenThousand, mulTenThousand } = useConvert();

const userStore = useUserStore();

const _props = defineProps({
	orderNo: {
		type: String,
		required: true,
	},
	itemId: {
		type: String,
		required: true,
	},
});

const _data = reactive({
	typeList: [],
	typeIndexs: [],
	typeItems: [],
});

const formData = ref({
	orderNo: "",
	type: "",
	typeText: "",
	shopId: "",
	itemId: "",
	reason: "",
	reasonText: "",
	remark: "",
	refundAmount: "",
	// evidences: []
});

const formRules = {
	reason: [
		{
			required: true,
			message: "请选择售后原因",
			trigger: ["blur"],
		},
	],
	remark: [
		// {
		// 	required: true,
		// 	message: '请填写退款说明',
		// 	trigger: ['blur']
		// },
	],
};

const orderInfo = ref();
const pageFormRef = ref();

//原因回显
const why = ref("");

const imageList = ref([]);
const refundsList = ref([]);

async function submit() {
	if (pageFormRef.value) {
		canENV(() => {
			console.log(formData);
		});

		const evidences = [];

		pageFormRef.value.validate().then(async (res) => {
			imageList.value.map((i) => {
				if (i.status === "success") {
					evidences.push(i.url);
				}
			});

			const price = mulTenThousand(formData.value.refundAmount).toString();
			const param = { ...unref(formData), evidences, refundAmount: price, serveGoods: orderInfo.value.serveGoods || 0 };

			uni.showLoading({
				title: "售后申请中",
			});

			const apiRes = await submitAfsOrder(param);
			if (apiRes && apiRes.apiStatus) {
				uni.navigateBack();

				uni.hideLoading();
			}
		});
	}
}

// 删除图片
function deletePic(event) {
	imageList.value.splice(event.index, 1);
}

// 新增图片
async function afterRead(event) {
	// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
	let lists = [].concat(event.file);
	let fileListLen = imageList.value.length;
	lists.map((item) => {
		imageList.value.push({
			...item,
			status: "uploading",
			message: "上传中",
		});
	});
	for (let i = 0; i < lists.length; i++) {
		let status = "failed";
		let fileUrl = lists[i].url;
		try {
			const result = await upload(lists[i].url);
			if (result.apiStatus) {
				status = "success";
				fileUrl = result.data;
			}
		} catch (error) {
			//TODO handle the exception
		}
		let item = imageList.value[fileListLen];
		imageList.value.splice(
			fileListLen,
			1,
			Object.assign(item, {
				status,
				message: status === "failed" ? "上传失败" : "",
				url: fileUrl,
			})
		);
		fileListLen += 1;
	}
}

function changeType(e) {
	formData.value.type = e.items[0].value;
	formData.value.typeText = e.items[0].name;

	initRefundsList(formData.value.type);
}

function initTypeList() {
	//是否未发货
	const notDelivery = orderInfo.value.packageStatus === "WAITING_FOR_DELIVER";
	if (notDelivery || orderInfo.value.serveGoods === 1) {
		_data.typeList = [
			{
				name: "仅退款",
				value: "REFUND",
			},
		];
	} else {
		_data.typeList = [
			{
				name: "退货退款",
				value: "RETURN_REFUND",
			},
			{
				name: "仅退款",
				value: "REFUND",
			},
		];
	}

	_data.typeIndexs.push(0);
	_data.typeItems.push(_data.typeList[0]);

	changeType({ items: _data.typeItems });
}

/**
 * 退款原因
 */
function initRefundsList(type) {
	refundsList.value = [];
	formData.value.reason = "";
	formData.value.reasonText = "";
	const isReturnRefund = type === "RETURN_REFUND";
	const undelivered = orderInfo.value?.packageStatus === "WAITING_FOR_RECEIVE";
	for (const key in aRefundWhy) {
		if (aRefundWhy[key].isReturnRefund === isReturnRefund) {
			refundsList.value.push({ name: aRefundWhy[key].title, key, undelivered: aRefundWhy[key].undelivered });
			if (!isReturnRefund && !undelivered) {
				// 退货退款 并且没有发货
				refundsList.value = refundsList.value.filter((item) => item.undelivered);
			}
		}
	}
}

// 退款金额 没发货加上运费 发货后不计运费
const maxPrice = computed(() => {
	if (orderInfo.value) {
		const { dealPrice, num, fixPrice, freightPrice } = orderInfo.value;
		const tPrice = new Decimal(dealPrice).mul(num).add(fixPrice).add(freightPrice);
		return divTenThousand(tPrice).toNumber() * 10000;
	} else {
		return 0;
	}
});

async function loadData() {
	const res = await getShopOrderItem({
		orderNo: _props.orderNo,
		itemId: _props.itemId,
	}).catch((err) => {});

	if (res && res.apiStatus) {
		const { data } = res;
		orderInfo.value = data;
		formData.value.orderNo = _props.orderNo;
		// formData.value.type = type;
		formData.value.itemId = data.id;
		formData.value.shopId = data.shopId;
		formData.value.refundAmount = divTenThousand(new Decimal(data.dealPrice).mul(data.num).add(data.fixPrice)).toString();

		initTypeList();
	}
}

function confirmRefunds(e) {
	if (e) {
		formData.value.reason = e.key;
		formData.value.reasonText = e.name;
	}
}

onLoad(() => {
	loadData();
});

onReady(() => {
	if (pageFormRef.value) pageFormRef.value.setRules(formRules);
});

const formStyle = ref({
	labelStyle: {
		fontSize: "30rpx",
		fontWeight: "bold",
	},
	inputStyle: {
		height: "98rpx",
		padding: "0rpx 0rpx",
		borderBottom: `solid 1rpx #E9EDF1`,
	},
	labelStyle2: {
		fontSize: "26rpx",
		fontWeight: "bold",
		color: "#1152D7",
	},
	inputStyle2: {
		height: "88rpx",
		borderRadius: "10rpx",
		background: "#fff",
		padding: "0rpx 30rpx",
	},
	inputStyle3: {
		borderRadius: "16rpx",
		background: "#F2F6FB",
		padding: "25rpx 30rpx",
	},
	inputStyle4: {
		borderRadius: "16rpx",
		background: "#fff",
		padding: "25rpx 30rpx",
	},
	inputStyle5: {
		height: "105rpx",
		borderRadius: "16rpx",
		background: "#F2F6FB",
		padding: "0rpx 30rpx",
	},
	labelStyle6: {
		fontSize: "28rpx",
		fontWeight: "500",
		color: "#1A1A1B",
	},
	inputStyle6: {
		fontWeight: "bold",
		fontSize: "28rpx",
	},
	labelStyle7: {
		fontSize: "28rpx",
		fontWeight: "500",
		color: "#1A1A1B",
	},
	inputStyle7: {
		fontWeight: "500",
		fontSize: "30rpx",
		padding: 0,
	},
	inputStyle8: {
		// borderRadius: '16rpx',
		background: "#F3F4F7",
		padding: "25rpx 30rpx",
		borderRadius: "53rpx",
	},
	inputPlaceholderStyle: `font-size: 30rpx; font-weight: 500; color: #AEAEB4;`,
	inputPlaceholderStyle3: `font-size: 26rpx; font-weight: 500; color: #B1B6BC;`,
	inputPlaceholderStyle4: `font-size: 30rpx; font-weight: 500; color: #B1B6BC;`,
	inputPlaceholderStyle6: `font-size: 28rpx; font-weight: 500; color: #A7ACB7;`,
	inputPlaceholderStyle7: `font-size: 30rpx; font-weight: 500; color: #93939C;`,
});
</script>

<template>
	<view class="apply-after-sales page-main bg-#F0F3F7 pb-150">
		<view class="w-full py-30 px-20">
			<view class="w-full border-rd-20 bg-#FFFFFF px-30">
				<uv-form :model="formData" :rules="formRules" :labelStyle="formStyle.labelStyle6" label-width="150rpx" labelPosition="left" errorType="toast" ref="pageFormRef">
					<!-- 售后类型 -->
					<uv-form-item label="售后类型" prop="type" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
						<pageSelectBtn :dataList="_data.typeList" v-model:indexs="_data.typeIndexs" v-model:items="_data.typeItems" :maxLength="1" :minLength="1" title="售后类型" @confirm="changeType">
							<uv-input v-model="formData.typeText" placeholder="请选择售后类型" :placeholderStyle="formStyle.inputPlaceholderStyle6" :customStyle="{ ...formStyle.inputStyle6 }" fontSize="28rpx" border="none" type="text" inputAlign="right" readonly></uv-input>
						</pageSelectBtn>

						<template #right>
							<view class="flex items-center">
								<app-image :src="common.iconRightHui" size="22" mr="6" mode=""></app-image>
							</view>
						</template>
					</uv-form-item>

					<!-- 退款原因 -->
					<uv-form-item label="退款原因" prop="reason" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
						<pageOrderRefundsPopup :refundsList="refundsList" @confirm="confirmRefunds">
							<uv-input v-model="formData.reasonText" placeholder="请选择退款原因" :placeholderStyle="formStyle.inputPlaceholderStyle6" :customStyle="{ ...formStyle.inputStyle6 }" fontSize="28rpx" border="none" type="text" inputAlign="right" readonly></uv-input>
						</pageOrderRefundsPopup>

						<template #right>
							<view class="flex items-center">
								<app-image :src="common.iconRightHui" size="22" mr="6" mode=""></app-image>
							</view>
						</template>
					</uv-form-item>

					<!-- 退款金额 -->
					<uv-form-item label="退款金额" prop="name" class="w-full" :customStyle="{ padding: '42rpx 0' }">
						<template #right>
							<view class="flex items-center">
								<view class="text-#FC3F33 text-30 font-bold">￥{{ getPriceInfo(maxPrice).integer }}.{{ getPriceInfo(maxPrice).decimalText }}</view>

								<template v-if="orderInfo?.freightPrice">
									<view class="text-22 ml-10 text-#999">(运费 ￥{{ getPriceInfo(orderInfo?.freightPrice).integer }}.{{ getPriceInfo(orderInfo?.freightPrice).decimalText }})</view>
								</template>
							</view>
						</template>
					</uv-form-item>
				</uv-form>
			</view>

			<view class="w-full border-rd-20 bg-#FFFFFF px-30 py-30 mb-20 mt-20">
				<view class="text-30 font-bold pb-20">补充描述和凭证</view>
				<view class="w-full">
					<textarea v-model="formData.remark" cols="30" rows="5" confirm-type="done" :maxlength="200" auto-height placeholder="补充描述，有利于商家更好的处理售后问题" class="min-h-200 w-full text-28" placeholderStyle="color: #A7ACB7; fontSize: 28rpx"></textarea>

					<view class="w-full">
						<uv-upload :fileList="imageList" name="file" multiple :maxCount="5" :previewFullImage="true" @afterRead="afterRead" @delete="deletePic"></uv-upload>
					</view>
				</view>
			</view>
		</view>

		<view class="w-full px-30 pt-15 bg-#fff fixed left-0 bottom-0 border-t-1 border-t-solid border-t-#EDEEF0 flex items-center">
			<view class="flex-1">
				<uv-button @click="submit()" color="#00B496" text="提交" class="flex-1 h-88" custom-style="border-radius: 44rpx;" customTextStyle="font-size: 30rpx; color: #fff; font-weight: bold;"></uv-button>

				<app-safeAreaBottom></app-safeAreaBottom>
			</view>
		</view>
	</view>
</template>

<style lang="scss" scoped>
.apply-after-sales {
}
</style>
