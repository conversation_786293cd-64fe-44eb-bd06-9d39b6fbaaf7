<script setup>
import { ref, reactive, computed } from "vue";

import { onLoad } from "@dcloudio/uni-app";

import {
	getDeliveryPackage, // 获取包裹列表
	getLogisticsTrajectoryByWaybillNo, // 获取包裹物流信息
} from "@/server/api";

import { getPriceInfo } from "@/common/utils";

import { useOrderStore } from "@/store";

const props = defineProps({
	orderNo: {
		type: String,
		required: true,
	},
	shopOrderNo: {
		type: String,
		required: true,
	},
	shopId: {
		type: String,
		required: true,
	},
});

const pageData = reactive({
	list: [], // 物流信息
	orderNo: "",
	shopOrderNo: "",
	shopId: "",
	packageList: [],
	subCurrent: 0,
	orderInfoItem: {},
});

const subList = computed(() => {
	let list = pageData.packageList.map((elem, index) => {
		return `包裹${index + 1}`;
	});
	return [...list];
});

const packageItem = computed(() => {
	if (pageData.packageList.length > 0) {
		return pageData.packageList[pageData.subCurrent];
	}
	return {};
});

onLoad((options) => {
	pageData.orderNo = options.orderNo;
	pageData.shopOrderNo = options.shopOrderNo;
	pageData.shopId = options.shopId;
	initData();
});

function initData() {
	uni.showLoading({
		title: "加载中...",
		mask: true,
	});

	pageData.orderInfoItem = useOrderStore().orderInfoItem;

	// 获取包裹列表
	getDeliveryPackage({ orderNo: pageData.orderNo, shopOrderNo: pageData.shopOrderNo })
		.then((res) => {
			if (res.apiStatus) {
				pageData.packageList = res.data;
				const items = pageData.packageList[0];
				// uni.hideLoading();
				// return
				getLogisticsInfo(items.expressCompanyCode, items.expressNo, items.receiverMobile.slice(-4), pageData.shopId);
			} else {
				uni.hideLoading();
			}
		})
		.catch(() => {
			uni.hideLoading();
		});
}

// 获取包裹物流信息
function getLogisticsInfo(expressCompanyCode, expressNo, receiverMobile, shopId) {
	if (pageData.list[pageData.subCurrent] && pageData.list[pageData.subCurrent]?.length > 0) {
		return;
	}
	getLogisticsTrajectoryByWaybillNo({
		companyCode: expressCompanyCode,
		waybillNo: expressNo,
		recipientsPhone: receiverMobile,
		shopId: shopId,
	})
		.then((res) => {
			uni.hideLoading();
			if (res.code == 200) {
				pageData.list[pageData.subCurrent] = res.data.data
					.map((elem) => {
						return {
							title: elem.status,
							desc: elem.context,
							time: elem.time,
						};
					})
					.reverse();
			}
		})
		.catch(() => {
			uni.hideLoading();
		});
}

function onCopy(text) {
	uni.setClipboardData({
		data: text,
		showToast: true,
	});
}

function subChange(e) {
	pageData.subCurrent = e;
	const items = pageData.packageList[e];
	getLogisticsInfo(items.expressCompanyCode, items.expressNo, items.receiverMobile.slice(-4), pageData.shopId);
}
</script>

<template>
	<app-layout>
		<z-paging>
			<view class="p-30" v-if="pageData.packageList.length > 0">
				<uv-subsection v-if="subList.length > 1" :list="subList" :current="pageData.subCurrent" custom-style="height: 64rpx;border-radius: 32rpx;padding: 4rpx;" custom-item-style="height: 56rpx;border-radius: 28rpx;" bg-color="#72BFB9" inactiveColor="#fff" activeColor="#222" @change="subChange"></uv-subsection>
				<view class="p-26 bg-#E8FBF9 border-rd-16 mt-20">
					<view class="flex justify-between items-center">
						<view class="flex justify-start items-center">
							<!-- <image src="/static/logo.png" class="w-40 h-40 mr-12" mode=""></image> -->
							<view class="text-28 text-#222 font-bold mr-8">{{ packageItem.expressCompanyName }} {{ packageItem.expressNo }}</view>
							<image :src="common.iconCopyBai" class="w-20 h-20" mode="" @click="onCopy(packageItem.expressNo)"></image>
						</view>
					</view>
					<view class="text-24 text-#777 font-500 mt-20">{{ packageItem.receiverName }} {{ packageItem.receiverMobile }} {{ packageItem.receiverAddress }}</view>

					<template v-if="Array.isArray(pageData.orderInfoItem.shopOrderItems)">
						<template v-for="(goods, index) in pageData.orderInfoItem.shopOrderItems" :key="index">
							<view class="flex justify-start items-center p-15 bg-#FBFFFF border-rd-10 mt-20">
								<app-image :src="goods.image" class="w-80 h-80 min-w-80 border-rd-10 mr-20" mode=""></app-image>
								<view class="flex-1">
									<view class="flex justify-between items-center">
										<view class="text-26 text-#222 font-500">{{ goods.productName }}</view>
										<view class="text-26 text-#555 font-500">
											<text class="text-24">×{{ goods.num }}</text>
										</view>
									</view>
									<view class="flex justify-between items-center mt-10">
										<view class="text-26 text-#888 font-500">{{ goods.specs.join(" ") }}</view>
										<view class="text-24 text-#323232 font-bold">合计￥{{ getPriceInfo(goods.salePrice).integer }}.{{ getPriceInfo(goods.salePrice).decimalText }}</view>
									</view>
								</view>
							</view>
						</template>
					</template>
				</view>
				<view class="mt-30">
					<uni-steps v-if="pageData.list[pageData.subCurrent]?.length > 0" :key="pageData.subCurrent" :options="pageData.list[pageData.subCurrent]" active-color="#00B698" deactive-color="#222" direction="column" />
				</view>
			</view>
			<template v-if="pageData.packageList.length === 0">
				<uv-empty marginTop="200rpx"></uv-empty>
			</template>
		</z-paging>
	</app-layout>
</template>

<style lang="scss" scoped></style>
