import Decimal from 'decimal.js';

export default class CalculateHandler {
	nextHandler;
	totalPrice = '0';
	productInfo;

	constructor(productInfo) {
		this.productInfo = productInfo;
		this.totalPrice = productInfo
			.reduce((pre, cur) => {
				return pre + new Decimal(cur.productPrice).mul(cur.quantity).toNumber();
			}, 0)
			.toString();
	}

	setNext(handler) {
		this.nextHandler = handler;
		return handler;
	}

	handle(params) {
		if (this.nextHandler) {
			return this.nextHandler.handle(params);
		}
		return params;
	}
}