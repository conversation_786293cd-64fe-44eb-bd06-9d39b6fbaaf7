<script setup>
import { onLoad, onShow, onUnload, onReady } from "@dcloudio/uni-app";

import dayjs from "dayjs";

import { ref, reactive, computed, nextTick, watch, unref } from "vue";

import { Decimal } from "decimal.js";

import { useUserStore, useOrderStore, useGoodsStore } from "@/store";

import { canENV, route, getPriceInfo } from "@/common/utils";

import { getStoreDistanceList, toConfirmOrderValid, getDefaultAddressDetails, getBudget, getOrderCreateConditions, getBasicsSet, getOrderPayment, getUserBalance, getRebatePayBalance, generateOrders, getOrderPayPage, getOrderIsPaySuccess } from "@/server/api";

import { EXPRESS_CODE, INTRA_CITY_DISTRIBUTION_CODE_1, INTRA_CITY_DISTRIBUTION_CODE_2, DISTRIBUTION, serviceHandler, ActivityType, DiscountType, PAY_TYPE, ORDERPAYMENT } from "@/common/types/goods";

import { deepClone, sleep } from "@/uni_modules/uv-ui-tools/libs/function/index.js";
import calculate from "./hooks/calculate";
import { common } from "@/common/images";

const userStore = useUserStore();
const orderStore = useOrderStore();

const { addDiscount, delDiscount, activeResume } = calculate();

const $props = defineProps({
	source: {
		type: String,
		default: "PRODUCT",
	},
});

const $state = reactive({
	pageLoading: false,
	pageInit: false,
	notLoadAddress: false,
	notice: "",
});

const commodityList = ref([]);
const submitForm = reactive({
	receiver: {
		name: "",
		mobile: "",
		area: [],
		address: "",
		isDefault: false,
		location: {
			type: "Point",
			coordinates: [0, 0],
		},
	},
	shopPackages: [],
	source: "CART",
	orderType: "COMMON",
	activity: {},
	discounts: {},
	extra: {},
	distributionMode: DISTRIBUTION.EXPRESS,

	extendReceive: {
		caredUserid: "", // 被照顾人id
		startTime: "", // 开始时间(上门时间/就诊时间)
		hospitals: "", // 就诊医院
		caredUsername: "", // 被服务人名称
		caredMobile: "", // 被服务人电话
		hospitalsAddress: "", // 就诊医院详细地址
		hospitalsLocation: {
			type: "Point",
			coordinates: [0, 0],
		}, // 就诊医院定位
	},
});

const storePointRef = ref(null);
const pointList = ref([]);
const currentPoint = ref();
const storesParam = reactive({
	distributionMode: DISTRIBUTION.EXPRESS,
	shopStoreId: "",
	packUpTime: "",
});

//订单明细
const budget = ref({
	total: 0, // 商品总价
	shopDiscount: 0, // 优惠
	platformDiscount: 0, // 优惠
	memberDiscount: 0, // 会员优惠
	freight: 0, // 总运费
	payAmount: 0, // 实付金额
	shopFull: {}, // 满减 优惠的金额 key:店铺 id，value 满减优惠金额
	shopFreight: {}, // 店铺运费 key:店铺 id，value 运费
});

const handleGetPoint = (e) => {
	currentPoint.value = e;
	storesParam.shopStoreId = currentPoint.value.id;
};

// 优惠券
const allCoupons = ref(new Map());
const computedCoupon = (item) => {
	return allCoupons.value.has(item.shopId) ? allCoupons.value.get(item.shopId) : {};
};
const currentCouponPopupShopId = ref("0");
const productAmounts = ref([]);
const orderCouponPopupRef = ref();

const currentShopProducts = ref([]);

//同城配送运费
const intraFalse = ref(false);
const isIntar = computed(() => submitForm.distributionMode === DISTRIBUTION.INTRA_CITY_DISTRIBUTION);

const orderInfo = computed(() => orderStore.createOrderInfo);
const orderCrdItem = ref();

const paySelectRef = ref();
const showPay = ref(false);
const payLoading = ref(false);
const payInfo = reactive({
	price: 0,
	balanceTotalShow: true,
	balanceTotal: 0,
	timeout: 0,
	timeout: 0,
});

/**
 * 选择地址
 */
function chooseAddress() {
	route({
		type: "to",
		url: "/pages/my/pages/address/addressGoods",
		params: {
			select: 1,
		},
		events: {
			selectAddress(data) {
				$state.notLoadAddress = true;
				submitForm.receiver = data;
			},
		},
	});
}

function handleQueryCoupon() {
	currentShopProducts.value = [];
	const productAmounts = [
		{
			productId: "0",
			amount: budget.value.total,
		},
	];
	QueryCoupons("0", productAmounts);
}
async function QueryCoupons(shopId, productAmount = []) {
	currentCouponPopupShopId.value = shopId;
	productAmounts.value = productAmount;
	if (orderCouponPopupRef.value) orderCouponPopupRef.value.openPopup();
}
function queryStoreCoupon(shopProducts) {
	const map = new Map();
	shopProducts.products.forEach((item) => {
		const currentPrice = map.get(item.productId);
		const currentTotalPrice = new Decimal(item.salePrice).mul(new Decimal(item.num));
		map.set(item.productId, currentPrice ? currentPrice.add(currentTotalPrice) : currentTotalPrice);
	});
	let arr = [];
	for (const iterator of map) {
		arr.push({ productId: iterator[0], amount: iterator[1].toString() });
	}
	return arr;
}
function openChooseCoupon(item) {
	if (!orderStore.orderBenefit.coupon) {
		uni.showToast({ title: "当前商品不参与优惠券优惠哦", icon: "none" });
		return;
	}

	currentShopProducts.value = item.products.map((priduct) => ({
		id: priduct.productId,
		image: priduct.image,
	}));
	QueryCoupons(item.shopId, queryStoreCoupon(item));
}

function chooseCoupon(coupon, shopId) {
	if (!orderStore.orderBenefit.coupon || !coupon) {
		delDiscount(shopId, shopId === "0" ? "addon-platform-coupon" : "addon-coupon");
		orderStore.setDiscount(activeResume());
		allCoupons.value.delete(shopId);
		return;
	}

	if (shopId !== "0") {
		const choosedProduct = commodityList.value.find((item) => item.shopId === shopId)?.products || [];
		const tempArr = choosedProduct.map((item) => {
			return {
				shopId,
				productId: item.productId,
				productPrice: item.salePrice,
				quantity: item.num,
			};
		});
		addDiscount(shopId, {
			name: "addon-coupon",
			productInfoArr: tempArr,
			rule: coupon,
			priority: 1,
		});
	} else {
		const allProducts = commodityList.value.map((item) => item.products).flat(1);
		const tempArr = allProducts.map((item) => {
			return {
				shopId,
				productId: item.productId,
				productPrice: item.salePrice,
				quantity: item.num,
			};
		});
		addDiscount("0", {
			name: "addon-platform-coupon",
			productInfoArr: tempArr,
			rule: coupon,
			priority: 1,
		});
	}
	orderStore.setDiscount(activeResume());
	allCoupons.value.set(shopId, coupon);
}

async function initData() {
	await initOrderInfo();

	if (orderInfo.value.length) {
		await initDefaultAddress();

		await loadBudget();
	}
}

async function initOrderInfo() {
	// commodityList.value = [];

	const storageCommodity = deepClone(orderInfo.value);

	if (storageCommodity.length) {
		storesParam.distributionMode = storageCommodity[0].distributionMode;
		submitForm.distributionMode = storageCommodity[0].distributionMode;

		if (storesParam.distributionMode === DISTRIBUTION.SHOP_STORE || storesParam.distributionMode === DISTRIBUTION.VIRTUAL) {
			submitForm.receiver = void 0;
			if (storesParam.distributionMode === DISTRIBUTION.SHOP_STORE) {
				initStoreDistanceList(storageCommodity[0].shopId);
			}
		}
		const allProducts = storageCommodity.map((item) => item.products).flat(1);
		const tempArr = allProducts.map((item) => {
			return {
				shopId: "0",
				productId: item.productId,
				productPrice: item.salePrice,
				quantity: item.num,
				productFeaturesValue: item.productFeaturesValue,
			};
		});

		addDiscount("0", {
			name: "addon-member",
			productInfoArr: tempArr,
			rule: null,
			priority: 3,
		});

		nextTick(() => {
			commodityList.value = storageCommodity.map((item) => ({ ...item, coupon: "" }));

			integrationData(storageCommodity);

			orderStore.setDiscount(activeResume());
		});
	} else {
		uni.showToast({
			icon: "none",
			title: "获取商品数据失败",
			duration: 3000,
			success: () => {
				let time = setTimeout(() => {
					uni.navigateBack({
						delta: 1,
					});
					clearTimeout(time);
				}, 2000);
			},
		});
	}
}

async function initDefaultAddress() {
	if ($state.notLoadAddress) {
		$state.notLoadAddress = false;
		return false;
	}

	if (["VIRTUAL", "SHOP_STORE"].includes(storesParam.distributionMode)) return false;

	let receiver = {
		name: "",
		mobile: "",
		area: [],
		address: "",
		location: {
			type: "Point",
			coordinates: [0],
		},
		isDefault: false,
	};

	submitForm.receiver = receiver;

	try {
		const { code, data, apiStatus } = await getDefaultAddressDetails({
			serverType: orderInfo.value[0].serveGoods || 0, // 0->商城 1->服务
		});
		if (apiStatus) {
			if (data?.area?.length) {
				receiver = data;

				submitForm.receiver = receiver;

				return data;
			} else {
				// uni.showToast({ icon: 'none', title: '请添加收货地址' });
				return false;
			}
		} else {
			return false;
		}
	} catch (error) {
		//TODO handle the exception
		return false;
	}
}

async function changeGoodsNum(e) {
	const { value, goodsIndex, commodityIndex } = e;
	const olOrderInfo = deepClone(orderInfo.value);
	const olNum = olOrderInfo[commodityIndex].products[goodsIndex].num;
	olOrderInfo[commodityIndex].products[goodsIndex].num = value;
	commodityList.value[commodityIndex].products[goodsIndex].num = value;

	const res = await toConfirmOrderValid(olOrderInfo);

	if (!res.success) {
		uni.showModal({
			title: res.data.content ? res.data.title : "提示",
			content: res.data.content || res.data.title,
			showCancel: false,
			cancelText: "我知道了",
		});

		olOrderInfo[commodityIndex].products[goodsIndex].num = olNum;
	}

	orderStore.changeCreateOrderInfo(olOrderInfo);

	initOrderInfo();
}

/**
 * 整理提交表单格式
 */
function integrationData(shopProducsList) {
	submitForm.shopPackages = shopProducsList.map((shopProducs) => {
		const { shopId, shopName, shopLogo, products } = shopProducs;
		return {
			id: shopId,
			name: shopName,
			logo: shopLogo,
			remark: {},
			products: products.map(({ id, skuId, num, productFeaturesValue }) => ({ id, skuId, num, productFeaturesValue })),
		};
	});
}

function checkOrderCreation(data) {
	return new Promise((resolve, reject) => {
		loopCheckOrderCreation(1, data.orderNo, resolve, reject);
	});
}
function loopCheckOrderCreation(count, orderNo, resolve, reject) {
	console.log(count);
	if (count >= 20) {
		reject();
		return;
	}
	getOrderCreateConditions({ orderNo }).then(({ code, data, apiStatus }) => {
		if (code !== 200 || !data) {
			sleep(550).then(() => {
				loopCheckOrderCreation(count + 1, orderNo, resolve, reject);
			});
			return;
		}
		resolve();
	});
}

async function submit() {
	if (!submitForm.receiver?.area?.length && !["VIRTUAL", "SHOP_STORE"].includes(storesParam.distributionMode)) {
		uni.showToast({ title: "请先选择地址", icon: "none" });
		return;
	}

	const remarkList = [];

	let validate = true;
	if (orderCrdItem.value.length > 0) {
		for (let [index, item] of orderCrdItem.value.entries()) {
			if (item.shopRemarkForm && item.shopRemarkForm.remarkForm) {
				try {
					await item.shopRemarkForm.remarkForm.validate();

					let remarkItem = void 0;

					if (item.shopRemarkForm.$data?.formList && item.shopRemarkForm.$data?.formList.length) {
						remarkItem = {};
						for (let i of item.shopRemarkForm.$data.formList) {
							remarkItem[i.key] = item.shopRemarkForm.$data.formData[i.formKey];
						}
					}

					remarkList.push(remarkItem);
				} catch (error) {
					//TODO handle the exception

					validate = false;

					break;
				}
			}
		}
	}

	canENV(() => {
		console.log("备注校验 =>", validate);
	});

	if (validate) {
		// #ifdef  MP-WEIXIN
		// 调起客户端小程序订阅消息界面，返回用户订阅消息的操作结果。当用户勾选了订阅面板中的“总是保持以上选择，不再询问”时，模板消息会被添加到用户的小程序设置页。
		try {
			const res = await uni.requestSubscribeMessage({
				tmplIds: [
					"HA8G6APZuXamC-W8WUeFJNyEGaSH8og0Oxb38MprqiQ", // 发货
					"Yjtuq-FfzGWD9SR_XNWQWIhEXW34TVXAJABa-aHavzo", // 完成

					// 'IjhUqEhCGqqpYQ6i_8suidaGqNYrwHMD5Nk-fl7VTac', // 订单确认
					// 'IjhUqEhCGqqpYQ6i_8suieMstG7SoJAOXp3LHnGA9_Y', // 价格变化
					// 'fS1bkoRJpogM86SxOte7XZGN_YSWYqg4QeY_opB0LUA', // 分配人员
				],
			});
		} catch (error) {
			//TODO handle the exception
		}
		// #endif

		$state.pageLoading = true;

		submitForm.activity = orderStore.activity;
		submitForm.discounts = orderStore.discounts;
		submitForm.orderType = orderStore.orderType;
		submitForm.extra = storesParam;

		const cloneSubmitForm = deepClone(submitForm);

		cloneSubmitForm.receiver = {
			...cloneSubmitForm.receiver,
		};

		cloneSubmitForm.shopPackages.forEach((shopPackages, index) => {
			shopPackages.products.forEach((product) => {
				const valueList = product.productFeaturesValue;
				const valueMap = {};
				valueList?.forEach((value) => {
					valueMap[value.id] = value?.featureValues?.map((item) => item.featureValueId);
				});
				product.productFeaturesValue = valueMap;
			});
			cloneSubmitForm.shopPackages[index].remark = remarkList[index];
		});

		if (cloneSubmitForm.extra?.distributionMode === DISTRIBUTION.SHOP_STORE && !cloneSubmitForm.extra?.packUpTime) {
			return uni.showToast({ title: "请选择提货时间", icon: "none" });
		}

		try {
			const { data, code, msg } = await generateOrders(cloneSubmitForm, {
				custom: {
					toast: false,
				},
			});

			switch (code) {
				case 200:
					uni.showLoading({
						title: "处理中...",
					});
					checkOrderCreation(data)
						.then(() => {
							uni.hideLoading();

							//是否不需要支付
							const noNeedPay = new Decimal(budget.value.payAmount).cmp(0) < 1;

							if (noNeedPay) {
								paySuccess(data.orderNo, "ORDER");
								return;
							}

							openPay({
								orderNo: data.orderNo,
								orderType: PAY_TYPE.ORDER,
								extra: "{}",
							});

							$state.pageLoading = false;

							// if (Object.keys(activity.value).length) {
							// 	const extra = encodeURIComponent(JSON.stringify({ ...data.extra, activeType: submitForm.orderType }));

							// 	if (noNeedPay) {
							// 		const redirectToObj = {
							// 			BARGAIN: ``,
							// 			TEAM: ``,
							// 			SPIKE: ``,
							// 			PACKAGE: ``,
							// 			COMMON: ``
							// 		};

							// 		uni.redirectTo({
							// 			url: redirectToObj[submitForm.orderType]
							// 		});
							// 		return;
							// 	}

							// 	openPay(data.orderNo, PAY_TYPE.ORDER, extra);
							// } else {
							// 	if (noNeedPay) {
							// 		paySuccess(data.orderNo, 'ORDER');
							// 		return;
							// 	}

							// 	openPay(data.orderNo, PAY_TYPE.ORDER, extra);
							// }
						})
						.catch(() => {
							uni.hideLoading();
							$state.pageLoading = false;
						});
					return;
				case 30000:
					//收货人不能为空
					errGoodsId.value = errorOrderProcessing(commodityList.value, data.orderNo, msg, " 收货人不能为空");
					break;
				case 30001:
					// 物流公司不能为空
					errGoodsId.value = errorOrderProcessing(commodityList.value, data.orderNo, msg, " 物流校验失败");
					break;
				case 30002:
					// 物流公司名称与物流公司代码不能为空
					errGoodsId.value = errorOrderProcessing(commodityList.value, data.orderNo, msg, " 物流校验失败");
					break;
				case 30003:
					// 快递单号不能为空
					errGoodsId.value = errorOrderProcessing(commodityList.value, data.orderNo, msg, " 快递单号不能为空");
					break;
				case 30004:
					// 发货地址不能为空
					errGoodsId.value = errorOrderProcessing(commodityList.value, data.orderNo, msg, " 发货地址不能为空");
					break;
				case 30005:
					// 店铺表单校验失败
					errGoodsId.value = errorOrderProcessing(commodityList.value, data.orderNo, msg, " 店铺表单校验失败");
					break;
				case 30006:
					// 商品不可用
					errGoodsId.value = errorOrderProcessing(commodityList.value, data.orderNo, msg, " 商品不可用");
					break;
				case 30007:
					// 商品库存不足
					errGoodsId.value = errorOrderProcessing(commodityList.value, data.orderNo, msg, " 商品库存不足");
					break;
				case 30008:
					// 商品超过限购次数
					errGoodsId.value = errorOrderProcessing(commodityList.value, data.orderNo, msg, " 商品超限购");
					break;

				default:
					uni.showToast({
						icon: "none",
						title: `${msg ? msg : "订单提交失败"}`,
					});
					break;
			}

			setTimeout(() => uni.navigateBack(), 500);

			$state.pageLoading = false;
		} catch (e) {
			$state.pageLoading = false;
		}
	}
}

const payExtra = ref();
const payFrom = ref(PAY_TYPE.ORDER);
const orderNumber = ref();
async function openPay(params) {
	uni.showLoading({
		title: "加载中...",
	});
	await initUserBalance();
	if (params.extra && JSON.stringify(params.extra).length) {
		//记录额外参数
		payExtra.value = JSON.parse(decodeURIComponent(params.extra));
	}

	if (params.orderType === PAY_TYPE.ORDER) {
		initFromOrder(params);
	}
}

async function initFromOrder(params) {
	payFrom.value = PAY_TYPE.ORDER;
	orderNumber.value = params.orderNo;

	try {
		const { data, code, apiStatus } = await getOrderPayment(
			{ orderNo: orderNumber.value },
			{
				custom: {
					toast: true,
				},
			}
		);
		switch (code) {
			case 30012:
				paySuccess(orderNumber.value, PAY_TYPE.ORDE);
				break;
			case 30011: //订单不存在
			case 30013: // 订单已无法支付
				//重定向到订单列表
				uni.hideLoading();
				break;
			case 200:
				payInfo.timeout = data.order.timeout.payTimeout * 1000 - (dayjs().valueOf() - dayjs(data.createTime).valueOf());
				payInfo.price = `${getPriceInfo(data.payAmount).integer}.${getPriceInfo(data.payAmount).decimalText}`;
				initRebateBalance(data.payAmount);
				uni.hideLoading();
				showPay.value = true;
				break;
			default:
				// uni.showToast({ icon: 'none', title: '获取支付金额失败' });
				// initRebateBalance(data.payAmount);
				break;
		}
	} catch (error) {
		//TODO handle the exception
	}
}

async function initUserBalance() {
	const { code, msg, data, apiStatus } = await getUserBalance();
	payInfo.balanceTotalShow = apiStatus;
	payInfo.balanceTotal = apiStatus ? `${getPriceInfo(data).integer}.${getPriceInfo(data).decimalText}` : "";
}

function initRebateBalance(payAmount) {
	if (payFrom.value !== "ORDER") {
		return;
	}
	// getRebatePayBalance({ payAmount, orderNo: orderNumber.value });
}

async function paySubmit(e) {
	if (e.type === "balance") {
		const { code, data, msg, apiStatus } = await getOrderPayPage({
			orderNo: unref(orderNumber),
			payType: ORDERPAYMENT.BALANCE,
			rebate: false,
		}).catch((e) => {
			payLoading.value = false;
		});

		if (!apiStatus) {
			payError(unref(orderNumber), payFrom.value);
		}

		orderNumber.value = data.outTradeNo;
		loopCheckPayStatus();
	}

	if (e.type === "wechat") {
		const { code, data, msg, apiStatus } = await getOrderPayPage({
			orderNo: unref(orderNumber),
			payType: ORDERPAYMENT.BALANCE,
			rebate: false,
		}).catch((e) => {
			payLoading.value = false;
		});

		if (!apiStatus) {
			payError(unref(orderNumber), payFrom.value);
		}

		orderNumber.value = data.outTradeNo;

		requestPayment(e.payItem.payment, data.data);
	}

	if (e.type === "alipay") {
		const { code, data, msg, apiStatus } = await getOrderPayPage({
			orderNo: unref(orderNumber),
			payType: ORDERPAYMENT.BALANCE,
			rebate: false,
		}).catch((e) => {
			payLoading.value = false;
		});

		if (!apiStatus) {
			payError(unref(orderNumber), payFrom.value);
		}

		orderNumber.value = data.outTradeNo;
		loopCheckPayStatus();
	}
}

/**
 * uni requestPayment 支付
 * @param {*} data
 */
function requestPayment(provider, data) {
	uni.requestPayment({
		provider,
		orderInfo: data,
		...data,
		success: () => {
			loopCheckPayStatus();
		},
		fail: () => {
			closeLoopCheck(false);
		},
	});
}

let time = null;

/**
 * 循环检查支付状态
 */
function loopCheckPayStatus() {
	uni.showLoading({
		title: "支付校验中...",
		mask: true,
	});
	let count = 1;
	checkPayStatus(false);
	try {
		//先清除一次定时器
		time && clearInterval(time);
		// 开启轮训
		time = setInterval(() => checkPayStatus(++count > 10), 800);
	} catch (error) {
		uni.hideLoading();
		time && clearInterval(time);
	}
	// payBtnLoading.value = false;
}

/**
 * 定时器循环体
 */
function checkPayStatus(skipLoop) {
	if (skipLoop) {
		return closeLoopCheck();
	}
	getOrderIsPaySuccess({
		outTradeNo: unref(orderNumber),
	}).then((res) => {
		if (res.apiStatus) {
			closeLoopCheck(true);
		}
	});
}

/**
 * 取消轮训检查
 */
function closeLoopCheck(state = false) {
	time && clearInterval(time);
	uni.hideLoading();
	if (state) {
		paySuccess(unref(orderNumber), payFrom.value);
	} else {
		payError(unref(orderNumber), payFrom.value);
	}
}

async function paySuccess(orderType, payFrom) {
	// uni.hideLoading();
	payLoading.value = false;
	uni.showToast({
		icon: "success",
		title: "支付成功",
		duration: 1000,
		mask: true,
	});

	await sleep(1400);

	// uni.switchTab({
	// 	url: '/pages/tabBar/order/order'
	// });
	uni.redirectTo({
		url: "/pages/goods/pages/paySuccess/paySuccess",
	});
}

async function payError(orderType, payFrom) {
	// uni.hideLoading();
	payLoading.value = false;
	uni.showToast({
		icon: "error",
		title: "支付失败",
		duration: 1500,
		mask: true,
	});

	await sleep(1400);

	uni.switchTab({
		url: "/pages/tabBar/order/order",
	});
}

function errorOrderProcessing(list, data, msg, title) {
	let id = "";
	const errGoods = list.find((item) => item.shopId === data?.shopId);
	if (!errGoods) {
		uni.showToast({
			icon: "none",
			title: `${msg ? title : "订单提交失败"}`,
		});
		return id;
	}
	id = data.shopId;
	if (data.productId && !data.skuId) {
		const currentErrGoods = errGoods.products.find((item) => item.productId === data.productId);
		if (currentErrGoods) {
			id = data.productId;
			uni.showToast({
				icon: "none",
				// title: `${msg ? currentErrGoods.productName + title : '订单提交失败'}`,
				title: `${msg ? title : "订单提交失败"}`,
			});
		}
		return id;
	}
	if (data.skuId) {
		const currentErrGoods = errGoods.products.find((item) => item.productId === data.productId && item.skuId === data.skuId);
		if (currentErrGoods) {
			id = data.skuId;
			uni.showToast({
				icon: "none",
				title: `${msg ? title : "订单提交失败"}`,
			});
		}
		return id;
	}
	uni.showToast({
		icon: "none",
		title: `${msg ? msg : "订单提交失败"}`,
	});
	return id;
}

/**
 * 提货获取 - 预留功能
 */
async function initStoreDistanceList(shopId, res) {
	const params = { shopId, point: { coordinates: res ? [res.longitude, res.latitude] : getterLocation.value?.coordinates || [0, 0], type: "Point" } };
	const { code, data, msg } = await getStoreDistanceList(params);
	if (code === 500 && msg === "当前店铺无可用门店,请稍后再试") {
		if (!storePointRef.value) {
			uni.showToast({
				title: "当前店铺无可用门店",
				icon: "none",
				duration: 2500,
				success: () => {},
			});
		} else {
			storePointRef.value.temporarilyUnableShopStorePoint();
		}
	} else if (code !== 200) {
		uni.showToast({ title: `${msg || "门店提货点获取失败"}`, icon: "none" });
	} else {
		pointList.value = data;
	}
}

function loadData() {
	submitForm.source = $props.source || "PRODUCT";
	loadSetting();

	initData();
}

function loadSetting() {
	getBasicsSet();
}

async function updateAddress() {
	if (orderInfo.value.length) {
		await initDefaultAddress();
	}
}

async function loadBudget() {
	if (!submitForm?.receiver?.area?.length && ![DISTRIBUTION.SHOP_STORE, DISTRIBUTION.VIRTUAL].includes(submitForm.distributionMode)) {
		return;
	}

	$state.pageLoading = true;

	submitForm.activity = orderStore.activity;
	submitForm.discounts = orderStore.discounts;
	submitForm.orderType = orderStore.orderType;
	submitForm.extra = storesParam;

	const cloneSubmitForm = deepClone(submitForm);

	cloneSubmitForm.receiver = {
		...cloneSubmitForm.receiver,
	};

	cloneSubmitForm.shopPackages.forEach((shopPackages) => {
		shopPackages.products.forEach((product) => {
			const valueList = product.productFeaturesValue;
			const valueMap = {};
			valueList?.forEach((value) => {
				valueMap[value.id] = value?.featureValues?.map((item) => item.featureValueId);
			});
			product.productFeaturesValue = valueMap;
		});
	});

	try {
		const { data, code, msg, apiStatus } = await getBudget(cloneSubmitForm);
		$state.pageLoading = false;

		if (apiStatus) {
			budget.value = data;
		}
	} catch (e) {
		$state.pageLoading = false;
	} finally {
		$state.pageInit = true;
	}
}

function paySelectChange(e) {
	if (!e.show) {
		closeLoopCheck(false);
	}
}

watch(
	() => orderStore.discounts,
	() => {
		loadBudget();
	}
);

onLoad(() => {
	loadData();

	orderStore.initOrderBenefit();

	uni.$on("updateAddress", updateAddress);
});

onShow(async () => {
	if ($state.pageInit) {
		loadBudget();
	}
});

onUnload(() => {
	uni.$off("updateAddress", updateAddress);
});

onReady(() => {});
</script>

<template>
	<view class="create-order-product page-main bg-#F4F5F8 pb-150">
		<!-- 同城 - 预留 -->
		<template v-if="storesParam.distributionMode === DISTRIBUTION.INTRA_CITY_DISTRIBUTION"></template>

		<!-- 到店 - 预留 -->
		<template v-if="storesParam.distributionMode === DISTRIBUTION.SHOP_STORE"></template>

		<!-- 快递 -->
		<template v-if="storesParam.distributionMode === DISTRIBUTION.EXPRESS">
			<view class="address-box w-full bg-#fff">
				<view class="w-full px-36 py-36">
					<view class="flex items-center justify-between" @click="chooseAddress">
						<view class="">
							<template v-if="submitForm.receiver.area.length">
								<view class="w-full flex items-center">
									<view class="flex-1 flex flex-col">
										<view class="uv-line-2">
											<template v-if="submitForm.receiver.isDefault">
												<text class="w-46 h-24 mr-20 text-18 font-500 text-#fff border-rd-3 bg-#34B1FF inline-block line-height-24 text-center">默认</text>
											</template>

											<text class="text-28 font-bold">{{ submitForm.receiver.address }}</text>
										</view>
										<view class="flex items-center">
											<view class="text-#555555 text-26 font-500 mr-20">{{ submitForm.receiver.name }}</view>
											<view class="text-#555555 text-26 font-500">{{ submitForm.receiver.mobile }}</view>
										</view>
									</view>
								</view>
							</template>
							<template v-else>
								<view class="text-30 font-bold">请选择你的地址</view>
							</template>
						</view>
						<view class="">
							<app-image :src="common.iconRightHui" width="20" height="22" mode=""></app-image>
						</view>
					</view>
				</view>
				<view class="w-full">
					<app-image src="@/pages/order/static/iconorder_address_botton_border.png" mode="widthFix"></app-image>
				</view>
			</view>
		</template>

		<!-- 无需配送 -->
		<template v-if="storesParam.distributionMode === DISTRIBUTION.EXPRESS"></template>

		<view class="px-20 py-20">
			<template v-for="(order, index) in commodityList" :key="index">
				<view class="w-full bg-#fff border-rd-20 px-26 py-30 mb-20">
					<view class="w-full mb-0">
						<page-order-card-item
							:item="order"
							:index="index"
							:freight="budget.shopFreight ? budget.shopFreight[order.shopId] : '0'"
							:fullDiscount="budget.shopFull[order.shopId]"
							:coupon="computedCoupon(order)"
							@changeGoodsNum="
								($event) =>
									changeGoodsNum({
										...$event,
										commodityIndex: index,
									})
							"
							@chooseShopCoupon="($event) => openChooseCoupon(order)"
							ref="orderCrdItem"></page-order-card-item>
					</view>
				</view>
			</template>

			<view class="w-full bg-#fff border-rd-20 px-26 py-30">
				<template v-if="orderStore.orderBenefit.coupon">
					<view class="w-full">
						<view class="flex items-center mt-0">
							<view class="w-130 text-26rpx font-bold">优惠券</view>
							<view class="flex-1 flex items-center justify-end" @click="handleQueryCoupon">
								<view class="flex items-center pr-10">
									<view class="flex items-end">
										<view class="text-26 text-#FC3F33">￥</view>
										<view class="text-26 text-#FC3F33">{{ getPriceInfo(budget.platformDiscount).integer }}</view>
										<view class="text-24 text-#FC3F33">.{{ getPriceInfo(budget.platformDiscount).decimalText }}</view>
									</view>
								</view>

								<view class="flex flex-center">
									<app-image :src="common.iconRightHui" size="20rpx" mode=""></app-image>
								</view>
							</view>
						</view>
					</view>

					<view class="w-full h-1 bg-#EAEDF3 mt-30"></view>
				</template>

				<template v-if="orderStore.orderBenefit.vip">
					<view class="w-full">
						<view class="flex items-center mt-30">
							<view class="w-130 text-26rpx font-bold">会员折扣</view>
							<view class="flex-1 flex items-center justify-end">
								<view class="flex items-center pr-10">
									<view class="flex items-end">
										<view class="text-26 text-#FC3F33">￥</view>
										<view class="text-26 text-#FC3F33">{{ getPriceInfo(budget.memberDiscount).integer }}</view>
										<view class="text-24 text-#FC3F33">.{{ getPriceInfo(budget.memberDiscount).decimalText }}</view>
									</view>
								</view>

								<view class="flex flex-center">
									<!-- <app-image :src="common.iconRightHui" size="20rpx" mode=""></app-image> -->
								</view>
							</view>
						</view>
					</view>

					<view class="w-full h-1 bg-#EAEDF3 mt-30"></view>
				</template>

				<template v-if="orderStore.orderBenefit.coupon">
					<view class="w-full">
						<view class="flex items-center mt-30">
							<view class="w-130 text-26rpx font-bold">其他优惠</view>
							<view class="flex-1 flex items-center justify-end">
								<view class="flex items-center pr-10">
									<view class="flex items-end">
										<view class="text-26 text-#FC3F33">￥</view>
										<view class="text-26 text-#FC3F33">{{ getPriceInfo(budget.shopDiscount).integer }}</view>
										<view class="text-24 text-#FC3F33">.{{ getPriceInfo(budget.shopDiscount).decimalText }}</view>
									</view>
								</view>

								<view class="flex flex-center">
									<!-- <app-image :src="common.iconRightHui" size="20rpx" mode=""></app-image> -->
								</view>
							</view>
						</view>
					</view>

					<view class="w-full h-1 bg-#EAEDF3 mt-30"></view>
				</template>

				<view class="w-full">
					<view class="flex items-center mt-30">
						<view class="w-130 text-26rpx font-bold">总运费</view>
						<view class="flex-1 flex items-center justify-end">
							<view class="flex items-center pr-10">
								<template v-if="new Decimal(budget.freight).cmp(0) > 0">
									<view class="flex items-end">
										<view class="text-26 text-#FC3F33">￥</view>
										<view class="text-26 text-#FC3F33">{{ getPriceInfo(budget.freight).integer }}</view>
										<view class="text-24 text-#FC3F33">.{{ getPriceInfo(budget.freight).decimalText }}</view>
									</view>
								</template>
								<template v-else>
									<view class="text-26">包邮</view>
								</template>
							</view>

							<view class="flex flex-center">
								<!-- <app-image :src="common.iconRightHui" size="20rpx" mode=""></app-image> -->
							</view>
						</view>
					</view>
				</view>

				<view class="w-full h-1 bg-#EAEDF3 mt-30"></view>

				<view class="w-full">
					<view class="flex items-center mt-40">
						<view class="w-100 text-32 font-bold">总计</view>
						<view class="flex-1 flex items-center justify-end">
							<view class="flex items-center">
								<view class="text-26 text-#323232 flex items-center">
									<view class="flex items-end">
										<!-- <view class="text-24 font-500 text-#FC3F33 pb-12">预估到手</view> -->
										<view class="text-30 font-500 text-#111010 pb-2">¥</view>
										<view class="text-36 font-500 text-#111010">{{ getPriceInfo(budget.total + budget.freight).integer }}</view>
										<view class="text-30 font-500 text-#111010 pb-2">.{{ getPriceInfo(budget.total + budget.freight).decimalText }}</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<view class="w-full bg-#fff border-rd-20 px-26 py-30 mt-20">
				<view class="text-30 font-bold">购买须知</view>
				<view class="mt-20 w-full">
					<uv-parse class="mt-10" :content="$state.notice"></uv-parse>
				</view>
			</view>

			<view class="w-full bg-#fff fixed bottom-0 left-0">
				<view class="py-15 px-40">
					<view class="w-full flex justify-between">
						<view class="">
							<view class="flex items-end">
								<view class="text-30 font-bold mr-10">需支付</view>
								<view class="flex items-end">
									<!-- <view class="text-24 font-500 text-#FC3F33 pb-12">预估到手</view> -->
									<view class="text-30 font-500 text-#111010 pb-2">¥</view>
									<view class="text-36 font-500 text-#111010">{{ getPriceInfo(budget.payAmount).integer }}</view>
									<view class="text-30 font-500 text-#111010 pb-2">.{{ getPriceInfo(budget.payAmount).decimalText }}</view>
								</view>
							</view>
							<view class="mt-15">
								<view class="text-24 text-#FC3F33 font-500"> 已优惠￥{{ getPriceInfo(budget.total + budget.freight - budget.payAmount).integer }}.{{ getPriceInfo(budget.total + budget.freight - budget.payAmount).decimalText }} </view>
							</view>
						</view>

						<view class="w-220">
							<uv-button color="#00B496" text="提交订单" loadingText="加载中..." class="w-full mt-0 flex-center" custom-style="height: 84rpx;" customTextStyle="font-size: 30rpx; font-weight: bold;" shape="circle" loadingMode="circle" :loading="$state.pageLoading" @click="submit"></uv-button>
						</view>
					</view>
				</view>
				<app-safeAreaBottom></app-safeAreaBottom>
			</view>
		</view>

		<page-order-coupon-popup ref="orderCouponPopupRef" :images="currentShopProducts" :productAmounts="productAmounts" :shopId="currentCouponPopupShopId" @confirm="($event) => chooseCoupon($event, currentCouponPopupShopId)"></page-order-coupon-popup>

		<app-pay-select ref="paySelectRef" v-model:show="showPay" v-model:btnLoading="payLoading" :price="payInfo.price" :balanceTotal="payInfo.balanceTotal" :balanceTotalShow="payInfo.balanceTotalShow" :timeout="payInfo.timeout" @submit="paySubmit" @change="paySelectChange"></app-pay-select>
	</view>
</template>

<style lang="scss" scoped>
.create-order-product {
	.address-box {
		position: static;
		top: 0;
	}
}
</style>
