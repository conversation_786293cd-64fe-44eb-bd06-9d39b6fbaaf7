import Decimal from 'decimal.js';
import CalculateHandler from '../common/calculate';
import useMember from '@/common/useMember'
import useConvert from '@/common/useConvert';

export default class CalculateForMember extends CalculateHandler {
	discountPrice = '0';
	constructor(process) {
		super(process.productInfoArr || []);
	}

	handle(params) {
		const totalPrice = params?.totalPrice || this.totalPrice;
		const {
			memberPrice
		} = useMember();
		const {
			mulTenThousand
		} = useConvert();

		this.discountPrice = new Decimal(totalPrice)
			.sub(mulTenThousand(memberPrice(totalPrice)))
			.toString();

		const disPrice = new Decimal(this.discountPrice);
		const resultPrice = Number(params?.discountPrice) ?
			disPrice.add(Number(params.discountPrice)).toString() :
			disPrice.toString();

		const tempObj = {
			shopId: '0',
			totalPrice,
			discountPrice: resultPrice,
			preferTreatment: {
				...(params?.preferTreatment || {}),
				'addon-member': {
					discount: disPrice.toString()
				}
			}
		};

		return super.handle({
			...params,
			...tempObj
		});
	}
}