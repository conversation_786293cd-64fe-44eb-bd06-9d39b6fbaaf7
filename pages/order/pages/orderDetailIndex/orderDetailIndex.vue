<template>
	<view class="template"></view>
</template>

<script setup>
import { onLoad, onReady } from '@dcloudio/uni-app';

import { ref, reactive } from 'vue';

import { useUserStore } from '@/store';

import { getOrderTypeByOrderNo } from '@/server/api';

import { canENV, route } from '@/common/utils';

const userStore = useUserStore();

const pageData = reactive({});

const props = defineProps({
	orderNo: {
		type: String,
		required: true
	}
});

async function loadData() {
	if (!props.orderNo) {
		uni.showToast({
			icon: 'error',
			title: '订单参数异常！'
		});
		return;
	}

	uni.showLoading({
		title: '加载中...'
	});

	try {
		const res = await getOrderTypeByOrderNo({
			orderNo: props.orderNo
		});
		if (res.apiStatus && res.data.orderType) {
			uni.hideLoading();

			if (['nurse', 'attend', 'homemaking'].includes(res.data.orderType)) {
				route({
					type: 'redirectTo',
					url: '/pages/order/pages/orderDetailServer/orderDetailServer',
					params: {
						orderNo: props.orderNo,
						shopId: res.data.shopId,
						orderType: res.data.orderType
					}
				});
			}

			if (['shop'].includes(res.data.orderType)) {
				route({
					type: 'redirectTo',
					url: '/pages/order/pages/orderDetail/orderDetail',
					params: {
						orderNo: props.orderNo,
						shopId: res.data.shopId,
						orderType: res.data.orderType
					}
				});
			}

			if (['integral'].includes(res.data.orderType)) {
				route({
					type: 'redirectTo',
					url: '/pages/order/pages/orderDetail/orderDetail',
					params: {
						orderNo: props.orderNo,
						// shopId: res.data.shopId,
						orderType: res.data.orderType
					}
				});
			}
			return;
		}
	} catch (error) {
		//TODO handle the exception
	}

	uni.hideLoading();

	uni.showToast({
		icon: 'error',
		title: '订单异常！'
	});
}

onLoad(() => {
	// loadData();
});

onReady(() => {
	loadData();
});
</script>

<style lang="scss" scoped>
.template {
}
</style>
