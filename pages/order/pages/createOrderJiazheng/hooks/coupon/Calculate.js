import CalculateHandler from '../common/calculate';
import Decimal from 'decimal.js';

export default class CalculateForCoupon extends CalculateHandler {
	couponRule = {};
	constructor(process) {
		super(process.productInfoArr || []);
		this.couponRule = process.rule;
	}
	handle(params = {}) {
		let disPrice = new Decimal('0');
		let filterProduct = this.productInfo;
		// 处理指定商品类型
		if (this.couponRule.productType === 'ASSIGNED') {
			filterProduct = this.productInfo.filter((item) => {

				if (this.couponRule.productIds !== void(0) && this.couponRule.productIds !== null) {
					return this.couponRule.productIds.some((id) => id === item.productId)
				} else {
					return ([item.productId]).some((id) => id === item.productId)
				}

				// return (this.couponRule.productIds ?? [item.productId]).some((id) => id === item.productId);
			});
		}
		// 处理排除指定商品类型
		if (this.couponRule.productType === 'ASSIGNED_NOT') {
			filterProduct = this.productInfo.filter((item) => {
				if (!this.couponRule.productIds) return true;
				return !this.couponRule.productIds.some((id) => id === item.productId);
			});
		}
		// 计算总价
		const totalPrice = filterProduct.reduce((acc, cur) => {
			return new Decimal(acc).add(new Decimal(cur.productPrice).mul(cur.quantity));
		}, new Decimal('0'));
		// 计算优惠金额
		if (['PRICE_REDUCE', 'REQUIRED_PRICE_REDUCE'].includes(this.couponRule.type)) {
			const compareValue = new Decimal(this.couponRule.amount || '0');
			disPrice = totalPrice.lessThanOrEqualTo(compareValue) ? totalPrice : compareValue;
		} else {
			const discountRate = new Decimal(10).minus(this.couponRule.discount || '0').div(10);
			disPrice = totalPrice.mul(discountRate);
		}
		// 构建结果对象
		const result = {
			shopId: params.shopId || this.productInfo?.shopId,
			totalPrice: params.totalPrice || this.totalPrice,
			discountPrice: disPrice.toString(),
			preferTreatment: {
				...(params.preferTreatment || {}),
				COUPON: {
					discountId: this.couponRule.couponUserId,
					discount: disPrice.toString()
				}
			}
		};
		return super.handle({
			...params,
			...result
		});
	}
}