import CalculateForCoupon from './coupon/Calculate';
import CalculateForPlatformCoupon from './coupon/calculatePlatform';
import CalculateForMember from './member/Calculate';

export default class RegisterDisPlugin {
	process = [];

	setProcess(process) {
		this.process.push(this.initPlugin(process));
	}

	generateChain() {
		const sortProcess = this.sortProcess();

		if (sortProcess.length) {
			sortProcess.reduce((pre, cur) => {
				const target = pre.handler || pre;
				return target.setNext(cur.handler);
			});
			return sortProcess[0].handler;
		}
		return null;
	}

	sortProcess() {
		return this.process.sort((a, b) => a.priority - b.priority);
	}

	initPlugin(process) {
		switch (process.name) {
			case 'addon-coupon':
				process.handler = new CalculateForCoupon(process);
				break;
			case 'addon-platform-coupon':
				process.handler = new CalculateForPlatformCoupon(process);
				break;
			case 'addon-member':
				process.handler = new CalculateForMember(process);
				break;
			default:
				process.handler = new CalculateForMember(process);
		}
		return process;
	}
}