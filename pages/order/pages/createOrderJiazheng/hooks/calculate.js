import RegisterDisPlugin from './registerdisplugin';

export default function calculate() {
	const discountsMap = new Map();

	const addDiscount = (shopId, process) => {
		if (discountsMap.has(shopId)) {
			if (isExist(shopId, process.name)) {
				editDiscount(shopId, process);
			} else {
				discountsMap.get(shopId).push(process);
			}
		} else {
			discountsMap.set(shopId, [process]);
		}
	};

	const delDiscount = (shopId, disName) => {
		const disArr = discountsMap.get(shopId);
		if (!disArr || !disArr.length) return;
		const filterArr = disArr.filter(item => item.name !== disName);
		discountsMap.set(shopId, filterArr);
	};

	const isExist = (shopId, disName) => {
		let returnVal = false;
		if (discountsMap.size) {
			const processArr = discountsMap.get(shopId);
			returnVal = processArr?.some(item => item.name === disName) || false;
		}
		return returnVal;
	};

	const editDiscount = (shopId, process) => {
		if (isExist(shopId, process.name)) {
			const processArr = discountsMap.get(shopId);
			const replaceArr = processArr.map(item => {
				if (item.name === process.name) {
					return process;
				}
				return item;
			});
			discountsMap.set(shopId, replaceArr);
		}
	};

	const resume = (shopId = '') => {
		const chainArr = [];
		if (shopId) {
			const newRegisterDisPlugin = new RegisterDisPlugin();
			const processArr = discountsMap.get(shopId) || [];
			processArr.forEach(item => {
				newRegisterDisPlugin.setProcess(item);
			});
			chainArr.push(newRegisterDisPlugin.generateChain());
			return chainArr;
		}

		const iterator = discountsMap.values();

		let size = discountsMap.size;
		if (!size) return [];
		while (size > 0) {
			const newRegisterDisPlugin = new RegisterDisPlugin();

			const processArr = iterator.next().value;
			processArr?.forEach(item => {
				newRegisterDisPlugin.setProcess(item);
			});

			chainArr.push(newRegisterDisPlugin.generateChain());
			size--;
		}
		return chainArr;
	};

	function activeResume(shopId = '') {
		const chain = resume(shopId);

		const calResult = [];
		if (chain?.length) {
			chain.forEach(item => {
				const result = item?.handle();
				if (result) {
					calResult.push(result);
				}
			});
		}
		return calResult;
	}

	return {
		addDiscount,
		delDiscount,
		resume,
		editDiscount,
		activeResume
	};
}