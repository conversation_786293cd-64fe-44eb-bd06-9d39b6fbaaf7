<script setup>
import { onLoad, onShow } from '@dcloudio/uni-app';

import { ref, reactive } from 'vue';

import { useUserStore, useOrderStore } from '@/store';

import { canENV, route, getPriceInfo } from '@/common/utils';

import { REGEX_CITIZEN_ID, REGEX_MOBILE } from '@/common/test';

import { getShopInvoiceSettings, getInvoiceHeader, delInvoiceHeader, invoiceRequest } from '@/server/api';

const userStore = useUserStore();

const orderStore = useOrderStore();

const props = defineProps({
	id: {
		type: [String, Number],
		default: ''
	}
});

const pageData = reactive({
	recognizeText: '',
	recognizeLoading: false,

	swipeActionOptions: [
		{
			text: '删除',
			style: {
				backgroundColor: '#F94B4A',
				fontSize: '24rpx'
			}
		}
	]
});

const formData = ref({
	applicantId: '',
	applicantShopId: '',
	shopSupplierName: '',
	orderNo: '',
	invoiceAmount: '',
	invoiceType: '',
	billingRemarks: '',
	invoiceHeaderId: '',
	invoiceStatus: '',
	denialReason: '',
	openInvoiceType: 1,
	electronicInvoiceFormat: 'PDF',
	invoiceOwnerType: 'USER',
	id: ''
});

const invoiceHeaderList = ref([]);
const invoiceHeaderItem = ref({});
const invoiceHeaderSelectRef = ref();

const formRules = {
	invoiceHeaderId: [
		{
			required: true,
			message: '请选择发票抬头',
			trigger: ['blur']
		}
	]
};

const invoiceTypeList = ref([
	// {
	// 	text: '普通发票',
	// 	value: 'VAT_GENERAL'
	// },
	// {
	// 	text: '专用发票',
	// 	value: 'VAT_SPECIAL'
	// }
]);
function changeInvoiceType(index) {
	const item = invoiceTypeList.value[index];
	if (item.value !== formData.value.invoiceType) {
		formData.value.invoiceType = item.value;

		if (formData.value.invoiceType === 'VAT_SPECIAL') {
			if (invoiceHeaderItem.value.invoiceHeaderType !== 'ENTERPRISE') {
				invoiceHeaderItem.value = {};
				formData.value.invoiceHeaderId = '';
			}
		}
	}
}

const invoiceMethodList = ref([
	{
		text: '电子',
		value: 1
	},
	{
		text: '纸质',
		value: 2
	}
]);
function changeInvoiceMethod(index) {
	const item = invoiceMethodList.value[index];
	if (item.value !== formData.value.openInvoiceType) {
		formData.value.openInvoiceType = item.value;
		changeElectronicFormat(0);
	}
}

const electronicFormatList = ref([
	{
		text: 'PDF',
		value: 'PDF'
	},
	{
		text: 'OFD',
		value: 'OFD'
	}
]);
function changeElectronicFormat(index) {
	const item = electronicFormatList.value[index];
	if (item.value !== formData.value.electronicInvoiceFormat) {
		formData.value.electronicInvoiceFormat = item.value;
	}
}

const pageFormRef = ref();

const orderInfo = ref({});
async function loadData(reset = false) {
	uni.showLoading({
		title: '加载中...'
	});

	orderInfo.value = orderStore.invoiceGoodsInfoList[0].shopOrders[0];
	formData.value.invoiceAmount = orderStore.invoiceGoodsInfoList[0].invoiceAmount;
	formData.value.orderNo = orderStore.invoiceGoodsInfoList[0].orderNo;
	formData.value.shopSupplierName = orderInfo.value.shopName;
	formData.value.applicantShopId = orderInfo.value.shopId;

	try {
		const settingRes = await getShopInvoiceSettings({ invoiceSettingsClientType: 'SHOP', shopId: orderInfo.value.shopId });
		if (settingRes.apiStatus) {
			const shopSetInvoiceType = settingRes.data.invoiceSetupValue[0].invoiceType;
			if (shopSetInvoiceType === 'VAT_GENERAL') {
				invoiceTypeList.value = [
					{
						text: '普通发票',
						value: 'VAT_GENERAL'
					}
				];
			}
			if (shopSetInvoiceType === 'VAT_SPECIAL') {
				invoiceTypeList.value = [
					{
						text: '专用发票',
						value: 'VAT_SPECIAL'
					}
				];
			}
			if (shopSetInvoiceType === 'VAT_COMBINED') {
				invoiceTypeList.value = [
					{
						text: '普通发票',
						value: 'VAT_GENERAL'
					},
					{
						text: '专用发票',
						value: 'VAT_SPECIAL'
					}
				];
			}
			if (reset) {
				changeInvoiceType(0);
			}
		}
	} catch (error) {
		//TODO handle the exception
	}

	try {
		const invoiceHeaderListRes = await getInvoiceHeader({
			size: 150,
			ownerType: 'USER'
		});
		if (invoiceHeaderListRes.apiStatus) {
			invoiceHeaderList.value = invoiceHeaderListRes.data.records;
		}
	} catch (error) {
		//TODO handle the exception
	}

	uni.hideLoading();
}

function openChooseInvoiceHeaderId() {
	if (invoiceHeaderSelectRef.value) invoiceHeaderSelectRef.value.open();
}

function selectInvoiceHeader(index) {
	const item = invoiceHeaderList.value[index];
	invoiceHeaderItem.value = item;
	formData.value.invoiceHeaderId = item.id;
	if (invoiceHeaderSelectRef.value) invoiceHeaderSelectRef.value.close();
}

function clickActionItem({ position, index }, item) {
	if (position === 'right' && index === 0) {
		delInvoiceHeader({
			invoiceHeaderId: item.id
		}).then((res) => {
			if (res.apiStatus) {
				loadData(false);
			}
		});
	}
}

function egitItem(item) {
	if (invoiceHeaderSelectRef.value) invoiceHeaderSelectRef.value.close();
	// console.log(item);
	route('/pages/order/pages/invoice/addInvoiceHeader/addInvoiceHeader', {
		id: item.id
	});
}

function addInvoiceHeader() {
	if (invoiceHeaderSelectRef.value) invoiceHeaderSelectRef.value.close();
	if (invoiceHeaderList.value.length >= 150) {
		uni.showToast({
			icon: 'error',
			title: '已达最大值'
		});
		return;
	}
	route('/pages/order/pages/invoice/addInvoiceHeader/addInvoiceHeader');
}

const submitAffirmModalRef = ref();

function submit(affirm = false) {
	if (pageFormRef.value) {
		canENV(() => {
			console.log(formData);
		});
		if (userStore.checkLogin) {
			pageFormRef.value.validate().then(async (res) => {
				if (affirm) {
					const data = {
						...formData.value
					};
					uni.showLoading({
						title: '申请中...',
						mask: true
					});

					try {
						const submitRes = await invoiceRequest(data);
						if (submitRes.apiStatus) {
							if (res.data) {
								useOrderStore().setInvoiceGoodsInfoList([
									{
										...orderStore.invoiceGoodsInfoList[0],
										id: submitRes.data
									}
								]);
								route({
									type: 'redirectTo',
									url: '/pages/order/pages/invoice/applyInvoiceDetails'
								});
							} else {
								uni.navigateBack();
							}
						}
					} catch (error) {
						//TODO handle the exception
					}

					submitAffirmModalRef.value.close();

					uni.hideLoading();
				} else {
					if (submitAffirmModalRef.value) submitAffirmModalRef.value.open();
				}
			});
		} else {
			uni.showToast({
				icon: 'fail',
				title: '请先登录'
			});
		}
	}
}

let pageInit = false;

onLoad(async () => {
	await loadData(true);
	pageInit = true;
});

onShow(() => {
	if (!pageInit) return;
	loadData(false);
});

const formStyle = ref({
	labelStyle: {
		fontSize: '30rpx',
		fontWeight: 'bold'
	},
	inputStyle: {
		height: '98rpx',
		padding: '0rpx 0rpx',
		borderBottom: `solid 1rpx #E9EDF1`
	},
	labelStyle2: {
		fontSize: '26rpx',
		fontWeight: 'bold',
		color: '#1152D7'
	},
	inputStyle2: {
		height: '88rpx',
		borderRadius: '10rpx',
		background: '#fff',
		padding: '0rpx 30rpx'
	},
	inputStyle3: {
		borderRadius: '16rpx',
		background: '#F2F6FB',
		padding: '25rpx 30rpx'
	},
	inputStyle4: {
		borderRadius: '16rpx',
		background: '#fff',
		padding: '25rpx 30rpx'
	},
	inputStyle5: {
		height: '105rpx',
		borderRadius: '16rpx',
		background: '#F2F6FB',
		padding: '0rpx 30rpx'
	},
	labelStyle6: {
		fontSize: '28rpx',
		fontWeight: '500',
		color: '#1A1A1B'
	},
	inputStyle6: {
		fontWeight: 'bold',
		fontSize: '28rpx'
	},
	labelStyle7: {
		fontSize: '28rpx',
		fontWeight: '500',
		color: '#1A1A1B'
	},
	inputStyle7: {
		fontWeight: '500',
		fontSize: '30rpx',
		padding: 0
	},
	inputStyle8: {
		// borderRadius: '16rpx',
		background: '#F3F4F7',
		padding: '25rpx 30rpx',
		borderRadius: '53rpx'
	},
	inputPlaceholderStyle: `font-size: 30rpx; font-weight: 500; color: #AEAEB4;`,
	inputPlaceholderStyle3: `font-size: 26rpx; font-weight: 500; color: #B1B6BC;`,
	inputPlaceholderStyle4: `font-size: 30rpx; font-weight: 500; color: #B1B6BC;`,
	inputPlaceholderStyle6: `font-size: 28rpx; font-weight: 500; color: #A7ACB7;`,
	inputPlaceholderStyle7: `font-size: 30rpx; font-weight: 500; color: #93939C;`
});
</script>

<template>
	<view class="add-caregiver pb-150">
		<view class="w-full py-30 px-20">
			<view class="w-full border-rd-20 bg-#FFFFFF px-30 py-30 mb-20">
				<view class="w-full flex">
					<app-image :src="orderInfo.shopLogo" size="100" rd="16rpx"></app-image>
					<view class="flex-1 ml-22">
						<view class="text-28 font-bold">
							<text>订单编号：</text>
							<text>{{ formData.orderNo }}</text>
						</view>
						<view class="flex items-center">
							<view class="text-28 font-bold">开票金额：</view>
							<view class="text-36 font-bold text-#FC3F33">￥{{ getPriceInfo(formData.invoiceAmount).integer }}.{{ getPriceInfo(formData.invoiceAmount).decimalText }}</view>
						</view>
					</view>
				</view>
			</view>

			<view class="w-full border-rd-20 bg-#FFFFFF px-30">
				<uv-form :model="formData" :rules="formRules" :labelStyle="formStyle.labelStyle6" label-width="140rpx" labelPosition="left" errorType="toast" ref="pageFormRef">
					<!-- 发票类型 -->
					<uv-form-item label="发票类型" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
						<view class="w-full flex">
							<template v-for="(item, index) in invoiceTypeList" :key="index">
								<view
									@click="changeInvoiceType(index)"
									class="bg-#F2F4F8 border-rd-30 h-60 text-center flex flex-center mr-24 px-28 border-solid border-1 border-#F2F4F8"
									:style="[formData.invoiceType === item.value && 'background-color: #EFFAF7; border-color: #09C1B1;']"
								>
									<text class="text-26 text-#323232 font-500" :style="[formData.invoiceType === item.value && 'color: #09C1B1; font-weight: bold']">
										{{ item.text }}
									</text>
								</view>
							</template>
						</view>
					</uv-form-item>

					<!-- 开票方式 -->
					<uv-form-item label="开票方式" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
						<view class="w-full flex">
							<template v-for="(item, index) in invoiceMethodList" :key="index">
								<view
									@click="changeInvoiceMethod(index)"
									class="bg-#F2F4F8 border-rd-30 h-60 text-center flex flex-center mr-24 px-28 border-solid border-1 border-#F2F4F8"
									:style="[formData.openInvoiceType === item.value && 'background-color: #EFFAF7; border-color: #09C1B1;']"
								>
									<text class="text-26 text-#323232 font-500" :style="[formData.openInvoiceType === item.value && 'color: #09C1B1; font-weight: bold']">
										{{ item.text }}
									</text>
								</view>
							</template>
						</view>
					</uv-form-item>

					<!-- 发票格式 -->
					<template v-if="formData.openInvoiceType === 1">
						<uv-form-item label="发票格式" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
							<view class="w-full flex">
								<template v-for="(item, index) in electronicFormatList" :key="index">
									<view
										@click="changeElectronicFormat(index)"
										class="bg-#F2F4F8 border-rd-30 h-60 text-center flex flex-center mr-24 px-28 border-solid border-1 border-#F2F4F8"
										:style="[formData.electronicInvoiceFormat === item.value && 'background-color: #EFFAF7; border-color: #09C1B1;']"
									>
										<text class="text-26 text-#323232 font-500" :style="[formData.electronicInvoiceFormat === item.value && 'color: #09C1B1; font-weight: bold']">
											{{ item.text }}
										</text>
									</view>
								</template>
							</view>
						</uv-form-item>
					</template>

					<!-- 发票抬头 -->
					<uv-form-item label="发票抬头" prop="invoiceHeaderId" class="w-full" :customStyle="{ padding: '42rpx 0' }">
						<view class="w-full text-28 font-500" @click="openChooseInvoiceHeaderId">
							<template v-if="formData.invoiceHeaderId">
								<view>{{ invoiceHeaderItem.header }}</view>
							</template>
							<template v-else>
								<view class="text-#A7ACB7">请选择发票抬头</view>
							</template>
						</view>

						<template #right>
							<uv-icon name="arrow-right" color="#9D9E9F" size="26rpx"></uv-icon>
						</template>
					</uv-form-item>
				</uv-form>
			</view>

			<view class="w-full border-rd-20 bg-#FFFFFF px-30 py-30 mt-20">
				<view class="mb-20 text-30 font-bold">开票备注</view>
				<view class="w-full">
					<textarea
						v-model="formData.billingRemarks"
						cols="30"
						rows="5"
						confirm-type="done"
						:maxlength="500"
						auto-height
						placeholder="请输入开票备注"
						class="min-h-150 w-full text-26"
					></textarea>
				</view>
			</view>

			<template v-if="invoiceHeaderItem.id">
				<view class="w-full border-rd-20 bg-#FFFFFF px-30 mt-20 pt-20">
					<view class="mt-0 text-30 font-bold text-center">抬头信息</view>
					<uv-form :labelStyle="formStyle.labelStyle6" label-width="140rpx" labelPosition="left">
						<!-- 抬头类型 -->
						<uv-form-item label="抬头类型" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
							<uv-input
								v-model="{ ENTERPRISE: '企业抬头', PERSONAL: '个人抬头' }[invoiceHeaderItem.invoiceHeaderType]"
								placeholder="请输入抬头类型"
								:placeholderStyle="formStyle.inputPlaceholderStyle6"
								:customStyle="{ ...formStyle.inputStyle6 }"
								fontSize="28rpx"
								border="none"
								type="text"
								inputAlign="left"
								readonly
							></uv-input>
						</uv-form-item>

						<!-- 邮箱 -->
						<uv-form-item label="邮箱" prop="name" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
							<uv-input
								v-model="invoiceHeaderItem.email"
								placeholder="请输入邮箱"
								:placeholderStyle="formStyle.inputPlaceholderStyle6"
								:customStyle="{ ...formStyle.inputStyle6 }"
								fontSize="28rpx"
								border="none"
								type="text"
								inputAlign="left"
								readonly
							></uv-input>
						</uv-form-item>

						<template v-if="invoiceHeaderItem.invoiceHeaderType === 'ENTERPRISE'">
							<!-- 单位税号 -->
							<uv-form-item label="单位税号" prop="name" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
								<uv-input
									v-model="invoiceHeaderItem.taxIdentNo"
									placeholder="纳税人识别号或社会统一征信代码"
									:placeholderStyle="formStyle.inputPlaceholderStyle6"
									:customStyle="{ ...formStyle.inputStyle6 }"
									fontSize="28rpx"
									border="none"
									type="text"
									inputAlign="left"
									readonly
								></uv-input>
							</uv-form-item>

							<!-- 单位税号 -->
							<uv-form-item label="开户银行" prop="name" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
								<uv-input
									v-model="invoiceHeaderItem.openingBank"
									placeholder="请输入开户银行"
									:placeholderStyle="formStyle.inputPlaceholderStyle6"
									:customStyle="{ ...formStyle.inputStyle6 }"
									fontSize="28rpx"
									border="none"
									type="text"
									inputAlign="left"
									readonly
								></uv-input>
							</uv-form-item>

							<!-- 银行账号 -->
							<uv-form-item label="银行账号" prop="name" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
								<uv-input
									v-model="invoiceHeaderItem.bankAccountNo"
									placeholder="请输入银行账号"
									:placeholderStyle="formStyle.inputPlaceholderStyle6"
									:customStyle="{ ...formStyle.inputStyle6 }"
									fontSize="28rpx"
									border="none"
									type="text"
									inputAlign="left"
									readonly
								></uv-input>
							</uv-form-item>

							<!-- 注册地址 -->
							<uv-form-item label="注册地址" prop="name" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
								<uv-input
									v-model="invoiceHeaderItem.enterpriseAddress"
									placeholder="请输入注册地址"
									:placeholderStyle="formStyle.inputPlaceholderStyle6"
									:customStyle="{ ...formStyle.inputStyle6 }"
									fontSize="28rpx"
									border="none"
									type="text"
									inputAlign="left"
									readonly
								></uv-input>
							</uv-form-item>

							<!-- 注册电话 -->
							<uv-form-item label="注册电话" prop="name" class="w-full" :customStyle="{ padding: '42rpx 0' }">
								<uv-input
									v-model="invoiceHeaderItem.enterprisePhone"
									placeholder="请输入注册电话"
									:placeholderStyle="formStyle.inputPlaceholderStyle6"
									:customStyle="{ ...formStyle.inputStyle6 }"
									fontSize="28rpx"
									border="none"
									type="text"
									inputAlign="left"
									readonly
								></uv-input>
							</uv-form-item>
						</template>
					</uv-form>
				</view>
			</template>
		</view>

		<my-uv-popup mode="bottom" ref="invoiceHeaderSelectRef" round="20rpx" bgColor="#F4F5F8" closeable>
			<view class="w-full h-800 flex flex-col">
				<view class="w-full text-center text-30 py-36">抬头选择</view>
				<view class="w-full flex-1 h-0">
					<scroll-view class="w-full h-full" scroll-y="true">
						<uni-swipe-action>
							<template v-for="(item, index) in invoiceHeaderList" :key="item.id">
								<template v-if="formData.invoiceType !== 'VAT_SPECIAL' || (formData.invoiceType === 'VAT_SPECIAL' && item.invoiceHeaderType === 'ENTERPRISE')">
									<view class="px-20 py-10">
										<view class="w-full bg-#fff border-rd-20 overflow-hidden">
											<uni-swipe-action-item :right-options="pageData.swipeActionOptions" @click="clickActionItem($event, item)">
												<view class="w-full py-32 px-30 flex items-center">
													<view class="flex-1 flex flex-col" @click="selectInvoiceHeader(index)">
														<view class="uv-line-2">
															<text class="text-28 font-bold">{{ item.header }}</text>
														</view>
														<view class="flex items-center">
															<view class="text-#555555 text-26 font-500 mr-20">
																{{ { ENTERPRISE: '企业抬头', PERSONAL: '个人抬头' }[item.invoiceHeaderType] }}
															</view>
														</view>
													</view>
													<view class="w-30 h-30 flex-shrink-0 ml-10">
														<uv-icon name="edit-pen" color="#ADB2B9" size="40rpx" @click="egitItem(item)"></uv-icon>
													</view>
												</view>
											</uni-swipe-action-item>
										</view>
									</view>
								</template>
							</template>
						</uni-swipe-action>
					</scroll-view>
				</view>
				<view class="w-full px-30 pt-15 bg-#fff border-t-1 border-t-solid border-t-#EDEEF0 flex items-center">
					<view class="flex-1">
						<uv-button
							@click="addInvoiceHeader()"
							color="#00B496"
							text="添加新的抬头"
							class="flex-1 h-88"
							custom-style="border-radius: 44rpx;"
							customTextStyle="font-size: 30rpx; color: #fff; font-weight: bold;"
						></uv-button>
						<app-safeAreaBottom></app-safeAreaBottom>
					</view>
				</view>
			</view>
		</my-uv-popup>

		<my-uv-modal
			ref="submitAffirmModalRef"
			confirmText="确认提交"
			:confirmColor="'#38B597'"
			:lineColor="'#F2F6FB'"
			cancelText="取消"
			:duration="200"
			width="580rpx"
			showCancelButton
			@confirm="submit(true)"
			:asyncClose="true"
			:closeOnClickOverlay="false"
		>
			<view class="flex-center w-full">
				<view class="text-34 font-bold w-full text-center">提示</view>
				<view class="w-full mt-20 text-28">
					<view class="">
						<text>发票抬头：</text>
						<text>{{ invoiceHeaderItem.header }}</text>
					</view>
					<view class="">
						<text>接收邮箱：</text>
						<text class="text-#FC3F33 font-bold">{{ invoiceHeaderItem.email }}</text>
					</view>
				</view>
				<view class="w-full mt-20">
					<text class="text-26">电子发票商家开票后会发送至您的接收邮箱，请再次确认邮箱是否正确；纸质发票邮寄具体细节可商家客服。</text>
				</view>
			</view>
		</my-uv-modal>

		<view class="w-full px-30 pt-15 bg-#fff fixed left-0 bottom-0 border-t-1 border-t-solid border-t-#EDEEF0 flex items-center">
			<view class="flex-1">
				<uv-button
					@click="submit()"
					color="#00B496"
					text="提交申请"
					class="flex-1 h-88"
					custom-style="border-radius: 44rpx;"
					customTextStyle="font-size: 30rpx; color: #fff; font-weight: bold;"
				></uv-button>
				<app-safeAreaBottom></app-safeAreaBottom>
			</view>
		</view>
	</view>
</template>

<style lang="scss" scoped>
.add-caregiver {
	min-height: calc(100vh - var(--window-top) - var(--window-bottom));
	background-color: #f1f3f7;
}
</style>