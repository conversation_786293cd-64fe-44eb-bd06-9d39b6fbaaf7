<script setup>
import { onLoad } from '@dcloudio/uni-app';

import { ref, reactive } from 'vue';

import { useUserStore } from '@/store';

import { canENV, route } from '@/common/utils';

import { REGEX_CITIZEN_ID, REGEX_MOBILE, REGEX_EMAIL } from '@/common/test';

import { delInvoiceHeader, editInvoiceHeader, getInvoiceHeaderDetail } from '@/server/api';

const userStore = useUserStore();

const props = defineProps({
	id: {
		type: [String, Number],
		default: ''
	}
});

const pageData = reactive({
	recognizeText: '',
	recognizeLoading: false,

	sign: false
});

const invoiceHeaderTypeList = ref([
	{
		text: '企业抬头',
		value: 'ENTERPRISE'
	},
	{
		text: '个人抬头',
		value: 'PERSONAL'
	}
]);
function changeInvoiceHeaderType(index) {
	const item = invoiceHeaderTypeList.value[index];
	if (item.value !== formData.value.invoiceHeaderType) {
		formData.value.invoiceHeaderType = item.value;
	}
}

const formData = ref({
	id: '',
	ownerType: 'USER',
	ownerId: '',
	invoiceHeaderType: 'ENTERPRISE',
	header: '',
	email: '',
	taxIdentNo: '',
	openingBank: '',
	bankAccountNo: '',
	enterprisePhone: '',
	enterpriseAddress: ''
});

const formRules = {
	header: [
		{
			required: true,
			message: '请输入发票抬头',
			trigger: ['blur']
		}
	],
	email: [
		{
			required: true,
			message: '请输入接收邮箱',
			trigger: ['blur']
		},
		{
			validator: (rule, value, callback) => {
				if (!REGEX_EMAIL(value)) {
					callback(new Error('请输入正确的邮箱'));
				} else {
					callback();
				}
			},
			trigger: 'blur'
		}
	],
	taxIdentNo: [
		{
			required: true,
			message: '请输入单位税号',
			trigger: ['blur']
		}
	],
	openingBank: [
		{
			required: true,
			message: '请输入开户银行',
			trigger: ['blur']
		}
	],
	bankAccountNo: [
		{
			required: true,
			message: '请输入银行账号',
			trigger: ['blur']
		}
	],
	enterprisePhone: [
		{
			required: true,
			message: '请输入注册电话',
			trigger: ['blur']
		},
		{
			validator: (rule, value, callback) => {
				if (!REGEX_MOBILE(value)) {
					callback(new Error('请输入正确的注册电话'));
				} else {
					callback();
				}
			},
			trigger: 'blur'
		}
	],
	enterpriseAddress: [
		{
			required: true,
			message: '请输入注册地址',
			trigger: ['blur']
		}
		// {
		// 	message: '详细地址不能包含~号',
		// 	trigger: ['blur'],
		// 	validator: (rule, value, callback) => {
		// 		if (value.includes('~')) {
		// 			callback(new Error('详细地址不能包含~'));
		// 		} else {
		// 			callback();
		// 		}
		// 	}
		// }
	]
};

const pageFormRef = ref();

async function loadData() {
	if (props.id) {
		uni.setNavigationBarTitle({
			title: '修改抬头'
		});
		formData.value.id = props.id;
		await getItemDetails();
	}
	if (userStore.checkLogin) {
		formData.value.ownerId = userStore.userData.userId;
	}
}

async function getItemDetails() {
	try {
		const res = await getInvoiceHeaderDetail({
			invoiceHeaderId: props.id,
			invoiceHeaderOwnerType: 'USER'
		});

		if (res.apiStatus) {
			formData.value = { ...formData.value, ...res.data };
		}
	} catch (error) {}
}

const delModal = ref();

function del(verify = false) {
	if (!verify) {
		delModal.value.open();
		return;
	}
	delInvoiceHeader({
		invoiceHeaderId: props.id
	}).then((res) => {
		if (res.apiStatus) {
			route({
				type: 'back'
			});
		}
	});
}

const signPopupRef = ref();
function changeSign(affirm = false) {
	if (pageData.sign) {
		pageData.sign = false;
	} else {
		if (affirm) {
			pageData.sign = true;
			if (signPopupRef.value) signPopupRef.value.close();
		} else {
			if (signPopupRef.value) signPopupRef.value.open();
		}
	}
}

function submit() {
	if (pageFormRef.value) {
		canENV(() => {
			console.log(formData);
		});
		if (userStore.checkLogin) {
			if (formData.value.invoiceHeaderType === 'ENTERPRISE' && !pageData.sign) {
				uni.showToast({
					icon: 'none',
					title: '请先同意确认书'
				});
				return;
			}

			pageFormRef.value.validate().then((res) => {
				const data = {
					...formData.value
				};

				editInvoiceHeader({
					...data
				}).then((res) => {
					// console.log(res);
					if (res.apiStatus) {
						route({
							type: 'back'
						});
					}
				});

				// if (props.id) {

				// } else {

				// }
			});
		} else {
			uni.showToast({
				icon: 'fail',
				title: '请先登录'
			});
		}
	}
}

onLoad(() => {
	loadData();
});

const formStyle = ref({
	labelStyle: {
		fontSize: '30rpx',
		fontWeight: 'bold'
	},
	inputStyle: {
		height: '98rpx',
		padding: '0rpx 0rpx',
		borderBottom: `solid 1rpx #E9EDF1`
	},
	labelStyle2: {
		fontSize: '26rpx',
		fontWeight: 'bold',
		color: '#1152D7'
	},
	inputStyle2: {
		height: '88rpx',
		borderRadius: '10rpx',
		background: '#fff',
		padding: '0rpx 30rpx'
	},
	inputStyle3: {
		borderRadius: '16rpx',
		background: '#F2F6FB',
		padding: '25rpx 30rpx'
	},
	inputStyle4: {
		borderRadius: '16rpx',
		background: '#fff',
		padding: '25rpx 30rpx'
	},
	inputStyle5: {
		height: '105rpx',
		borderRadius: '16rpx',
		background: '#F2F6FB',
		padding: '0rpx 30rpx'
	},
	labelStyle6: {
		fontSize: '28rpx',
		fontWeight: '500',
		color: '#1A1A1B'
	},
	inputStyle6: {
		fontWeight: 'bold',
		fontSize: '28rpx'
	},
	labelStyle7: {
		fontSize: '28rpx',
		fontWeight: '500',
		color: '#1A1A1B'
	},
	inputStyle7: {
		fontWeight: '500',
		fontSize: '30rpx',
		padding: 0
	},
	inputStyle8: {
		// borderRadius: '16rpx',
		background: '#F3F4F7',
		padding: '25rpx 30rpx',
		borderRadius: '53rpx'
	},
	inputPlaceholderStyle: `font-size: 30rpx; font-weight: 500; color: #AEAEB4;`,
	inputPlaceholderStyle3: `font-size: 26rpx; font-weight: 500; color: #B1B6BC;`,
	inputPlaceholderStyle4: `font-size: 30rpx; font-weight: 500; color: #B1B6BC;`,
	inputPlaceholderStyle6: `font-size: 28rpx; font-weight: 500; color: #A7ACB7;`,
	inputPlaceholderStyle7: `font-size: 30rpx; font-weight: 500; color: #93939C;`
});
</script>

<template>
	<view class="add-caregiver pb-150">
		<view class="w-full py-30 px-20">
			<view class="w-full border-rd-20 bg-#FFFFFF px-30">
				<uv-form :model="formData" :rules="formRules" :labelStyle="formStyle.labelStyle6" label-width="150rpx" labelPosition="left" errorType="toast" ref="pageFormRef">
					<!-- 抬头类型 -->
					<uv-form-item label="抬头类型" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
						<view class="w-full flex">
							<template v-for="(item, index) in invoiceHeaderTypeList" :key="index">
								<view
									@click="changeInvoiceHeaderType(index)"
									class="bg-#F2F4F8 border-rd-30 h-60 text-center flex flex-center mr-24 px-28 border-solid border-1 border-#F2F4F8"
									:style="[formData.invoiceHeaderType === item.value && 'background-color: #EFFAF7; border-color: #09C1B1;']"
								>
									<text class="text-26 text-#323232 font-500" :style="[formData.invoiceHeaderType === item.value && 'color: #09C1B1; font-weight: bold']">
										{{ item.text }}
									</text>
								</view>
							</template>
						</view>
					</uv-form-item>

					<!-- 发票抬头 -->
					<uv-form-item label="发票抬头" prop="header" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
						<uv-input
							v-model="formData.header"
							placeholder="请输入发票抬头"
							:placeholderStyle="formStyle.inputPlaceholderStyle6"
							:customStyle="{ ...formStyle.inputStyle6 }"
							fontSize="28rpx"
							border="none"
							type="text"
							inputAlign="left"
						></uv-input>
					</uv-form-item>

					<!-- 接收邮箱 -->
					<uv-form-item label="接收邮箱" prop="email" class="w-full" :customStyle="{ padding: '42rpx 0' }" :borderBottom="formData.invoiceHeaderType === 'ENTERPRISE'">
						<uv-input
							v-model="formData.email"
							placeholder="请输入接收邮箱"
							:placeholderStyle="formStyle.inputPlaceholderStyle6"
							:customStyle="{ ...formStyle.inputStyle6 }"
							fontSize="28rpx"
							border="none"
							type="text"
							inputAlign="left"
						></uv-input>
					</uv-form-item>

					<template v-if="formData.invoiceHeaderType === 'ENTERPRISE'">
						<!-- 单位税号 -->
						<uv-form-item label="单位税号" prop="taxIdentNo" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
							<uv-input
								v-model="formData.taxIdentNo"
								placeholder="纳税人识别号或社会统一征信代码"
								:placeholderStyle="formStyle.inputPlaceholderStyle6"
								:customStyle="{ ...formStyle.inputStyle6 }"
								fontSize="28rpx"
								border="none"
								type="text"
								inputAlign="left"
							></uv-input>
						</uv-form-item>

						<!-- 开户银行 -->
						<uv-form-item label="开户银行" prop="openingBank" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
							<uv-input
								v-model="formData.openingBank"
								placeholder="请输入开户银行"
								:placeholderStyle="formStyle.inputPlaceholderStyle6"
								:customStyle="{ ...formStyle.inputStyle6 }"
								fontSize="28rpx"
								border="none"
								type="text"
								inputAlign="left"
							></uv-input>
						</uv-form-item>

						<!-- 银行账号 -->
						<uv-form-item label="银行账号" prop="bankAccountNo" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
							<uv-input
								v-model="formData.bankAccountNo"
								placeholder="请输入银行账号"
								:placeholderStyle="formStyle.inputPlaceholderStyle6"
								:customStyle="{ ...formStyle.inputStyle6 }"
								fontSize="28rpx"
								border="none"
								type="text"
								inputAlign="left"
							></uv-input>
						</uv-form-item>

						<!-- 注册地址 -->
						<uv-form-item label="注册地址" prop="enterpriseAddress" class="w-full" :customStyle="{ padding: '42rpx 0' }" borderBottom>
							<uv-input
								v-model="formData.enterpriseAddress"
								placeholder="请输入注册地址"
								:placeholderStyle="formStyle.inputPlaceholderStyle6"
								:customStyle="{ ...formStyle.inputStyle6 }"
								fontSize="28rpx"
								border="none"
								type="text"
								inputAlign="left"
							></uv-input>
						</uv-form-item>

						<!-- 注册电话 -->
						<uv-form-item label="注册电话" prop="enterprisePhone" class="w-full" :customStyle="{ padding: '42rpx 0' }">
							<uv-input
								v-model="formData.enterprisePhone"
								placeholder="请输入注册电话"
								:placeholderStyle="formStyle.inputPlaceholderStyle6"
								:customStyle="{ ...formStyle.inputStyle6 }"
								fontSize="28rpx"
								border="none"
								type="number"
								inputAlign="left"
							></uv-input>
						</uv-form-item>
					</template>

					<!-- 设为默认发票 -->
					<!-- <uv-form-item label="设为默认" class="w-full" :customStyle="{ padding: '42rpx 0' }">
						<template #right>
							<view>
								<uv-switch v-model="formData.isDefault" :inactiveValue="false" :activeValue="true" size="45rpx" :activeColor="'#38B597'"></uv-switch>
							</view>
						</template>
					</uv-form-item> -->
				</uv-form>
			</view>
		</view>

		<view class="w-full fixed left-0 bottom-0">
			<template v-if="formData.invoiceHeaderType === 'ENTERPRISE'">
				<view @click="changeSign()" class="flex items-center text-22 text-#131311 px-30 py-15 w-full">
					<app-image v-if="pageData.sign" class="w-28 h-28 mr-15" size="28" src="@/pages/order/static/agree_true.png" mode=""></app-image>
					<app-image v-else class="w-28 h-28 mr-15" size="28" src="@/pages/order/static/agree_false.png" mode=""></app-image>
					<text>我已阅读并同意</text>
					<text class="text-#00B496">《专用发票抬头确认书》</text>
				</view>
			</template>
			<view class="w-full bg-#fff border-t-1 border-t-solid border-t-#EDEEF0 flex items-center px-30 pt-15">
				<!-- <view class="flex-1 mr-20" v-if="id">
					<uv-button
						@click="del()"
						type="error"
						text="删除"
						class="h-88"
						custom-style="border-radius: 44rpx;"
						customTextStyle="font-size: 30rpx; color: #fff; font-weight: bold;"
					></uv-button>
				</view> -->
				<view class="flex-1">
					<uv-button
						@click="submit()"
						color="#00B496"
						:text="id ? '确认修改' : '确认提交'"
						class="flex-1 h-88"
						custom-style="border-radius: 44rpx;"
						customTextStyle="font-size: 30rpx; color: #fff; font-weight: bold;"
					></uv-button>
				</view>
			</view>
			<app-safeAreaBottom></app-safeAreaBottom>
		</view>

		<my-uv-popup mode="bottom" ref="signPopupRef" round="20rpx" bgColor="#FFFFFF" closeable>
			<view class="w-full h-800 flex flex-col">
				<view class="w-full text-center text-30 py-36">专用发票抬头确认书</view>
				<view class="w-full flex-1 h-0">
					<!-- <view class="px-30 text-28">
						根据国家税法及发票管理相关规定，任何单位和个人不得要求他人为自己开具与实际经营业务情况不符的专用发票【包括并不限于(1)
						在没有货物采购或者没有接受应税劳务的情况下要求他人为自己开具专用发票; (2) 虽有货物采购或者接受应税劳务但要求他人为自己开具数量或金额与实际情况不符的专用发票】，
						否则属于“虚开专用发票”。
						我已充分了解上述各项相关国家税法和发票管理规定，并确认仅就我司实际购买商品或服务索取发票。如我司未按国家相关规定申请开具或使用专用发票，由我司自行承担相应法律后果。
					</view> -->
					<view class="px-30 text-28" style="text-indent: 0em">
						根据国家税法及发票管理相关规定，任何单位和个人不得要求他人为自己开具与实际经营业务情况不符的专用发票，包括并不限于
					</view>
					<view class="px-30 text-28" style="text-indent: 0em">(1)在没有货物采购或者没有接受应税劳务的情况下要求他人为自己开具专用发票;</view>
					<view class="px-30 text-28" style="text-indent: 0em">
						(2) 虽有货物采购或者接受应税劳务但要求他人为自己开具数量或金额与实际情况不符的专用发票，否则属于“虚开专用发票”。
					</view>
					<view class="px-30 text-28" style="text-indent: 0em">我已充分了解上述各项相关国家税法和发票管理规定，并确认仅就我司实际购买商品或服务索取发票。</view>
					<view class="px-30 text-28" style="text-indent: 0em">如我司未按国家相关规定申请开具或使用专用发票，由我司自行承担相应法律后果。</view>
				</view>
				<view class="w-full px-30 pt-15 bg-#fff border-t-1 border-t-solid border-t-#EDEEF0 flex items-center">
					<view class="flex-1">
						<uv-button
							@click="changeSign(true)"
							color="#00B496"
							text="我已阅读并同意"
							class="flex-1 h-88"
							custom-style="border-radius: 44rpx;"
							customTextStyle="font-size: 30rpx; color: #fff; font-weight: bold;"
						></uv-button>
						<app-safeAreaBottom></app-safeAreaBottom>
					</view>
				</view>
			</view>
		</my-uv-popup>

		<my-uv-modal
			ref="delModal"
			confirmText="删除"
			:confirmColor="'#38B597'"
			:lineColor="'#F2F6FB'"
			cancelText="取消"
			:duration="200"
			width="580rpx"
			showCancelButton
			@confirm="del(true)"
			:asyncClose="true"
			:closeOnClickOverlay="false"
		>
			<view class="flex-center h-100">
				<view class="text-34 font-bold">确定删除吗？</view>
			</view>
		</my-uv-modal>
	</view>
</template>

<style lang="scss" scoped>
.add-caregiver {
	min-height: calc(100vh - var(--window-top) - var(--window-bottom));
	background-color: #f1f3f7;
}
</style>
