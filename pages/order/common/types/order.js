/**
 * 退款原因
 * @param isReturnRefund 退货退款
 * @param undelivered  未发货
 *
 */
export const aRefundWhy = {
	DONT_NEED_IT: {
		title: '多拍/拍错/不想要',
		isReturnRefund: false,
		undelivered: true
	},
	WRONG_FORM: {
		title: '地址/联系电话 填写错误',
		isReturnRefund: false,
		undelivered: true
	},
	EXPRESS_STAGNATED: {
		title: '快递停滞/未送达',
		isReturnRefund: false,
		undelivered: false
	},
	EXPRESS_REJECTION: {
		title: '货物破损已拒签',
		isReturnRefund: false,
		undelivered: false
	},
	EMPTY_PARCEL: {
		title: '空包裹',
		isReturnRefund: false,
		undelivered: false
	},
	LOGISTICS_NO_UPDATE: {
		title: '快递/物流无跟踪记录',
		isReturnRefund: false,
		undelivered: false
	},
	SEVEN_DAYS_NO_REASON: {
		title: '七天无理由退货（商品无损）',
		isReturnRefund: true,
		undelivered: false
	},
	WRONG_PRODUCT_DESC: {
		title: '尺寸/容量/参数与商品描述不符',
		isReturnRefund: true,
		undelivered: false
	},
	DONT_LIKE_IT: {
		title: '不喜欢或效果不好',
		isReturnRefund: true,
		undelivered: false
	},
	SOME_MISSING: {
		title: '缺件/漏发',
		isReturnRefund: true,
		undelivered: false
	},
	QUALITY_PROBLEM: {
		title: '质量问题',
		isReturnRefund: true,
		undelivered: false
	},
	WRONG_PRODUCT: {
		title: '商家发错货',
		isReturnRefund: true,
		undelivered: false
	},
}

/**
 * 退款类型
 */
export const aRefundType = {
	REFUND: '仅退款',
	RETURN_REFUND: '退货退款',
}

/**
 * 申请售后按钮提示
 */
export const afsStatusBtn = {
	NONE: '申请售后',
	REFUND_REQUEST: '待商家审核',
	REFUND_AGREE: '退款中',
	SYSTEM_REFUND_AGREE: '退款中',
	REFUND_REJECT: '拒绝退款',
	REFUNDED: '退款成功',
	SYSTEM_RETURN_REFUND_AGREE: '待买家退货',
	RETURN_REFUND_REQUEST: '待商家审核',
	RETURN_REFUND_AGREE: '待买家退货',
	RETURN_REFUND_REJECT: '拒绝退货退款',
	RETURNED_REFUND: '待商家确认收货',
	RETURNED_REFUND_CONFIRM: '退款中',
	RETURNED_REFUND_REJECT: '商家已拒绝收货',
	RETURNED_REFUNDED: '退款成功',
	SYSTEM_RETURNED_REFUND_CONFIRM: '退款成功',
	BUYER_CLOSED: '申请售后',
	SYSTEM_CLOSED: '申请售后',
}

/**
 * 退款详情提示
 * closable 是否可以撤销申请
 * applyForAgain 是否可以再次申请售后
 * isConsumer (协商历史页面 消费者？true:false)
 * canChangePackageStatus
 */
export const afsStatus = {
	NONE: {
		title: '申请售后',
		desc: '暂未申请售后',
		closable: false,
		list: '申请退款 待审核',
		applyForAgain: true,
		isConsumer: false,
		typeStatus: 0,
		msg: [{
			title: ''
		}],
		success: false,
		isSystem: false,
		countDown: {
			isCountDown: false,
			leftText: '',
			rightText: '',
		},
		type: '',
		canChangePackageStatus: true,
		canChangePackageStatusText: false,
		isRefunded: false,
	},
	REFUND_REQUEST: {
		title: '请等待商家处理',
		desc: '你已成功发起退款申请，请耐心请等待商家处理',
		list: '申请退款 待审核',
		type: '退款',
		isSystem: false,
		countDown: {
			isCountDown: true,
			leftText: '还剩',
			rightText: '',
		},
		closable: true,
		applyForAgain: false,
		isConsumer: true,
		success: false,
		typeStatus: 0,
		msg: [{
			title: '商家同意或者超时未支付，系统将退款给你'
		}, {
			title: '如果商家拒绝，你可以再次发起售后，卖家会重新处理'
		}],
		canChangePackageStatus: true,
		canChangePackageStatusText: true,
		isRefunded: false,
	},
	REFUND_AGREE: {
		title: '退款中',
		type: '退款',
		desc: '商家已同意退款申请，请耐心请等待退款到账',
		list: '已同意退款申请',
		closable: false,
		isSystem: false,
		success: true,
		applyForAgain: false,
		countDown: {
			isCountDown: false,
			leftText: '',
			rightText: '',
		},
		isConsumer: false,
		typeStatus: 0,
		msg: [{
			title: '如你的问题未解决，你还可以继续发起申请'
		}],
		canChangePackageStatus: false,
		canChangePackageStatusText: false,
		isRefunded: false,
	},
	SYSTEM_REFUND_AGREE: {
		title: '退款中',
		type: '退款',
		desc: '系统已同意退款申请，请耐心请等待退款到账',
		closable: false,
		list: '系统已同意退款申请',
		success: true,
		applyForAgain: false,
		countDown: {
			isCountDown: false,
			leftText: '',
			rightText: '',
		},
		isConsumer: false,
		isSystem: true,
		typeStatus: 0,
		msg: [{
			title: '如你的问题未解决，你还可以继续发起申请'
		}],
		canChangePackageStatus: false,
		canChangePackageStatusText: false,
		isRefunded: false,
	},
	REFUND_REJECT: {
		title: '退款关闭',
		type: '退款',
		list: '退款关闭',
		desc: '因商家拒绝了你的退款申请，本次售后关闭',
		countDown: {
			isCountDown: false,
			leftText: '',
			rightText: '',
		},
		closable: false,
		isSystem: false,
		success: false,
		applyForAgain: true,
		isConsumer: true,
		typeStatus: -1,
		msg: [{
			title: '如你的问题未解决，你还可以继续发起申请'
		}],
		canChangePackageStatus: true,
		canChangePackageStatusText: false,
		isRefunded: false,
	},
	REFUNDED: {
		title: '退款成功',
		desc: '退款成功',
		list: '退款成功',
		type: '退款',
		success: true,
		isSystem: false,
		countDown: {
			isCountDown: false,
			leftText: '',
			rightText: '',
		},
		closable: false,
		applyForAgain: false,
		isConsumer: false,
		typeStatus: 1,
		msg: [{
			title: ''
		}, {
			title: ''
		}],
		canChangePackageStatus: true,
		canChangePackageStatusText: false,
		isRefunded: true,
	},
	RETURN_REFUND_REQUEST: {
		title: '请等待商家处理',
		type: '退货退款',
		list: '申请退货退款 待审核',
		desc: '你已成功发起退货退款申请，请耐心请等待商家处理',
		countDown: {
			isCountDown: true,
			leftText: '还剩',
			rightText: '',
		},
		success: false,
		isSystem: false,
		closable: true,
		applyForAgain: false,
		isConsumer: true,
		typeStatus: 0,
		msg: [{
			title: '商家同意或者超时未支付，系统将退款给你'
		}, {
			title: '如果商家拒绝，你可以再次发起售后，卖家会重新处理'
		}],
		canChangePackageStatus: true,
		canChangePackageStatusText: true,
		isRefunded: false,
	},
	RETURN_REFUND_AGREE: {
		title: '已同意退货退款',
		type: '退货退款',
		list: '申请退货退款 待退货',
		desc: '商家已同意退货退款申请，请尽早退货',
		countDown: {
			isCountDown: true,
			leftText: '还剩',
			rightText: '',
		},
		closable: true,
		isSystem: false,
		success: false,
		applyForAgain: false,
		isConsumer: false,
		typeStatus: 0,
		msg: [{
				title: '超时未处理，系统将自动关闭退款申请'
			},
			{
				title: '未予商家协商一致，请勿使用到付或者平邮'
			},
			{
				title: '以免商家拒签货物请填写真实物流信息'
			},
		],
		canChangePackageStatus: true,
		canChangePackageStatusText: true,
		isRefunded: false,
	},
	SYSTEM_RETURN_REFUND_AGREE: {
		title: '系统已同意退货退款',
		list: '申请退货退款 待退货',
		type: '退货退款',
		desc: '系统已同意退货退款申请，请尽早退货',
		isSystem: true,
		countDown: {
			isCountDown: true,
			leftText: '还剩',
			rightText: '',
		},
		closable: true,
		success: false,
		applyForAgain: false,
		isConsumer: false,
		typeStatus: 0,
		msg: [{
				title: '超时未处理，系统将自动关闭退款申请'
			},
			{
				title: '未予商家协商一致，请勿使用到付或者平邮'
			},
			{
				title: '以免商家拒签货物请填写真实物流信息'
			},
		],
		canChangePackageStatus: true,
		canChangePackageStatusText: true,
		isRefunded: false,
	},
	RETURN_REFUND_REJECT: {
		title: '退款关闭',
		type: '退货退款',
		list: '退货退款关闭',
		desc: '因商家拒绝了你的退货退款申请，本次售后关闭',
		countDown: {
			isCountDown: false,
			leftText: '',
			rightText: '',
		},
		closable: false,
		isSystem: false,
		success: false,
		applyForAgain: true,
		isConsumer: true,
		typeStatus: -1,
		msg: [{
			title: '商家同意或者超时未支付，系统将退款给你'
		}, {
			title: '如果商家拒绝，你可以再次发起售后，卖家会重新处理'
		}],
		canChangePackageStatus: true,
		canChangePackageStatusText: false,
		isRefunded: false,
	},
	RETURNED_REFUND: {
		title: '请等待商家收货',
		list: '申请退货退款 待商家收货',
		type: '退货退款',
		desc: '如果商家收到货并验货无误，将操作退款给您',
		success: false,
		closable: false,
		isSystem: false,
		applyForAgain: false,
		countDown: {
			isCountDown: true,
			leftText: '还剩',
			rightText: '',
		},
		isConsumer: true,
		typeStatus: 0,
		msg: [{
			title: '如果商家拒绝退款，需要您和商家协商'
		}, {
			title: '如果商家超时未处理，将自动退款给您'
		}],
		canChangePackageStatus: false,
		canChangePackageStatusText: false,
		isRefunded: false,
	},
	RETURNED_REFUND_CONFIRM: {
		title: '商家已收到退货，等待退款',
		type: '退货退款',
		list: '确认退货已收到',
		success: false,
		isSystem: false,
		desc: '商家已收到退货，请耐心请等待退款到账',
		countDown: {
			isCountDown: true,
			leftText: '还剩',
			rightText: '',
		},
		closable: false,
		applyForAgain: false,
		isConsumer: false,
		typeStatus: 0,
		msg: [{
			title: '商家同意或者超时未支付，系统将退款给你'
		}, {
			title: '如果商家拒绝，你可以再次发起售后，卖家会重新处理'
		}],
		canChangePackageStatus: false,
		canChangePackageStatusText: false,
		isRefunded: false,
	},
	SYSTEM_RETURNED_REFUND_CONFIRM: {
		title: '系统确认收到退货，等待退款',
		type: '退货退款',
		list: '系统确认退货已收到',
		success: false,
		isSystem: true,
		desc: '系统确认收到退货，请耐心请等待退款到账',
		countDown: {
			isCountDown: true,
			leftText: '还剩',
			rightText: '',
		},
		closable: false,
		applyForAgain: false,
		isConsumer: false,
		typeStatus: 0,
		msg: [{
			title: '商家同意或者超时未支付，系统将退款给你'
		}, {
			title: '如果商家拒绝，你可以再次发起售后，卖家会重新处理'
		}],
		canChangePackageStatus: false,
		canChangePackageStatusText: false,
		isRefunded: false,
	},
	RETURNED_REFUND_REJECT: {
		title: '退款关闭',
		type: '退货退款',
		list: '退货退款关闭',
		isSystem: false,
		countDown: {
			isCountDown: false,
			leftText: '',
			rightText: '',
		},
		desc: '因商家拒绝收货，本次售后关闭',
		success: false,
		closable: false,
		applyForAgain: true,
		isConsumer: true,
		typeStatus: -1,
		msg: [{
			title: '商家同意或者超时未支付，系统将退款给你'
		}, {
			title: '如果商家拒绝，你可以再次发起售后，卖家会重新处理'
		}],
		canChangePackageStatus: true,
		canChangePackageStatusText: false,
		isRefunded: false,
	},
	RETURNED_REFUNDED: {
		countDown: {
			isCountDown: false,
			leftText: '',
			rightText: '',
		},
		title: '退款成功',
		list: '退货退款成功',
		desc: '退款成功',
		type: '退货退款',
		success: true,
		closable: false,
		isSystem: false,
		applyForAgain: false,
		isConsumer: false,
		typeStatus: 1,
		msg: [{
			title: ''
		}, {
			title: ''
		}],
		canChangePackageStatus: true,
		canChangePackageStatusText: false,
		isRefunded: true,
	},
	BUYER_CLOSED: {
		title: '售后关闭',
		type: '售后',
		list: '售后关闭',
		desc: '您已撤销售后申请',
		countDown: {
			isCountDown: false,
			leftText: '',
			rightText: '',
		},
		success: false,
		isSystem: false,
		closable: false,
		applyForAgain: true,
		isConsumer: true,
		typeStatus: -1,
		msg: [{
			title: '如你的问题未解决，你还可以继续发起申请'
		}],
		canChangePackageStatus: true,
		canChangePackageStatusText: false,
		isRefunded: false,
	},
	SYSTEM_CLOSED: {
		title: '售后关闭',
		list: '超时未处理，系统关闭',
		type: '售后',
		desc: '系统自动关闭',
		countDown: {
			isCountDown: false,
			leftText: '',
			rightText: '',
		},
		success: false,
		closable: false,
		isSystem: true,
		applyForAgain: true,
		isConsumer: false,
		typeStatus: -1,
		msg: [{
			title: '如你的问题未解决，你还可以继续发起申请'
		}],
		canChangePackageStatus: true,
		canChangePackageStatusText: false,
		isRefunded: false,
	},
}

/**
 * 退款原因提示转为中文
 * @param  status
 */
export const getARefundReasonCn = status => {
	return aRefundWhy[status].title
}

/**
 * 售后状态配置
 * @param status
 */
export const getAfsStatusCn = status => {
	return afsStatus[status]
}

/**
 * Btn售后状态转中文
 * @param status
 */
export const getAsfsStatusBtnCn = status => {
	return afsStatusBtn[status]
}