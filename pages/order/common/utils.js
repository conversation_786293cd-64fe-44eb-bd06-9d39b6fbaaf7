export const OrderStatusZh = {
	WAITING_FOR_DELIVER: '待发货',
	WAITING_FOR_RECEIVE: '待收货',
	WAITING_FOR_DELIVER_PART: '待发货 (部分发货)',
	COMMENTED_COMPLETED: '已完成',
	WAITING_FOR_COMMENT: '待评价',
}

/**
 * OK: '正常状态',
 * SYSTEM_CLOSED: '系统关闭',
 * BUYER_CLOSED: '买家关闭订单',
 * SELLER_CLOSED: '卖家关闭订单',
 */
export const ShopOrderStatusPlusHandler = {
	OK: {
		desc: '正常状态',
		isClosed: false,
	},
	SYSTEM_CLOSED: {
		desc: '已关闭',
		isClosed: true,
	},
	BUYER_CLOSED: {
		desc: '已关闭',
		isClosed: true,
	},
	SELLER_CLOSED: {
		desc: '已关闭',
		isClosed: true,
	},
}

/**
 * UNPAID: '等待买家付款',
 * PAID: '已支付',
 * BUYER_CLOSED: '买家关闭订单',
 * SYSTEM_CLOSED: '系统关闭订单',
 * SELLER_CLOSED: '卖家关闭订单',
 */
export const OrderStatusPlusHandler = {
	UNPAID: {
		desc: '待支付',
		isClosed: false,
	},
	PAID: {
		desc: '待发货',
		isClosed: false,
	},
	BUYER_CLOSED: {
		desc: '已关闭',
		isClosed: true,
	},
	SYSTEM_CLOSED: {
		desc: '已关闭',
		isClosed: true,
	},
	SELLER_CLOSED: {
		desc: '已关闭',
		isClosed: true,
	},
	TEAMING: {
		desc: '拼团中 - 预留',
		isClosed: false,
	},
	TEAM_FAIL: {
		desc: '拼团失败 - 预留',
		isClosed: true,
	},
}

/**
 * 如果是等待付款就展示付款按钮
 * @param {String} status
 */
export const isUnpaidOrder = (status) => {
	return ['UNPAID'].includes(status)
}

/**
 * 未付款订单状态改中文
 * @param {String} str
 */
export const getOrdercn = (str) => {
	return orderStatus[str]
}

// 拼团判断 展示我的拼团按钮
export const isTeam = (order) => {
	return ['TEAM'].includes(order.type) && order.status !== 'TEAM_FAIL' && order.status !== 'UNPAID'
}

/**
 * 是否支付订单
 * @param {String} status 订单下所有状态
 */
export const isPayOrder = (status) => {
	return !['UNPAID'].includes(status)
}

/**
 * 是否待收货
 * @param {String} status 订单下所有状态
 */
export const isReceive = (status) => {
	return ['WAITING_FOR_RECEIVE', 'BUYER_COMMENTED_COMPLETED'].includes(status)
}

/**
 * 是否已完成
 * @param {String} status 订单下所有状态
 */
export const isCompleted = (status) => {
	return ['BUYER_COMMENTED_COMPLETED', 'SYSTEM_COMMENTED_COMPLETED'].includes(status)
}

/**
 * 是否待评价
 * @param {String} status 订单下所有状态
 */
export const isComment = (status) => {
	return ['BUYER_WAITING_FOR_COMMENT', 'SYSTEM_WAITING_FOR_COMMENT'].includes(status)
}

/**
 * 关闭订单 展示删除订单按钮
 * @param {String} status 订单下所有状态
 */
export const isCloseOrder = (status) => {
	return ['BUYER_CLOSED', 'SYSTEM_CLOSED', 'SELLER_CLOSED'].includes(status)
}

export const IcStatus = {
	/**
	 * 运费价格计算 待发单（未发货）
	 * 这种状态的单子可以随时丢弃
	 */
	PRICE_PADDING: '待发货',
	/**
	 * 待接单
	 */
	PENDING: '待接单',
	/**
	 * 已接单待到店
	 */
	TAKEN: '待到店',
	/**
	 * 已到店 待取货
	 */
	ARRIVED_SHOP: '待取货',
	/**
	 * 已取货 配送中
	 */
	PICKUP: '配送中',
	/**
	 * 已送达
	 */
	DELIVERED: '已送达',
	/**
	 * 异常 配送异常
	 */
	ERROR: '配送异常',
}

export function orderStatusPlusInfo(order, shopOrder = {}, showIc = false) {
	const statusInfo = orderStatusPlus(order, shopOrder, showIc)

	if (statusInfo.desc === '待支付') {
		statusInfo.status = 'UNPAID'
	}

	if (statusInfo.desc === '待发货' || statusInfo.desc === '待发货 (部分发货)') {
		statusInfo.status = 'UN_DELIVERY'
	}

	if (statusInfo.desc === '待收货') {
		statusInfo.status = 'UN_RECEIVE'
	}

	if (statusInfo.desc === '待评价') {
		statusInfo.status = 'UN_COMMENT'
	}

	if (statusInfo.desc === '已完成') {
		statusInfo.status = 'COMPLETED'
	}

	if (statusInfo.desc === '已关闭') {
		statusInfo.status = 'CLOSED'
	}

	return statusInfo
}

export function orderStatusPlus(order, shopOrder = {}, showIc = true) {
	const statusPlus = {
		desc: '已关闭',
		isClosed: true, // 是否展示评价按钮
	}

	if (order?.status === 'UNPAID') {
		statusPlus.desc = '待支付'
		return statusPlus
	}

	if (order?.shopOrders?.[0]?.status !== 'OK') {
		return statusPlus
	}

	if (order && order.icStatus && order.extra?.distributionMode && showIc) {
		statusPlus.desc = IcStatus[order.icStatus || 'PRICE_PADDING']
		statusPlus.isClosed = order.icStatus !== 'DELIVERED'
		const shopOrderItems = shopOrder.shopOrderItems
		if (order.icStatus === 'ERROR') {
			for (let i = 0; i < shopOrderItems.length; i++) {
				const shopOrderItem = shopOrderItems[i]
				if (shopOrderItem.status === 'OK' && shopOrderItem.packageStatus === 'WAITING_FOR_DELIVER' && shopOrderItem
					.sellType !== 'CONSIGNMENT') {
					statusPlus.desc = IcStatus['PRICE_PADDING']
				}
			}
		} else if (order.icStatus === 'DELIVERED') {
			// 拿到所有的包裹状态
			const mergeStatus = shopOrderItems.map((item) => item.packageStatus)
			/**
			 * packageStatus:
			 * WAITING_FOR_DELIVER: '待发货',
			 * WAITING_FOR_RECEIVE: '待收货',
			 * BUYER_WAITING_FOR_COMMENT: '待评价',
			 * SYSTEM_WAITING_FOR_COMMENT: '待评价',
			 * BUYER_COMMENTED_COMPLETED: '已完成',
			 * SYSTEM_COMMENTED_COMPLETED: '已完成',
			 */
			if (mergeStatus.includes('WAITING_FOR_DELIVER')) {
				statusPlus.desc = '待发货'
			}
			if (mergeStatus.includes('WAITING_FOR_RECEIVE')) {
				statusPlus.desc = '待收货'
			}
			// 有一个待评价就待评价
			if (mergeStatus.includes('BUYER_WAITING_FOR_COMMENT') || mergeStatus.includes('SYSTEM_WAITING_FOR_COMMENT')) {
				statusPlus.desc = '待评价'
			}
			// 有一个已完成就已完成
			if (mergeStatus.includes('BUYER_COMMENTED_COMPLETED') || mergeStatus.includes('SYSTEM_COMMENTED_COMPLETED')) {
				statusPlus.desc = '已完成'
			}
		}
		return statusPlus
	}
	if (shopOrder.status !== 'OK') {
		const statusPlus = ShopOrderStatusPlusHandler[shopOrder.status]
		statusPlus.closeTime = shopOrder.updateTime
		return statusPlus
	}
	const shopOrderItems = shopOrder.shopOrderItems

	//检查是否全部关闭
	const deliverConfig = {
		deliverNum: 0,
		unDeliverNum: 0,
		okNum: 0,
		// 已评价
		evaluation: 0,
	}
	for (let shopOrderItem of shopOrderItems) {
		if (shopOrderItem.status === 'OK') {
			deliverConfig.okNum += 1
			deliverConfig.unDeliverNum += shopOrderItem.packageStatus === 'WAITING_FOR_DELIVER' ? 1 : 0
			deliverConfig.deliverNum += shopOrderItem.packageStatus === 'WAITING_FOR_RECEIVE' ? 1 : 0
			deliverConfig.evaluation += ['BUYER_COMMENTED_COMPLETED', 'SYSTEM_COMMENTED_COMPLETED'].includes(shopOrderItem
				.packageStatus) ? 1 : 0
		}

		const currentCloseTime = statusPlus.closeTime
		const closeTime = shopOrderItem.updateTime
		if (!closeTime) {
			continue
		}
		statusPlus.closeTime = !currentCloseTime ? closeTime : closeTime > currentCloseTime ? closeTime : currentCloseTime
	}
	const {
		unDeliverNum,
		evaluation,
		deliverNum,
		okNum
	} = deliverConfig
	if (okNum) {
		const deliverConfigTotal = deliverNum + unDeliverNum
		statusPlus.isClosed = false
		statusPlus.desc =
			unDeliverNum === okNum ?
			OrderStatusZh.WAITING_FOR_DELIVER :
			deliverNum === okNum ?
			OrderStatusZh.WAITING_FOR_RECEIVE :
			deliverConfigTotal === okNum ?
			OrderStatusZh.WAITING_FOR_DELIVER_PART :
			evaluation === okNum ?
			OrderStatusZh.COMMENTED_COMPLETED :
			OrderStatusZh.WAITING_FOR_COMMENT
	}
	return statusPlus
}