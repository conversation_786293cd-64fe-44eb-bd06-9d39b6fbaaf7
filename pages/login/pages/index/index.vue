<script setup>
import { computed, reactive, ref } from 'vue'

import { onShow, onLoad } from '@dcloudio/uni-app'

import { sendSMScaptcha, doSignByUser } from '@/server/api'

import { REGEX_MOBILE } from '@/common/test'

import { canENV, goArticlePage, route } from '@/common/utils'

import { useUserStore, usePublicStore, useLoginStore, useAppStore } from '@/store'
import { common, logo } from '@/common/images'

const userStore = useUserStore()
const loginStore = useLoginStore()
const appStore = useAppStore()

let univerifyManager = null
// #ifdef APP
univerifyManager = uni.getUniverifyManager()
// #endif

const publicStore = usePublicStore()
const interTimer = ref(null)
const pageData = reactive({
    formData: {
        mobile: '',
        code: '',
    },
    timer: 60,

    canIOneClickLogin: false,

    pageLoading: false,
})

const pagePhoneLoading = ref(true)

onLoad(async () => {
    pageData.pageLoading = true

    publicStore.changeAgreeStatus(false)

    await appStore.initLogin()

    // pagePhoneLoading.value = false;
    pageData.pageLoading = false

    // #ifndef MP-WEIXIN
    if (!appStore.configInfo.appOneClickLogin && !appStore.configInfo.wxLogin) {
        route({
            url: '/pages/login/pages/codeLogin/codeLogin',
            type: 'redirectTo',
        })
    }
    // #endif
    // #ifdef MP-WEIXIN
    if (!appStore.configInfo.mpOneClickLogin) {
        route({
            url: '/pages/login/pages/codeLogin/codeLogin',
            type: 'redirectTo',
        })
    }
    // #endif

    canENV(() => {
        pageData.formData.mobile = '15093420120'
        console.log(appStore.configInfo)
    })
})

onShow(() => {
    // const storedTimer = uni.getStorageSync('codeTimer');
    // if (storedTimer > 0) {
    // 	// 如果有，则使用该时间重新设置定时器
    // 	pageData.timer = storedTimer;
    // 	startTimer();
    // }
})

const codeRef = ref()
const codeTips = ref('')

function codeChange(text) {
    codeTips.value = text
}

// 获取验证码
function getCode() {
    // startTimer();

    if (codeRef.value && codeRef.value.canGetCode) {
        if (REGEX_MOBILE(pageData.formData.mobile)) {
            sendSMScaptcha({
                form: pageData.formData.mobile,
            }).then((res) => {
                if (res.apiStatus) {
                    uni.showToast({
                        title: '验证码已发送',
                        position: 'bottom',
                        mask: false,
                        icon: 'none',
                    })
                    canENV(
                        () => {
                            pageData.formData.code = res.data
                        },
                        () => {
                            if (res.data !== 0) {
                                pageData.formData.code = res.data
                            }
                        },
                    )
                    if (codeRef.value) codeRef.value.start()
                }
            })
        } else {
            uni.showToast({
                title: '请输入正确的手机号',
                position: 'bottom',
                mask: true,
                icon: 'none',
            })
        }
    }
}

const submitDisabled = computed(() => {
    return !pageData.formData.mobile || !pageData.formData.code
})

function submit() {
    if (!publicStore.agreeStatus) {
        uni.showToast({
            title: '请先阅读并同意协议',
            position: 'bottom',
            mask: true,
            icon: 'none',
        })
        return
    }

    pageData.pageLoading = true
    loginStore
        .codeLogin(pageData.formData)
        .then((res) => {
            pageData.pageLoading = false
        })
        .catch((err) => {
            pageData.pageLoading = false
        })
}

function goCodeLogin() {
    if (!publicStore.agreeStatus) {
        uni.showToast({
            title: '请先阅读并同意协议',
            position: 'bottom',
            mask: true,
            icon: 'none',
        })
        return
    }

    route('/pages/login/pages/codeLogin/codeLogin')
}

function submitAgree() {
    if (!publicStore.agreeStatus) {
        uni.showToast({
            title: '请先阅读并同意协议',
            position: 'bottom',
            mask: true,
            icon: 'none',
        })
        return
    }
}

function openPhoneConvenientLogin() {
    // #ifdef APP

    if (univerifyManager) {
        // pagePhoneLoading.value = true;
        pageData.pageLoading = true

        // 取消订阅自定义按钮点击事件
        univerifyManager.offButtonsClick(PhoneConvenientLoginButtons)

        // 订阅自定义按钮点击事件
        univerifyManager.onButtonsClick(PhoneConvenientLoginButtons)

        univerifyManager.login({
            provider: 'univerify',
            univerifyStyle: {
                fullScreen: false, // 是否全屏显示，默认值： false
                backgroundColor: '#F9FBFF', // 授权页面背景颜色，默认值：#ffffff
                backgroundImage: '', // 全屏显示的背景图片，默认值："" （仅支持本地图片，只有全屏显示时支持）
                icon: {
                    // path: '/static/logo.png', // 自定义显示在授权框中的logo，仅支持本地图片 默认显示App logo
                    width: '100px', //图标宽度 默认值：60px
                    height: '100px', //图标高度 默认值：60px
                },
                closeIcon: {
                    path: '/pages/login/static/close_page.png', // 自定义显示在授权框中的logo，仅支持本地图片
                    width: '20px', //图标宽度 默认值：60px (HBuilderX 4.0+ 仅iOS支持)
                    height: '20px', //图标高度 默认值：60px (HBuilderX 4.0+ 仅iOS支持)
                },
                phoneNum: {
                    color: '#101010', // 手机号文字颜色 默认值：#202020
                },
                slogan: {
                    color: '#101010', //  slogan 字体颜色 默认值：#BBBBBB
                },
                authButton: {
                    normalColor: '#00B496', // 授权按钮正常状态背景颜色 默认值：#3479f5
                    highlightColor: '#008f77', // 授权按钮按下状态背景颜色 默认值：#2861c5（仅ios支持）
                    disabledColor: '#9bded3', // 授权按钮不可点击时背景颜色 默认值：#73aaf5（仅ios支持）
                    textColor: '#ffffff', // 授权按钮文字颜色 默认值：#ffffff
                    title: '手机号码一键登录', // 授权按钮文案 默认值：“本机号码一键登录”
                    borderRadius: '24px', // 授权按钮圆角 默认值："24px" （按钮高度的一半）
                },
                otherLoginButton: {
                    visible: true, // 是否显示其他登录按钮，默认值：true
                    normalColor: '#FFFFFF', // 其他登录按钮正常状态背景颜色 默认值：透明
                    highlightColor: '#f1f1f1', // 其他登录按钮按下状态背景颜色 默认值：透明
                    textColor: '#000000', // 其他登录按钮文字颜色 默认值：#656565
                    title: '其他手机号码登录', // 其他登录方式按钮文字 默认值：“其他登录方式”
                    borderColor: '#EEF1F6', //边框颜色 默认值：透明（仅iOS支持）
                    borderRadius: '24px', // 其他登录按钮圆角 默认值："24px" （按钮高度的一半）
                },
                privacyTerms: {
                    defaultCheckBoxState: false, // 条款勾选框初始状态 默认值： true
                    isCenterHint: false, //未勾选服务条款时点击登录按钮的提示是否居中显示 默认值: false (3.7.13+ 版本支持)
                    uncheckedImage: common.iconXuianzeWeixuanzhong, // 可选 条款勾选框未选中状态图片（仅支持本地图片 建议尺寸 24x24px）(3.2.0+ 版本支持)
                    checkedImage: common.iconXuanzeXhuanzhong, // 可选 条款勾选框选中状态图片（仅支持本地图片 建议尺寸24x24px）(3.2.0+ 版本支持)
                    checkBoxSize: 16, // 可选 条款勾选框大小
                    textColor: '#131311', // 文字颜色 默认值：#BBBBBB
                    termsColor: '#00B496', //  协议文字颜色 默认值： #5496E3
                    prefix: '我已阅读并同意', // 条款前的文案 默认值：“我已阅读并同意”
                    suffix: '并使用本机号码登录', // 条款后的文案 默认值：“并使用本机号码登录”
                },
                buttons: {
                    iconWidth: '45px',
                },
            },
            success(res) {
                canENV(() => {
                    console.log('一键登录 => ', {
                        ...res,
                        appid: uni.getSystemInfoSync().appId,
                    })
                })

                loginStore
                    .appOneClickLogin({
                        ...res.authResult,
                        appid: uni.getSystemInfoSync().appId,
                    })
                    .then((loginRes) => {
                        canENV(() => {
                            console.log('一键登录登录 => ', loginRes)
                        })
                        univerifyManager.close()
                        // userStore.userLogin(loginRes);
                        // pagePhoneLoading.value = false;
                        pageData.pageLoading = false
                    })
                    .catch((err) => {
                        canENV(() => {
                            console.error('一键登录登录 => ', err)
                        })
                        uni.showToast({
                            icon: 'none',
                            title: '登录失败，请使用其他方式登录!',
                            position: 'bottom',
                        })
                        univerifyManager.close()
                        // pagePhoneLoading.value = false;
                        pageData.pageLoading = false
                        // 其他登录
                        goCodeLogin()
                    })
            },
            fail(err) {
                canENV(() => {
                    console.error(err)
                })

                if (err.code !== 30002 && err.code !== 30003) {
                    uni.showToast({
                        icon: 'none',
                        title: '登录失败，请使用其他方式登录!',
                        position: 'bottom',
                    })
                }

                if (err.code !== 30003) {
                    univerifyManager.close()
                    // pagePhoneLoading.value = false;
                    pageData.pageLoading = false
                    // 其他登录
                    goCodeLogin()
                } else {
                    // 手动关闭
                    pageData.pageLoading = false
                }
            },
        })
    }
    // #endif
}

function appWXLogin() {
    if (!publicStore.agreeStatus) {
        uni.showToast({
            title: '请先阅读并同意协议',
            position: 'bottom',
            mask: true,
            icon: 'none',
        })
        return
    }

    pageData.pageLoading = true
    loginStore
        .weiXinLogin()
        .then((res) => {
            pageData.pageLoading = false
        })
        .catch((err) => {
            pageData.pageLoading = false
        })
}

function PhoneConvenientLoginButtons() {
    if (univerifyManager) {
        // 获取一键登录弹框协议勾选状态
        univerifyManager.getCheckBoxState({
            success(res) {
                // console.log('getCheckBoxState res: ', res);
                if (res.state) {
                    // 关闭一键登录弹框
                    univerifyManager.close()
                }
            },
        })
    }
}

function submitGetPhoneNumber(e) {
    pageData.pageLoading = true
    loginStore
        .getPhoneNumberLogin(e)
        .then((res) => {
            pageData.pageLoading = false
        })
        .catch((err) => {
            pageData.pageLoading = false
        })
}
</script>
<template>
    <app-layout>
        <app-navBar
            back
            backIconSize="30rpx"
            left-placeholder-size="70rpx"
            rightPlaceholderSize="70rpx"
            :backIcon="common.iconCloseHei"
            :fixed="false"
            :fixed-placeholder="false"
        >
            <template #content>
                <view class="w-full h-full flex justify-between items-center px-0">
                    <view class="flex-1 nav-center">
                        <text class="text-32 text-#fff font-bold"></text>
                    </view>
                </view>
            </template>
        </app-navBar>

        <view class="oneClickLogin">
            <view class="pt-60 flex flex-col items-center">
                <image class="w-340 h-340" :src="logo" mode=""></image>

                <view class="mt-200">
                    <!-- #ifdef APP -->
                    <template v-if="appStore.configInfo.appOneClickLogin">
                        <view class="mb-30">
                            <template v-if="publicStore.agreeStatus">
                                <uv-button
                                    @click="openPhoneConvenientLogin"
                                    color="#00B496"
                                    text="手机号码一键登录"
                                    :loading="pageData.pageLoading"
                                    class="mt-60"
                                    custom-style="width: 604rpx; height: 90rpx; border-radius: 45rpx;"
                                    customTextStyle="font-size: 30rpx; color: #fff;font-bold;"
                                ></uv-button>
                            </template>
                            <template v-else>
                                <uv-button
                                    @click="submitAgree"
                                    color="#00B496"
                                    text="手机号码一键登录"
                                    :loading="pageData.pageLoading"
                                    class="mt-60"
                                    custom-style="width: 604rpx; height: 90rpx; border-radius: 45rpx;"
                                    customTextStyle="font-size: 30rpx; color: #fff;font-bold;"
                                ></uv-button>
                            </template>
                        </view>
                    </template>
                    <!-- #endif -->

                    <!-- #ifdef MP-WEIXIN -->
                    <template v-if="appStore.configInfo.mpOneClickLogin">
                        <view class="mb-30">
                            <template v-if="publicStore.agreeStatus">
                                <uv-button
                                    open-type="getPhoneNumber"
                                    withCredentials="true"
                                    @getphonenumber="submitGetPhoneNumber"
                                    color="#00B496"
                                    text="手机号码一键登录"
                                    :loading="pageData.pageLoading"
                                    class="mt-60"
                                    custom-style="width: 604rpx; height: 90rpx; border-radius: 45rpx;"
                                    customTextStyle="font-size: 30rpx; color: #fff;font-bold;"
                                ></uv-button>
                            </template>
                            <template v-else>
                                <uv-button
                                    @click="submitAgree"
                                    color="#00B496"
                                    text="手机号码一键登录"
                                    :loading="pageData.pageLoading"
                                    class="mt-60"
                                    custom-style="width: 604rpx; height: 90rpx; border-radius: 45rpx;"
                                    customTextStyle="font-size: 30rpx; color: #fff;font-bold;"
                                ></uv-button>
                            </template>
                        </view>
                    </template>
                    <!-- #endif -->

                    <uv-button
                        @click="goCodeLogin"
                        color="#EFEFEF"
                        text="验证码登录"
                        class="mt-0"
                        custom-style="width: 604rpx; height: 90rpx; border-radius: 45rpx;"
                        customTextStyle="font-size: 30rpx; color: #323232; font-bold;"
                    ></uv-button>
                </view>
            </view>

            <view class="fixed w-full left-0 bottom-80">
                <template v-if="appStore.configInfo.wxLogin">
                    <view class="w-full flex items-center justify-center mb-40">
                        <view class="w-70 h-1 bg-#D4DDE4 mr-0"></view>
                        <app-image
                            src="@/pages/login/static/wx_login.png"
                            @click="appWXLogin"
                            class="mx-30 ac-op"
                            rd="104"
                            size="104"
                            mode="widthFix"
                            v-if="appStore.configInfo.wxLogin"
                        ></app-image>
                        <view class="w-70 h-1 bg-#D4DDE4 ml-0"></view>
                    </view>
                </template>

                <view @click="publicStore.updateAgreeStatus()" class="flex justify-center items-center text-22 text-#131311 mt-42">
                    <image v-if="publicStore.agreeStatus" class="w-28 h-28 mr-15" src="@/pages/login/static/agree_true.png" mode=""></image>
                    <image v-else class="w-28 h-28 mr-15" src="@/pages/login/static/agree_false.png" mode=""></image>
                    <text>我已阅读并同意</text>
                    <text class="text-#00B496" @click.stop="goArticlePage('fwxy')">《和家无忧服务协议》</text>
                    <text>与</text>
                    <text class="text-#00B496" @click.stop="goArticlePage('yszc')">《和家无忧隐私政策》</text>
                </view>
                <app-safeAreaBottom></app-safeAreaBottom>
            </view>
        </view>
    </app-layout>
</template>
<style lang="scss" scoped>
.oneClickLogin {
}
</style>
