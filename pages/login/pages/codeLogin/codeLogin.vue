<script setup>
import { computed, reactive, ref } from "vue";

import { onShow, onLoad } from "@dcloudio/uni-app";

import { sendSMScaptcha, doSignByUser } from "@/server/api";

import { REGEX_MOBILE } from "@/common/test";

import { canENV, goArticlePage } from "@/common/utils";

import { useUserStore, usePublicStore, useLoginStore } from "@/store";
import { logo } from "@/common/images";

const userStore = useUserStore();
const loginStore = useLoginStore();

const publicStore = usePublicStore();
const interTimer = ref(null);
const pageData = reactive({
	formData: {
		mobile: "",
		code: "",
	},
	timer: 60,

	pageLoading: false,
});

onLoad(() => {
	canENV(() => {
		pageData.formData.mobile = "15093420120";
	});

	publicStore.changeAgreeStatus(false);
});

onShow(() => {
	// const storedTimer = uni.getStorageSync('codeTimer');
	// if (storedTimer > 0) {
	// 	// 如果有，则使用该时间重新设置定时器
	// 	pageData.timer = storedTimer;
	// 	startTimer();
	// }
});

const codeRef = ref();
const codeTips = ref("");

function codeChange(text) {
	codeTips.value = text;
}

// 获取验证码
function getCode() {
	// startTimer();

	if (codeRef.value && codeRef.value.canGetCode) {
		if (REGEX_MOBILE(pageData.formData.mobile)) {
			sendSMScaptcha({
				form: pageData.formData.mobile,
			}).then((res) => {
				if (res.apiStatus) {
					uni.showToast({
						title: "验证码已发送",
						position: "bottom",
						mask: false,
						icon: "none",
					});
					canENV(
						() => {
							pageData.formData.code = res.data;
						},
						() => {
							pageData.formData.code = res.data;
						}
					);
					if (codeRef.value) codeRef.value.start();
				}
			});
		} else {
			uni.showToast({
				title: "请输入正确的手机号",
				position: "bottom",
				mask: true,
				icon: "none",
			});
		}
	}
}

const submitDisabled = computed(() => {
	return !pageData.formData.mobile || !pageData.formData.code;
});

function submit() {
	if (!publicStore.agreeStatus) {
		uni.showToast({
			title: "请先阅读并同意协议",
			position: "bottom",
			mask: true,
			icon: "none",
		});
		return;
	}

	pageData.pageLoading = true;
	loginStore
		.codeLogin(pageData.formData)
		.then((res) => {
			pageData.pageLoading = false;
		})
		.catch((err) => {
			pageData.pageLoading = false;
		});
}

function submitAgree() {
	if (!publicStore.agreeStatus) {
		uni.showToast({
			title: "请先阅读并同意协议",
			position: "bottom",
			mask: true,
			icon: "none",
		});
		return;
	}
}

function submitGetPhoneNumber(e) {
	pageData.pageLoading = true;
	loginStore
		.getPhoneNumberLogin(e)
		.then((res) => {
			pageData.pageLoading = false;
		})
		.catch((err) => {
			pageData.pageLoading = false;
		});
}
</script>
<template>
	<app-layout>
		<app-navBar back rightPlaceholderSize="70rpx" :fixed="false" :fixed-placeholder="false">
			<template #content>
				<view class="w-full h-full flex justify-between items-center px-0">
					<view class="flex-1 nav-center">
						<text class="text-32 text-#fff font-bold"></text>
					</view>
				</view>
			</template>
		</app-navBar>
		<view class="oneClickLogin">
			<view class="pt-60 flex flex-col items-center">
				<image class="w-340 h-340" :src="logo" mode=""></image>

				<view class="w-640 mt-50">
					<uni-forms :modelValue="pageData.formData" label-position="top">
						<uni-forms-item name="mobile">
							<template #label>
								<view class="text-26 text-#222222 font-bold mb-22">手机号</view>
							</template>
							<input class="bg-#F2F4F8 h-105 px-40 border-rd-53 text-36 text-#222222 font-bold" type="number" v-model="pageData.formData.mobile" placeholder="请输入手机号" :maxlength="11" placeholder-class="text-32 text-#A7ACB7 font-500" />
						</uni-forms-item>
						<uni-forms-item name="code">
							<template #label>
								<view class="text-26 text-#222222 font-bold mb-22">验证码</view>
							</template>
							<view class="bg-#F2F4F8 h-105 border-rd-53 flex items-center">
								<input class="w-370 h-105 px-40 border-rd-53 text-36 text-#222222 font-bold" type="number" v-model="pageData.formData.code" placeholder="请输入验证码" placeholder-class="text-32 text-#A7ACB7 font-500" :maxlength="4" />
								<view v-if="pageData.timer == 60" @click="getCode()" class="w-230 text-30 text-#0072FE font-bold text-center">
									{{ codeTips }}
								</view>
								<view v-else class="w-230 text-26 text-#616161 font-bold text-center">
									{{ codeTips }}
								</view>

								<uv-code ref="codeRef" @change="codeChange" :seconds="60"></uv-code>
							</view>
						</uni-forms-item>
					</uni-forms>
				</view>
				<view class="">
					<uv-button @click="submit" color="#00B496" text="登录" :loading="pageData.pageLoading" :disabled="submitDisabled" class="mt-60" custom-style="width: 640rpx; height: 90rpx; border-radius: 45rpx;" customTextStyle="font-size: 30rpx; color: #fff;font-bold;"></uv-button>
					<!-- <view class="mt-24 text-24 text-#434141 font-500 text-center">
						没有账号？去注册
					</view> -->
				</view>
			</view>

			<view class="fixed w-full left-0 bottom-80">
				<view @click="publicStore.updateAgreeStatus()" class="flex justify-center items-center text-22 text-#131311 mt-42">
					<image v-if="publicStore.agreeStatus" class="w-28 h-28 mr-15" src="@/pages/login/static/agree_true.png" mode=""></image>
					<image v-else class="w-28 h-28 mr-15" src="@/pages/login/static/agree_false.png" mode=""></image>
					<text>登录即同意</text>
					<text class="text-#00B496" @click.stop="goArticlePage('fwxy')">《和家无忧服务协议》</text>
					<text>与</text>
					<text class="text-#00B496" @click.stop="goArticlePage('yszc')">《和家无忧隐私政策》</text>
				</view>
				<app-safeAreaBottom></app-safeAreaBottom>
			</view>
		</view>
	</app-layout>
</template>
<style lang="scss" scoped>
.oneClickLogin {
}
</style>
