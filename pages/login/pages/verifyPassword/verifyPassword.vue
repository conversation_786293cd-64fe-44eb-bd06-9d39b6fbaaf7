<script setup>
	import {
		onShow,
		onLoad,
		onNavigationBarButtonTap
	} from '@dcloudio/uni-app';

	import {
		ref,
		reactive,
		computed,
		watchEffect
	} from 'vue';

	import {
		useUserStore
	} from '@/store';

	import {
		goArticlePage,
		canENV,
		route
	} from '@/common/utils';

	import {
		REGEX_MOBILE
	} from '@/common/test'

	// import {
	// 	sendSMS,
	// 	checkUserPassword
	// } from '@/server/api';

	const userStore = useUserStore();

	const checkboxValue = ref(['agree']);
	const agreementModal = ref();

	const verifyCaptcha = ref();
	const verifyCaptchaTips = ref('获取验证码');
	const verifyCaptchaSend = computed(() => (verifyCaptcha.value ? verifyCaptcha.value.canGetCode : false));

	const loginForm = reactive({
		password: '',
	});

	const loginLoading = ref(false)

	canENV(() => {
		loginForm.password = '888888';
	});

	const passwordTypes = reactive({
		password: 'password',
		passwordConfirmed: 'password'
	});

	const passwordTypeChange = (type) => {
		passwordTypes[type] = passwordTypes[type] === 'password' ? 'text' : 'password';
	};

	const loginFormRules = {
		password: [{
			required: true,
			message: '请输入密码',
			trigger: 'blur'
		}],
	};
	const loginFormRef = ref();
	const loginFormSend = computed(() => {
		return Boolean(loginForm.password);
	});

	const formStyle = ref({
		labelStyle: {
			fontSize: '30rpx',
			fontWeight: 'bold'
		},
		inputStyle: {
			height: '98rpx',
			padding: '0rpx 0rpx',
			borderBottom: `solid 1rpx #E9EDF1`
		},
		labelStyle2: {
			fontSize: '26rpx',
			fontWeight: 'bold',
			color: '#1152D7'
		},
		inputStyle2: {
			height: '88rpx',
			borderRadius: '10rpx',
			background: '#fff',
			padding: '0rpx 30rpx'
		},
		inputStyle3: {
			borderRadius: '16rpx',
			background: '#F2F6FB',
			padding: '25rpx 30rpx'
		},
		inputStyle4: {
			borderRadius: '16rpx',
			background: '#fff',
			padding: '25rpx 30rpx'
		},
		inputStyle5: {
			height: '105rpx',
			borderRadius: '16rpx',
			background: '#F2F6FB',
			padding: '0rpx 30rpx'
		},
		labelStyle6: {
			fontSize: '28rpx',
			fontWeight: '500',
			color: '#1A1A1B'
		},
		inputStyle6: {
			fontWeight: 'bold',
			fontSize: '28rpx',
		},
		labelStyle7: {
			fontSize: '28rpx',
			fontWeight: '500',
			color: '#1A1A1B'
		},
		inputStyle7: {
			fontWeight: '500',
			fontSize: '30rpx',
			padding: 0,
		},
		inputStyle8: {
			// borderRadius: '16rpx',
			background: '#F3F4F7',
			padding: '25rpx 30rpx',
			borderRadius: '53rpx',
		},
		inputPlaceholderStyle: `font-size: 30rpx; font-weight: 500; color: #AEAEB4;`,
		inputPlaceholderStyle3: `font-size: 26rpx; font-weight: 500; color: #B1B6BC;`,
		inputPlaceholderStyle4: `font-size: 30rpx; font-weight: 500; color: #B1B6BC;`,
		inputPlaceholderStyle6: `font-size: 28rpx; font-weight: 500; color: #939398;`,
		inputPlaceholderStyle7: `font-size: 30rpx; font-weight: 500; color: #93939C;`,
	});

	const labelStyle = {
		fontSize: '26rpx',
		fontWeight: 'bold'
	};

	function submit() {
		loginFormRef.value.validate().then((res) => {
			if (checkboxValue.value.length == 0) {
				agreementModal.value.open();
				return;
			}
			loginLoading.value = true
			checkUserPassword({
					...loginForm,
				}, {
					custom: {
						catch: true
					}
				})
				.then((loginRes) => {
					if (loginRes.apiStatus) {
						route('/pages/login/pages/verifyNewMobile/verifyNewMobile')
					} else {
						uni.showToast({
							icon: 'error',
							title: '密码校验失败'
						});
						loginLoading.value = false
					}
				})
				.catch((err) => {
					loginLoading.value = false
				});
		});
	}

	function getCaptcha() {
		if (verifyCaptchaSend) {
			if (REGEX_MOBILE(loginForm.mobile)) {
				sendSMS({
					mobile: loginForm.mobile,
					event: 'mobilelogin'
				}).then((res) => {
					if (res.apiStatus) {
						uni.showToast({
							icon: 'success',
							title: '发送成功'
						});
						verifyCaptcha.value.start();
					}

				});
			} else {
				uni.showToast({
					title: '请输入正确的手机号码'
				});
			}
		} else {
			// uni.$uv.toast('验证码已发送');
		}
	}

	function captchaChange(text) {
		verifyCaptchaTips.value = text;
	}

	function agreementModalConfirm() {
		checkboxValue.value = ['agree'];
		agreementModal.value.close();
		openPhoneLogin();
	}

	onNavigationBarButtonTap((e) => {
		if (e.index === 0) {
			uni.navigateBack();
		}
	});
</script>

<template>
	<app-layout>
		<view class="login-code">
			<view class="w-full pt-56 px-56">
				<view class="w-full flex items-center">
					<view class="text-50 font-bold">密码验证</view>
				</view>
				<view class="w-full flex items-center mt-25">
					<view class="text-24 font-500">请输入你账号的密码</view>
				</view>

				<view class="mt-130">
					<uv-form labelPosition="top" labelWidth="auto" :labelStyle="formStyle.labelStyle" :model="loginForm"
						:rules="loginFormRules" ref="loginFormRef">
						<uv-form-item label="密码" class="mt-0" prop="password">
							<uv-input v-model="loginForm.password" placeholder="请输入新密码" class="mt-0 text-32"
								:placeholderStyle="formStyle.inputPlaceholderStyle" :customStyle="formStyle.inputStyle8"
								fontSize="32rpx" border="none" clearable :type="passwordTypes.password">
								<template #suffix>
									<template v-if="passwordTypes.password === 'password'">
										<app-image src="@/static/common/icon_input_pass_hide.png" mode="widthFix" size="30rpx"
											@click="passwordTypeChange('password')"></app-image>
									</template>
									<template v-if="passwordTypes.password === 'text'">
										<app-image src="@/static/common/icon_input_pass_show.png" mode="widthFix" size="30rpx"
											@click="passwordTypeChange('password')"></app-image>
									</template>
								</template>
							</uv-input>
						</uv-form-item>

						<uv-button :color="'#00B496'" text="确认" loadingText="加载中..." class="w-640 mt-70 flex-center"
							custom-style="height: 90rpx;" customTextStyle="font-size: 30rpx; font-weight: bold;" shape="circle"
							loadingMode="circle" :loading="loginLoading" :disabled="!loginFormSend" @click="submit">
						</uv-button>
					</uv-form>
				</view>
			</view>

			<view class="bottom-box w-full fixed bottom-120 flex-center" v-if="false">
				<uv-checkbox-group class="w-full flex-center" shape="circle" v-model="checkboxValue">
					<uv-checkbox name="agree" size="28rpx" iconSize="18rpx" :labelDisabled="true">
						<view class="flex-center">
							<view class="text-22">登录即同意</view>
							<view class="text-22 text-main active:opacity-80" @click="goArticlePage('yhxy')">《用户协议》</view>
							<view class="text-22">与</view>
							<view class="text-22 text-main active:opacity-80" @click="goArticlePage('yszc')">《隐私政策》</view>
						</view>
					</uv-checkbox>
				</uv-checkbox-group>
			</view>

			<my-uv-modal ref="agreementModal" title="服务协议与隐私政策" confirmText="同意" cancelText="不同意" :duration="200"
				width="580rpx" showCancelButton @confirm="agreementModalConfirm">
				<view class="flex flex-wrap line-height-35">
					<view class="text-22">为了保护您的权益，请阅读并同意</view>
					<view class="text-22 text-main active:opacity-80" @click="goArticlePage('yhxy')">《用户协议》</view>
					<view class="text-22">与</view>
					<view class="text-22 text-main active:opacity-80" @click="goArticlePage('yszc')">《隐私政策》</view>
				</view>
			</my-uv-modal>
		</view>
	</app-layout>
</template>

<style lang="scss" scoped></style>