# 开发技巧记录

## 样式隔离配置

### 问题描述

在 uni-app 中，当需要修改组件内部样式时，可能会遇到样式不生效的问题。

### 解决方案

在组件中添加 `options` 配置：

```vue
<script>
export default {
	options: {
		styleIsolation: "shared", // 共享样式
	},
};
</script>

<script setup>
// 组件逻辑
</script>
```

### 说明

-   `styleIsolation` 可选值：
    -   `isolated`: 默认值，表示启用样式隔离
    -   `shared`: 表示页面 wxss 样式将影响到自定义组件，自定义组件 wxss 样式也会影响页面
    -   `apply-shared`: 表示页面 wxss 样式将影响到自定义组件，但自定义组件 wxss 样式不会影响页面
    -   `page-isolated`: 表示页面 wxss 样式不会影响到自定义组件，自定义组件 wxss 样式也不会影响页面
    -   `page-shared`: 表示页面 wxss 样式将影响到自定义组件，自定义组件 wxss 样式也会影响页面

## 组件开发技巧

### 1. 样式穿透

在 Vue 3 中，使用 `:deep` 选择器进行样式穿透：

```scss
:deep {
	.some-class {
		// 样式
	}
}
```

### 2. 组件配置

-   使用 `defineProps` 定义属性
-   使用 `defineEmits` 定义事件
-   使用 JSDoc 注释提供类型提示

### 3. 最佳实践

-   将样式配置放在父组件中统一管理
-   使用具名插槽增加组件的灵活性
-   保持组件的单一职责
-   提供完整的类型定义和文档说明

## 常见问题解决

### 1. JSON 配置

-   JSON 文件中必须使用双引号，不能使用单引号
-   配置项需要放在正确的位置（组件 options 中）

### 2. 样式问题

-   使用 `:deep` 进行样式穿透
-   配置 `styleIsolation` 控制样式隔离
-   使用 `scoped` 避免样式污染

### 3. 组件通信

-   使用 `props` 和 `emit` 进行父子组件通信
-   使用 `provide/inject` 进行深层组件通信
-   使用 `ref` 获取组件实例

## 开发建议

1. 组件开发

    - 保持组件的单一职责
    - 提供完整的类型定义
    - 编写清晰的文档说明
    - 添加必要的注释

2. 样式管理

    - 使用 SCSS 预处理器
    - 遵循 BEM 命名规范
    - 合理使用样式隔离
    - 避免样式冲突

3. 性能优化

    - 合理使用计算属性
    - 避免不必要的渲染
    - 使用 `v-show` 替代 `v-if`（频繁切换时）
    - 合理使用 `key` 属性

4. 代码规范
    - 使用 ESLint 进行代码检查
    - 遵循 Vue 3 组合式 API 的最佳实践
    - 保持代码风格一致
    - 编写单元测试

## 参考资源

-   [Vue 3 文档](https://v3.cn.vuejs.org/)
-   [uni-app 文档](https://uniapp.dcloud.io/)
-   [SCSS 文档](https://sass-lang.com/documentation)
