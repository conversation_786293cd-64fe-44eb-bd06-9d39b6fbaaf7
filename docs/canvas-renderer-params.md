# Canvas 圆环绘制器参数详细说明

## 圆环绘制器 (<PERSON><PERSON><PERSON>er) 参数

### 基础几何参数

| 参数名        | 类型         | 默认值 | 说明                                      | 示例              |
| ------------- | ------------ | ------ | ----------------------------------------- | ----------------- |
| `radius`      | number       | 35     | 中线半径（像素），两个环的共同中线        | `radius: 40`      |
| `lineWidth`   | number       | 8      | 进度环线宽（像素）                        | `lineWidth: 10`   |
| `bgLineWidth` | number\|null | null   | 背景环线宽（像素），null 时使用 lineWidth | `bgLineWidth: 12` |

**重叠环设计说明：**

-   背景环和进度环使用**相同的中线半径**
-   通过不同的线宽创建重叠效果
-   视觉上看起来是两个环重叠，只是粗细不同
-   例如：`radius: 35, lineWidth: 6, bgLineWidth: 12` 会创建细进度环叠在粗背景环上的效果

### 角度参数

| 参数名       | 类型         | 默认值 | 说明                                  | 示例                       |
| ------------ | ------------ | ------ | ------------------------------------- | -------------------------- |
| `startAngle` | number       | -π/2   | 起始角度（弧度），-π/2 表示 12 点方向 | `startAngle: 0` (3 点方向) |
| `endAngle`   | number\|null | null   | 结束角度（弧度），null 时根据分数计算 | `endAngle: π/2`            |
| `maxAngle`   | number       | 2π     | 最大角度范围（弧度），2π 表示完整圆环 | `maxAngle: π` (半圆)       |
| `clockwise`  | boolean      | true   | 是否顺时针绘制                        | `clockwise: false`         |

**角度计算公式：**

```javascript
// 自动计算结束角度
const progress = (score - minValue) / (maxValue - minValue);
const endAngle = startAngle + (clockwise ? progress * maxAngle : -progress * maxAngle);
```

### 颜色和主题参数

| 参数名         | 类型         | 默认值                | 说明                           | 示例                     |
| -------------- | ------------ | --------------------- | ------------------------------ | ------------------------ |
| `theme`        | object       | THEMES.health         | 主题配置对象                   | `theme: THEMES.business` |
| `healthLevels` | array        | DEFAULT_HEALTH_LEVELS | 健康等级配置                   | 见下方自定义配置         |
| `fgColor`      | string\|null | null                  | 进度环颜色（覆盖主题自动选择） | `fgColor: '#ff4d4f'`     |
| `bgColor`      | string\|null | null                  | 背景环颜色（覆盖主题背景色）   | `bgColor: '#f0f0f0'`     |

**自定义健康等级配置示例：**

```javascript
const customHealthLevels = [
	{ score: 95, level: "优秀", color: "#4CAF50" },
	{ score: 80, level: "良好", color: "#8BC34A" },
	{ score: 60, level: "一般", color: "#FF9800" },
	{ score: 0, level: "较差", color: "#F44336" },
];
```

### 样式参数

| 参数名           | 类型    | 默认值            | 说明           | 示例                               |
| ---------------- | ------- | ----------------- | -------------- | ---------------------------------- |
| `lineCap`        | string  | "round"           | 线条端点样式   | "butt", "round", "square"          |
| `showBackground` | boolean | true              | 是否显示背景环 | `showBackground: false`            |
| `shadowBlur`     | number  | 0                 | 阴影模糊度     | `shadowBlur: 5`                    |
| `shadowColor`    | string  | "rgba(0,0,0,0.1)" | 阴影颜色       | `shadowColor: 'rgba(255,0,0,0.3)'` |

### 功能参数

| 参数名      | 类型    | 默认值 | 说明         | 示例              |
| ----------- | ------- | ------ | ------------ | ----------------- |
| `minValue`  | number  | 0      | 最小值       | `minValue: 10`    |
| `maxValue`  | number  | 100    | 最大值       | `maxValue: 200`   |
| `animation` | boolean | false  | 是否启用动画 | `animation: true` |

### 文字参数

| 参数名        | 类型         | 默认值    | 说明                          | 示例                 |
| ------------- | ------------ | --------- | ----------------------------- | -------------------- |
| `showText`    | boolean      | false     | 是否显示文字                  | `showText: true`     |
| `scoreText`   | string\|null | null      | 分数文字，null 时显示分数值   | `scoreText: '85分'`  |
| `statusText`  | string\|null | null      | 状态文字，null 时显示健康等级 | `statusText: '健康'` |
| `textColor`   | string       | "#1a1a1a" | 文字颜色                      | `textColor: '#333'`  |
| `scoreSize`   | number       | 24        | 分数字体大小                  | `scoreSize: 28`      |
| `statusSize`  | number       | 14        | 状态字体大小                  | `statusSize: 16`     |
| `textOffsetY` | number       | 0         | 文字垂直偏移                  | `textOffsetY: -5`    |

## 使用示例

### 基础圆环

```javascript
await canvasManager.render("circle", 85, {
	radius: 35,
	lineWidth: 8,
	theme: THEMES.health,
});
```

### 重叠环效果

```javascript
await canvasManager.render("circle", 85, {
	radius: 35, // 统一的中线半径
	lineWidth: 6, // 进度环线宽（较细）
	bgLineWidth: 12, // 背景环线宽（较粗）
	theme: THEMES.business,
});
```

### 半圆进度条

```javascript
await canvasManager.render("circle", 75, {
	startAngle: -Math.PI, // 9点方向开始
	maxAngle: Math.PI, // 半圆范围
	theme: THEMES.rainbow,
});
```

### 带文字的圆环

```javascript
await canvasManager.render("circle", 85, {
	radius: 35,
	lineWidth: 8,
	theme: THEMES.health,
	showText: true,
	scoreSize: 24,
	statusSize: 14,
	textColor: "#1a1a1a",
});
```

### 动画圆环

```javascript
await canvasManager.render("circle", 85, {
	radius: 35,
	lineWidth: 8,
	theme: THEMES.health,
	animation: true,
	animationDuration: 800,
});
```

**性能优化说明：**

-   **上下文缓存**: Canvas 上下文只获取一次，避免重复获取开销
-   **健康等级缓存**: 常用分数的等级和颜色信息缓存，避免重复计算
-   **静态配置**: 健康等级配置使用静态属性，避免重复创建数组
-   **现代语法**: 使用 Array.find() + 解构赋值，代码更简洁高效
-   **合并查找**: 等级和颜色一次性获取，减少重复遍历
-   **稳定文字**: 确保文字在动画过程中稳定显示
-   **性能提升**: 动画更流畅，减少 CPU 使用率

**健康等级颜色配置：**

-   **≥95 分**: 良好 - #05B89B (青绿色)
-   **≥80 分**: 一般 - #05B89B (青绿色)
-   **<80 分**: 警惕 - #FF9800 (橙色)

## 主题系统

### 预设主题

-   **health**: 健康主题（绿色系）
-   **business**: 商务主题（蓝色系）
-   **rainbow**: 彩虹主题（多彩）
-   **dark**: 暗色主题（高对比度）

### 自定义主题

```javascript
const customTheme = {
	name: "自定义主题",
	colors: {
		excellent: "#your-color",
		good: "#your-color",
		normal: "#your-color",
		poor: "#your-color",
		bad: "#your-color",
	},
	background: "#your-bg-color",
};
```

## 动画系统

### 动画参数

| 参数名              | 类型    | 默认值           | 说明             | 示例                             |
| ------------------- | ------- | ---------------- | ---------------- | -------------------------------- |
| `animation`         | boolean | false            | 是否启用动画     | `animation: true`                |
| `animationDuration` | number  | 1000             | 动画时长（毫秒） | `animationDuration: 800`         |
| `animationEasing`   | string  | 'easeInOutCubic' | 缓动函数类型     | `animationEasing: 'easeOutQuad'` |

### 可用缓动函数

-   `linear`: 线性动画
-   `easeInQuad`: 二次缓入
-   `easeOutQuad`: 二次缓出
-   `easeInOutQuad`: 二次缓入缓出
-   `easeInCubic`: 三次缓入
-   `easeOutCubic`: 三次缓出
-   `easeInOutCubic`: 三次缓入缓出（推荐）
-   `easeInQuart`: 四次缓入
-   `easeOutQuart`: 四次缓出
-   `easeInOutQuart`: 四次缓入缓出

### 动画使用示例

```javascript
// 基础动画
await canvasManager.render("circle", 85, {
	animation: true,
	animationDuration: 800,
	animationEasing: "easeInOutCubic",
});

// 快速动画
await canvasManager.render("circle", 60, {
	animation: true,
	animationDuration: 400,
	animationEasing: "easeOutQuad",
});

// 慢速动画
await canvasManager.render("circle", 95, {
	animation: true,
	animationDuration: 1500,
	animationEasing: "easeInOutQuart",
});
```

### 动画特性

-   **平滑过渡**: 从当前值平滑过渡到目标值
-   **中断处理**: 新动画会自动停止旧动画
-   **性能优化**: 使用 setTimeout 控制帧率（~60fps）
-   **状态管理**: 自动跟踪当前显示值

## 扩展绘制器

### 创建自定义绘制器

```javascript
class CustomRenderer extends BaseRenderer {
	constructor(canvasManager) {
		super(canvasManager);
		this.animationManager = new AnimationManager(); // 支持动画
		this.currentValue = 0; // 当前值
	}

	static defaultOptions = {
		animation: false,
		animationDuration: 1000,
		animationEasing: "easeInOutCubic",
		// 你的其他默认参数
	};

	async render(data, options = {}) {
		const opts = { ...CustomRenderer.defaultOptions, ...options };

		// 动画支持
		if (opts.animation && data !== this.currentValue) {
			return this._renderWithAnimation(data, opts);
		}

		// 直接渲染
		return this._renderFrame(data, opts);
	}

	async _renderWithAnimation(targetValue, opts) {
		// 使用通用动画函数实现动画逻辑
		return await renderWithAnimation(`custom-${this.canvasManager.canvasId}`, this.currentValue, targetValue, (value) => this._renderFrame(value, opts), { duration: opts.animationDuration, easing: opts.animationEasing });
	}

	_renderFrame(value, opts) {
		// 实现单帧绘制逻辑
		// 例如：绘制自定义图形
	}
}

// 注册绘制器
canvasManager.registerRenderer("custom", CustomRenderer);

// 使用
await canvasManager.render("custom", data, options);
```
