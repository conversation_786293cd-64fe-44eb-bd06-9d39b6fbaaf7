import App from "./App";
import uvUI from "@/uni_modules/uv-ui-tools";

import Request from "@/server/request/index";

import { pinia } from "@/store/index";

// #ifdef VUE3
import { createSSRApp } from "vue";

import "virtual:uno.css";

uni.$zp = {
	config: {
		"refresher-vibrate": true,
	},
};

export function createApp() {
	const app = createSSRApp(App);
	app.use(pinia);
	app.use(uvUI, { mpShare: true });
	app.use(Request);
	return {
		app,
	};
}
// #endif

// #ifndef VUE3
import Vue from "vue";
import "./uni.promisify.adaptor";
Vue.config.productionTip = false;
App.mpType = "app";
Vue.use(uvUI, { mpShare: true });
const app = new Vue({
	...App,
});
app.$mount();
// #endif
