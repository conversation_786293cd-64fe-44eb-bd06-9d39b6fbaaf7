<script setup>
import "uno.css";
import { ref, reactive } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { common } from "@/common/images";
const emit = defineEmits(["click"]);
const props = defineProps({
	placeholder: {
		type: String,
		default: "请选择",
	},
	textValue: {
		type: String,
		default: "",
	},
	textAlign: {
		type: String,
		default: "left",
	},
});
const pageData = reactive({});
function onClick() {
	emit("click");
}
</script>

<template>
	<view @click="onClick" class="w-full flex-1 h-70 border-rd-8 border-solid border-1 border-#dcdfe6 px-20 flex justify-between items-center" :style="[`text-align: ${textAlign}`]">
		<view v-if="textValue" class="text-28 text-#333 flex-1 text-hidden">
			{{ textValue }}
		</view>
		<view v-else class="text-24 text-#999 flex-1 text-hidden">
			{{ placeholder }}
		</view>
		<image :src="common.iconRightHui" class="w-20 h-20" mode=""></image>
	</view>
</template>

<style lang="scss" scoped>
.text-hidden {
	display: -webkit-box;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
	line-clamp: 1;
	-webkit-line-clamp: 1; /* 设置显示的行数 */
}
</style>
