<script setup>
import { ref, reactive, computed, onMounted, onBeforeUnmount, watch } from 'vue';

import { onHide } from '@dcloudio/uni-app';

import { useAppStore } from '@/store';

import { canENV, goLogin } from '@/common/utils';

const emit = defineEmits(['scroll', 'scrolltolower']);

const props = defineProps({
	// 当前组件的index，也就是当前组件是swiper中的第几个
	// lowerThreshold: {
	// 	type: Number,
	// 	default: 50
	// }
	topPlaceholder: {
		type: Boolean,
		default: false
	},
	bottomPlaceholder: {
		type: Boolean,
		default: false
	}
});

const appStore = useAppStore();

const pagesHeight = ref(0);
const stateBarHeight = ref(0);

const showLoginModalRef = ref();

function onScroll(event) {
	emit('scroll', event);
}

function onScrolltolower(event) {
	emit('scrolltolower', event);
}

watch(
	() => appStore.showLoginModal,
	() => {
		if (appStore.showLoginModal) {
			if (showLoginModalRef.value) showLoginModalRef.value.open();
		} else {
			if (showLoginModalRef.value) showLoginModalRef.value.close();
		}
	},
	{
		immediate: true,
		deep: true
	}
);

function loginModalClose() {
	appStore.changeShowLoginModal(false);
}

onMounted(() => {
	// #ifdef APP || H5 || MP-WEIXIN
	const windowInfo = uni.getWindowInfo();
	pagesHeight.value = windowInfo.windowHeight;
	// #endif
	// #ifndef APP || H5 || MP-WEIXIN
	const systemInfo = uni.getSystemInfoSync();
	pagesHeight.value = systemInfo.windowHeight;
	// #endif
});

onBeforeUnmount(() => {
	appStore.changeShowLoginModal(false);
});
</script>

<template>
	<view class="my-layout">
		<template v-if="topPlaceholder">
			<view class="top-placeholder"></view>
		</template>
		<!-- 预留组件，暂无实际作用 -->
		<slot></slot>

		<my-uv-modal ref="showLoginModalRef" content="" width="600rpx" @close="loginModalClose" showCancelButton>
			<view class="text-center text-34 font-bold py-30">登录后使用完整功能</view>
			<template #confirmButton>
				<view class="w-full flex items-center justify-between px-41 pb-44">
					<view class="w-246">
						<uv-button
							color="#F2F3F6"
							text="暂不登录"
							class="w-full mt-0 flex-center"
							custom-style="height: 90rpx;"
							customTextStyle="font-size: 30rpx; font-weight: bold; color: #828D9C;"
							shape="circle"
							loadingMode="circle"
							@click="loginModalClose"
						></uv-button>
					</view>
					<view class="w-246">
						<uv-button
							color="#00B496"
							text="前往登录"
							class="w-full mt-0 flex-center"
							custom-style="height: 90rpx;"
							customTextStyle="font-size: 30rpx; font-weight: bold;"
							shape="circle"
							loadingMode="circle"
							@click="goLogin(true)"
						></uv-button>
					</view>
				</view>
			</template>
		</my-uv-modal>

		<template v-if="bottomPlaceholder">
			<view class="bottom-placeholder"></view>
		</template>
	</view>
</template>

<style lang="scss" scoped>
.my-layout {
	width: 100%;
	height: 100%;
}

.top-placeholder {
	width: 100%;
	// height: var(--window-top);
	height: var(--status-bar-height);
}

.bottom-placeholder {
	width: 100%;
	height: var(--window-bottom);
}
</style>
