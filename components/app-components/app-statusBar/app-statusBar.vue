<script setup>
	import {
		usePublicStore
	} from "@/store";
	const publicStore = usePublicStore();
	const props = defineProps({
		bgColor: {
			type: String,
			default: "rgba(255,255,255,0)"
		}
	})
</script>
<template>
	<!-- #ifdef H5 -->
	<view class="w-full" :style="[`height: ${publicStore.statusBarHeight}px`,`background-color: ${bgColor}`]"></view>
	<!-- #endif -->
	<!-- #ifndef H5 -->
	<view class="w-full" :style="[`height: ${publicStore.statusBarHeight}px`,`background-color: ${bgColor}`]"></view>
	<!-- #endif -->
</template>
<style lang="scss" scoped>

</style>