<script setup>
import { computed, nextTick, watch, getCurrentInstance, ref } from "vue";
import { usePublicStore } from "@/store";
import { common } from "@/common/images";
const publicStore = usePublicStore();

const instance = getCurrentInstance();

const props = defineProps({
	bgColor: {
		type: String,
		default: "rgba(255,255,255,0)",
	},
	height: {
		type: String,
		default: "88rpx",
	},
	hasRight: {
		type: Boolean,
		default: true,
	},
	back: {
		type: Boolean,
		default: false,
	},
	backIcon: {
		type: String,
		default: common.iconLeftHei,
	},
	backIconSize: {
		type: String,
		default: `40rpx`,
	},
	showRight: {
		type: Boolean,
		default: true,
	},
	rightPlaceholderSize: {
		type: String,
		default: `80rpx`,
	},
	leftPlaceholderSize: {
		type: String,
		default: `auto`,
	},
	fixed: {
		type: Boolean,
		default: false,
	},
	fixedPlaceholder: {
		type: <PERSON><PERSON><PERSON>,
		default: true,
	},
	zIndex: {
		type: [Number, String],
		default: "auto",
	},
});

const navHeight = ref(0);

watch(
	() => props.fixed,
	() => {
		refreshNavFixedPlaceholderHeight();
	},
	{
		immediate: true,
	}
);

function refreshNavFixedPlaceholderHeight() {
	if (props.fixed && props.fixedPlaceholder) {
		nextTick(() => {
			const query = uni.createSelectorQuery().in(instance.proxy);
			query
				.select("#navBox")
				.boundingClientRect((data) => {
					navHeight.value = data.height;
				})
				.exec();
		});
	}
}

const menuButtonStyle = computed(() => {
	return {
		// #ifdef MP-WEIXIN
		width: props.hasRight ? publicStore.menuButtonInfo.left + "px" : publicStore.windowWidth + "px",
		// #endif
		// #ifndef MP-WEIXIN
		width: publicStore.windowWidth + "px",
		// #endif
		height: publicStore.menuButtonInfo.height + "px",
	};
});

function routeBack() {
	const pages = getCurrentPages();
	if (pages.length > 1) {
		uni.navigateBack();
	} else {
		uni.switchTab({
			url: "/pages/tabBar/home/<USER>",
		});
	}
}

defineExpose({
	refreshNavFixedPlaceholderHeight,
});
</script>
<template>
	<template v-if="!fixed">
		<view class="app-navBar top-0 left-0 z-99 box-border" :style="[`background: ${bgColor}`, `position: ${fixed ? 'fixed' : 'sticky'}`, { zIndex: zIndex !== 'auto' ? zIndex : '' }]">
			<app-statusBar></app-statusBar>
			<!-- #ifdef H5 -->
			<view class="w-full h-15"></view>
			<!-- #endif -->
			<view class="flex relative box-border" :style="[`width: ${menuButtonStyle.width}`, `height: ${props.height}`]">
				<template v-if="back">
					<view class="" :style="{ width: leftPlaceholderSize }">
						<view class="h-full flex flex-center pl-20 pr-20 box-border max-w-fit">
							<app-image :src="backIcon" :size="backIconSize" mode="" @click="routeBack"></app-image>
						</view>
					</view>
				</template>
				<view class="flex-1 w-0">
					<slot name="content"></slot>
				</view>
				<template v-if="back && showRight">
					<view class="" :style="{ width: rightPlaceholderSize }">
						<view class="h-full flex flex-center pl-20 pr-20 box-border">
							<slot name="right"></slot>
						</view>
					</view>
				</template>
			</view>
			<view class="w-full footer-box">
				<slot name="footer"></slot>
			</view>
		</view>
	</template>
	<template v-else>
		<view class="">
			<view class="app-navBar top-0 left-0 z-99 box-border w-750" id="navBox" :style="[`background: ${bgColor}`, `position: ${fixed ? 'fixed' : 'sticky'}`, { zIndex: zIndex !== 'auto' ? zIndex : '' }]">
				<app-statusBar></app-statusBar>
				<!-- #ifdef H5 -->
				<view class="w-full h-15"></view>
				<!-- #endif -->
				<view class="flex relative box-border" :style="[`width: ${menuButtonStyle.width}`, `height: ${props.height}`]">
					<template v-if="back">
						<view class="" :style="{ width: leftPlaceholderSize }">
							<view class="h-full flex flex-center pl-20 pr-20 box-border max-w-fit">
								<app-image :src="backIcon" :size="backIconSize" mode="" @click="routeBack"></app-image>
							</view>
						</view>
					</template>
					<view class="flex-1 w-0">
						<slot name="content"></slot>
					</view>
					<template v-if="back && showRight">
						<view class="" :style="{ width: rightPlaceholderSize }">
							<view class="h-full flex flex-center pl-20 pr-20 box-border">
								<slot name="right"></slot>
							</view>
						</view>
					</template>
				</view>
				<view class="w-full footer-box">
					<slot name="footer"></slot>
				</view>
			</view>
			<template v-if="fixedPlaceholder">
				<view class="fixed-placeholder-box" :style="{ width: '100%', height: `${navHeight}px` }"></view>
			</template>
		</view>
	</template>
</template>
<style lang="scss" scoped></style>
