<script setup>
import { usePublicStore } from '@/store';

// #ifdef MP-WEIXIN
const publicStore = usePublicStore();
// #endif

// #ifndef MP-WEIXIN
const publicStore = {
	safeAreaBottom: 0
};
// #endif

const props = defineProps({
	bgColor: {
		type: String,
		default: 'rgba(255,255,255,0)'
	}
});
</script>
<template>
	<view class="w-full" :style="[`height: ${publicStore.safeAreaBottom}px`, `background-color: ${bgColor}`]"></view>
</template>
<style lang="scss" scoped></style>
