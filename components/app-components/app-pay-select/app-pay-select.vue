<script setup>
import { ref, computed, onMounted, watch, reactive, watchEffect, nextTick } from "vue";

import { usePayStore } from "@/store/modules/pay";
import { common } from "@/common/images";

const payStore = usePayStore();

const props = defineProps({
	autoClose: {
		type: Boolean,
		default: false,
	},
	btnLoadingText: {
		type: String,
		default: "支付中...",
	},
	showZfb: {
		type: Number,
		default: -1,
	},
	showWx: {
		type: Number,
		default: -1,
	},
	showYe: {
		type: Number,
		default: -1,
	},
	showXianXia: {
		type: Number,
		default: -1,
	},
	mode: {
		type: String,
		default: "bottom",
	},
	closeOnClickOverlay: {
		type: Boolean,
		default: false,
	},
	closeable: {
		type: Boolean,
		default: false,
	},
	closeIconPos: {
		type: String,
		default: "top-right",
	},
	bgColor: {
		type: String,
		default: "#fff",
	},
	round: {
		type: [String, Number],
		default: "20rpx",
	},
	customStyle: {
		type: Object,
		default: () => {
			return {};
		},
	},
	price: {
		type: [String, Number],
		default: 0,
	},
	timeout: {
		type: [String, Number],
		default: 0,
	},
	balanceVerify: {
		type: Boolean,
		default: true,
	},
	balanceTotal: {
		type: [String, Number],
		default: 0,
	},
	balanceTotalShow: {
		type: Boolean,
		default: true,
	},

	integralPriceShow: {
		type: Boolean,
		default: false,
	},
	integralPrice: {
		type: [String, Number],
		default: 0,
	},
	integralTotal: {
		type: [String, Number],
		default: 0,
	},
	integralTotalShow: {
		type: Boolean,
		default: true,
	},
});

const show = defineModel("show");
const btnLoading = defineModel("btnLoading");

const emit = defineEmits(["change", "maskClick", "submit"]);

const payPopup = ref();
const payCountDown = ref();

const payListInfo = reactive(payStore.payList);
const showSatus = ref(false);

const modalRef = ref();

const payType = ref("");
const payKey = ref("");

watch(
	() => payListInfo,
	() => {
		let findKey = "";
		for (let key in payListInfo) {
			if (payListInfo[key].show) {
				findKey = key;
				break;
			}
		}

		payType.value = findKey ? payListInfo[findKey].type : payType.value;
		payKey.value = findKey || payKey.value;
	},
	{
		deep: true,
		immediate: true,
	}
);

const integralDefault = ref("integral");

watchEffect(() => {
	if (payListInfo.zfb && props.showZfb !== -1) payListInfo.zfb.show = !!props.showZfb;
	if (payListInfo.wx && props.showWx !== -1) payListInfo.wx.show = !!props.showWx;
	if (payListInfo.balance && props.showYe !== -1) payListInfo.balance.show = !!props.showYe;
	// payListInfo.xianxia.show = props.showXianXia;
});

const payList = computed(() => {
	let arr = new Array(Object.keys(payListInfo).length);
	for (let key in payListInfo) {
		const item = payListInfo[key];

		arr[item.index] = {
			...item,
			key,
		};
	}

	return arr;
});

function countDownFinish() {
	if (show.value) {
		uni.showToast({
			title: "支付超时",
			icon: "error",
			mask: true,
		});
		show.value = false;
	}
}

watch(show, () => {
	if (show.value) {
		openPopup();
	} else {
		closePopup();
	}
});

function openPopup() {
	if (!showSatus.value) {
		payPopup.value.open();
	}
}

function beforeclosePopup(confirm = false) {
	if (!confirm) {
		if (modalRef.value) modalRef.value.open();
	} else {
		modalRef.value.close();
		closePopup();
	}
}

function closeModal() {
	modalRef.value.close();
}

function closePopup() {
	if (showSatus.value) {
		payPopup.value.close();
	}
}

function popupChange(e) {
	showSatus.value = e.show;
	if (!e.show) {
		show.value = e.show;
		btnLoading.value = false;
		if (payCountDown.value) payCountDown.value.reset();
	} else {
		nextTick(() => {
			if (payCountDown.value) payCountDown.value.start();
		});
	}
	emit("change", e);
}

function maskClick(e) {
	emit("maskClick", e);
}

function submit() {
	emit("submit", {
		list: payList,
		type: payListInfo[payKey.value]?.type,
		payItem: payListInfo[payKey.value],
	});
	btnLoading.value = true;
	if (props.autoClose) {
		closePopup();
	}
}

function verifyBalance() {
	if (props.balanceVerify) {
		if (Number(props.balanceTotal) <= Number(props.price)) {
			if (payListInfo.balance) payListInfo.balance.disabled = true;
			if (payKey.value === "balance") {
				let findKey = "";
				for (let key in payListInfo) {
					if (payListInfo[key].show && !payListInfo[key].disabled) {
						findKey = key;
						break;
					}
				}
				payKey.value = findKey;
				payType.value = payListInfo[findKey].type || "";
			}
		} else {
			if (payListInfo.balance) payListInfo.balance.disabled = false;
		}
	}
}

watch(
	() => props.price,
	() => {
		verifyBalance();
	},
	{
		immediate: true,
	}
);

watch(
	() => props.balanceTotal,
	() => {
		verifyBalance();
	},
	{
		immediate: true,
	}
);

onMounted(() => {
	if (show.value) {
		openPopup();
	}
});
</script>

<template>
	<view class="app-pay-select" @touchmove.stop.prevent>
		<my-uv-popup ref="payPopup" class="pay-popup" :mode="mode" :closeOnClickOverlay="closeOnClickOverlay" :closeable="closeable" :closeIconPos="closeIconPos" :bgColor="bgColor" :round="round" :customStyle="customStyle" @change="popupChange" @maskClick="maskClick">
			<view class="w-full bg-#fff">
				<view class="title-box h-110 flex items-center justify-between border-bottom-1 border-bottom-solid border-#E8EDFA w-full px-40">
					<view class=""></view>
					<view class="text-30 font-bold">支付</view>
					<view class="">
						<app-image :src="common.iconCloseHui" mode="" size="22" @click="beforeclosePopup()"></app-image>
					</view>
				</view>
				<view class="w-full border-1 border-solid border-solid border-#E8EDFA border-left-0 border-right-0 px-70 pt-64 pb-76">
					<view class="text-30 font-bold text-center">实付金额</view>
					<template v-if="!integralPriceShow">
						<view class="flex items-end justify-center mt-37">
							<view class="text-30 pb-12">￥</view>
							<view class="text-72 font-bold">
								{{ price }}
							</view>
						</view>
					</template>
					<template v-else>
						<view class="flex items-end justify-center mt-37">
							<view class="text-72 font-bold">
								{{ integralPrice }}
							</view>
							<view class="text-35 pb-12">积分</view>
							<template v-if="Number(price) > 0">
								<view class="text-60 pb-12 px-5">+</view>
								<view class="text-35 pb-12">￥</view>
								<view class="text-72 font-bold">
									{{ price }}
								</view>
							</template>
						</view>
					</template>

					<view class="mt-47 flex flex-center" v-if="Number(timeout) > 0">
						<view class="text-26 text-51535E mr-15">剩余支付时间</view>
						<my-uv-count-down
							:time="Number(timeout)"
							format="HH:mm:ss"
							ref="payCountDown"
							:autoStart="false"
							:textStyle="{
								fontSize: '26rpx',
								color: '#F85D5D',
								fontWeight: 500,
							}"
							@finish="countDownFinish"></my-uv-count-down>
					</view>
					<view class="mt-46">
						<uv-radio-group v-model="payKey" placement="column" size="32rpx" iconSize="20rpx" iconPlacement="right" activeColor="#00B496">
							<template v-for="(item, index) in payList" :key="index">
								<template v-if="item && item.show">
									<uv-radio
										:name="item.key"
										:label="item.name"
										:disabled="item.disabled"
										:customStyle="{
											padding: '20rpx 0',
										}">
										<view class="flex items-center">
											<app-image :src="item.icon" size="38rpx" mr="28rpx"></app-image>
											<view class="text-30 font-bold">{{ item.name }}</view>
											<template v-if="item.key === 'balance' && props.balanceTotalShow">
												<view class="pl-10 text-#F85D5D">￥{{ props.balanceTotal }}</view>
											</template>
										</view>
									</uv-radio>
								</template>
							</template>
						</uv-radio-group>
						<template v-if="integralPriceShow">
							<uv-radio-group v-model="integralDefault" placement="column" size="32rpx" iconSize="20rpx" iconPlacement="right" activeColor="#00B496">
								<template v-for="(item, index) in payList.filter((i) => i.key === 'integral')" :key="index">
									<template v-if="item">
										<uv-radio
											:name="item.key"
											:label="item.name"
											:customStyle="{
												padding: '20rpx 0',
											}"
											disabled>
											<view class="flex items-center">
												<app-image :src="item.icon" size="38rpx" mr="28rpx"></app-image>
												<view class="text-30 font-bold">{{ item.name }}</view>
												<template v-if="item.key === 'integral' && props.integralTotalShow">
													<view class="pl-10 text-#F85D5D">{{ props.integralTotal }}积分</view>
												</template>
											</view>
										</uv-radio>
									</template>
								</template>
							</uv-radio-group>
						</template>
					</view>
				</view>
				<view class="w-full px-50 pt-15 pb-45">
					<uv-button color="#00B496" text="确认支付" :loadingText="btnLoadingText" class="w-full mt-0 flex-center" custom-style="height: 90rpx;" customTextStyle="font-size: 30rpx; font-weight: bold;" shape="circle" loadingMode="circle" :loading="btnLoading" :disabled="payType === ''" @click="submit"></uv-button>
				</view>
			</view>
		</my-uv-popup>

		<my-uv-modal ref="modalRef" content="" width="600rpx" showCancelButton>
			<view class="text-center text-34 font-bold py-30">确定取消付款吗？</view>
			<template #confirmButton>
				<view class="w-full flex items-center justify-between px-41 pb-44">
					<view class="w-246">
						<uv-button color="#F2F3F6" text="确认取消" class="w-full mt-0 flex-center" custom-style="height: 90rpx;" customTextStyle="font-size: 30rpx; font-weight: bold; color: #828D9C;" shape="circle" loadingMode="circle" @click="beforeclosePopup(true)">
							<view class="text-30 text-#828D9C font-bold">确认取消</view>
						</uv-button>
					</view>
					<view class="w-246">
						<uv-button color="#00B496" text="再考虑下" class="w-full mt-0 flex-center" custom-style="height: 90rpx;" customTextStyle="font-size: 30rpx; font-weight: bold;" shape="circle" loadingMode="circle" @click="closeModal"></uv-button>
					</view>
				</view>
			</template>
		</my-uv-modal>
	</view>
</template>

<style lang="scss" scoped>
.app-pay-select {
}
</style>
