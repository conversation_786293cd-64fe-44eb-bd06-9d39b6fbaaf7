<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { logo } from "@/common/images";

const imageModules = import.meta.glob([`/static/**/*.{png,jpg,gif,webp,svg}`, `/pages/**/static/**/*.{png,jpg,gif,webp,svg}`], {
	eager: true
});

const props = defineProps({
	src: {
		type: [String, undefined, Number],
		required: true,
		default: ''
	},
	mode: {
		type: String,
		default: 'scaleToFill'
	},
	fadeShow: {
		type: Boolean,
		default: false
	},
	lazyLoad: {
		type: Boolean,
		default: false
	},
	webp: {
		type: Boolean,
		default: false
	},
	showMenuByLongpress: {
		type: Boolean,
		default: false
	},
	draggable: {
		type: Boolean,
		default: true
	},
	size: {
		type: String,
		default: ''
	},
	ml: {
		type: String,
		default: ''
	},
	mt: {
		type: String,
		default: ''
	},
	mr: {
		type: String,
		default: ''
	},
	mb: {
		type: String,
		default: ''
	},
	mx: {
		type: String,
		default: ''
	},
	my: {
		type: String,
		default: ''
	},
	m: {
		type: String,
		default: ''
	},
	width: {
		type: String,
		default: ''
	},
	height: {
		type: String,
		default: ''
	},
	rd: {
		type: String,
		default: ''
	},
	type: {
		type: String,
		default: ''
	},
	imageClass: {
		type: [Object, String],
		default: () => ['w-full', 'h-full']
	},
	imageStyle: {
		type: [Object, String],
		default: () => ({})
	},
	imgTextStyle: {
		type: [Object, String],
		default: () => ({})
	},
	typeData: {
		type: Object,
		default: () => ({})
	},
	imgText: {
		type: String,
		default: ''
	},
	bgc: {
		type: String,
		default: 'auto'
	},
	colorIndex: {
		type: [String, Number],
		default: -1
	},
	addImgWatermark: {
		type: Boolean,
		default: false
	},
	flexCenter: {
		type: Boolean,
		default: true
	},
	positionCenter: {
		type: Boolean,
		default: false
	}
});

const emit = defineEmits(['error', 'load', 'click']);

const imageGetLoading = ref(false);
const imageGetSrc = ref('');

const imageLoad = ref(false);
const imageLoading = ref(false);
const imageError = ref(false);
const imageErrUrl = ref('');

const isText = ref(false);
const textBgText = ref('');

const backgroundColors = [
	'#ffb34b',
	'#f2bba9',
	'#f7a196',
	'#f18080',
	'#88a867',
	'#bfbf39',
	'#89c152',
	'#94d554',
	'#f19ec2',
	'#afaae4',
	'#e1b0df',
	'#c38cc1',
	'#72dcdc',
	'#9acdcb',
	'#77b1cc',
	'#448aca',
	'#86cefa',
	'#98d1ee',
	'#73d1f1',
	'#80a7dc'
];

const backgroundColor = computed(() => {
	if (props.bgc !== 'auto') {
		return props.bgc;
	} else {
		if (props.colorIndex >= 0 && props.colorIndex < backgroundColors.length) {
			return backgroundColors[props.colorIndex];
		} else if (props.bgc === 'random') {
			return backgroundColors[random(0, 19)];
		} else if (props.bgc === 'auto' && isText.value) {
			return backgroundColors[random(0, 19)];
		}
	}
});

const imgSrc = computed(() => {
	const videoCoverSrc = `?x-oss-process=video/snapshot,t_1000,f_jpg`;

	let src = '/';

	if (!props.src) {
		return src;
	}
	if (props.type && props.type !== 'videoCover') {
		return imageGetSrc.value;
	} else if (props.src) {
		if (props.src.startsWith('http://') || props.src.startsWith('https://')) {
			if (props.type === 'videoCover') {
				src = `${props.src}${videoCoverSrc}`;
			} else if (props.addImgWatermark) {
				src = `${props.src}${'?t=水印'}`;
			} else {
				src = props.src;
			}
		} else if (props.src.startsWith('/uploads/')) {
			if (props.type === 'videoCover') {
				src = `${'https://medusa-small-file-1253272780.cos.ap-shanghai.myqcloud.com'}${props.src}${videoCoverSrc}`;
			} else if (props.addImgWatermark) {
				src = `${'https://medusa-small-file-1253272780.cos.ap-shanghai.myqcloud.com'}${props.src}${'?t=水印'}`;
			} else {
				src = `${'https://medusa-small-file-1253272780.cos.ap-shanghai.myqcloud.com'}${props.src}`;
			}
		} else if (props.src.startsWith('@/') || props.src.startsWith('~/')) {
			const regex = /^@\/|^~\//;
			const imgAbsoluteAddress = props.src.replace(regex, '/');
			if (imageModules[imgAbsoluteAddress]) {
				src = imageModules[imgAbsoluteAddress].default;
			} else {
				console.error('AppImage 未找到当前资源 => ', props.src);
			}
		} else {
			src = props.src;
		}
	} else {
		src = '/';
	}

	return src;
});

const sizeStyle = computed(() => {
	let style = {};

	if (!isEmpty(props.rd)) {
		style.borderRadius = addUnit(props.rd);
	}

	if (!isEmpty(props.width)) {
		style.width = addUnit(props.width);
	} else if (!isEmpty(props.size)) {
		style.width = addUnit(props.size);
	}

	if (!isEmpty(props.height)) {
		style.height = addUnit(props.height);
	} else if (!isEmpty(props.size)) {
		style.height = addUnit(props.size);
	}

	if (!isEmpty(props.rd)) {
		style.borderRadius = addUnit(props.rd);
	}

	return style;
});

const marginStyle = computed(() => {
	let style = {};

	if (!isEmpty(props.ml)) {
		style.marginLeft = addUnit(props.ml);
	}

	if (!isEmpty(props.mr)) {
		style.marginRight = addUnit(props.mr);
	}

	if (!isEmpty(props.mt)) {
		style.marginTop = addUnit(props.mt);
	}

	if (!isEmpty(props.mb)) {
		style.marginBottom = addUnit(props.mb);
	}

	if (!isEmpty(props.mx)) {
		if (isEmpty(style.marginLeft)) {
			style.marginLeft = addUnit(props.mx);
		}
		if (isEmpty(style.marginRight)) {
			style.marginRight = addUnit(props.mx);
		}
	}

	if (!isEmpty(props.my)) {
		if (isEmpty(style.marginTop)) {
			style.marginTop = addUnit(props.my);
		}
		if (isEmpty(style.marginBottom)) {
			style.marginBottom = addUnit(props.my);
		}
	}

	if (!isEmpty(props.m)) {
		if (isEmpty(style.marginLeft)) {
			style.marginLeft = addUnit(props.m);
		}

		if (isEmpty(style.marginTop)) {
			style.marginTop = addUnit(props.m);
		}

		if (isEmpty(style.marginRight)) {
			style.marginRight = addUnit(props.m);
		}

		if (isEmpty(style.marginBottom)) {
			style.marginBottom = addUnit(props.m);
		}
	}

	return style;
});

const showImage = computed(() => {
	return true;
});

watch(
	() => props.typeData,
	() => {
		getImageUrl();
	},
	{
		immediate: false,
		deep: true
	}
);

watch(
	() => props.imgText,
	() => {
		if (isEmpty(props.imgText)) {
			isText.value = false;
			textBgText.value = '';
		} else {
			isText.value = true;
			textBgText.value = props.imgText;
		}
	},
	{
		immediate: true
	}
);

watch(imgSrc, () => {
	imageOpenLoad();
});

watch(
	isText,
	() => {
		if (isText.value && imageLoading.value) {
			imageLoadFun();
		}
	},
	{
		immediate: false
	}
);

function getImageUrl() {
	if (props.type === 'firmLogo') {
		isText.value = true;
		textBgText.value = props.typeData.name ? props.typeData.name.substring(0, 1) : props.typeData.keyword ? props.typeData.keyword.substring(0, 1) : '';

		imageGetLoading.value = false;
	}

	if (props.type === 'staticMap') {
		const gdMapImgBaseUrl = `https://restapi.amap.com/v3/staticmap`;
		const gdMapWebApiKey = ``;
		let src = `${gdMapImgBaseUrl}?location=${props.typeData.longitude},${props.typeData.latitude}&zoom=18&size=500*350&markers=mid,,:${props.typeData.longitude},${props.typeData.latitude}&key=${gdMapWebApiKey}`;
		imageGetSrc.value = src;
		imageGetLoading.value = false;
	}
}

function imageOpenLoad() {
	imageLoading.value = true;
	imageLoad.value = false;
	imageError.value = false;
}

onMounted(() => {
	if (props.type && props.type !== 'videoCover') {
		imageGetLoading.value = true;
		getImageUrl();
	}

	imageOpenLoad();
});

function imageErrorFun(event) {
	imageLoad.value = true;
	imageError.value = true;
	imageLoading.value = false;
	emit('error', event);
}

function imageLoadFun(event) {
	imageLoad.value = true;
	imageError.value = false;
	imageLoading.value = false;
	emit('load', event);
}

function isEmpty(value) {
	switch (typeof value) {
		case 'undefined':
			return true;
		case 'string':
			if (value.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g, '').length == 0) return true;
			break;
		case 'boolean':
			if (!value) return true;
			break;
		case 'number':
			if (value === 0 || isNaN(value)) return true;
			break;
		case 'object':
			if (value === null || value.length === 0) return true;
			for (const i in value) {
				return false;
			}
			return true;
	}
	return false;
}

function isNumber(value) {
	return /^[\+-]?(\d+\.?\d*|\.\d+|\d\.\d+e\+\d+)$/.test(value);
}

function trim(str, pos = 'both') {
	str = String(str);
	if (pos == 'both') {
		return str.replace(/^\s+|\s+$/g, '');
	}
	if (pos == 'left') {
		return str.replace(/^\s*/, '');
	}
	if (pos == 'right') {
		return str.replace(/(\s*$)/g, '');
	}
	if (pos == 'all') {
		return str.replace(/\s+/g, '');
	}
	return str;
}

function addUnit(value = 'auto', unit = 'rpx') {
	value = String(value);
	return isNumber(value) ? `${value}${unit}` : value;
}

function addStyle(customStyle, target = 'object') {
	if (isEmpty(customStyle) || (typeof customStyle === 'object' && target === 'object') || (target === 'string' && typeof customStyle === 'string')) {
		return customStyle;
	}
	if (target === 'object') {
		customStyle = trim(customStyle);
		const styleArray = customStyle.split(';');
		const style = {};
		for (let i = 0; i < styleArray.length; i++) {
			if (styleArray[i]) {
				const item = styleArray[i].split(':');
				style[trim(item[0])] = trim(item[1]);
			}
		}
		return style;
	}
	let string = '';
	for (const i in customStyle) {
		const key = i.replace(/([A-Z])/g, '-$1').toLowerCase();
		string += `${key}:${customStyle[i]};`;
	}
	return trim(string);
}

function random(min, max) {
	if (min >= 0 && max > 0 && max >= min) {
		const gab = max - min + 1;
		return Math.floor(Math.random() * gab + min);
	}
	return 0;
}

function clickImg(e) {
	emit('click', e);
}
</script>

<template>
	<view
		class="app-image-box"
		:class="{ 'flex-center': flexCenter, 'position-center': positionCenter }"
		:style="[sizeStyle, marginStyle, { backgroundColor: backgroundColor }]"
		v-if="showImage"
	>
		<template v-if="imageGetLoading">
			<view class="image-loading-box" :class="[imageClass]" :style="[sizeStyle, addStyle(imageStyle)]"></view>
		</template>
		<template v-else>
			<template v-if="isText">
				<view class="img-text-box" :style="[addStyle(imgTextStyle)]">{{ textBgText }}</view>
			</template>
			<template v-else>
				<!-- <image class="app-image" :src="imgSrc" :class="[imageClass]" :mode="props.mode" :lazy-load="props.lazyLoad"
					:fade-show="props.fadeShow" :webp="props.webp" :show-menu-by-longpress="props.showMenuByLongpress"
					:draggable="props.draggable" :style="[sizeStyle, addStyle(imageStyle)]" @error="imageErrorFun"
					@load="imageLoadFun" v-show="!imageLoading && !imageError"></image> -->
				<image
					class="app-image"
					:src="imgSrc"
					:class="[imageClass]"
					:mode="props.mode"
					:lazy-load="props.lazyLoad"
					:fade-show="props.fadeShow"
					:webp="props.webp"
					:show-menu-by-longpress="props.showMenuByLongpress"
					:draggable="props.draggable"
					:style="[sizeStyle, addStyle(imageStyle)]"
					@error="imageErrorFun"
					@load="imageLoadFun"
					@click="clickImg"
					v-show="!imageError"
				></image>
				<view
					class="image-loading-box"
					:class="[imageClass]"
					:style="[sizeStyle, addStyle(imageStyle)]"
					v-show="imageLoading && !imageError && positionCenter"
					@click="clickImg"
				></view>
				<view class="app-image-error-box" :style="[sizeStyle, addStyle(imageStyle)]" v-if="imageError && !imageLoading">
					<image
						:src="logo"
						:class="['app-image-error', imageClass]"
						:mode="props.mode"
						:lazy-load="props.lazyLoad"
						:fade-show="props.fadeShow"
						:webp="props.webp"
						:show-menu-by-longpress="props.showMenuByLongpress"
						:draggable="props.draggable"
						:style="[sizeStyle, addStyle(imageStyle)]"
						@click="clickImg"
					></image>
				</view>
			</template>
		</template>
	</view>
</template>

<style lang="scss" scoped>
@keyframes skeleton-loading {
	0% {
		background-position: 100% 50%;
	}

	to {
		background-position: 0 50%;
	}
}

.app-image-box {
	&.flex-center {
		display: flex;
		align-items: center;
		justify-content: center;

		.app-image {
			flex-shrink: 0;
		}
	}

	&.position-center {
		position: relative;

		.app-image {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
		}
	}
}

.image-loading-box {
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, #f1f2f4 25%, #e6e6e6 37%, #f1f2f4 50%);
	background-size: 400% 100%;
	animation: skeleton-loading 1.8s ease infinite;
}

.img-text-box {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
}

.app-image-error-box {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.app-image-error {
	flex-shrink: 0;
}
</style>
