<script setup>
import { reactive, computed, watch } from 'vue';
import { image } from '../../../uni_modules/uv-ui-tools/libs/function/test';

const props = defineProps({
	list: {
		type: [Array, String],
		default: () => []
	},
	urlKey: {
		type: String,
		default: 'url'
	}
});

const pageData = reactive({
	dataList: [],
	imageList: [],
	videoList: []
});

const gridCount = computed(() => {
	let sum = pageData.dataList.length;
	if (sum > 0 && sum <= 2) {
		return 1;
	} else if (sum > 2) {
		return 2;
	}
});

function isImage(str) {
	return /.(gif|jpg|jpeg|png|gif|jpg|png|webp|avif|jfif)$/i.test(str);
}

function previewImage(item, index) {
	uni.previewImage({
		urls: pageData.imageList.map((i) => i.src),
		count: item.src,
		current: item.imageIndex
	});
}

watch(
	() => props.list,
	() => {
		let list = [];
		let dataList = [];
		let imageList = [];
		let videoList = [];

		if (Array.isArray(props.list)) {
			list = props.list;
		}

		if (typeof props.list === 'string') {
			list = props.list.split(',');
		}

		list.map((i) => {
			if (i) {
				let item = {
					src: '',
					type: 'image',
					imageIndex: 0,
					videoIndex: 0
				};

				if (typeof i === 'string') {
					item.src = i;
				}

				if (typeof i === 'object') {
					item.src = i[props.key];
				}

				if (item.src) {
					item.type = isImage(item.src) ? 'image' : 'video';

					if (item.type === 'image') {
						item.imageIndex = imageList.length;
						imageList.push(item);
					}

					if (item.type === 'video') {
						item.videoIndex = videoList.length;
						videoList.push(item);
					}

					dataList.push(item);
				}
			}
		});

		pageData.dataList = dataList;
		pageData.imageList = imageList;
		pageData.videoList = videoList;
	},
	{
		immediate: true
	}
);
</script>
<template>
	<view class="page-image-grid">
		<view class="w-full grid" :class="[['', 'grid-cols-2', 'grid-cols-3'][gridCount]]">
			<template v-for="(item, index) in pageData.dataList" :key="index">
				<view class="mt-6" :class="[['', 'w-340 h-340', 'h-224 w-224'][gridCount]]">
					<template v-if="item.type === 'image'">
						<image class="w-full h-full border-rd-10 overflow-hidden" :src="item.src" mode="aspectFill" @click="previewImage(item, index)"></image>
					</template>
					<template v-if="item.type === 'video'"></template>
				</view>
			</template>
		</view>
	</view>
</template>
<style lang="scss" scoped>
.page-image-grid {
}
</style>
