<template>
    <view class="flex items-center justify-between px-20 py-15 mt-10">
        <view
            v-for="(item, index) in sortType"
            :key="index"
            @click="clickOrderType(item)"
            :style="[activeSortType === item.key ? `color: ${activeColor}` : '']"
        >
            <view v-if="item.key !== SORT_TYPE.all" class="flex items-center">
                {{ item.name }}
                <app-image
                    :src="activeSortType !== item.key ? common.normal : item.order === ORDER_TYPE.ASC ? common.asc : common.desc"
                    size="26"
                    mode=""
                ></app-image>
            </view>
            <view v-else class="px-10">
                {{ item.name }}
            </view>
        </view>
    </view>
</template>
<script setup>
import { ref } from 'vue'
import { SORT_TYPE, ORDER_TYPE } from '@/common/types/goods'
import { common } from '@/common/images'

const props = defineProps({
    activeColor: {
        type: String,
        default: '#00B496',
    },
})
const sortType = ref([
    {
        key: SORT_TYPE.all,
        name: '综合',
    },
    {
        key: SORT_TYPE.salesVolume,
        name: '销量',
        order: ORDER_TYPE.ASC,
    },
    {
        key: SORT_TYPE.price,
        name: '价格',
        order: ORDER_TYPE.ASC,
    },
])

const emits = defineEmits(['onChange'])

const activeSortType = ref(SORT_TYPE.all)
const activeSortOrder = ref()
const clickOrderType = (item) => {
    if (activeSortType.value !== item.key && !Object.prototype.hasOwnProperty.call(item, 'order')) {
        activeSortType.value = item.key
        activeSortOrder.value = ''
        emits('onChange', {
            sortType: item.key,
            sortOrder: '',
        })
    }
    if (Object.prototype.hasOwnProperty.call(item, 'order')) {
        let order = ''
        order = item.order === ORDER_TYPE.ASC ? ORDER_TYPE.DESC : ORDER_TYPE.ASC
        sortType.value.map((ele) => {
            if (ele.key === item.key) {
                ele.order = order
            }
        })
        activeSortOrder.value = order
        activeSortType.value = item.key
        emits('onChange', {
            sortType: item.key,
            sortOrder: order,
        })
    }
}
</script>
