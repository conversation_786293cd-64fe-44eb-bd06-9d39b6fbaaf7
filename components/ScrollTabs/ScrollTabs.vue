<script setup>
import { ref, computed, getCurrentInstance, onMounted } from "vue";

// 定义组件属性
const props = defineProps({
	// 选项卡数据，可以是字符串数组或对象数组
	tabs: {
		type: Array,
		default: () => [],
	},
	// 内容区高度，需要定义高度才能触发scroll事件
	contentHeight: {
		type: [String, Number],
		default: "100vh",
	},
	// 选项卡宽度，单位upx
	tabWidth: {
		type: Number,
		default: 150,
	},
	// 选项卡高度，单位upx
	tabHeight: {
		type: Number,
		default: 64,
	},
	// 下划线宽度，单位upx
	lineWidth: {
		type: Number,
		default: 40,
	},
	// 下划线高度，单位upx
	lineHeight: {
		type: Number,
		default: 6,
	},
	// 下划线颜色
	lineColor: {
		type: String,
		default: "#198B6B",
	},
	// 选项卡激活时的文字颜色
	activeColor: {
		type: String,
		default: "#101010",
	},
	// 选项卡未激活时的文字颜色
	inactiveColor: {
		type: String,
		default: "#767676",
	},
});

const { windowWidth } = uni.getSystemInfoSync();

// 响应式数据
const activeIndex = ref(0); // 当前激活的选项卡索引
const tabDomList = ref([]); // 选项卡节点信息
const scrollRightTop = ref(0); // 选项卡滚动到最右侧时，内容区域顶部位置
const isClickTab = ref(false); // 是否点击选项卡

// 计算属性
const tabWidthPx = computed(() => uni.upx2px(props.tabWidth));
const tabHeightPx = computed(() => uni.upx2px(props.tabHeight));
const lineWidthPx = computed(() => uni.upx2px(props.lineWidth));
const lineHeightPx = computed(() => uni.upx2px(props.lineHeight));

// tab点击以后判断当前激活项是否超过屏幕一半，超过则需要自动将激活菜单居中显示，自动向左向右滚动的效果
const scrollLeft = computed(() => {
	// const tabLeft = tabWidthPx.value * activeIndex.value + tabWidthPx.value / 2;
	// return tabLeft - windowWidth / 2;
	const currentTab = tabItemRefs.value[activeIndex.value];
	if (!currentTab) return 0;

	// 计算当前选项卡前面所有选项卡的宽度总和
	let leftOffset = 0;
	for (let i = 0; i < activeIndex.value; i++) {
		if (tabItemRefs.value[i]) {
			leftOffset += tabItemRefs.value[i].width || 0;
		}
	}

	// 计算当前选项卡的中心位置
	const tabCenter = leftOffset + currentTab.width / 2;
	// 返回需要滚动的距离，使当前选项卡居中
	return tabCenter - windowWidth / 2;
});

// 计算下划线位置
// const lineLeft = computed(() => {
// 	return tabWidthPx.value * activeIndex.value + (tabWidthPx.value - lineWidthPx.value) / 2 + "px";
// });

// 计算选项卡文字样式
const getTabStyle = (index) => {
	const style = {
		color: activeIndex.value === index ? props.activeColor : props.inactiveColor,
		fontWeight: activeIndex.value === index ? "bold" : "normal",
	};
	// 自适应宽度时，设置最小宽度和padding
	style.minWidth = tabWidthPx.value / 2 + "px";
	style.height = tabHeightPx.value + "px";
	style.padding = "0 15px";
	return style;
};

const instance = getCurrentInstance();
const query = uni.createSelectorQuery().in(instance.proxy);

// 获取内容区域高度
const getContentHeights = () => {
	return new Promise((resolve) => {
		query
			.selectAll(`.menu_item`)
			.boundingClientRect((data) => {
				if (Array.isArray(data) && data.length > 0) {
					const data0 = data[0];
					tabDomList.value = data.map((v) => v.top - data0.top);
					resolve();
				} else {
					console.error("未找到内容区域元素，请检查contentClass属性");
					resolve();
				}
			})
			.exec();
	});
};

// 选项卡dom引用
const tabItemRefs = ref([]);
// 计算下划线位置
const lineLeft = computed(() => {
	// 自适应宽度模式，需要计算当前选项卡的中心位置
	const currentTab = tabItemRefs.value[activeIndex.value];
	if (!currentTab) return "0px";

	// 计算当前选项卡前面所有选项卡的宽度总和
	let leftOffset = 0;
	for (let i = 0; i < activeIndex.value; i++) {
		if (tabItemRefs.value[i]) {
			leftOffset += tabItemRefs.value[i].width || 0;
		}
	}

	// 计算当前选项卡的中心位置，减去下划线宽度的一半
	return leftOffset + (currentTab.width - lineWidthPx.value) / 2 + "px";
});
// 获取选项卡DOM信息
const getTabItemRects = () => {
	return new Promise((resolve) => {
		query
			.selectAll(".tab-item")
			.boundingClientRect((data) => {
				if (Array.isArray(data) && data.length > 0) {
					tabItemRefs.value = data;
					resolve();
				} else {
					resolve();
				}
			})
			.exec();
	});
};

const leftMenuStatus = (index) => {
	activeIndex.value = index;
};

// 处理选项卡点击事件
const handleTabClick = (index) => {
	if (activeIndex.value === index) return;
	isClickTab.value = true;
	scrollRightTop.value = tabDomList.value[index];
	leftMenuStatus(index);
	// uni.pageScrollTo({
	// 	scrollTop: top,
	// });
};

const sleep = (ms = 300) => {
	return new Promise((resolve) => {
		const timer = setTimeout(() => {
			clearTimeout(timer);
			resolve();
		}, ms);
	});
};

// 处理内容区域滚动事件
const handleContentScroll = async ({ detail: { scrollTop } }) => {
	if (isClickTab.value) {
		isClickTab.value = false;
		return;
	}
	// #ifndef MP-WEIXIN
	if (!tabDomList.value.length) {
		await getContentHeights();
	}
	// #endif
	// 等待内容区域高度更新
	await sleep();
	const scrollHeight = scrollTop + 3; // 加偏移量
	for (let i = 0; i < tabDomList.value.length; i++) {
		const height = tabDomList.value[i];
		const nextHeight = tabDomList.value[i + 1];
		if (!nextHeight || (scrollHeight >= height && scrollHeight < nextHeight)) {
			leftMenuStatus(i);
			break;
		}
	}
};

// 组件挂载后初始化
onMounted(() => {
	// #ifndef MP-WEIXIN
	getContentHeights();
	// #endif
	getTabItemRects();
});
</script>
<template>
	<view class="scroll-tabs">
		<!-- 顶部选项卡 -->
		<scroll-view class="tabs-scroll" scroll-x scroll-with-animation :scroll-left="scrollLeft" :style="{ height: tabHeightPx + 'px' }">
			<view class="tabs-wrap">
				<view class="tab-item" v-for="(item, index) in tabs" :key="index" :style="getTabStyle(index)" @click="handleTabClick(index)">
					{{ typeof item === "object" ? item.name || item.title || item.label : item }}
				</view>
				<!-- 下划线 -->
				<view
					class="tab-line"
					:style="{
						left: lineLeft,
						width: lineWidthPx + 'px',
						height: lineHeightPx + 'px',
						backgroundColor: lineColor,
					}">
				</view>
			</view>
		</scroll-view>

		<!-- 内容区域 -->
		<scroll-view scroll-y scroll-with-animation :scroll-top="scrollRightTop" :style="{ paddingTop: tabHeightPx + 'px', height: contentHeight }" @scroll="handleContentScroll">
			<slot> </slot>
		</scroll-view>
	</view>
</template>

<style lang="scss" scoped>
.scroll-tabs {
	height: 100%;
	position: relative;
}

.tabs-scroll {
	position: fixed;
	left: 0;
	top: var(--window-top, 0);
	width: 100%;
	background-color: #fff;
	z-index: 10;
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.tabs-wrap {
	position: relative;
	white-space: nowrap;

	.tab-item {
		display: inline-block;
		text-align: center;
		line-height: v-bind(tabHeightPx + "px");
		transition: color 0.3s;
	}

	.tab-line {
		position: absolute;
		bottom: 0;
		border-radius: 3px;
		transition: left 0.3s;
	}
}

/* 默认内容样式 */
// :deep(.menu_item) {
// 	padding: 15px;
// 	margin-bottom: 10px;
// 	background-color: #f8f8f8;
// 	border-radius: 5px;
// }
</style>
