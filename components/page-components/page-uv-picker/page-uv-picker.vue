<script setup>
	import uvPickerProps from './props'

	import {
		computed,
		ref,
	} from 'vue';

	const props = defineProps({
		...uvPickerProps.props,
		clickAutoOpen: {
			type: Boolean,
			default: true
		},
	})

	const pickerProps = computed(() => {
		const newProps = {
			...props
		}
		if (!newProps.confirmColor) newProps.confirmColor = '#38B597'

		return newProps
	})

	const emit = defineEmits(['confirm', 'change', 'cancel', 'close'])

	const indexs = defineModel('indexs', {
		default: 0
	})
	const values = defineModel('values', {
		default: []
	})

	const pickerRef = ref()

	function pickerConfirm(e) {
		indexs.value = e.indexs
		values.value = e.value
		emit('confirm', e)
	}

	function pickerChange(e) {
		emit('change', e)
	}

	function pickerCancel(e) {
		emit('cancel', e)
	}

	function pickerClose(e) {
		emit('close', e)
	}

	function open(e) {
		pickerRef.value.open(e)
	}

	function close(e) {
		pickerRef.value.open(e)
	}

	function setIndexs(index, setLastIndex) {
		pickerRef.value.open(index, setLastIndex)
	}

	function setColumnValues(columnIndex, values) {
		pickerRef.value.open(columnIndex, values)
	}

	function clickPickerCentent() {
		if (props.clickAutoOpen) {
			pickerRef.value.open()
		}
	}

	defineExpose({
		open,
		close,
		setIndexs,
		setColumnValues,
	})
</script>

<template>
	<view class="w-full" @click="clickPickerCentent">
		<slot></slot>
	</view>

	<uv-picker ref="pickerRef" v-bind="pickerProps" @confirm="pickerConfirm" @change="pickerChange" @cancel="pickerCancel"
		@close="pickerClose"></uv-picker>
</template>

<style scoped lang="scss">
	.page-uv-picker {}
</style>