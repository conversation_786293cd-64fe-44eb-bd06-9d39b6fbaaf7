<script setup>
import { computed, onMounted, ref, watch, shallowReactive, toRefs, toRaw } from 'vue';

import { Decimal } from 'decimal.js';

import useConvert from '@/common/useConvert';

import { canENV, getPriceInfo } from '@/common/utils';

import { deepClone } from '@/uni_modules/uv-ui-tools/libs/function/index.js';

import throttle from '@/uni_modules/uv-ui-tools/libs/function/throttle.js';
import debounce from '@/uni_modules/uv-ui-tools/libs/function/debounce.js';

import { consumerCollectCoupon } from '@/server/api';

import { useGoodsStore } from '@/store';

import GoodNormClass from './utils/goodNorm';

const props = defineProps({
	title: {
		type: String,
		default: ''
	},
	// ['XXX,XXX','XXX,XXX']
	disableOptions: {
		type: Array,
		default: () => []
	},
	disableSpec: {
		type: Array,
		default: () => []
	},
	listStyle: {
		type: String,
		default: 'card' // card line
	},
	autoOpenClose: {
		type: Boolean,
		default: true
	}
});

const { divTenThousand } = useConvert();

const goodsStore = useGoodsStore();

const selectBtnPopup = ref();

const pageFormSend = ref(true);
// const pageLoading = ref(false);

const emit = defineEmits(['confirm']);

const pageLoading = computed(() => goodsStore.setOperation.loading);

const currentChoosedSku = computed(() => goodsStore.currentChoosedSku);
const goodInfo = computed(() => goodsStore.goodInfo);
const actionFlags = computed(() => goodsStore.actionFlags);
const skuGroup = computed(() => goodsStore.skuGroup);

const currentSpecs = computed(() => goodsStore.currentGoodsExtraData.currentSpecs);
const currentParams = computed(() => goodsStore.currentGoodsExtraData.currentParams);

const extra = computed(() => goodInfo.value.extra);

const goodPriceInfo = computed(() => {
	const price =
		goodInfo.value.activityOpen && goodInfo.value.skuActivity && (actionFlags.value.isGroup || goodInfo.value.activity?.type === 'SPIKE')
			? currentChoosedSku.value.activePrice || 0
			: currentChoosedSku.value.salePrice;
	return getPriceInfo(price);
});

let defaultSpecs = [];
const specsData = ref({});

// const productAttributes = ref(extra.value.productAttributes?.map((i) => ({ ...i, featureValues: specsData[i.id] || [] })));
const productAttributes = computed({
	get() {
		return goodsStore.productAttributes;
	},
	set(value) {
		goodsStore.changeProductAttributes(value);
	}
});

const goodsCount = computed({
	get() {
		return goodsStore.goodsCount;
	},
	set(val) {
		goodsStore.changeGoodsCount(val);
	}
});

const goodsCountMax = computed(() => {
	let num = 0;

	if (goodsStore.goodsInitType === 'productDetailsIntegral') {
		return goodInfo.value.stock || 0;
	}

	const { limitType, stockType, stock, limitNum } = currentChoosedSku.value;

	const teamStock = goodsStore.activitePlugin.TEAM
		? goodsStore.activitePlugin.TEAM.groupInfo.value.skus.find((item) => item.skuId === currentChoosedSku.value.id)?.stock || '0'
		: '0';

	if (limitType !== 'UNLIMITED' && stockType !== 'UNLIMITED') {
		// 限购 +有限库存 取最小值
		return Math.min(Number(limitNum), Number(stock));
	} else if (limitType !== 'UNLIMITED') {
		// 限购 取限购的数量
		num = Number(limitNum);
	} else if (stockType !== 'UNLIMITED') {
		//  不限购限制库存 取库存的数量
		num = Number(stock);
	} else {
		return Number(teamStock) > 0 ? Number(teamStock) : 999;
	}

	return num;
});

const pageData = shallowReactive({
	commoditySpecs: [],
	chooseSpec: [],
	optionSpec: [],
	normClass: new GoodNormClass([], [], [])
});

const stockMap = ref(new Map());
function initStockMap(skus = []) {
	for (let i = 0; i < skus.length; i++) {
		const item = skus[i];
		stockMap.value.set(item.specs.join(','), item.stockType === 'LIMITED' && Number(item.stock) === 0);
	}
}

function filterClassArrField(specArr = []) {
	if (specArr) {
		return specArr.map((item) => {
			return {
				title: item.name,
				list: item.children.map((item) => {
					return item.name;
				})
			};
		});
	}

	return [];
}

function filterListField(list = [], disableSpecs = [], sku = false) {
	const filterArr = [];
	list.forEach((item) => {
		const tag = disableSpecs.includes(item.specs?.join(','));
		if (!tag) {
			if (sku) {
				filterArr.push({
					...item
				});
			} else {
				filterArr.push({
					id: item.id,
					specs: item.specs
				});
			}
		}
	});
	return filterArr;
}

// 选择多规格
function chooseSpecHandle(flag, item, index) {
	const disable = pageData.optionSpec.indexOf(item) === -1;
	const over = stockMap.value.get(sellOutStyle(item, index));
	// if (disable || over) return;
	if (disable) return;

	throttle(() => {
		const { commoditySpecs, chooseSpec, normClass, optionSpec } = toRefs(pageData);
		if (chooseSpec.value[index] === item || !flag) return;
		chooseSpec.value[index] = chooseSpec.value[index] === item ? '' : item;
		chooseSpec.value = chooseSpec.value.slice();
		optionSpec.value = normClass.value.querySpecsOptions(chooseSpec.value.slice());
		// 向父级传递选中的规格
		if (chooseSpec.value.filter(Boolean).length === commoditySpecs.value.length) {
			// console.log(productAttributes.value);
			handleChooseSpec(getChooseSpecs());
		}
	});
}

// 选中参数
function handleCheckbox(flag, item, key, isMultiSelect, isRequired, upload = true) {
	throttle(() => {
		if (flag) {
			if (isMultiSelect) {
				specsData.value[key] = [...specsData.value[key], item];
			} else {
				specsData.value[key] = [item];
			}
		} else {
			const newVal = specsData.value[key].filter((i) => i.featureValueId !== item.featureValueId);

			if (isRequired && newVal.length === 0) {
				uni.showToast({
					icon: 'none',
					title: '请至少选择一项'
				});
				return;
			}

			specsData.value[key] = newVal;
		}
		let count = new Decimal(0);
		for (const item in specsData.item) {
			count = specsData.value[item].reduce((pre, ite) => {
				return pre.add(divTenThousand(ite.secondValue).toNumber());
			}, count);
		}
		productAttributes.value = extra.value.productAttributes
			? extra.value.productAttributes.map((item) => {
					return {
						...item,
						featureValues: specsData.value[item.id]
					};
			  })
			: [];
		if (upload) {
			handleChooseSpec(getChooseSpecs(), false);
		}
	}, 0);
}

//获取选中规格
function getChooseSpecs() {
	let activePrice = skuGroup.value.skus[0].salePrice;
	if (goodInfo.value.activity?.type === 'SPIKE') {
		activePrice = goodInfo.value.activity?.activityPrice || 0;
	}
	// 所有 SKU
	const tempArr = toRaw(
		skuGroup.value.skus.map((v) => {
			return {
				...v,
				activePrice: activePrice
			};
		})
	);
	// 选中的 SKU
	const arr = skuGroup.value.specGroups?.length === 0 ? tempArr : tempArr.filter((item) => equar(item.specs, pageData.chooseSpec));
	const temp = deepClone(arr);
	let count = 0;

	for (let item in specsData.value) {
		count += specsData.value[item].reduce((pre, ite) => {
			return pre + Number(ite.secondValue);
		}, 0);
	}

	if (temp[0]) {
		if (goodInfo.value.activityOpen && goodInfo.value.skuActivity && (actionFlags.value.isGroup || goodInfo.value.activity?.type === 'SPIKE')) {
			temp[0].activePrice = String((+temp[0].activePrice || 0) + count);
		} else {
			temp[0].salePrice = String((+temp[0].salePrice || 0) + count);
		}
	}
	return temp;
}

function equar(a, b) {
	// 判断数组的长度
	if (a.length !== b.length) {
		return false;
	} else {
		// 循环遍历数组的值进行比较
		for (let i = 0; i < a.length; i++) {
			if (a[i] !== b[i]) {
				return false;
			}
		}
		return true;
	}
}

// 整理库存map的key
function sellOutStyle(item, index) {
	let key = [...pageData.chooseSpec];
	key[index] = item;
	return key.join(',');
}

function handleChooseSpec(skuList, isUpdate = true) {
	const currentSpecs = productAttributes.value || [];
	// 处理选择校验
	if (handleProductAttributes(currentSpecs)) {
		goodsStore.changeCurrentGoodsExtraDataSpecs(currentSpecs);
	}
	// count.value = 1;
	goodsStore.updateSku(skuList[0], isUpdate, currentSpecs);
}

function handleProductAttributes(productAttributes = []) {
	const length = productAttributes?.length;
	if (!length) return true;
	for (let i = 0; i < length; i++) {
		const item = productAttributes[i];
		if (item.isRequired && item.featureValues?.length === 0) {
			uni.showToast({
				title: `${item.featureName}为必选`,
				icon: 'none'
			});
			return false;
		}
		if (!item.isMultiSelect && item.featureValues?.length > 1) {
			uni.showToast({
				title: `${item.featureName}为单选`,
				icon: 'none'
			});
			return false;
		}
	}
	return true;
}

function initData() {
	if (skuGroup.value.skus.length === 0) return;
	const skuGroupClone = deepClone(skuGroup.value);

	initStockMap(skuGroupClone.skus);

	let filterClassArr = [];
	let filterList = [];
	let filterSkuList = [];

	if (skuGroup.value.specGroups && skuGroup.value.specGroups.length > 0) {
		// 整理传参数据
		filterClassArr = filterClassArrField(skuGroupClone.specGroups);
		filterList = filterListField(skuGroupClone.skus, props.disableSpec);
		filterSkuList = filterListField(skuGroupClone.skus, props.disableSpec, true);
	}

	// 创建商品规格矩阵
	const newGoodNormClass = new GoodNormClass(filterClassArr, filterList, filterSkuList);

	pageData.commoditySpecs = filterClassArr;
	pageData.normClass = newGoodNormClass;

	canENV(() => {
		console.log('已选 sku =>', currentChoosedSku.value.specs);
	});

	// 初始化选择规格数组
	if (!currentChoosedSku.value.specs) return;

	if (!currentChoosedSku.value.specs.length && skuGroupClone.skus.length > 0) {
		pageData.chooseSpec = skuGroupClone.skus[0].specs;
	} else if (currentChoosedSku.value.specs.length && skuGroupClone.skus.length > 0) {
		pageData.chooseSpec = deepClone(currentChoosedSku.value.specs);
	} else {
		pageData.chooseSpec = Array.from({ length: filterClassArr.length });
	}

	pageData.optionSpec = newGoodNormClass.querySpecsOptions(pageData.chooseSpec);

	const attributes = extra.value.productAttributes;
	const adopt = skuGroup.value.specGroups?.length === 0 ? productAttributes.value : pageData.chooseSpec.length;

	if (attributes && adopt) {
		attributes.forEach((item) => {
			const data = currentSpecs.value?.find((ite) => ite.id === item.id);

			if (data) {
				specsData.value[item.id] = [...data.featureValues];
			} else {
				specsData.value[item.id] = [];
			}
		});
		productAttributes.value = attributes.map((item) => {
			return {
				...item,
				featureValues: specsData.value[item.id]
			};
		});

		extra.value.productAttributes.map((item) => {
			if (item.isRequired) {
				if (item.featureValues.length > 0) {
					const attributesItem = item.featureValues[0];
					handleCheckbox(
						specsData[item.id]?.find((it) => it.featureValueId === attributesItem.featureValueId) === void 0,
						attributesItem,
						item.id,
						item.isMultiSelect,
						item.isRequired,
						false
					);
				}
			}
		});

		defaultSpecs = getChooseSpecs();

		handleChooseSpec(defaultSpecs, false);
		// $emit('chooseSpec', defaultSpecs, false)
	}
}

function numberChange() {}

function submit() {
	// emit('confirm', {
	// 	indexs: indexs.value,
	// 	items: items.value,
	// 	values: items.value.map((i) => i[props.valueKey]),
	// 	texts: items.value.map((i) => i[props.textKey]),
	// 	textsText: items.value.map((i) => i[props.textKey]).join(','),
	// 	valuesText: items.value.map((i) => i[props.valueKey]).join(',')
	// });
	goodsStore.submitBottomBtn();
	// closePopup();
}

function clickPopupCentent() {}

function openPopup() {
	selectBtnPopup.value.open();
}

function closePopup() {
	selectBtnPopup.value.close();
}

function changePopup(e) {
	goodsStore.changeSkuPopupShow(e.show);
}

function loadData() {
	if (props.autoOpenClose) {
		watch(
			() => goodsStore.setOperation.control,
			(val) => {
				if (val) {
					openPopup();
				} else {
					closePopup();
				}
			},
			{
				immediate: true
			}
		);
	}
}

function previewSkuImage() {
	const images = [];
	let chooseSkuImage = currentChoosedSku.value.image || goodInfo.value.pic;
	pageData.normClass.skus.map((i) => {
		if (i.image) {
			images.push(i.image);
		}
		// if (i.specs.join('') === pageData.chooseSpec.join('')) {
		// 	chooseSkuImage = i.image;
		// }
	});

	uni.previewImage({
		// urls: images,
		urls: [chooseSkuImage],
		current: chooseSkuImage
	});
}

watch(
	props,
	() => {
		debounce(() => {
			initData();
		});
	},
	{
		immediate: true
	}
);

watch(
	skuGroup,
	() => {
		debounce(() => {
			initData();
		});
	},
	{
		immediate: true
	}
);

onMounted(() => {
	loadData();
});

defineExpose({
	openPopup,
	closePopup
});
</script>

<template>
	<view class="page-select-btn w-full" @touchmove.stop.prevent>
		<view class="w-full" @click="clickPopupCentent">
			<slot></slot>
		</view>
		<my-uv-popup mode="bottom" ref="selectBtnPopup" bgColor="#fff" round="20rpx" class="" @change="changePopup" closeable close-on-click-overlay>
			<view class="w-full bg-#fff border-top-left-rd-20">
				<template v-if="title">
					<view class="w-full py-40 text-center text-34 font-bold">
						{{ title }}
					</view>
				</template>
				<view class="px-26 pb-50 pt-20">
					<view class="w-full pt-50">
						<!-- 商品信息 -->
						<view class="w-full flex">
							<view class="flex-1 flex items-center">
								<view class="w-181 h-181 flex flex-center border-rd-10 border-solid border-1 border-#F5F5F5 flex-shrink-0">
									<app-image :src="currentChoosedSku.image || goodInfo.pic" size="180" rd="10" mode="" @click="previewSkuImage"></app-image>
								</view>
								<view class="flex-1 ml-40 flex flex-col">
									<view class="text-32 font-bold mt-0 uv-line-1">{{ goodInfo.name }}</view>
									<template v-if="goodsStore.goodsInitType === 'productDetailsIntegral'">
										<view class="flex">
											<view class="flex items-end">
												<view class="text-30 font-500 text-#FC3F33">
													{{ goodInfo.integralPrice }}
												</view>
												<view class="text-26 font-500 text-#FC3F33 pb-4">积分</view>
											</view>
											<template v-if="goodInfo.salePrice > 0">
												<view class="flex items-end">
													<view class="text-28 font-500 text-#FC3F33 pb-0 mx-5">+</view>
													<view class="text-26 font-500 text-#FC3F33 pb-4">¥</view>
													<view class="text-30 font-500 text-#FC3F33">
														{{ getPriceInfo(goodInfo.salePrice).integer }}
													</view>
													<view class="text-26 font-500 text-#FC3F33 pb-1">.{{ getPriceInfo(goodInfo.salePrice).decimalText }}</view>
												</view>
											</template>
										</view>
									</template>
									<template v-else>
										<view class="flex items-end">
											<!-- <view class="text-24 font-500 text-#FC3F33 pb-12">预估到手</view> -->
											<view class="text-30 font-500 text-#FC3F33 pb-2">¥</view>
											<view class="text-36 font-500 text-#FC3F33">
												{{ goodPriceInfo.integer }}
											</view>
											<view class="text-30 font-500 text-#FC3F33 pb-2">.{{ goodPriceInfo.decimalText }}</view>
											<!-- <view class="text-24 font-500 text-#FC3F33 pb-12">起</view> -->
										</view>
									</template>
									<view class="flex items-center mt-10">
										<template v-if="goodInfo.serveGoods === 0">
											<template v-if="goodsStore.goodsInitType === 'productDetailsIntegral'">
												<view class="text-22 text-#4E515B">库存：{{ goodInfo.stock }}</view>
											</template>
											<template v-else>
												<template v-if="currentChoosedSku.stockType !== 'UNLIMITED'">
													<view class="text-22 text-#4E515B">库存：{{ currentChoosedSku.stock }}</view>
												</template>
												<template v-if="goodInfo && skuGroup.skus.length >= 1 && currentChoosedSku.limitType !== 'UNLIMITED'">
													<view class="text-22 text-#4E515B ml-15">限购：{{ currentChoosedSku.limitNum }}</view>
												</template>
											</template>
										</template>
									</view>
								</view>
								<template v-if="goodsStore.goodsInitType !== 'productDetailsIntegral'">
									<view class="flex-shrink-0">
										<my-uv-number-box
											v-model="goodsCount"
											@change="numberChange"
											:min="1"
											:max="goodsCountMax"
											bgColor="#F3F4F6"
											inputBgColor="rgba(255,255,255,0)"
											color="#111010"
											buttonSize="56rpx"
											integer
										></my-uv-number-box>
									</view>
								</template>
							</view>
						</view>
						<!-- 选择配送方式 -->
						<template v-if="!goodsStore.setOperation.isCar && Array.isArray(goodInfo.distributionMode) && goodInfo.distributionMode.length > 1">
							<view class="w-full"></view>
						</template>
					</view>
					<view class="w-full">
						<template v-if="(goodInfo && skuGroup.specGroups && skuGroup.specGroups.length > 0) || goodInfo.extra?.productAttributes.length > 0">
							<view class="w-full mt-20 sku-box">
								<scroll-view scroll-y="true" class="w-full max-h-500">
									<!-- SKU -->
									<template v-if="skuGroup.specGroups && skuGroup.specGroups.length > 0">
										<template v-for="(sku, index) in pageData.commoditySpecs" :key="index">
											<view class="w-full sku-item-box mt-30" :class="[listStyle]">
												<view class="w-full mb-30 text-28 font-bold">
													{{ sku.title }}
												</view>
												<view class="w-full sku-item-content">
													<template v-for="(skuItem, itemIndex) in sku.list" :key="itemIndex">
														<view
															class="sku-item"
															:class="{
																option: pageData.optionSpec.indexOf(skuItem) > -1,
																active: pageData.chooseSpec.indexOf(skuItem) > -1,
																disable: pageData.optionSpec.indexOf(skuItem) === -1,
																over: stockMap.get(sellOutStyle(skuItem, index))
															}"
															@click="chooseSpecHandle(pageData.optionSpec.indexOf(skuItem) > -1, skuItem, index)"
														>
															<view class="">
																{{ skuItem }}
															</view>
														</view>
													</template>
												</view>
											</view>
										</template>
									</template>
									<!-- 属性 -->
									<template v-if="Object.keys(specsData).length > 0">
										<template v-for="(item, index) in extra.productAttributes" :key="index">
											<view class="w-full sku-item-box mt-30 card">
												<view class="w-full mb-30 text-28 font-bold">{{ item.featureName }}{{ item.isMultiSelect ? '(可多选)' : '' }}</view>
												<view class="w-full sku-item-content">
													<template v-for="(attributesItem, itemIndex) in item.featureValues" :key="itemIndex">
														<view
															class="sku-item"
															:class="{
																// option: pageData.optionSpec.indexOf(skuItem) > -1,
																active: specsData[item.id]?.find((it) => it.featureValueId === attributesItem.featureValueId) !== void 0
																// disable: pageData.optionSpec.indexOf(skuItem) === -1,
																// over: stockMap.get(sellOutStyle(skuItem, index))
															}"
															@click="
																handleCheckbox(
																	specsData[item.id]?.find((it) => it.featureValueId === attributesItem.featureValueId) === void 0,
																	attributesItem,
																	item.id,
																	item.isMultiSelect,
																	item.isRequired
																)
															"
														>
															<view class="attributes-item">
																<view class="content-box">{{ attributesItem.firstValue }}</view>

																<template v-if="Number(divTenThousand(attributesItem.secondValue)) !== 0">
																	<view class="line-box"></view>

																	<view class="value-box">
																		<!-- <text>{{ attributesItem.secondValue && new Decimal(attributesItem.secondValue).lte(0) ? '' : '+' }}</text> -->
																		<text>￥{{ divTenThousand(attributesItem.secondValue) }}</text>
																	</view>
																</template>
																<template v-else></template>
															</view>
														</view>
													</template>
												</view>
											</view>
										</template>
									</template>
								</scroll-view>
							</view>
						</template>
					</view>

					<view class="mt-60">
						<template v-if="!goodsStore.isSkuSellOut">
							<uv-button
								color="#00B496"
								text="确认"
								loadingText="设置中..."
								class="w-full mt-0 flex-center"
								custom-style="height: 90rpx;"
								customTextStyle="font-size: 30rpx; font-weight: bold;"
								shape="circle"
								loadingMode="circle"
								:loading="pageLoading"
								:disabled="!pageFormSend"
								@click="submit"
							></uv-button>
						</template>
						<template v-else>
							<uv-button
								color="#00B496"
								text="商品缺货"
								loadingText="设置中..."
								class="w-full mt-0 flex-center"
								custom-style="height: 90rpx;"
								customTextStyle="font-size: 30rpx; font-weight: bold;"
								shape="circle"
								loadingMode="circle"
								:disabled="true"
							></uv-button>
						</template>
					</view>
				</view>
			</view>
		</my-uv-popup>
	</view>
</template>

<style scoped lang="scss">
.page-select-btn {
	.select-btn-box {
		width: 216rpx;
		height: 80rpx;
		border-radius: 30rpx;
		font-weight: 500;
		font-size: 28rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		margin: 0 8rpx 24rpx 8rpx;
		background: #f7f7f7;
		color: #1a1a1b;

		.select-icon {
			display: none;
			position: absolute;
			top: 0;
			right: 0;
		}

		&.active {
			background: #eee8ff;
			color: #5933e9;

			.select-icon {
				display: block;
			}
		}
	}
}

.sku-item-box {
	&.card {
		.sku-item-content {
			flex-direction: row;
			flex-wrap: wrap;

			.sku-item {
			}
		}
	}
	&.line {
		.sku-item-content {
			flex-direction: column;

			.sku-item {
				width: 100%;
			}
		}
	}
	.sku-item-content {
		width: 100%;
		display: flex;
		.sku-item {
			min-width: 325rpx;
			min-height: 90rpx;
			margin: 10rpx 20rpx 10rpx 0rpx;
			background: #f2f4f8;
			border-radius: 16rpx;
			padding: 0 26rpx;
			display: flex;
			align-items: center;
			font-weight: 500;
			font-size: 28rpx;
			color: #111010;
			border: solid 1rpx #f2f4f8;
			&.active {
				background: #e8faf3;
				border: solid 1rpx #00b496;
			}
			&.disable {
				background-color: #f2f4f8;
				color: #aaaeb2;
			}
			&.over {
				background-color: #f2f4f8;
				color: #aaaeb2;
			}
			&:active {
				opacity: 0.8;
			}

			.attributes-item {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: space-between;
				.content-box {
					flex: 1;
				}
				.line-box {
					width: 1rpx;
					height: 25rpx;
					background-color: #888;
					margin: 0 10rpx;
				}
				.value-box {
					min-width: 80rpx;
					flex-shrink: 0;
					display: flex;
					align-items: center;
					justify-content: center;
					color: #fc3f33;
				}
			}
		}
	}
}
</style>
