# PageInfoSharePopup 分享弹窗组件

一个支持微信小程序、App、H5 三端的通用分享弹窗组件，内置完整的分享逻辑。

## 功能特性

-   ✅ 支持微信小程序、App、H5 三端
-   ✅ 内置分享逻辑，开箱即用
-   ✅ 支持自定义分享参数
-   ✅ 支持外部自定义分享逻辑
-   ✅ 自动环境检测和版本判断
-   ✅ 完善的错误处理和用户提示

## 基础用法

### 1. 使用 shareData 对象（推荐）

```vue
<template>
	<view>
		<button @click="openShare">分享</button>
		<PageInfoSharePopup
			ref="sharePopupRef"
			:shareData="{
				title: article.title,
				path: `/pages/detail/detail?id=${article.id}`,
				imageUrl: article.cover,
				summary: '这是一篇很棒的文章',
			}" />
	</view>
</template>

<script setup>
import { ref, computed } from "vue";
import PageInfoSharePopup from "@/components/page-components/page-info-share-popup/page-info-share-popup.vue";

const sharePopupRef = ref(null);
const article = ref({
	id: 123,
	title: "健康生活指南",
	cover: "https://example.com/cover.jpg",
});

// 也可以使用计算属性
const shareData = computed(() => ({
	title: article.value.title,
	path: `/pages/detail/detail?id=${article.value.id}`,
	imageUrl: article.value.cover,
	summary: "这是一篇很棒的文章",
}));

const openShare = () => {
	sharePopupRef.value.open();
};
</script>
```

### 2. 自定义配置

```vue
<!-- 使用 shareData 对象 + 自定义配置（推荐） -->
<PageInfoSharePopup
	ref="sharePopupRef"
	:shareData="{
		title: '自定义标题',
		path: customPath,
		imageUrl: customImage,
		summary: '自定义描述',
	}"
	:showReport="false"
	miniProgramId="your_miniprogram_id"
	domain="https://your-domain.com"
	@handleReport="handleCustomReport" />

<!-- 或者使用传统方式 -->
<PageInfoSharePopup ref="sharePopupRef" title="自定义标题" :path="customPath" :imageUrl="customImage" summary="自定义描述" :showReport="false" miniProgramId="your_miniprogram_id" domain="https://your-domain.com" @handleReport="handleCustomReport" />
```

### 3. 使用外部分享逻辑

```vue
<PageInfoSharePopup ref="sharePopupRef" :useBuiltinShare="false" @shareToFriend="handleCustomShareToFriend" @shareToMoments="handleCustomShareToMoments" @copyLink="handleCustomCopyLink" />
```

## Props 参数

| 参数               | 类型    | 默认值                    | 说明                                  |
| ------------------ | ------- | ------------------------- | ------------------------------------- |
| shareData          | Object  | {}                        | 分享数据对象（推荐使用）              |
| shareData.title    | String  | -                         | 分享标题                              |
| shareData.path     | String  | -                         | 分享路径（小程序页面路径）            |
| shareData.imageUrl | String  | -                         | 分享图片 URL                          |
| shareData.summary  | String  | -                         | 分享描述（App 端使用）                |
| title              | String  | "分享转发"                | 弹窗标题和分享标题（向后兼容）        |
| path               | String  | ""                        | 分享路径（向后兼容）                  |
| imageUrl           | String  | ""                        | 分享图片 URL（向后兼容）              |
| summary            | String  | "这是分享内容的描述"      | 分享描述（向后兼容）                  |
| showReport         | Boolean | true                      | 是否显示举报按钮                      |
| useBuiltinShare    | Boolean | true                      | 是否使用内置分享逻辑                  |
| miniProgramId      | String  | "gh_d331c8817c01"         | 小程序 ID（App 端分享小程序卡片使用） |
| domain             | String  | "https://api.ukangai.com" | 域名配置                              |

### 参数优先级

当同时传入 `shareData` 对象和单独的 props 时，优先级如下：

1. `shareData.title` > `title`
2. `shareData.path` > `path`
3. `shareData.imageUrl` > `imageUrl`
4. `shareData.summary` > `summary`

## Events 事件

| 事件名         | 说明                                                | 参数 |
| -------------- | --------------------------------------------------- | ---- |
| shareToFriend  | 分享到微信好友（仅在 useBuiltinShare=false 时触发） | -    |
| shareToMoments | 分享到朋友圈（仅在 useBuiltinShare=false 时触发）   | -    |
| handleReport   | 点击举报按钮                                        | -    |
| copyLink       | 复制链接（仅在 useBuiltinShare=false 时触发）       | -    |
| closeShare     | 关闭分享弹窗                                        | -    |

## Methods 方法

| 方法名  | 说明         | 参数 |
| ------- | ------------ | ---- |
| open()  | 打开分享弹窗 | -    |
| close() | 关闭分享弹窗 | -    |

## 平台差异说明

### 微信小程序

-   分享到好友：使用 `open-type="share"` 触发系统分享
-   分享到朋友圈：提示用户手动操作
-   复制链接：复制完整的小程序页面链接

### App 端

-   分享到好友/朋友圈：使用 `uni.share` 分享小程序卡片
-   自动检测开发版/正式版环境
-   复制链接：复制 H5 页面链接

### H5 端

-   优先使用浏览器原生分享 API
-   降级到复制链接功能
-   复制当前页面 URL

## 注意事项

1. **小程序分享配置**：需要在页面中配置 `onShareAppMessage` 和 `onShareTimeline` 生命周期
2. **App 端权限**：需要在 `manifest.json` 中配置微信分享权限
3. **图片资源**：分享图片建议使用 HTTPS 链接，尺寸建议 500x400 以上
4. **路径格式**：小程序路径需要以 `/` 开头，如 `/pages/detail/detail?id=123`

## 完整示例

```vue
<template>
	<view class="article-detail">
		<view class="article-content">
			<!-- 文章内容 -->
		</view>

		<view class="share-btn" @click="openShare">
			<text>分享文章</text>
		</view>

		<PageInfoSharePopup
			ref="sharePopupRef"
			:shareData="{
				title: articleData.title,
				path: `/pages/article/detail?id=${articleData.id}`,
				imageUrl: articleData.cover,
				summary: articleData.summary,
			}"
			@handleReport="handleReport"
			@closeShare="onShareClose" />
	</view>
</template>

<script setup>
import { ref, reactive } from "vue";
import { onShareAppMessage, onShareTimeline } from "@dcloudio/uni-app";
import PageInfoSharePopup from "@/components/page-components/page-info-share-popup/page-info-share-popup.vue";

const sharePopupRef = ref(null);
const articleData = reactive({
	id: 123,
	title: "健康生活的10个小贴士",
	cover: "https://example.com/article-cover.jpg",
	summary: "分享健康生活的实用建议，让你的生活更美好",
});

const openShare = () => {
	sharePopupRef.value.open();
};

const handleReport = () => {
	uni.navigateTo({
		url: `/pages/report/report?articleId=${articleData.id}`,
	});
};

const onShareClose = () => {
	console.log("分享弹窗已关闭");
};

// 小程序分享配置
// #ifdef MP-WEIXIN
onShareAppMessage(() => {
	return {
		title: articleData.title,
		path: `/pages/article/detail?id=${articleData.id}`,
		imageUrl: articleData.cover,
	};
});

onShareTimeline(() => {
	return {
		title: articleData.title,
		query: `id=${articleData.id}`,
		imageUrl: articleData.cover,
	};
});
// #endif
</script>
```
