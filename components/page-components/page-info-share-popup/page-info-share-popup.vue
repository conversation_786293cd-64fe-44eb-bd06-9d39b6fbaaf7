<template>
	<uv-action-sheet title="分享转发" ref="actionSheetRef" :round="10" cancelText="取消" @close="closeShare">
		<view class="flex">
			<button class="item flex flex-col gap-20 py-30 px-40 items-center" plain open-type="share" @click="handleShareToFriend">
				<app-image class="w-80 h-80" :src="information.wechat" size="80" mode=""> </app-image>
				微信好友
			</button>
			<!-- #ifdef MP-WEIXIN -->
			<button class="item flex flex-col gap-20 py-30 px-40 items-center" plain @click="handleShareToMoments">
				<app-image class="w-80 h-80" :src="information.moments" size="80" mode=""> </app-image>
				朋友圈
			</button>
			<!-- #endif -->

			<view v-if="showReport" class="flex flex-col gap-20 py-30 px-40 items-center" @click="handleReport">
				<app-image class="w-80 h-80" :src="information.report" size="80" mode=""> </app-image>
				<text class="text-24 color-#222222">举报</text>
			</view>
			<!-- #ifdef H5 -->
			<view class="flex flex-col gap-20 py-30 px-40 items-center" @click="handleCopyLink">
				<app-image class="w-80 h-80" :src="information.link" size="80" mode=""> </app-image>
				<text class="text-24 color-#222222">复制链接</text>
			</view>
			<!-- #endif -->
		</view>
	</uv-action-sheet>
</template>

<script setup>
import { ref, reactive } from "vue";
import { information } from "@/common/images";
import { canENV } from "@/common/utils";
import { toast } from "@/uni_modules/uv-ui-tools/libs/function/index.js";

const actionSheetRef = ref(null);
const emit = defineEmits(["shareToFriend", "shareToMoments", "handleReport", "copyLink", "closeShare"]);

const props = defineProps({
	// 分享数据对象（推荐使用）
	shareData: {
		type: Object,
		default: () => ({}),
		validator: (value) => {
			// 验证shareData对象的结构
			if (typeof value !== "object") return false;
			return true;
		},
	},
	// 是否显示举报按钮
	showReport: {
		type: Boolean,
		default: true,
	},
	// 是否使用内置分享逻辑
	useBuiltinShare: {
		type: Boolean,
		default: true,
	},
	// 小程序ID配置
	miniProgramId: {
		type: String,
		default: "gh_d331c8817c01",
	},
	// 域名配置
	domain: {
		type: String,
		default: "https://api.ukangai.com",
	},
});

// 默认分享配置
const defaultShareConfig = reactive({
	title: "和家无忧",
	path: "/pages/tabBar/home/<USER>",
	imageUrl: "https://hejiawuyou.oss-cn-shanghai.aliyuncs.com/wxapp/imgzip/logo.png",
	summary: "健康生活，从这里开始",
});

// 判断小程序环境版本 0=正式版 1=开发版
let mpType = 0;
canENV(
	() => {
		mpType = 1; // 开发版
	},
	() => {
		mpType = 0; // 正式版
	}
);

const open = () => {
	actionSheetRef.value.open();
};

const close = () => {
	actionSheetRef.value.close();
};

// 获取当前分享参数
const getShareParams = () => {
	// 优先使用 shareData 对象，如果不存在则使用单独的props（向后兼容）
	const shareData = props.shareData || {};

	return {
		title: shareData.title || defaultShareConfig.title,
		path: shareData.path || defaultShareConfig.path,
		imageUrl: shareData.imageUrl || defaultShareConfig.imageUrl,
		summary: shareData.summary || defaultShareConfig.summary,
	};
};

// 分享到微信好友
const handleShareToFriend = () => {
	if (props.useBuiltinShare) {
		const shareParams = getShareParams();

		// #ifdef H5
		// H5端使用浏览器原生分享API
		if (navigator.share) {
			navigator
				.share({
					title: shareParams.title,
					url: window.location.href,
				})
				.then(() => {
					toast("分享成功");
					close();
				})
				.catch(() => {
					toast("分享失败");
				});
		} else {
			toast("当前浏览器不支持原生分享");
		}
		// #endif

		// #ifdef APP
		// App端使用uni.share分享小程序卡片
		uni.share({
			provider: "weixin",
			scene: "WXSceneSession",
			type: 5, // 小程序类型
			imageUrl: shareParams.imageUrl,
			title: shareParams.title,
			miniProgram: {
				id: props.miniProgramId,
				path: shareParams.path,
				type: mpType,
				webUrl: props.domain,
			},
			success: () => {
				toast("分享成功");
				setTimeout(() => {
					close();
				}, 1000);
			},
			fail: (err) => {
				console.error("分享失败:", err);
				toast("分享失败");
			},
		});
		// #endif

		// #ifdef MP-WEIXIN
		// 小程序端通过open-type="share"触发，这里只需要关闭弹窗
		setTimeout(() => {
			close();
		}, 500);
		// #endif
	} else {
		// 使用外部自定义逻辑
		emit("shareToFriend");
	}
};

// 分享到朋友圈（仅支持分享微信小程序到微信聊天界面，想进入朋友圈需改为分享图片方式，在图片中包含小程序码。一般通过canvas绘制图片）
const handleShareToMoments = () => {
	if (props.useBuiltinShare) {
		const shareParams = getShareParams();
		// #ifdef H5
		// H5端使用浏览器原生分享API
		if (navigator.share) {
			const shareParams = getShareParams();
			navigator
				.share({
					title: shareParams.title,
					url: window.location.href,
				})
				.then(() => {
					toast("分享成功");
					close();
				})
				.catch(() => {
					toast("分享失败");
				});
		} else {
			toast("当前浏览器不支持原生分享");
		}
		// #endif

		// #ifdef APP
		// App端分享到朋友圈
		uni.share({
			provider: "weixin",
			scene: "WXSceneTimeline",
			type: 5, // 小程序类型
			imageUrl: shareParams.imageUrl,
			title: shareParams.title,
			miniProgram: {
				id: props.miniProgramId,
				path: shareParams.path,
				type: mpType,
				webUrl: props.domain,
			},
			success: () => {
				toast("分享成功");
				setTimeout(() => {
					close();
				}, 1000);
			},
			fail: (err) => {
				console.error("分享失败:", err);
				toast("分享失败");
			},
		});
		// #endif

		// #ifdef MP-WEIXIN
		// 小程序端提示用户手动分享到朋友圈
		uni.showModal({
			title: "操作提示",
			content: '请点击右上角"..."，选择"分享到朋友圈"',
			confirmText: "知道了",
			showCancel: false,
		});
		close();
		// #endif
	} else {
		// 使用外部自定义逻辑
		emit("shareToMoments");
	}
};

// 复制链接
const handleCopyLink = () => {
	if (props.useBuiltinShare) {
		// #ifdef H5
		const path = window.location.href;
		// #endif
		// #ifndef H5
		const shareParams = getShareParams();
		const path = props.domain + shareParams.path;
		// #endif

		uni.setClipboardData({
			data: path,
			success: () => {
				toast("复制成功");
				close();
			},
			fail: () => {
				toast("复制失败");
			},
		});
	} else {
		// 使用外部自定义逻辑
		emit("copyLink");
	}
};

// 举报功能
const handleReport = () => {
	emit("handleReport");
	close();
};

// 关闭分享弹窗
const closeShare = () => {
	emit("closeShare");
};

// 暴露方法给父组件
defineExpose({
	open,
	close,
});
</script>

<style lang="scss" scoped>
.item {
	border: none;
	font-size: 24rpx;
	color: #222222;
	margin: 0;
	line-height: 1.5;
}
</style>
