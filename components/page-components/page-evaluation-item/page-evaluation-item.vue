<script setup>
import dayjs from "dayjs";

import { route } from "@/common/utils";

import { useUserStore } from "@/store";
import { common } from "@/common/images";

const props = defineProps({
	item: {
		type: Object,
		default: () => ({}),
	},
	index: {
		type: Number,
		default: -1,
	},
	type: {
		type: String,
		default: "all", // all user
	},
	serveGoods: {
		type: [String, Number],
		default: 0,
	},
});

// 商品详情
function goGoodsDetails(goods) {
	route("/pages/goods/pages/productDetails/productDetails", {
		productId: goods.productId,
		shopId: goods.shopId,
	});
}

const emit = defineEmits(["likeEvaluation"]);

function more() {
	route("/pages/settings/pages/feedback/feedback");
}

function previewImage(urls) {
	uni.sh;
}

function likeEvaluation(item, index) {
	emit("likeEvaluation", { item, index });
}
</script>

<template>
	<view class="">
		<view class="p-30">
			<template v-if="type === 'all'">
				<view class="flex justify-between items-center">
					<view class="flex justify-start items-center">
						<image :src="item.avatar" class="w-80 h-80 border-rd-1/2 mr-20" mode=""></image>
						<view class="h-80 flex flex-col justify-around">
							<text class="text-26 text-#222 font-bold">{{ item.nickname }}</text>
							<text class="text-26 text-#888 font-500">{{ item.createTime }}</text>
						</view>
					</view>
					<!-- 更多操作，暂时无效 -->
					<!-- <app-image :src="common.iconMenuHui" class="w-32 h-32" size="32" @click="more" ml="15"></app-image> -->
				</view>
			</template>
			<template v-if="type === 'user'">
				<view class="flex justify-between items-center">
					<view class="flex items-end">
						<view class="text-36 font-bold">{{ dayjs(item.createTime).format("DD") }}</view>
						<view class="text-24 font-500 pb-6">/</view>
						<view class="text-24 font-500 pb-4">{{ dayjs(item.createTime).format("MM") }}月</view>
						<view class="text-24 font-500 pb-6">/</view>
						<view class="text-24 font-500 pb-4">{{ dayjs(item.createTime).format("YYYY") }}年</view>
					</view>

					<view class="flex items-center">
						<!-- <view class="text-#888 text-24 font-500">{{ item.specs.join('/') }}</view> -->
						<!-- <app-image :src="common.iconDelHui" size="30" mode="" ml="10"></app-image> -->
						<!-- <app-image :src="common.iconMenuHui" class="w-32 h-32" size="32" mode="" @click="more" ml="15"></app-image> -->
					</view>
				</view>
			</template>
			<template v-if="type === 'details'">
				<view class="flex justify-between items-center">
					<view class="flex items-end">
						<view class="text-36 font-bold">{{ dayjs(item.createTime).format("DD") }}</view>
						<view class="text-24 font-500 pb-6">/</view>
						<view class="text-24 font-500 pb-4">{{ dayjs(item.createTime).format("MM") }}月</view>
						<view class="text-24 font-500 pb-6">/</view>
						<view class="text-24 font-500 pb-4">{{ dayjs(item.createTime).format("YYYY") }}年</view>
					</view>

					<view class="flex items-center">
						<!-- <view class="text-#888 text-24 font-500">{{ item.specs.join('/') }}</view> -->
						<!-- <app-image :src="common.iconDelHui" size="30" mode="" ml="10"></app-image> -->
						<!-- <app-image :src="common.iconMenuHui" class="w-32 h-32" size="32" mode="" @click="more" ml="15"></app-image> -->
					</view>
				</view>
			</template>

			<template v-if="Number(serveGoods) === 0">
				<view class="flex justify-start items-center mt-20">
					<uv-rate :count="5" :value="item.rate" active-color="#F8BC35" inactive-color="#E2E2E2" inactiveIcon="star-fill" size="14" gutter="0" readonly></uv-rate>
					<template v-if="item.rate < 3">
						<text class="text-24 text-#FC3F33 font-bold ml-5">差评</text>
					</template>
					<template v-if="item.rate === 3">
						<text class="text-24 text font-bold ml-5">中评</text>
					</template>
					<template v-if="item.rate > 3">
						<text class="text-24 text-#FF950A font-bold ml-5">好评</text>
					</template>
					<!--  -->

					<view class="w-1 h-14 bg-#E9E9E9 mx-15"></view>

					<text class="text-24 text-#888 font-500">{{ item.specs.join(",") || "" }}</text>
				</view>
			</template>

			<template v-if="Number(serveGoods) === 1">
				<view class="w-full flex flex-col">
					<view class="flex justify-start items-center mt-20">
						<text class="text-24 text-#888 font-500">商家：</text>
						<view class="mx-4 flex items-center">
							<uv-rate :count="5" :value="item.rate" active-color="#F8BC35" inactive-color="#E2E2E2" inactiveIcon="star-fill" size="14" gutter="0" readonly></uv-rate>
							<template v-if="item.rate < 3">
								<text class="text-24 text-#FC3F33 font-bold ml-5">差评</text>
							</template>
							<template v-if="item.rate === 3">
								<text class="text-24 text font-bold ml-5">中评</text>
							</template>
							<template v-if="item.rate > 3">
								<text class="text-24 text-#FF950A font-bold ml-5">好评</text>
							</template>
						</view>
					</view>
					<view class="flex justify-start items-center mt-20">
						<text class="text-24 text-#888 font-500">师傅：</text>

						<template v-if="item.masterType === 'good'">
							<text class="text-24 text-#FF950A font-500 mx-4">满意</text>
							<template v-for="(ite, index) in item.masterWords || []">
								<view class="mx-4 px-8 h-36 flex flex-center bg-#FFF4EC border-rd-4 text-#FF7E00 text-24 font-500">{{ ite }}</view>
							</template>
						</template>
						<template v-if="item.masterType === 'bad'">
							<text class="text-24 text-#FC3F33 font-500 mx-4">不满意</text>

							<template v-for="(ite, index) in item.masterWords || []">
								<view class="mx-4 px-8 h-36 flex flex-center bg-#FFECEB border-rd-4 text-#FF7E00 text-24 font-500">{{ ite }}</view>
							</template>
						</template>
					</view>
				</view>
			</template>

			<view class="text-28 text-#323232 font-500 mt-20">{{ item.comment }}</view>
			<template v-if="item.medias.length > 0">
				<view class="mt-20">
					<!-- 评价暂时只上传图片 -->
					<app-image-grid :list="item.medias"></app-image-grid>
				</view>
			</template>

			<template v-if="item.shopReply">
				<view class="text-28 text-#222 font-500 px-27 py-24 bg-#F6F6F6 border-rd-16 mt-20" style="word-wrap: break-word">商家回复：{{ item.shopReply }}</view>
			</template>

			<template v-if="type === 'user'">
				<view class="p-20 bg-#F1F3F7 border-rd-16 mt-20 flex justify-start items-center" @click="goGoodsDetails(item)">
					<image :src="item.image" class="w-100 h-100" mode=""></image>
					<view class="ml-20 flex-1">
						<view class="text-28 text-#222 font-bold">{{ item.name }}</view>
						<view class="text-#888 text-24 font-500 mt-10">{{ item.specs.join("/") }}</view>
						<view class="flex justify-between items-center mt-10">
							<!-- 暂无商品价格，后续添加 -->
							<text class="text-30 text-#222 font-bold"></text>
							<!-- <text class="text-30 text-#222 font-bold">¥ 1888</text> -->
							<!-- <text class="text-28 text-#555 font-500">×1</text> -->
						</view>
					</view>
				</view>
			</template>

			<!-- 暂无点赞，后续添加 -->
			<template v-if="type === 'all' && useUserStore().checkLogin && false">
				<view class="flex justify-end items-center mt-20" @click="likeEvaluation(item, index)">
					<template v-if="item.userEvaluateStatus">
						<image :src="common.iconDianzanHong" class="w-30 h-30" mode=""></image>
					</template>
					<template v-else>
						<image :src="common.iconDianzanHei" class="w-30 h-30" mode=""></image>
					</template>
					<text class="text-26 text-#323232 font-500 ml-8">{{ item.likeNum }}人觉得有用</text>
				</view>
			</template>
		</view>
		<view class="w-full h-14 bg-#F1F3F7"></view>
	</view>
</template>

<style lang="scss" scoped></style>
