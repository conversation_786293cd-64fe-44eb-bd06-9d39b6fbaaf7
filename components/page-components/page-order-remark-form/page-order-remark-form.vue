<script setup>
import { ref, reactive, computed, watch, nextTick } from "vue";

import { EXPRESS_CODE, INTRA_CITY_DISTRIBUTION_CODE_1, INTRA_CITY_DISTRIBUTION_CODE_2, DISTRIBUTION, serviceHandler, ActivityType, DiscountType } from "@/common/types/goods";

import useConvert from "@/common/useConvert";

import { Decimal } from "decimal.js";

import { getOrderSettingsDealBatch, upload } from "@/server/api";

import { REGEX } from "@/common/test";
import { common } from "@/common/images";

const { divTenThousand } = useConvert();

const $props = defineProps({
	item: {
		type: Object,
		default: () => ({}),
	},
	index: {
		type: Number,
		default: -1,
	},
	shopRemarkSetting: {
		type: Array,
		default: () => [],
	},
});

const $data = reactive({
	formData: {},
	formList: [],
	formRules: {},
});

const remarkForm = ref();

function loadDFormInfo() {
	$data.formData = {};
	$data.formList = [];
	$data.formRules = {};

	$props.shopRemarkSetting.map((i, index) => {
		const { type, key, required, placeholder } = i;
		const formKey = `${type}_${index}`;
		const formItem = {
			...i,
			formKey,
		};

		const formItemRules = [];

		if (required) {
			formItemRules.push({
				required: true,
				message: placeholder || `${key}为必填项`,
				// blur和change事件触发检验
				trigger: ["blur"],
			});
		}

		// 文本
		if (type === "TEXT") {
			// formItemRules.push({
			// 	pattern: REGEX.TEXT,
			// 	message: `${key}不正确`,
			// 	trigger: ['blur']
			// });
		}

		// 数字
		if (type === "NUMBER") {
			// formItemRules.push({
			// 	pattern: REGEX.NUMBERS,
			// 	message: `${key}不正确`,
			// 	trigger: ['blur']
			// });
		}

		// 电话号码
		if (type === "MOBILE") {
			formItemRules.push({
				pattern: REGEX.MOBILE,
				message: `${key}不正确`,
				trigger: ["blur"],
			});
		}

		// 身份证
		if (type === "CITIZEN_ID") {
			formItemRules.push({
				pattern: REGEX.CITIZEN_ID,
				message: `${key}不正确`,
				trigger: ["blur"],
			});
		}

		// 日期
		if (type === "DATE") {
			formItemRules.push({
				pattern: REGEX.DATE,
				message: `${key}不正确`,
				trigger: ["blur"],
			});
		}

		// 时间
		// if (type === 'TIME') {
		// 	formItemRules.push({
		// 		pattern: REGEX.TIME,
		// 		message: `${key}不正确`,
		// 		trigger: ['blur']
		// 	});
		// }

		// 日期时间
		// if (type === 'DATETIME') {
		// 	formItemRules.push({
		// 		pattern: REGEX.DATETIME,
		// 		message: `${key}不正确`,
		// 		trigger: ['blur']
		// 	});
		// }

		// 图片
		if (type === "IMAGE") {
			// formItemRules.push({
			// 	pattern: REGEX.HTTP_URL,
			// 	message: `${key}不正确`,
			// 	trigger: ['blur']
			// });
		}

		$data.formData[formKey] = "";
		$data.formRules[formKey] = formItemRules;
		$data.formList.push(formItem);
	});

	nextTick(() => {
		if (remarkForm.value) remarkForm.value.setRules($data.formRules);
	});
}

function uploadImg(key) {
	// #ifndef H5
	uni.chooseMedia({
		count: 1,
		mediaType: ['image'],
		success(res) {
			const tempFilePaths = res.tempFiles.map((i) => i.tempFilePath);

			uni.showLoading({
				title: "加载中...",
			});
			upload(tempFilePaths[0], {
				formData: tempFilePaths,
			})
				.then((res) => {
					uni.hideLoading();
					if (res.apiStatus) {
						$data.formData[key] = res.data;
					}
				})
				.catch((err) => {
					uni.hideLoading();
				});
		},
	});
	// #endif
	// #ifdef H5
	uni.chooseImage({
		count: 1,
		success(res) {
			const tempFilePaths = res.tempFilePaths;

			uni.showLoading({
				title: "加载中...",
			});
			upload(tempFilePaths[0], {
				formData: tempFilePaths,
			})
				.then((res) => {
					uni.hideLoading();
					if (res.apiStatus) {
						$data.formData[key] = res.data;
					}
				})
				.catch((err) => {
					uni.hideLoading();
				});
		},
	});
	// #endif
}

function validateForm() {
	if (remarkForm.value) remarkForm.value.validate();
}

watch(
	() => $props.shopRemarkSetting,
	(val) => {
		if (val) {
			// console.log(val);
			// console.log($props.shopRemarkSetting);

			loadDFormInfo();
		}
	},
	{
		immediate: true,
	}
);

defineExpose({ remarkForm, $data });
</script>

<template>
	<view class="w-full">
		<uv-form labelPosition="left" :model="$data.formData" :rules="$data.formRules" ref="remarkForm" errorType="toast" labelWidth="130rpx" :labelStyle="{ fontSize: '28rpx', fontWeight: 'bold' }">
			<template v-for="(item, index) in $data.formList" :key="index">
				<template v-if="['TEXT', 'MOBILE', 'CITIZEN_ID', 'NUMBER'].includes(item.type)">
					<uv-form-item :label="item.key" :prop="item.formKey" :required="item.required" :borderBottom="index !== $data.formList.length - 1 || true">
						<uv-input
							v-model="$data.formData[item.formKey]"
							:placeholder="item.placeholder"
							:type="
								{
									TEXT: 'text',
									MOBILE: 'text',
									CITIZEN_ID: 'idcard',
									NUMBER: 'digit',
								}[item.type]
							"
							inputAlign="right"
							border="none"></uv-input>
					</uv-form-item>
				</template>

				<template v-if="['DATE', 'TIME', 'DATETIME'].includes(item.type)">
					<uv-form-item :label="item.key" :prop="item.formKey" :required="item.required" :borderBottom="index !== $data.formList.length - 1 || true">
						<page-form-uv-datetime-picker
							ref="datetimePicker"
							v-model="$data.formData[item.formKey]"
							:mode="
								{
									DATE: 'date',
									TIME: 'time',
									DATETIME: 'datetime',
								}[item.type]
							">
							<uv-input v-model="$data.formData[item.formKey]" :placeholder="item.placeholder" type="text" inputAlign="right" border="none" :readonly="true"></uv-input>
						</page-form-uv-datetime-picker>

						<template #right>
							<view>
								<app-image :src="common.iconRightHui" size="18" mode=""></app-image>
							</view>
						</template>
					</uv-form-item>
				</template>

				<template v-if="item.type === 'IMAGE'">
					<uv-form-item :label="item.key" :prop="item.formKey" :required="item.required" :borderBottom="index !== $data.formList.length - 1 || true">
						<view class="w-full h-100 flex items-center justify-end px-0" @click="uploadImg(item.formKey)">
							<template v-if="$data.formData[item.formKey]">
								<app-image v-if="$data.formData[item.formKey]" :src="$data.formData[item.formKey]" mode="widthFix" width="100" mr="30"></app-image>
							</template>
							<template v-else>
								<view class="text-[rgb(192,196,204)] text-26 px-10">{{ item.placeholder }}</view>
							</template>
						</view>
						<template #right>
							<template v-if="$data.formData[item.formKey]">
								<view>
									<uv-icon name="trash" color="#FC3F33" size="40rpx" @click="$data.formData[item.formKey] = ''"></uv-icon>
								</view>
							</template>
						</template>
					</uv-form-item>
				</template>
			</template>
		</uv-form>
	</view>
</template>

<style lang="scss" scoped></style>
