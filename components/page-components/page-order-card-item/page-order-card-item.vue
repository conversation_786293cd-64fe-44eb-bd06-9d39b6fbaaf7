<script setup>
import { ref, reactive, computed, watch } from 'vue';

import { EXPRESS_CODE, INTRA_CITY_DISTRIBUTION_CODE_1, INTRA_CITY_DISTRIBUTION_CODE_2, DISTRIBUTION, serviceHandler, ActivityType, DiscountType } from '@/common/types/goods';

import useConvert from '@/common/useConvert';

import { canENV, getPriceInfo } from '@/common/utils';

import { Decimal } from 'decimal.js';

import { getOrderSettingsDealBatch } from "@/server/api";
import { common } from "@/common/images";

const { divTenThousand } = useConvert();

const $props = defineProps({
	item: {
		type: Object,
		default: () => ({})
	},
	index: {
		type: Number,
		default: -1
	},
	fullDiscount: {
		type: [String, Number],
		default: null
	},
	freight: {
		type: [String, Number],
		default: 0
	},
	coupon: {
		type: Object,
		default() {
			return null;
		}
	},
	goodsType: {
		type: String,
		default: 'DEFAULT'
	}
});

const $emit = defineEmits(['changeGoodsNum', 'chooseShopCoupon']);

const $data = reactive({
	shopRemarkSetting: [],
	full: null,
	freight: null
});

const shopRemarkForm = ref();

/**
 * 服务承诺
 */
function getConfigService(serviceIds = []) {
	if (!serviceIds.length) return [];
	const services = [];
	serviceIds.forEach((item) => {
		const currentHandler = serviceHandler[item];
		services.push(currentHandler.name);
	});
	return services;
}

function getGoodsSkuSale(goods) {
	if (!goods) return 0;
	const salesVolume = goods.salesVolume;
	const initSalesVolume = goods.initSalesVolume;
	const totalSale = new Decimal(salesVolume || 0).add(initSalesVolume || 0);
	return totalSale.gte(10000) ? totalSale.div(10000).toFixed(2) + 'w' : totalSale.toString();
}

const handleParams = (currentSpecs) => {
	if (Array.isArray(currentSpecs)) {
		const allfeatureValues = currentSpecs.flatMap((item) => item.featureValues) || [];
		return allfeatureValues.map((item) => {
			const propertyPrice = divTenThousand(item.secondValue);
			// return `${item.firstValue}${propertyPrice.lte(0) ? '' : '+'}${propertyPrice}元`
			return `${item.firstValue}`;
		});
	}
	return [];
};

function getGoodsAllSpecs(goods) {
	if (!goods) return [];
	const { specs = [], productFeaturesValue = [] } = goods;
	const allSpecs = [...specs, ...handleParams(productFeaturesValue)];

	return allSpecs;
}

function changeNumber(e, goodsIndex) {
	$emit('changeGoodsNum', {
		value: e.value,
		goodsIndex
	});
}

function loadShopDealSetting() {
	getOrderSettingsDealBatch({ shopIds: $props.item.shopId }).then((res) => {
		if (res.apiStatus) {
			const shopRemarkSetting = res.data[$props.item.shopId];
			if (Array.isArray(shopRemarkSetting) && shopRemarkSetting.length > 0) {
				$data.shopRemarkSetting = shopRemarkSetting;
			}
		}
	});
}

function convertPrice(price) {
	if (price === null || price === 0 || price === '0') {
		return null;
	}
	return price;
}

const formattingCouponRules = (cou, autocomplete = true) => {
	let text;
	if (!cou || !Object.values(cou).length) {
		text = '';
		return text;
	}
	switch (cou.type) {
		case 'PRICE_REDUCE':
			text = autocomplete ? `现金券减${cou.amount && divTenThousand(cou.amount)}元` : `无门槛现金券`;
			break;
		case 'PRICE_DISCOUNT':
			text = autocomplete ? `无门槛折扣券${cou.discount}折` : `无门槛折扣券`;
			break;
		case 'REQUIRED_PRICE_REDUCE':
			text = autocomplete
				? `满${divTenThousand(cou.requiredAmount || '0')}元减${cou.amount && divTenThousand(cou.amount)}元`
				: `满${divTenThousand(cou.requiredAmount || '0')}元可用`;
			break;
		case 'REQUIRED_PRICE_DISCOUNT':
			text = autocomplete ? `满${divTenThousand(cou.requiredAmount || '0')}元${cou.discount}折` : `满${divTenThousand(cou.requiredAmount || '0')}元可用`;
			break;
		default:
			break;
	}
	return text;
};

watch(
	() => $props.item.shopId,
	(val) => {
		if (val) {
			// console.log(val);
			loadShopDealSetting();
		}
	},
	{
		immediate: true
	}
);

watch(
	() => $props.item,
	(val) => {
		if (val) {
			// console.log(val);
		}
	},
	{
		immediate: true
	}
);

watch(
	() => $props.fullDiscount,
	(fullDiscount) => {
		$data.full = convertPrice(fullDiscount);
	},
	{
		immediate: true
	}
);

watch(
	() => $props.freight,
	(freight) => {
		$data.freight = convertPrice(freight);
	},
	{
		immediate: true
	}
);

defineExpose({ item: $props.item, shopRemarkForm });
</script>

<template>
	<view class="w-full">
		<template v-if="item.serveGoods === 0">
			<template v-if="goodsType === 'DEFAULT'">
				<view class="w-full flex items-center">
					<app-image :src="item.logo" size="42" rd="6" mr="12" mode=""></app-image>
					<view class="text-32 font-bold">{{ item.name }}</view>
				</view>
			</template>
		</template>
		<template v-if="item.serveGoods === 1">
			<view class="w-full flex items-center justify-between">
				<view class="text-30 font-bold">服务信息</view>
				<view class="flex flex-center">
					<app-image :src="item.logo" size="32" rd="6" mr="12" mode=""></app-image>
					<view class="text-26 font-500 text-#00B496">{{ item.name }}</view>
				</view>
			</view>
		</template>

		<template v-if="goodsType === 'DEFAULT'">
			<view class="w-full h-1 bg-#EAEDF3 mt-30"></view>
		</template>

		<template v-for="(goods, index) in item.products" :key="index">
			<view class="w-full flex">
				<view class="flex-1 flex items-center mt-30">
					<view class="w-181 h-181 flex flex-center border-rd-10 border-solid border-1 border-#F5F5F5 flex-shrink-0">
						<app-image :src="goods.image" size="180" rd="10" mode=""></app-image>
					</view>
					<view class="flex-1 ml-40 flex flex-col">
						<view class="text-32 font-bold mt-0 uv-line-1">{{ goods.productName }}</view>

						<template v-if="goods.specs || goods.productFeaturesValue">
							<view class="text-26 text-#555555 mt-10 uv-line-1">{{ getGoodsAllSpecs(goods).join(" ") }}</view>
						</template>

						<template v-if="item.serveGoods === 0">
							<view class="flex items-center mt-10">
								<template v-if="goods.stockType !== 'UNLIMITED'">
									<view class="text-24 text-#4E515B">库存：{{ goods.stock }}</view>
								</template>
								<template v-if="goods.limitType !== 'UNLIMITED' && goodsType === 'DEFAULT'">
									<view class="text-24 text-#4E515B ml-15">限购：{{ goods.limitNum }}</view>
								</template>
							</view>
						</template>

						<view class="flex items-end mt-30">
							<view class="flex-1">
								<template v-if="goodsType === 'DEFAULT'">
									<view class="flex items-end">
										<!-- <view class="text-24 font-500 text-#FC3F33 pb-12">预估到手</view> -->
										<view class="text-30 font-500 text-#FC3F33 pb-2">¥</view>
										<view class="text-36 font-500 text-#FC3F33">{{ getPriceInfo(goods.salePrice).integer }}</view>
										<view class="text-30 font-500 text-#FC3F33 pb-2">.{{ getPriceInfo(goods.salePrice).decimalText }}</view>
									</view>
								</template>
								<template v-if="goodsType === 'INTEGRAL'">
									<view class="flex">
										<view class="flex items-end">
											<!-- <view class="text-24 font-500 text-#FC3F33 pb-12">预估到手</view> -->

											<view class="text-30 font-500 text-#FC3F33">{{ goods.integralPrice }}</view>
											<view class="text-26 font-500 text-#FC3F33 pb-2">积分</view>
										</view>
										<view class="flex items-end">
											<view class="text-28 font-500 text-#FC3F33 pb-2 mx-5">+</view>
											<view class="text-26 font-500 text-#FC3F33 pb-2">¥</view>
											<view class="text-30 font-500 text-#FC3F33">{{ getPriceInfo(goods.salePrice).integer }}</view>
											<view class="text-26 font-500 text-#FC3F33 pb-2">.{{ getPriceInfo(goods.salePrice).decimalText }}</view>
										</view>
									</view>
								</template>
							</view>
							<view class="flex-shrink-0">
								<template v-if="goodsType === 'DEFAULT'">
									<my-uv-number-box
										:value="goods.num"
										:min="1"
										:max="goods.goodsCountMax"
										bgColor="#F3F4F6"
										color="#111010"
										buttonSize="56rpx"
										@change="($event) => changeNumber($event, index)"
										integer
									></my-uv-number-box>
								</template>
								<template v-if="goodsType === 'INTEGRAL'">
									<!-- <my-uv-number-box
										:value="goods.num"
										:min="1"
										:max="goods.goodsCountMax"
										bgColor="#F3F4F6"
										color="#111010"
										buttonSize="40rpx"
										@change="($event) => changeNumber($event, index)"
										integer
									></my-uv-number-box> -->
								</template>
							</view>
						</view>

						<template v-if="goods.serviceIds && goods.serviceIds.length">
							<view class="w-full flex items-center justify-between mt-0">
								<view class="flex-1 w-0">
									<scroll-view scroll-x="true">
										<view class="flex items-center" style="flex-wrap: wrap">
											<template v-for="(i, ind) in getConfigService(goods.serviceIds)" :key="ind">
												<view class="px-10 h-26 border-1 border-solid border-#FF717B border-rd-6 flex flex-center mr-10 mt-10 flex-shrink-0">
													<view class="text-18 text-#F93946 min-w-max">{{ i }}</view>
												</view>
											</template>
										</view>
									</scroll-view>
								</view>

								<view class="ml-15"></view>
							</view>
						</template>
					</view>
				</view>
			</view>

			<template v-if="item.serveGoods === 0">
				<view class="w-full h-1 bg-#EAEDF3 mt-30"></view>
			</template>
			<template v-if="item.serveGoods === 1">
				<view class="w-full h-1 mt-30"></view>
			</template>
		</template>

		<view class="w-full">
			<page-order-remark-form ref="shopRemarkForm" :shopRemarkSetting="$data.shopRemarkSetting"></page-order-remark-form>
		</view>

		<view class="w-full">
			<template v-if="false">
				<view class="flex items-center mt-40">
					<view class="w-130 text-28 font-bold">优惠券</view>
					<view class="flex-1 flex items-center justify-end" @click="($event) => $emit('chooseShopCoupon')">
						<view class="flex items-center">
							<view class="text-26 text-#323232 flex items-center">
								<view class="">{{ formattingCouponRules($props.coupon) }}</view>
							</view>
							<view class="">
								<app-image :src="common.iconRightHui" size="18" mode=""></app-image>
							</view>
						</view>
					</view>
				</view>
			</template>

			<template v-if="item.serveGoods === 0">
				<template v-if="goodsType === 'DEFAULT'">
					<view class="flex items-center mt-40">
						<view class="w-130 text-28 font-bold">运费</view>
						<view class="flex-1 flex items-center justify-end">
							<view class="flex items-center">
								<view class="text-26 text-#323232 flex items-center">
									<template v-if="$data.freight">
										<view class="flex items-center">
											<view class="text-26 font-500 text-#FC3F33 pb-0">¥</view>
											<view class="text-26 font-500 text-#FC3F33">{{ getPriceInfo($data.freight).integer }}</view>
											<view class="text-26 font-500 text-#FC3F33 pb-0">.{{ getPriceInfo($data.freight).decimalText }}</view>
										</view>
									</template>
									<template v-else>
										<view class="">免运费</view>
									</template>
								</view>
							</view>
						</view>
					</view>
				</template>
				<template v-if="goodsType === 'INTEGRAL'">
					<view class="flex items-center mt-40">
						<view class="w-130 text-28 font-bold">运费</view>
						<view class="flex-1 flex items-center justify-end">
							<view class="flex items-center">
								<view class="text-26 text-#323232 flex items-center">
									<template v-if="$data.freight">
										<view class="flex items-center">
											<view class="text-26 font-500 text-#FC3F33 pb-0">¥</view>
											<view class="text-26 font-500 text-#FC3F33">{{ getPriceInfo($data.freight).integer }}</view>
											<view class="text-26 font-500 text-#FC3F33 pb-0">.{{ getPriceInfo($data.freight).decimalText }}</view>
										</view>
									</template>
									<template v-else>
										<view class="">免运费</view>
									</template>
								</view>
							</view>
						</view>
					</view>
				</template>
			</template>

			<template v-if="$data.full">
				<view class="flex items-center mt-40">
					<view class="w-130 text-28 font-bold">满减优惠</view>
					<view class="flex-1 flex items-center justify-end">
						<view class="flex items-center">
							<view class="text-26 text-#323232 flex items-center">
								<view class="flex items-center">
									<view class="text-26 font-500 text-#FC3F33 pb-0">¥</view>
									<view class="text-26 font-500 text-#FC3F33">{{ getPriceInfo($data.full).integer }}</view>
									<view class="text-26 font-500 text-#FC3F33 pb-0">.{{ getPriceInfo($data.full).decimalText }}</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</template>

			<!-- <view class="flex items-center mt-40">
				<view class="w-100 text-28 font-bold">留言</view>
				<view class="flex-1 flex items-center justify-end">
					<view class="flex items-center">
						<view class="text-26 text-#323232 flex items-center">
							<view class=""></view>
						</view>
					</view>
				</view>
			</view> -->
		</view>
	</view>
</template>

<style lang="scss" scoped></style>
