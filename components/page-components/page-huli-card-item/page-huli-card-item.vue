<script setup>
import { getPriceInfo, salesVolumeToStr } from '@/common/utils';

const props = defineProps({
	item: {
		type: Object,
		default: () => ({})
	},
	index: {
		type: Number,
		default: -1
	},
	type: {
		type: String,
		default: 'default'
	}
});

function toDetails(item) {
	if (props.type === 'integral') {
		uni.$uv.route('/pages/goods/pages/productDetailsIntegral/productDetailsIntegral', {
			productId: item.id
		});
	} else {
		uni.$uv.route('/pages/goods/pages/productDetails/productDetails', {
			productId: item.statistics.productId,
			shopId: item.shopId
		});
	}
}
</script>

<template>
	<view class="w-full">
		<view class="w-full border-rd-20 bg-#fff overflow-hidden pb-10" @click="toDetails(item)">
			<view class="w-full min-h-346 flex items-center justify-center">
				<!-- <image :src="item.pic" class="w-full" mode="widthFix"></image> -->
				<image :src="item.pic" mode="aspectFit" class="w-full h-346"></image>
			</view>
			<view class="p-20">
				<!-- <view class="font-bold text-28 text-#222 uv-line-2">
					{{ item.name }}
				</view> -->
				<view class="font-bold text-28 text-#222 uv-line-1">
					{{ item.name }}
				</view>

				<template v-if="type === 'integral'">
					<view class="mt-10 flex items-center justify-between">
						<view class="flex items-center text-#888888" style="text-decoration: line-through">
							<view class="flex items-end">
								<view class="text-#888888 text-26 font-500 pb-0">￥</view>
								<view class="text-#888888 text-26 font-500">
									{{ getPriceInfo(item.salePrice || 0).integer }}
								</view>
								<text class="text-#888888 text-26 font-500 pb-0">.{{ getPriceInfo(item.salePrice || 0).decimalText }}</text>
							</view>
						</view>

						<view class="text-#888888 text-22 font-500 flex-shrink-0">已售{{ salesVolumeToStr(item.salesVolume) }}</view>
					</view>

					<view class="mt-10 flex items-center justify-between">
						<view class="flex items-center">
							<view class="flex items-end">
								<view class="text-#FC3F33 text-28 font-500">
									{{ item.integralPrice }}
								</view>
								<view class="text-#FC3F33 text-28 font-500 pb-0">积分</view>
							</view>
							<template v-if="item.salePrice > 0">
								<view class="flex items-end">
									<view class="text-#FC3F33 text-28 font-500 pb-0">+</view>
									<view class="text-#FC3F33 text-28 font-500 pb-0">￥</view>
									<view class="text-#FC3F33 text-28 font-500">
										{{ getPriceInfo(item.salePrice || 0).integer }}
									</view>
									<text class="text-#FC3F33 text-24 font-500 pb-0">.{{ getPriceInfo(item.salePrice || 0).decimalText }}</text>
								</view>
							</template>
						</view>
					</view>
				</template>
				<template v-else>
					<view class="mt-20 flex items-center justify-between" v-if="item.statistics">
						<view class="flex items-end">
							<view class="text-#FC3F33 text-24 font-500 pb-4">￥</view>
							<view class="text-#FC3F33 text-36 font-500">
								{{ getPriceInfo(item.statistics.lowestPrice || 0).integer }}
							</view>
							<text class="text-#FC3F33 text-24 font-500 pb-4">.{{ getPriceInfo(item.statistics.lowestPrice || 0).decimalText }}</text>

							<view class="text-#FC3F33 text-24 font-500 pb-4">起</view>
						</view>

						<template v-if="item.statistics && item.statistics.salesVolume">
							<view class="text-#888888 text-22 font-500">已服务{{ salesVolumeToStr(item.statistics.salesVolume) }}</view>
						</template>
					</view>
				</template>

				<template v-if="item.shopName">
					<view class="mt-30 flex">
						<view class="border-rd-16 border-1 border-solid border-#EFF2F9 flex items-center justify-center">
							<app-image :src="item.logo" size="30" rd="50%" mode=""></app-image>
						</view>
						<view class="text-#777777 text-24 font-500 ml-10">
							{{ item.shopName }}
						</view>
					</view>
				</template>
			</view>
		</view>
	</view>
</template>

<style lang="scss" scoped></style>
