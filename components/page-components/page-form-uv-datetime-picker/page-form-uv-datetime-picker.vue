<script setup>
import uvDatetimePickerProps from './props';

import { timeFormat } from '@/uni_modules/uv-ui-tools/libs/function/index.js';

import { computed, onMounted, ref } from 'vue';

const props = defineProps({
	...uvDatetimePickerProps.props,
	clickAutoOpen: {
		type: Boolean,
		default: true
	}
});

const pickerProps = computed(() => {
	const newProps = {
		...props
	};
	if (!newProps.confirmColor) newProps.confirmColor = '#38B597';
	delete newProps.value;
	return newProps;
});

const emit = defineEmits(['confirm', 'change', 'cancel', 'close']);

const value = defineModel({
	type: String
});

// const dateTimeVal = computed(() => {
// 	console.log(value.value);
// 	let dateVal = Date.now();
// 	if (value.value) {
// 		dateVal = new Date(value.value).getTime();
// 	}
// 	console.log(dateVal);
// 	return dateVal;
// });

const pickerRef = ref();

function pickerConfirm(e) {
	let valText = '';
	if (e.mode === 'date') {
		valText = timeFormat(e.value, 'yyyy-mm-dd');
	}
	if (e.mode === 'time') {
		// valText = timeFormat(e.value, 'hh:MM:ss');
		valText = `${e.value}:00`;
	}
	if (e.mode === 'datetime') {
		valText = timeFormat(e.value, 'yyyy-mm-dd hh:MM:ss');
	}
	value.value = valText;
	emit('confirm', e);
}

function pickerChange(e) {
	emit('change', e);
}

function pickerCancel(e) {
	emit('cancel', e);
}

function pickerClose(e) {
	emit('close', e);
}

function open(e) {
	pickerRef.value.open(e);
}

function close(e) {
	pickerRef.value.open(e);
}

function setIndexs(index, setLastIndex) {
	pickerRef.value.open(index, setLastIndex);
}

function setColumnValues(columnIndex, values) {
	pickerRef.value.open(columnIndex, values);
}

function clickPickerCentent() {
	if (props.clickAutoOpen) {
		pickerRef.value.open();
	}
}

function formatter(type, value) {
	if (type === 'year') {
		return `${value}年`;
	}
	if (type === 'month') {
		return `${value}月`;
	}
	if (type === 'day') {
		return `${value}日`;
	}
	if (type === 'hour') {
		return `${value}时`;
	}
	if (type === 'minute') {
		return `${value}分`;
	}
	return value;
}

onMounted(() => {
	if (pickerRef.value) pickerRef.value.setFormatter(formatter);
});

defineExpose({
	open,
	close,
	setIndexs,
	setColumnValues
});
</script>

<template>
	<view class="w-full" @click="clickPickerCentent">
		<slot></slot>

		<uv-datetime-picker
			v-bind="pickerProps"
			:value="value"
			ref="pickerRef"
			@confirm="pickerConfirm"
			@change="pickerChange"
			@cancel="pickerCancel"
			@close="pickerClose"
		></uv-datetime-picker>
	</view>
</template>

<style scoped lang="scss">
.page-uv-picker {
}
</style>
