<script setup>
import { Decimal } from 'decimal.js';

import useConvert from '@/common/useConvert';

const { divTenThousand } = useConvert();

const props = defineProps({
	item: {
		type: Object,
		default: () => ({})
	},
	shopName: {
		type: String,
		default: ''
	},
	index: {
		type: Number,
		default: -1
	},
	showMask: {
		type: Boolean,
		default: false
	},
	// 有用状态
	notUsedStatus: {
		type: String,
		default: 'CLAIM'
	},
	// 没用状态
	usedStatus: {
		type: String,
		default: 'UNCLAIMED'
	},
	status: {
		type: String,
		default: 'UNCLAIMED'
	},
	// 是否购物车
	isCar: {
		type: Boolean,
		default: false
	},
	// 是否领券中心
	couponCenter: {
		type: Boolean,
		default: false
	},
	type: {
		type: Number,
		default: 1
	}
});

const emit = defineEmits(['receiveClick']);

const couponIndexType = {
	PRICE_DISCOUNT: '无门槛折扣券',
	PRICE_REDUCE: '无门槛现金券',
	REQUIRED_PRICE_DISCOUNT: '满折券',
	REQUIRED_PRICE_REDUCE: '满减券'
};

const ProductType = {
	ALL: 'ALL',
	SHOP_ALL: 'SHOP_ALL',
	ASSIGNED: 'ASSIGNED',
	ASSIGNED_NOT: 'ASSIGNED_NOT'
};

const formattingName = (item) => {
	if (item.productType === ProductType.ALL || item.shopId === 0) {
		return '全场商品通用';
	}
	return `${props.shopName || item.shopName}可用`;
};

const productTypeCn = (params) => {
	if (params.productType === 'ALL') {
		return '全场商品通用';
	}
	const chinesObj = {
		ALL: '全场商品通用',
		SHOP_ALL: params.shopId === 0 ? '全场商品通用' : '全店商品可用',
		ASSIGNED: '部分商品可用',
		ASSIGNED_NOT: '部分商品可用'
	};
	return chinesObj[params.productType];
};

/**
 * 是否折扣
 * true 折扣 false 满减
 * @param {String} status
 * @returns {*} boolean
 */
const isDiscountFn = (status) => {
	return ['PRICE_DISCOUNT', 'REQUIRED_PRICE_DISCOUNT'].includes(status);
};

const claimedStatus = {
	/**
	 * 可以领取
	 */
	CLAIM: {
		isUseCoupon: true,
		text: '立即领取'
	},
	/**
	 * 领取达上限
	 */
	LIMIT: {
		isUseCoupon: false,
		text: '领取达上限'
	},
	/**
	 * 已领完
	 */
	FINISHED: {
		isUseCoupon: false,
		text: '已领完'
	}
};

const couponQueryStatusCn = {
	UNUSED: '去使用',
	UNCLAIMED: '立即领取',
	USED: '已使用',
	EXPIRED: '已过期'
};

/**
 * 查询店铺优惠券
 */
const queryStoreCoupon = (shopProducts) => {
	const map = new Map();
	shopProducts.products.forEach((item) => {
		const currentPrice = map.get(item.productId);
		const currentTotalPrice = new Decimal(item.salePrice).mul(new Decimal(item.num));
		map.set(item.productId, currentPrice ? currentPrice.add(currentTotalPrice) : currentTotalPrice);
	});
	let arr = [];
	for (const iterator of map) {
		arr.push({ productId: iterator[0], amount: iterator[1].toString() });
	}
	return arr;
};

/**
 * 优惠券有效期格式化
 */
function formattingTime(item) {
	if (item.effectType === 'IMMEDIATELY' && item.days) {
		return item.days <= 1 ? '领券当天内可用' : `领券当日起${item.days}天内可用`;
	}
	return `${formatTime(item.startDate)}-${formatTime(item.endDate)}`;
}
function formatTime(time) {
	return time ? time.replace(/[-]/g, '.') : '';
}

/**
 * 无门槛格式化
 */
function formattingPrice(item) {
	const isD = isDiscountFn(item.type);
	if (!isD) {
		return divTenThousand(item.amount).toString();
	}
	return item.discount;
}

/**
 *  优惠券规则格式化
 */
const formattingCouponRules = (cou, autocomplete = true) => {
	let text;
	if (!cou || !Object.values(cou).length) {
		text = '';
		return text;
	}
	switch (cou.type) {
		case 'PRICE_REDUCE':
			text = autocomplete ? `无门槛现金券减${cou.amount && divTenThousand(cou.amount)}元` : `无门槛现金券`;
			break;
		case 'PRICE_DISCOUNT':
			text = autocomplete ? `无门槛折扣券${cou.discount}折` : `无门槛折扣券`;
			break;
		case 'REQUIRED_PRICE_REDUCE':
			text = autocomplete
				? `满${divTenThousand(cou.requiredAmount || '0')}元减${cou.amount && divTenThousand(cou.amount)}元`
				: `满${divTenThousand(cou.requiredAmount || '0')}元可用`;
			break;
		case 'REQUIRED_PRICE_DISCOUNT':
			text = autocomplete ? `满${divTenThousand(cou.requiredAmount || '0')}元${cou.discount}折` : `满${divTenThousand(cou.requiredAmount || '0')}元可用`;
			break;
		default:
			break;
	}
	return text;
};

function receiveClick(type) {
	emit('receiveClick', type);
}
</script>

<template>
	<view class="w-full">
		<view class="w-full border-rd-20" :class="item.status === 'NOUSE' ? 'bg-F8F8F8' : 'bg-FFF1F1'">
			<view class="w-full">
				<view class="w-full flex items-center">
					<view class="min-w-200 h-160 px-20 border-0 border-right-1 border-dashed flex flex-col flex-center" :class="item.status === 'NOUSE' ? 'border-C5C5C5' : 'border-F93946'">
						<view class="flex items-end font-bold" :class="item.status === 'NOUSE' ? 'color-999999' : 'color-FC3F33'">
							<template v-if="!isDiscountFn(item.type)">
								<view class="text-30 pb-10">￥</view>
							</template>

							<view class="text-66">{{ formattingPrice(item) }}</view>

							<template v-if="isDiscountFn(item.type)">
								<view class="text-30 pb-10">折</view>
							</template>
						</view>
						<view class="text-24" :class="item.status === 'NOUSE' ? 'color-999999' : 'color-FC3F33'">{{ formattingCouponRules(item, false) }}</view>
					</view>
					<view class="flex-1 ml-20 h-160">
						<view class="w-full h-full flex flex-col justify-center">
							<view class="text-30 font-bold uv-line-1" :class="item.status === 'NOUSE' ? 'color-888888' : 'color-222222'">{{ formattingName(item) }}</view>
							<view class="w-full mt-10">
								<view class="flex-1 text-22 font-500 uv-line-1" :class="item.status === 'NOUSE' ? 'color-888888' : 'color-808592'">{{ formattingTime(item) }}</view>
							</view>
							<view class="w-full mt-8">
								<view class="flex-1 text-22 font-500 uv-line-1" :class="item.status === 'NOUSE' ? 'color-888888' : 'color-808592'">{{ productTypeCn(item) }}</view>
							</view>
						</view>
					</view>

					<!-- 领取 -->
					<template v-if="type === 1">
						<view class="flex-shrink-0 px-18">
							<template v-if="showMask">
								<uv-button
									color="#BCB9B9"
									text="已领取"
									:customStyle="{
										width: '136rpx',
										height: '48rpx',
										borderRadius: '8rpx'
									}"
									:customTextStyle="{
										fontWeight: 'bold',
										fontSize: '24rpx'
									}"
									:disabled="true"
								></uv-button>
							</template>
							<!-- 领券中心 -->
							<!-- <template v-else-if="couponCenter">
								<template v-if="claimedStatus[notUsedStatus].isUseCoupon">
									<uv-button
										color="linear-gradient(-90deg, #F84A2C 1%, #EC5746 100%)"
										:text="claimedStatus[notUsedStatus].text"
										:customStyle="{
											width: '136rpx',
											height: '48rpx',
											borderRadius: '8rpx'
										}"
										:customTextStyle="{
											fontWeight: 'bold',
											fontSize: '24rpx'
										}"
									></uv-button>
								</template>
								<template v-if="!claimedStatus[notUsedStatus].isUseCoupon">
									<uv-button
										color="#BCB9B9"
										:text="claimedStatus[notUsedStatus].text"
										:customStyle="{
											width: '136rpx',
											height: '48rpx',
											borderRadius: '8rpx'
										}"
										:customTextStyle="{
											fontWeight: 'bold',
											fontSize: '24rpx'
										}"
										:disabled="true"
									></uv-button>
								</template>
							</template> -->
							<!-- 购物车 -->
							<!-- <template v-else-if="isCar">
								<template v-if="claimedStatus[notUsedStatus].isUseCoupon">
									<uv-button
										color="linear-gradient(-90deg, #F84A2C 1%, #EC5746 100%)"
										:text="couponQueryStatusCn[usedStatus]"
										:customStyle="{
											width: '136rpx',
											height: '48rpx',
											borderRadius: '8rpx'
										}"
										:customTextStyle="{
											fontWeight: 'bold',
											fontSize: '24rpx'
										}"
									></uv-button>
								</template>
								<template v-if="!claimedStatus[notUsedStatus].isUseCoupon">
									<uv-button
										color="#BCB9B9"
										:text="couponQueryStatusCn[$props.usedStatus]"
										:customStyle="{
											width: '136rpx',
											height: '48rpx',
											borderRadius: '8rpx'
										}"
										:customTextStyle="{
											fontWeight: 'bold',
											fontSize: '24rpx'
										}"
										:disabled="true"
									></uv-button>
								</template>
							</template> -->
							<template v-else>
								<uv-button
									@click="receiveClick(claimedStatus[notUsedStatus])"
									color="linear-gradient(-90deg, #F84A2C 1%, #EC5746 100%)"
									:text="claimedStatus[notUsedStatus].text"
									:customStyle="{
										width: '136rpx',
										height: '48rpx',
										borderRadius: '8rpx'
									}"
									:customTextStyle="{
										fontWeight: 'bold',
										fontSize: '24rpx'
									}"
								></uv-button>
							</template>
						</view>
					</template>

					<!-- 使用 -->
					<template v-if="type === 2">
						<view class="flex-shrink-0 px-18">
							<template v-if="false">
								<uv-button
									color="linear-gradient(-90deg, #F84A2C 1%, #EC5746 100%)"
									text="使用"
									:customStyle="{
										width: '94rpx',
										height: '48rpx',
										borderRadius: '8rpx'
									}"
									:customTextStyle="{
										fontWeight: 'bold',
										fontSize: '24rpx'
									}"
								></uv-button>
							</template>
							<template v-if="true">
								<uv-button
									color="#BCB9B9"
									text="使用"
									:customStyle="{
										width: '94rpx',
										height: '48rpx',
										borderRadius: '8rpx'
									}"
									:customTextStyle="{
										fontWeight: 'bold',
										fontSize: '24rpx'
									}"
									:disabled="true"
								></uv-button>
							</template>
						</view>
					</template>

					<!-- 我的优惠券 -->
					<template v-if="type === 3"></template>
				</view>
			</view>
		</view>
	</view>
</template>

<style lang="scss" scoped>
.bg-F8F8F8 {
	background: #f8f8f8;
}
.bg-FFF1F1 {
	background: #fff1f1;
}
.color-FC3F33 {
	color: #fc3f33;
}
.color-222222 {
	color: #222222;
}
.color-808592 {
	color: #808592;
}
.color-999999 {
	color: #999999;
}
.color-888888 {
	color: #888888;
}
.border-F93946 {
	border-color: #f93946;
}
.border-C5C5C5 {
	border-color: #c5c5c5;
}
</style>
