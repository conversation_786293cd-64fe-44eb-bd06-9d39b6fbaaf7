<script setup>
import { onMounted, ref, watch, computed, nextTick } from 'vue';

import { canENV } from '@/common/utils';

import { consumerCollectCoupon, getOrderShopCouponPage } from '@/server/api';

import { useGoodsStore } from '@/store';

import useConvert from '@/common/useConvert';

const { divTenThousand } = useConvert();

const props = defineProps({
	title: {
		type: String,
		default: '选择优惠券'
	},

	shopId: {
		type: [String, Number],
		default: '0'
	},
	productAmounts: {
		type: Array,
		default: () => []
	},
	agencyId: {
		type: [String, Number],
		default: ''
	},
	images: {
		type: Array,
		default: () => []
	}
});

const emit = defineEmits(['confirm']);

const selectBtnPopup = ref();

const pageFormSend = ref(true);
const pageLoading = ref(false);

const groupValue = ref('reductionx');
const couponList = ref([]);
const animationData = ref({});
const imagesArr = computed(() => {
	if (groupValue.value === 'reductionx') {
		return props.images;
	}
	const coupon = couponList.value.find((item) => item.couponUserId === groupValue.value);
	if (!coupon) {
		return props.images;
	}
	return couponHandler[coupon.productType](props.images, coupon.productIds);
});

async function initCouponList() {
	// couponList.value = unique(couponList.value.concat(await getCouponList()));
	couponList.value = await getCouponList();
}

async function getCouponList() {
	pageLoading.value = true;

	const apiHeader = {};

	if (props.agencyId) {
		apiHeader['agency-id'] = props.agencyId;
	}

	try {
		const { code, data, apiStatus } = await getOrderShopCouponPage(
			{
				size: 999,
				current: 1,
				productAmounts: props.productAmounts,
				shopId: props.shopId
			},
			{
				header: apiHeader
			}
		);

		pageLoading.value = false;

		if (!apiStatus) {
			return [];
		}

		return data.records;
	} catch (error) {
		//TODO handle the exception
		pageLoading.value = false;
		return [];
	}
}

/**
 * 数组去重
 * @param {*} arr
 */
function unique(arr) {
	const res = new Map();
	return arr.filter((a) => !res.has(a.couponUserId) && res.set(a.couponUserId, 1));
}

function openPopup() {
	selectBtnPopup.value.open();
	nextTick(() => {
		initCouponList();
	});
}

function closePopup() {
	selectBtnPopup.value.close();
}

function submit() {
	const coupon = groupValue.value === 'reductionx' ? null : couponList.value.find((item) => item.couponUserId === groupValue.value);
	emit('confirm', coupon);
	closePopup();
}

function clickPopupCentent() {}

function receiveClick(coupon) {
	consumerCollectCoupon(
		{
			shopId: coupon.shopId,
			couponId: coupon.id
		},
		{
			custom: {
				loading: {
					awaitTime: 500
				}
			}
		}
	).then((res) => {
		if (res.apiStatus) {
			useGoodsStore().refreshPage();
		}
	});
}

const formattingCouponRules = (cou, autocomplete = true) => {
	let text;
	if (!cou || !Object.values(cou).length) {
		text = '';
		return text;
	}
	switch (cou.type) {
		case 'PRICE_REDUCE':
			text = autocomplete ? `现金券减${cou.amount && divTenThousand(cou.amount)}元` : `无门槛现金券`;
			break;
		case 'PRICE_DISCOUNT':
			text = autocomplete ? `无门槛折扣券${cou.discount}折` : `无门槛折扣券`;
			break;
		case 'REQUIRED_PRICE_REDUCE':
			text = autocomplete
				? `满${divTenThousand(cou.requiredAmount || '0')}元减${cou.amount && divTenThousand(cou.amount)}元`
				: `满${divTenThousand(cou.requiredAmount || '0')}元可用`;
			break;
		case 'REQUIRED_PRICE_DISCOUNT':
			text = autocomplete ? `满${divTenThousand(cou.requiredAmount || '0')}元${cou.discount}折` : `满${divTenThousand(cou.requiredAmount || '0')}元可用`;
			break;
		default:
			break;
	}
	return text;
};

onMounted(() => {});

defineExpose({
	openPopup,
	closePopup
});
</script>

<template>
	<view class="page-select-btn w-full" @touchmove.stop.prevent>
		<view class="w-full" @click="clickPopupCentent">
			<slot></slot>
		</view>
		<my-uv-popup mode="bottom" ref="selectBtnPopup" bgColor="#fff" round="20rpx" class="" closeable close-on-click-overlay>
			<view class="w-full bg-#fff border-top-left-rd-20">
				<template v-if="title">
					<view class="w-full py-40 text-center text-34 font-bold">
						{{ title }}
					</view>
				</template>
				<view class="px-26 pb-50">
					<view class="w-full">
						<template v-if="couponList.length > 0 || true">
							<view class="w-full">
								<scroll-view scroll-y="true" class="w-full h-500">
									<uv-radio-group v-model="groupValue" placement="column" iconPlacement="right" activeColor="#00B496">
										<view class="w-full mb-30">
											<uv-radio name="reductionx">
												<view class="w-full flex items-center justify-between pr-30">
													<view class="text-28 font-bold">不使用优惠券</view>
													<view class="text-26 text-#FC3F33 font-bold"></view>
												</view>
											</uv-radio>
										</view>
										<template v-for="(item, index) in couponList" :key="index">
											<view class="w-full mb-30">
												<uv-radio :name="item.couponUserId">
													<view class="w-full flex items-center justify-between pr-30">
														<view class="text-28 font-bold">{{ formattingCouponRules(item) }}</view>
														<view class="text-26 text-#FC3F33 font-bold">减 ￥{{ item.discountAmount && divTenThousand(item.discountAmount) }}</view>
													</view>
												</uv-radio>
											</view>
										</template>
									</uv-radio-group>
								</scroll-view>
							</view>
						</template>
						<template v-else>
							<view class="w-full">
								<uv-empty mode="coupon"></uv-empty>
							</view>
						</template>
					</view>

					<view class="mt-60">
						<uv-button
							color="#00B496"
							text="确认"
							loadingText="设置中..."
							class="w-full mt-0 flex-center"
							custom-style="height: 90rpx;"
							customTextStyle="font-size: 30rpx; font-weight: bold;"
							shape="circle"
							loadingMode="circle"
							:loading="pageLoading"
							:disabled="!pageFormSend"
							@click="submit"
						></uv-button>
					</view>
				</view>
			</view>
		</my-uv-popup>
	</view>
</template>

<style scoped lang="scss">
.page-select-btn {
	.select-btn-box {
		width: 216rpx;
		height: 80rpx;
		border-radius: 30rpx;
		font-weight: 500;
		font-size: 28rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		margin: 0 8rpx 24rpx 8rpx;
		background: #f7f7f7;
		color: #1a1a1b;

		.select-icon {
			display: none;
			position: absolute;
			top: 0;
			right: 0;
		}

		&.active {
			background: #eee8ff;
			color: #5933e9;

			.select-icon {
				display: block;
			}
		}
	}
}
</style>
