<script setup>
import { ref, nextTick, reactive, computed, onMounted } from 'vue';

import { onLoad } from '@dcloudio/uni-app';

import { getUploadSrc, openVideo } from '@/common/utils';

const videoContext = ref();

const props = defineProps({
	height: {
		type: String,
		default: '750rpx'
	},
	videos: {
		type: String,
		default: ''
	},
	images: {
		type: String,
		default: ''
	}
});
const data = reactive({
	isplay: false,
	timer: 0,
	current: 0
});

function swiperChange(e) {
	data.current = e.detail.current;
	if (props.videos) {
		if (data.current === 0) {
			if (videoContext) videoContext.value.pause();
		}
	}
}

function openVideoPlayer() {
	// route(`/pages/common/pages/videoPlayer/videoPlayer?video=${props.videos}&timer=${data.timer}`);
	openVideo(props.videos);
}

function pvewImage(index) {
	uni.previewImage({
		current: index,
		indicator: 'default',
		loop: true,
		urls: props.images.split(',').map((elem) => getUploadSrc(elem, '', false))
	});
}

const gridCount = computed(() => {
	let videos = props.videos.split(',').filter(function (item) {
		return item !== '';
	});
	let images = props.images.split(',').filter(function (item) {
		return item !== '';
	});
	return videos.length + images.length;
});

onMounted(async () => {
	await nextTick();
	videoContext.value = uni.createVideoContext('swiperVideo');
});
</script>
<template>
	<view class="page-swiper relative">
		<swiper class="swiper" :style="[`height: ${height}`]" circular @change="swiperChange">
			<swiper-item v-if="props.videos != ''">
				<view class="swiper-item h-full uni-bg-red relative bg-#fff">
					<app-image class="w-full h-full" :src="getUploadSrc(props.videos, 'videoCover')" mode="aspectFit"></app-image>
					<!-- <video
						:src="props.videos"
						:poster="`${props.videos}?x-oss-process=video/snapshot,t_1000,f_jpg,ar_auto`"
						class="w-full h-full"
						play-btn-position="center"
						:controls="true"
						:page-gesture="false"
						:show-progress="false"
						:enable-progress-gesture="false"
						:enable-play-gesture="true"
						id="swiperVideo"
					></video> -->
					<view class="w-full h-full flex justify-center items-center absolute left-0 top-0">
						<app-image class="w-100 h-100" src="@/pages/common/static/icon_video_play_btn.png" size="100" mode="" @click="openVideoPlayer()"></app-image>
					</view>
				</view>
			</swiper-item>
			<template v-if="props.images != ''">
				<swiper-item v-for="(item, index) in props.images.split(',')" :key="index">
					<view class="swiper-item h-full uni-bg-green bg-#fff">
						<app-image class="w-full h-full" :src="item" width="100%" :height="height" positionCenter mode="aspectFit" @click="pvewImage(index)"></app-image>
					</view>
				</swiper-item>
			</template>
		</swiper>
		<!-- <view class="indicator my-18">
			<view class="indicator__dot" v-for="(item, index) in gridCount" :key="index"
				:class="[index === data.current && 'indicator__dot--active']"></view>
		</view> -->
	</view>
</template>

<style lang="scss" scoped>
.page-swiper {
	.indicator {
		@include flex(row);
		justify-content: center;

		&__dot {
			height: 10rpx;
			width: 10rpx;
			border-radius: 50%;
			background-color: #cbcbcb;
			margin: 0 4rpx;
			transition: background-color 0.3s;

			&--active {
				background-color: #6429e3;
			}
		}
	}
}
</style>
