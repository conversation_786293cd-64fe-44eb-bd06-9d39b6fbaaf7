<!-- uv-form表单渲染组件 -->
<script setup>
import { ref, reactive } from "vue";
import FormItem from "./FormItem.vue";

/**
 * @typedef {Object} FormItem
 * @property {string} label - 标签
 * @property {string} prop - 属性名
 * @property {('input'|'radio')} type - 类型
 * @property {Object} [props] - 组件属性
 * @property {Object} [events] - 事件
 * @property {string} [suffix] - 后缀
 * @property {boolean} [showRight] - 是否显示右侧箭头
 * @property {Array<{label: string, value: any}>} [options] - 选项（用于radio）
 */

/**
 * @typedef {Object} FormGroup
 * @property {string} title - 分组标题
 * @property {FormItem[]} items - 分组内的表单项
 */

const props = defineProps({
	// 表单配置
	formSchema: {
		type: Array,
		default: () => [],
		validator: (value) => {
			return value.every(
				(item) =>
					// 检查是否为分组配置
					(item.title && Array.isArray(item.items)) ||
					// 或者为普通表单项配置
					(item.label && item.prop)
			);
		},
	},
	// 表单数据
	formData: {
		type: Object,
		default: () => ({}),
	},
	// 表单校验规则
	rules: {
		type: Object,
		default: () => ({}),
	},
});
</script>

<template>
	<view>
		<uv-form :model="formData" ref="formRef" :rules="rules" label-width="220">
			<template v-for="(group, groupIndex) in formSchema" :key="groupIndex">
				<!-- 分组渲染 -->
				<uv-cell-group v-if="group.title" :title="group.title">
					<template v-for="item in group.items" :key="item.prop">
						<form-item :item="item" :form-data="formData" />
					</template>
				</uv-cell-group>

				<!-- 非分组渲染 -->
				<template v-else>
					<form-item :item="group" :form-data="formData" />
				</template>
			</template>
		</uv-form>
	</view>
</template>

<style lang="scss" scoped>
:deep {
	.uv-form-item {
		padding: 0 37rpx;
	}

	.uv-cell-group {
		margin-bottom: 20rpx;

		&__title {
			padding: 20rpx 37rpx;
			font-size: 24rpx;
			color: #828282;
			background-color: #f1f3f7;
		}
	}
}
</style>
