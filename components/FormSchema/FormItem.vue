<script setup>
defineProps({
	// 表单项配置
	item: {
		type: Object,
		required: true,
	},
	// 表单数据
	formData: {
		type: Object,
		required: true,
	},
});
</script>

<template>
	<uv-form-item :label="item.label" :prop="item.prop" borderBottom>
		<!-- 文本输入 -->
		<uv-input v-if="item.type === 'input'" v-model="formData[item.prop]" v-bind="item.props" @input="item.events?.input" @blur="item.events?.blur" @focus="item.events?.focus" @change="item.events?.change" @click="item.events?.click" border="none" :type="item.type || 'text'" input-align="right">
			<template v-if="item.suffix" #suffix>
				<text>{{ item.suffix }}</text>
			</template>
		</uv-input>
		<!-- 单选（性别/是或否） -->
		<uv-radio-group v-else-if="item.type === 'radio'" v-model="formData[item.prop]" activeColor="#00B496">
			<uv-radio v-for="(option, index) in item.options" :key="index" :customStyle="{ margin: '0 17rpx' }" :label="option.label" :name="option.value" />
		</uv-radio-group>
		<!-- 箭头 -->
		<template v-if="item.showRight" #right>
			<uv-icon name="arrow-right" />
		</template>
	</uv-form-item>
</template>
