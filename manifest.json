{
    "name" : "和家无忧",
    "appid" : "__UNI__9E86F3E",
    "description" : "和家无忧用户端",
    "versionName" : "1.2.02",
    "versionCode" : 1202,
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "OAuth" : {},
            "Geolocation" : {},
            "Barcode" : {},
            "Camera" : {},
            "Maps" : {},
            "Payment" : {},
            "Share" : {},
            "VideoPlayer" : {},
            "Push" : {},
            "Record" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>"
                ],
                "minSdkVersion" : 28,
                "targetSdkVersion" : 33,
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a" ]
            },
            /* ios打包配置 */
            "ios" : {
                "dSYMs" : false,
                "privacyDescription" : {
                    "NSPhotoLibraryUsageDescription" : "和家无忧想访问你的相册权限，帮助您上传图片、修改头像、发送照片等",
                    "NSPhotoLibraryAddUsageDescription" : "和家无忧想访问你的相册权限，帮助您保存图片等",
                    "NSCameraUsageDescription" : "和家无忧想访问你的摄像头权限，帮助您上传图片、修改头像、发送照片等",
                    "NSMicrophoneUsageDescription" : "和家无忧想访问你的麦克风权限，帮助您语音搜索、发送语音等",
                    "NSLocationAlwaysUsageDescription" : "",
                    "NSLocationAlwaysAndWhenInUseUsageDescription" : "和家无忧想访问你的位置信息，帮助您获取最近的服务等",
                    "NSLocationWhenInUseUsageDescription" : "和家无忧想访问你的位置信息，帮助您获取最近的服务等"
                }
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "oauth" : {
                    "univerify" : {},
                    "weixin" : {
                        "appid" : "wxfb4d74e7ae4bf60d",
                        "UniversalLinks" : "https://api.ukangai.com/"
                    }
                },
                "geolocation" : {
                    "amap" : {
                        "name" : "amapCxKXLVnqR",
                        "__platform__" : [ "ios", "android" ],
                        "appkey_ios" : "da8a3ce488105c62dfe556b8f3054fea",
                        "appkey_android" : "89e54d03e77217ea8dfa18f4d2b61324"
                    },
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                },
                "maps" : {
                    "amap" : {
                        "name" : "amapCxKXLVnqR",
                        "appkey_ios" : "da8a3ce488105c62dfe556b8f3054fea",
                        "appkey_android" : "89e54d03e77217ea8dfa18f4d2b61324"
                    }
                },
                "payment" : {
                    "alipay" : {
                        "__platform__" : [ "ios", "android" ]
                    },
                    "weixin" : {
                        "__platform__" : [ "ios", "android" ],
                        "appid" : "wxfb4d74e7ae4bf60d",
                        "UniversalLinks" : "https://api.ukangai.com/"
                    }
                },
                "share" : {
                    "weixin" : {
                        "appid" : "wxfb4d74e7ae4bf60d",
                        "UniversalLinks" : "https://api.ukangai.com/"
                    }
                },
                "statics" : {},
                "push" : {
                    "unipush" : {
                        "version" : "2",
                        "offline" : true,
                        "hms" : {},
                        "oppo" : {},
                        "vivo" : {},
                        "mi" : {}
                    }
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "useOriginalMsgbox" : true
            }
        },
        "uniStatistics" : {
            "enable" : true
        },
        "safearea" : {
            "bottom" : {
                "offset" : "none"
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wx9818ca11df4bfa78",
        "setting" : {
            "urlCheck" : false,
            "postcss" : true,
            "minified" : true,
            "es6" : true
        },
        "requiredPrivateInfos" : [ "chooseLocation", "getFuzzyLocation" ],
        "permission" : {
            "scope.userLocation" : {
                "desc" : "你的位置信息将用于设置地区或设置地址,发现附近服务等"
            },
            "scope.userFuzzyLocation" : {
                "desc" : "你的位置信息将用于设置地区或设置地址,发现附近服务等"
            }
        },
        "usingComponents" : true,
        "unipush" : {
            "enable" : true
        },
        "uniStatistics" : {
            "enable" : true
        },
        "optimization" : {
            "subPackages" : true
        },
        "lazyCodeLoading" : "requiredComponents"
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false,
        "version" : "2"
    },
    "vueVersion" : "3",
    "app-harmony" : {
        "distribute" : {
            "modules" : {
                "uni-push" : {}
            }
        }
    },
    "h5" : {
        "optimization" : {
            "treeShaking" : {
                "enable" : true
            }
        },
        "sdkConfigs" : {
            "maps" : {
                "amap" : {
                    "key" : "",
                    "securityJsCode" : "",
                    "serviceHost" : ""
                }
            }
        },
        "devServer" : {
            "https" : false
        },
        "unipush" : {
            "enable" : true
        },
        "router" : {
            "base" : "/health/"
        }
    }
}
