{
    "easycom": {
        "autoscan": true,
        "custom": {
            "^app-(.*)": "@/components/app-components/app-$1/app-$1.vue",
            "^page-(.*)": "@/components/page-components/page-$1/page-$1.vue",
            "^my-uv-(.*)": "@/components/ui/my-uv-$1/my-uv-$1.vue"
        }
    },
    "pages": [
        //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
        {
            "path": "pages/index/index",
            "style": {
                "navigationBarTitleText": "和家无忧",
                "navigationStyle": "custom"
            }
        },
        {
            "path": "pages/tabBar/home/<USER>",
            "style": {
                "navigationBarTitleText": "",
                "navigationStyle": "custom",
                "backgroundColor": "#F0F3F7",
                "backgroundColorBottom": "#F0F3F7",
                "enablePullDownRefresh": false,
                "disableScroll": false,
                "app-plus": {
                    "bounce": "none"
                }
            }
        },
        // {
        // 	"path": "pages/tabBar/order/order",
        // 	"style": {
        // 		"navigationBarTitleText": "",
        // 		"navigationStyle": "custom",
        // 		"backgroundColor": "#F0F3F7"
        // 	}
        // },
        {
            "path": "pages/tabBar/message/message",
            "style": {
                "navigationBarTitleText": "消息",
                "navigationStyle": "custom"
            }
        },
        {
            "path": "pages/tabBar/information/information",
            "style": {
                "navigationBarTitleText": "",
                "navigationStyle": "custom",
                "backgroundColor": "#F1F3F7",
                "app-plus": {
                    "scrollIndicator": "none"
                }
            }
        },
        {
            "path": "pages/tabBar/healthy/healthy",
            "style": {
                "navigationBarTitleText": "",
                "navigationStyle": "custom",
                "backgroundColor": "#F1F3F7",
                "app-plus": {
                    "scrollIndicator": "none"
                }
            }
        },
        // {
        //     "path": "pages/tabBar/security/security",
        //     "style": {
        //         "navigationBarTitleText": "",
        //         "navigationStyle": "custom",
        //         "backgroundColor": "#F1F3F7",
        //         "app-plus": {
        //             "scrollIndicator": "none"
        //         }
        //     }
        // },
        {
            "path": "pages/tabBar/my/my",
            "style": {
                "navigationBarTitleText": "",
                "navigationStyle": "custom",
                "backgroundColor": "#F1F3F7",
                "app-plus": {
                    "scrollIndicator": "none"
                }
            }
        }
    ],
    "subPackages": [
        {
            "root": "pages/login",
            "pages": [
                {
                    "path": "pages/index/index",
                    "style": {
                        "navigationBarTitleText": "",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/codeLogin/codeLogin",
                    "style": {
                        "navigationBarTitleText": "",
                        "navigationStyle": "custom"
                    }
                },
                {
                    // 设置手机号页
                    "path": "pages/setMobile/setMobile",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "",
                        "app-plus": {
                            "titleNView": {
                                "type": "transparent"
                            }
                        }
                    }
                },
                {
                    // 更换手机号 - 验证密码
                    "path": "pages/verifyPassword/verifyPassword",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": ""
                    }
                },
                {
                    // 更换手机号 - 验证旧手机号
                    "path": "pages/verifyOlMobile/verifyOlMobile",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": ""
                    }
                },
                {
                    // 更换手机号 - 验证新手机号
                    "path": "pages/verifyNewMobile/verifyNewMobile",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": ""
                    }
                }
            ]
        },
        {
            "root": "pages/home",
            "pages": [
                {
                    "path": "pages/search/search",
                    "style": {
                        "navigationBarTitleText": "搜索",
                        "navigationStyle": "custom",
                        "backgroundColor": "#fff",
                        "app-plus": {
                            "bounce": "none"
                        }
                    }
                },
                {
                    "path": "pages/productHome/productHome",
                    "style": {
                        "navigationBarTitleText": "无忧商城",
                        "navigationStyle": "custom",
                        "backgroundColor": "#F0F3F7"
                    }
                },
                {
                    "path": "pages/huliHome/huliHome",
                    "style": {
                        "navigationBarTitleText": "护理",
                        "navigationStyle": "custom",
                        "backgroundColor": "#F0F3F7"
                    }
                },
                {
                    "path": "pages/jiazhengHome/jiazhengHome",
                    "style": {
                        "navigationBarTitleText": "家政",
                        "navigationStyle": "custom",
                        "backgroundColor": "#F0F3F7"
                    }
                },
                {
                    "path": "pages/peizhenHome/peizhenHome",
                    "style": {
                        "navigationBarTitleText": "陪诊",
                        "navigationStyle": "custom",
                        "backgroundColor": "#F0F3F7"
                    }
                },
                {
                    "path": "pages/serverHome/serverHome",
                    "style": {
                        "navigationBarTitleText": "服务首页",
                        "navigationStyle": "custom",
                        "backgroundColor": "#F0F3F7"
                    }
                },
                {
                    "path": "pages/serverTwoHome/serverTwoHome",
                    "style": {
                        "navigationBarTitleText": "服务二级首页",
                        "navigationStyle": "custom",
                        "backgroundColor": "#F0F3F7"
                    }
                },
                {
                    "path": "pages/integralProductlHome/integralProductlHome",
                    "style": {
                        "navigationBarTitleText": "积分商城",
                        "navigationStyle": "custom",
                        "backgroundColor": "#F0F3F7"
                    }
                },
                {
                    "path": "pages/addTendUser/addTendUser",
                    "style": {
                        "navigationBarTitleText": "入驻照护师",
                        "navigationStyle": "custom",
                        "backgroundColor": "#F0F3F7"
                    }
                }
            ]
        },
        {
            "root": "pages/common",
            "pages": [
                {
                    // 文章页面
                    "path": "pages/article/article",
                    "needLoginRoute": false,
                    "style": {
                        "navigationBarTitleText": ""
                    }
                },
                {
                    // webView 页
                    "path": "pages/webView/webView",
                    "needLoginRoute": false,
                    "style": {
                        "navigationBarTitleText": "",
                        "enablePullDownRefresh": false,
                        "app-plus": {
                            "titleNView": {
                                "homeButton": false
                                // ,"buttons": [{
                                // 	"type": "close",
                                // 	"float": "right"
                                // }]
                            }
                        }
                    }
                },
                {
                    // 选择城市
                    "path": "pages/selectCity/selectCity",
                    "needLoginRoute": false,
                    "style": {
                        "navigationBarTitleText": "选择城市"
                    }
                },
                {
                    // 选择城市
                    "path": "pages/videoPlayer/videoPlayer",
                    "needLoginRoute": false,
                    "style": {
                        // "navigationBarTitleText": "播放视频"
                        "navigationStyle": "custom",
                        "app-plus": {
                            "animationType": "fade-in"
                        }
                    }
                }
            ]
        },
        {
            "root": "pages/goods",
            "pages": [
                {
                    // 商品详情
                    "path": "pages/productDetails/productDetails",
                    "needLoginRoute": false,
                    "style": {
                        "navigationBarTitleText": "商品详情",
                        "navigationStyle": "custom",
                        "backgroundColor": "#F1F3F7"
                    }
                },
                {
                    // 商品详情
                    "path": "pages/huliDetails/huliDetails",
                    "needLoginRoute": false,
                    "style": {
                        "navigationBarTitleText": "商品详情",
                        "navigationStyle": "custom",
                        "backgroundColor": "#F1F3F7"
                    }
                },
                {
                    // 商品详情
                    "path": "pages/jiazhengDetails/jiazhengDetails",
                    "needLoginRoute": false,
                    "style": {
                        "navigationBarTitleText": "商品详情",
                        "navigationStyle": "custom",
                        "backgroundColor": "#F1F3F7"
                    }
                },
                {
                    // 商品详情
                    "path": "pages/productDetailsIntegral/productDetailsIntegral",
                    "needLoginRoute": false,
                    "style": {
                        "navigationBarTitleText": "商品详情",
                        "navigationStyle": "custom",
                        "backgroundColor": "#F1F3F7"
                    }
                },
                {
                    // 商品详情
                    "path": "pages/peizhenDetails/peizhenDetails",
                    "needLoginRoute": false,
                    "style": {
                        "navigationBarTitleText": "商品详情",
                        "navigationStyle": "custom",
                        "backgroundColor": "#F1F3F7"
                    }
                },
                {
                    "path": "pages/evaluation/evaluation",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "商品评价",
                        "navigationBarTextStyle": "black"
                    }
                },
                {
                    "path": "pages/shopHome/shopHome",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "店铺详情",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F4F5F8",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/paySuccess/paySuccess",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "", // 付款成功
                        "navigationBarTextStyle": "black"
                    }
                },
                {
                    "path": "pages/shoppingCart/shoppingCart",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "购物车",
                        "navigationBarTextStyle": "black",
                        "navigationStyle": "custom"
                    }
                }
            ]
        },
        {
            "root": "pages/order",
            "pages": [
                {
                    "path": "pages/createOrderProduct/createOrderProduct",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "确认订单",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F4F5F8",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/createOrderProductIntegral/createOrderProductIntegral",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "确认订单",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F4F5F8",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/createOrderJiazheng/createOrderJiazheng",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "确认订单",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F4F5F8",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/createOrderPeizhen/createOrderPeizhen",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "确认订单",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F4F5F8",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/createOrderHuli/createOrderHuli",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "确认订单",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F4F5F8",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/orderDetailIndex/orderDetailIndex",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "订单详情",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F4F5F8",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/orderDetail/orderDetail",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "订单详情",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F4F5F8",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/integralOrder/integralOrder",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "积分订单",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F4F5F8",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/orderDetailServer/orderDetailServer",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "订单详情",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F4F5F8",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/logistics/logistics",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "物流信息",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F4F5F8"
                    }
                },
                {
                    "path": "pages/logisticsOrderMore/logisticsOrderMore",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "物流信息",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F4F5F8"
                    }
                },
                {
                    "path": "pages/logisticsOrderSingle/logisticsOrderSingle",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "物流信息",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F4F5F8"
                    }
                },
                {
                    "path": "pages/logisticsAfterSale/logisticsAfterSale",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "物流信息",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F4F5F8"
                    }
                },
                {
                    "path": "pages/applyAfterSales/applyAfterSales",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "申请售后",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F4F5F8"
                    }
                },
                {
                    "path": "pages/afterSaleDetail/afterSaleDetail",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "售后详情",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F4F5F8",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/trackingNumber/trackingNumber",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "填写单号",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F4F5F8"
                    }
                },
                {
                    "path": "pages/storeReturn/storeReturn",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "到店退款",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F4F5F8"
                    }
                },
                {
                    "path": "pages/setComment/setComment",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "发布评价",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F4F5F8"
                    }
                },
                {
                    "path": "pages/invoice/applyInvoice",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "申请开票",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F4F5F8"
                    }
                },
                {
                    "path": "pages/invoice/applyInvoiceDetails",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "开票详情",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F4F5F8"
                    }
                },
                {
                    "path": "pages/invoice/addInvoiceHeader/addInvoiceHeader",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "添加抬头",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F4F5F8"
                    }
                }
            ]
        },
        {
            "root": "pages/settings",
            "pages": [
                {
                    "path": "pages/home/<USER>",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "设置"
                    }
                },
                {
                    "path": "pages/privacySettings/privacySettings",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "隐私设置"
                    }
                },
                {
                    "path": "pages/permissionManage/permissionManage",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "权限管理"
                    }
                },
                {
                    "path": "pages/accountSecurity/accountSecurity",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "账户安全"
                    }
                },
                {
                    "path": "pages/changeMobile/changeMobile",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "更换手机号",
                        "backgroundColor": "#fff"
                    }
                },
                {
                    "path": "pages/unsubscribe/unsubscribe",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "注销账户"
                    }
                },
                {
                    "path": "pages/feedback/feedback",
                    "style": {
                        "navigationStyle": "custom",
                        "navigationBarTitleText": "意见反馈"
                    }
                },
                {
                    "path": "pages/report/report",
                    "style": {
                        "navigationStyle": "custom",
                        "navigationBarTitleText": "举报"
                    }
                },
                {
                    "path": "pages/aboutUs/aboutUs",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "关于我们"
                    }
                }
            ]
        },
        {
            "root": "pages/my",
            "pages": [
                {
                    "path": "pages/userCode/userCode",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "会员码",
                        "navigationBarTextStyle": "white",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/editUserInfo/editUserInfo",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "个人信息",
                        "navigationBarTextStyle": "black"
                    }
                },
                {
                    "path": "pages/favorites/favorites",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "收藏夹",
                        "navigationBarTextStyle": "black",
                        "navigationStyle": "custom",
                        "backgroundColor": "#F0F3F7"
                    }
                },
                {
                    "path": "pages/follow/follow",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "关注店铺",
                        "navigationBarTextStyle": "black",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/footmark/footmark",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "足迹",
                        "navigationBarTextStyle": "black",
                        "navigationStyle": "custom",
                        "backgroundColor": "#F0F3F7"
                    }
                },
                {
                    "path": "pages/memberCenter/memberCenter",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "会员中心",
                        "navigationBarTextStyle": "white",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/memberCenter/openRecord/openRecord",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "开通记录"
                    }
                },
                {
                    "path": "pages/getCoupon/getCoupon",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "领券中心",
                        "navigationBarTextStyle": "white",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/coupon/coupon",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "我的优惠券",
                        "navigationBarTextStyle": "black"
                    }
                },
                {
                    "path": "pages/integral/integral",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "积分中心",
                        "navigationBarTextStyle": "white",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/teamCenter/teamCenter",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "我的邀请",
                        "navigationBarTextStyle": "white",
                        "navigationStyle": "custom",
                        "backgroundColor": "#F1F3F7"
                    }
                },
                {
                    "path": "pages/teamCenter/timeUserList/timeUserList",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "已邀请",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F1F3F7"
                    }
                },
                {
                    "path": "pages/teamCenter/distributeOrderList/distributeOrderList",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "佣金",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F1F3F7"
                    }
                },
                {
                    "path": "pages/teamCenter/walletCommission/walletCommission",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F1F3F7",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/teamCenter/walletCommission/withdraw/withdraw",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "佣金提现",
                        "navigationBarTextStyle": "black",
                        "backgroundColor": "#F1F3F7",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/evaluation/evaluation",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "我的评价",
                        "navigationBarTextStyle": "black"
                    }
                },
                {
                    "path": "pages/evaluationDetails/evaluationDetails",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "评价详情",
                        "navigationBarTextStyle": "black"
                    }
                },
                {
                    "path": "pages/caregiver/caregiver",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "被照护人",
                        "navigationBarTextStyle": "black"
                    }
                },
                {
                    "path": "pages/caregiver/addCaregiver/addCaregiver",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "新增被照护人",
                        "navigationBarTextStyle": "black"
                    }
                },
                {
                    "path": "pages/wallet/wallet",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "我的钱包",
                        "navigationBarTextStyle": "black",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/wallet/withdraw/withdraw",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "提现",
                        "navigationBarTextStyle": "black",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/wallet/recharge/recharge",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "充值",
                        "navigationBarTextStyle": "black",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/account/account",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "银行卡",
                        "navigationBarTextStyle": "black"
                    }
                },
                {
                    "path": "pages/account/bankCardGetCode/bankCardGetCode",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "添加银行卡",
                        "navigationBarTextStyle": "black"
                    }
                },
                {
                    "path": "pages/account/bankCard/bankCard",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "银行卡",
                        "navigationBarTextStyle": "black"
                    }
                },
                {
                    "path": "pages/account/bankCardAli/bankCardAli",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "支付宝",
                        "navigationBarTextStyle": "black"
                    }
                },
                {
                    "path": "pages/account/bankCardWeiXin/bankCardWeiXin",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "微信",
                        "navigationBarTextStyle": "black"
                    }
                },
                {
                    "path": "pages/address/addressGoods",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "收货地址",
                        "navigationBarTextStyle": "black"
                    }
                },
                {
                    "path": "pages/address/addAddress/addAddressGoods",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "添加收货地址",
                        "navigationBarTextStyle": "black"
                    }
                },
                {
                    "path": "pages/address/addressServer",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "服务地址",
                        "navigationBarTextStyle": "black"
                    }
                },
                {
                    "path": "pages/address/addAddress/addAddressServer",
                    "needLoginRoute": true,
                    "style": {
                        "navigationBarTitleText": "添加服务地址",
                        "navigationBarTextStyle": "black"
                    }
                },
                {
                    "path": "pages/merchantEnter/merchantEnter",
                    "style": {
                        "navigationBarTitleText": "",
                        "navigationStyle": "custom"
                    }
                }
            ]
        },
        {
            "root": "pages/invite",
            "pages": [
                {
                    "path": "pages/invite/invite",
                    "style": {
                        // "navigationStyle": "custom"
                        "navigationBarTitleText": "邀请好友",
                        "navigationStyle": "custom"
                        // "app-plus": {
                        // 	"animationType": "slide-in-bottom",
                        // 	"titleNView": {
                        // 		"type": "transparent"
                        // 	}
                        // }
                    }
                },
                {
                    "path": "pages/invite/rules/rules",
                    "style": {
                        "navigationBarTitleText": "邀请规则",
                        "navigationStyle": "custom",
                        "backgroundColor": "#0F0D07"
                    }
                },
                {
                    "path": "pages/invite/income/income",
                    "style": {
                        "navigationBarTitleText": "邀请好友总收入"
                    }
                },
                {
                    "path": "pages/invite/number/number",
                    "style": {
                        "navigationBarTitleText": "有效邀请人数"
                    }
                }
            ]
        },
        {
            "root": "pages/healthy",
            "pages": [
                {
                    "path": "pages/family_circle",
                    "style": {
                        "navigationBarTitleText": "家庭圈",
                        "backgroundColor": "#0F0D07",
                        "navigationStyle": "custom",
                        "app-plus": {
                            "scrollIndicator": "none"
                        }
                    }
                },
                {
                    "path": "pages/all_equipment",
                    "style": {
                        "navigationBarTitleText": "全部设备"
                    }
                },
                {
                    "path": "pages/equipment_type",
                    "style": {
                        "navigationBarTitleText": "设备类型"
                    }
                },
                {
                    "path": "pages/input_manual",
                    "style": {
                        "navigationBarTitleText": "手动输入设备编号"
                    }
                },
                {
                    "path": "pages/equipment_info",
                    "style": {
                        "navigationBarTitleText": "设备信息",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/health_record",
                    "style": {
                        "navigationBarTitleText": "健康档案"
                    }
                },
                {
                    "path": "pages/health_questionnaire",
                    "style": {
                        "navigationBarTitleText": "健康问卷",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/questionnaire_notes",
                    "style": {
                        "navigationBarTitleText": "问卷须知",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/question_and_nswer",
                    "style": {
                        "navigationBarTitleText": "问卷调查",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/questionnaire_details",
                    "style": {
                        "navigationBarTitleText": "问卷详情",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/daily_monitoring",
                    "style": {
                        "navigationBarTitleText": "日常监测",
                        "componentPlaceholder": {
                            "base-echarts": "view"
                        }
                    }
                },
                {
                    "path": "pages/health_reports",
                    "style": {
                        "navigationBarTitleText": "健康报告",
                        "componentPlaceholder": {
                            "base-echarts": "view"
                        }
                    }
                }
            ]
        },
        {
            "root": "pages/information",
            "pages": [
                {
                    "path": "pages/infoDetail/infoDetail",
                    "style": {
                        "navigationBarTitleText": "资讯详情",
                        "navigationStyle": "custom",
                        "backgroundColor": "#F1F3F7"
                    }
                },
                {
                    "path": "pages/infoReport/infoReport",
                    "style": {
                        "navigationBarTitleText": "举报"
                    }
                }
            ]
        },
        {
            "root": "pages/message",
            "pages": [
                {
                    "path": "pages/im/im",
                    "style": {
                        "navigationBarTitleText": "消息",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/system/system",
                    "style": {
                        "navigationBarTitleText": "通知"
                    }
                }
            ]
        },
        {
            "root": "pages/tabBar/order",
            "pages": [
                {
                    "path": "order",
                    "style": {
                        "navigationBarTitleText": "订单"
                    }
                }
            ]
        },
        {
            "root": "pages/echarts",
            "pages": [
                {
                    "path": "index",
                    "style": {
                        "navigationBarTitleText": "echarts"
                    }
                }
            ]
        },
        {
            "root": "pages/test",
            "pages": [
                {
                    "path": "health-report-final",
                    "style": {
                        "navigationBarTitleText": "Canvas测试"
                    }
                }
            ]
        }
    ],
    "preloadRule": {
        "pages/tabBar/home/<USER>": {
            "network": "all",
            // "packages": ["__APP__", "pages/common", "pages/home", "pages/goods"]
            "packages": ["__APP__"]
        },
        "pages/tabBar/my/my": {
            "network": "all",
            "packages": ["pages/my", "pages/settings", "pages/invite"]
        },
        // "pages/tabBar/order/order": {
        // 	"network": "all",
        // 	"packages": ["pages/order"]
        // },
        "pages/goods/pages/productDetails/productDetails": {
            "network": "all",
            "packages": ["pages/common", "pages/order"]
        },
        "pages/tabBar/healthy/healthy": {
            "network": "all",
            "packages": ["pages/echarts"]
        }
    },
    "globalStyle": {
        "navigationBarTextStyle": "black",
        "navigationBarTitleText": "和家无忧",
        "navigationBarBackgroundColor": "#ffffff",
        "backgroundColor": "#ffffff",
        "app-plus": {
            "bounce": "none"
        }
    },
    "uniIdRouter": {},
    "tabBar": {
        "backgroundColor": "#ffffff",
        "borderStyle": "white",
        "color": "#131314",
        "selectedColor": "#3D6248",
        "list": [
            {
                "pagePath": "pages/tabBar/home/<USER>",
                "iconPath": "static/tabBar/home.png",
                "selectedIconPath": "static/tabBar/home_active.png",
                "text": "生活服务"
            },
            // {
            // 	"pagePath": "pages/tabBar/order/order",
            // 	"iconPath": "static/tabBar/order.png",
            // 	"selectedIconPath": "static/tabBar/order_active.png",
            // 	"text": "订单"
            // },
            {
                "pagePath": "pages/tabBar/healthy/healthy",
                "iconPath": "/static/tabBar/healthy.png",
                "selectedIconPath": "/static/tabBar/healthy_active.png",
                "text": "健康管理"
            },
            {
                "pagePath": "pages/tabBar/information/information",
                "iconPath": "/static/tabBar/information.png",
                "selectedIconPath": "/static/tabBar/information_active.png",
                "text": "文体娱乐"
            },
            // {
            // 	"pagePath": "pages/tabBar/security/security",
            // 	"iconPath": "/static/tabBar/security.png",
            // 	"selectedIconPath": "/static/tabBar/security_active.png",
            // 	"text": "安全守护"
            // },
            // {
            //     "pagePath": "pages/tabBar/message/message",
            //     "iconPath": "static/tabBar/message.png",
            //     "selectedIconPath": "static/tabBar/message_active.png",
            //     "text": "消息"
            // },
            {
                "pagePath": "pages/tabBar/my/my",
                "iconPath": "static/tabBar/my.png",
                "selectedIconPath": "static/tabBar/my_active.png",
                "text": "我的"
            }
        ]
    }
}
