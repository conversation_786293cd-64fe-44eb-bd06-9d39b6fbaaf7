/**
 * 分页查询售后工单
 */
export const getAfsOrder = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-afs/afs/order`, {
		params: {
			...params
		},
		...config
	})
};

/**
 * 提交售后
 */
export const submitAfsOrder = (data = {}, config = {}) => {
	return uni.$uv.http.post(`/yays-mall-afs/afs/order`, {
		...data
	}, {
		...config
	})
};

/**
 * 获取售后订单信息
 */
export const getAfssInfo = (params = {
	afsNo: ''
}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-afs/afs/order/${params.afsNo}`, {
		params: {
			...params
		},
		...config
	})
};

/**
 * 撤销申请
 */
export const closeAfssCancel = (data = {
	afsNo: ''
}, config = {}) => {
	return uni.$uv.http.put(`/yays-mall-afs/afs/order/${data.afsNo}/close`, {
		...data
	}, {
		...config
	})
};

/**
 * 协商历史
 */
export const getAfssHistory = (params = {
	afsNo: ''
}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-afs/afs/order/${params.afsNo}/history`, {
		params: {
			...params
		},
		...config
	})
};

/**
 * 用户退货退款
 */
export const setReturnedByAfsNo = (data = {
	afsNo: '',
	type: '',
}, config = {}) => {
	return uni.$uv.http.put(`yays-mall-afs/afs/order/${data.afsNo}/${data.type}/returned`, {
		...data
	}, {
		...config
	})
};