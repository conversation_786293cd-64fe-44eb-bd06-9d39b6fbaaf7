/**
 * 生成价格预算
 */
export const getBudget = (data = {}, config = {}) => {
	return uni.$uv.http.post(
		`/yays-mall-order/order/budget`,
		{
			...data,
		},
		{
			...config,
		}
	);
};

/**
 * 生成订单
 */
export const generateOrders = (data = {}, config = {}) => {
	return uni.$uv.http.post(
		`/yays-mall-order/order`,
		{
			...data,
		},
		{
			...config,
		}
	);
};

/**
 * 订单修改地址
 */
export const putOrderReceiver = (data = {}, config = {}) => {
	return uni.$uv.http.put(
		`/yays-mall-order/order/${data.orderNo}/receiver`,
		{
			...data,
		},
		{
			...config,
		}
	);
};

/**
 * 拉起支付
 */
export const getOrderPayPage = (data = {}, config = {}) => {
	return uni.$uv.http.post(
		`/yays-mall-order/order/pay/page`,
		{
			...data,
		},
		{
			...config,
		}
	);
};

/**
 * 拉起支付 - 二次支付
 */
export const getOrderPayPageAgain = (data = {}, config = {}) => {
	return uni.$uv.http.post(
		`/yays-mall-order/order/pay/item/page`,
		{
			...data,
		},
		{
			...config,
		}
	);
};

/**
 * 查询订单是否支付完成
 */
export const getOrderIsPaySuccess = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-payment/merchant/pay/order/status`, {
		params: {
			...params,
		},
		...config,
	});
};

/**
 * 取消订单
 */
export const closeOrderByOrderNo = (data = {}, config = {}) => {
	return uni.$uv.http.put(
		`/yays-mall-order/order/${data.orderNo}/close`,
		{
			...data,
		},
		{
			...config,
		}
	);
};

/**
 * 取消订单 - 子订单
 */
export const closeOrderByOrderNoSub = (data = {}, config = {}) => {
	return uni.$uv.http.delete(
		`yays-mall-order/order/deleteItemOrder/${data.orderNo}`,
		{
			...data,
		},
		{
			...config,
		}
	);
};

/**
 * 确认订单
 */
export const confirmGoods = (
	data = {
		orderNo: "",
		shopId: "",
	},
	config = {}
) => {
	return uni.$uv.http.put(
		`/yays-mall-order/order/${data.orderNo}/shopOrder/${data.shopId}/confirm`,
		{
			...data,
		},
		{
			...config,
		}
	);
};

/**
 * 结束订单
 */
export const closeOrderServerGoods = (
	data = {
		orderNo: "",
		shopId: "",
		status: "SERVEREND",
	},
	config = {}
) => {
	return uni.$uv.http.put(
		`/yays-mall-order/order/startEndServe/${data.shopId}/${data.orderNo}`,
		{
			...data,
		},
		{
			params: {
				status: data.status,
				...config.params,
			},
			...config,
		}
	);
};

/**
 * 订单加时
 */
export const addOrderNumberServer = (
	data = {
		orderNo: "",
		shopId: "",
		addTime: "",
	},
	config = {}
) => {
	return uni.$uv.http.put(
		`/yays-mall-order/order/addTime/${data.orderNo}`,
		{
			...data,
		},
		{
			params: {
				addTime: data.addTime,
				...config.params,
			},
			...config,
		}
	);
};

/**
 * 订单备注
 * ids  总订单号 或者 店铺订单号 平台端 为总订单号列表 商家端 为店铺订单号列表
 */
export const putOrderRemark = (data = {}, config = {}) => {
	return uni.$uv.http.put(
		`/yays-mall-order/order/remark/batch`,
		{
			...data,
		},
		{
			...config,
		}
	);
};

/**
 * 获取订单信息
 */
export const getOrder = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-order/order`, {
		params: {
			...params,
		},
		...config,
	});
};

/**
 * 获取订单信息
 */
export const getServerOrder = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-order/order/userOrderPage`, {
		params: {
			...params,
		},
		...config,
	});
};

/**
 * 获取已支付订单信息
 */
export const getHavePayOrder = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-order/order/${params.orderNo}/shopOrder/${params.shopOrderNo}`, {
		params: {
			...params,
		},
		...config,
	});
};

/**
 * 获取订单详情信息
 */
export const getOrderInfo = (
	params = {
		orderNo: "",
	},
	config = {}
) => {
	return uni.$uv.http.get(`/yays-mall-order/order/${params.orderNo}`, {
		params: {
			...params,
		},
		...config,
	});
};

/**
 * 获取订单详情信息
 */
export const getServerOrderInfo = (
	params = {
		orderNo: "",
	},
	config = {}
) => {
	return uni.$uv.http.get(`/yays-mall-order/order/server/${params.orderNo}`, {
		params: {
			...params,
		},
		...config,
	});
};

/**
 * 查询未支付的订单支付信息
 */
export const getOrderPayment = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-order/order/${params.orderNo}/payment`, {
		params: {
			...params,
		},
		...config,
	});
};

/**
 * 查询订单创建情况
 */
export const getOrderCreateConditions = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-order/order/${params.orderNo}/creation`, {
		params: {
			...params,
		},
		...config,
	});
};

/**
 * 查询第一个已发货的包裹
 */
export const getFirstDeliveryPage = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-order/order/${params.orderNo}/shopOrder/${params.shopOrderNo}/delivered/01`, {
		params: {
			...params,
		},
		...config,
	});
};

/**
 * 查询所有已发货的包裹  outTradeNo
 */
export const getDeliveryPageAll = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-order/order/${params.orderNo}/shopOrder/${params.shopOrderNo}/delivered`, {
		params: {
			...params,
		},
		...config,
	});
};

/**
 * 查询所有已发货的包裹
 */
export const getDeliveryPackage = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-order/order/${params.orderNo}/shopOrder/${params.shopOrderNo}/delivered/package`, {
		params: {
			...params,
		},
		...config,
	});
};

/**
 * 根据物流单号查询物流轨迹
 */
export const getLogisticsTrajectoryByWaybillNo = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-freight/logistics/node`, {
		params: {
			...params,
		},
		header: params.shopId
			? {
					"Shop-Id": params.shopId,
			  }
			: {},
		...config,
	});
};

/**
 * 查询运费
 */
export const getFreightCalculation = (data = {}, config = {}) => {
	return uni.$uv.http.post(
		`/yays-mall-order/order/distribution/cost`,
		{
			...data,
		},
		{
			...config,
		}
	);
};

/**
 * 检验订单异常
 */
export const getOrderValid = (data = {}, config = {}) => {
	return uni.$uv.http.post(
		`/yays-mall-order/order/valid`,
		{
			...data,
		},
		{
			...config,
		}
	);
};

/**
 * 批量根据店铺 id 查询店铺交易设置
 */
export const getOrderSettingsDealBatch = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-order/order/config/form/batch`, {
		params: {
			...params,
		},
		...config,
	});
};

/**
 * 订单统计
 */
export const getOrderCount = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-order/order/my/count`, {
		params: {
			...params,
		},
		...config,
	});
};

/**
 * 查询订单商品项
 */
export const getShopOrderItem = (
	params = {
		orderNo: "",
		itemId: "",
	},
	config = {}
) => {
	return uni.$uv.http.get(`/yays-mall-order/order/${params.orderNo}/item/${params.itemId}`, {
		params: {
			...params,
		},
		...config,
	});
};

/**
 * 查询订单合同信息
 */
export const getServerOrderContract = (
	params = {
		orderNo: "",
	},
	config = {}
) => {
	return uni.$uv.http.get(`/yays-mall-order/order/orderItemInfo/${params.orderNo}`, {
		params: {
			...params,
		},
		...config,
	});
};

/**
 * 获取 TC 配送订单信详情
 */
export const getICOrder = (data = {}, config = {}) => {
	return uni.$uv.http.post(
		`/addon-ic/ic/shop/order/deliver/info`,
		{
			...data,
		},
		{
			...config,
		}
	);
};

/**
 * 获取 UU 配送最新信息
 */
export const getUUPTCourierInfo = (data = {}, config = {}) => {
	return uni.$uv.http.post(
		`/addon-ic/ic/shop/order/courier/uupt`,
		{
			...data,
		},
		{
			...config,
		}
	);
};

export const toConfirmOrderValid = async (params = []) => {
	const orderValid = {
		orderType: params[0]?.activityParam?.type || "COMMON",
		activityId: params[0]?.activityParam?.activityId || "",
		products: [],
		distributionMode: params[0]?.distributionMode || "EXPRESS",
	};
	const returnValue = {
		success: true,
		data: {},
	};
	params.forEach((item) => {
		item.products.forEach((ite) => {
			const { skuId, id: productId, num } = ite;
			const shopProduct = {
				key: {
					shopId: item.shopId,
					productId,
					skuId,
				},
				num,
			};
			orderValid.products.push(shopProduct);
		});
	});
	const { code, msg, data } = await getOrderValid(orderValid, {
		custom: {
			toast: false,
		},
	});
	switch (code) {
		case 200:
			return returnValue;
		case 30038:
			{
				const shopDisable = params
					.filter((shops) => data.includes(shops.shopId))
					.map((item) => item.shopName)
					.join(",");
				returnValue.success = false;
				returnValue.data = {
					title: "店铺已禁用",
					content: `${shopDisable}`,
				};
			}
			return returnValue;
		case 30040:
			returnValue.success = false;
			returnValue.data = {
				title: "店铺已禁用",
			};
			return returnValue;
		case 30034:
			returnValue.success = false;
			returnValue.data = {
				title: "提示",
				content: msg,
			};
			return returnValue;
		case 30039:
			switch (true) {
				// notExists, 不存在的 sku key 集合格式 [{shopId,productId,skuId},{shopId,productId,skuId}]
				case data.notExists.length > 0:
					returnValue.success = false;
					returnValue.data = {
						title: "规格不存在",
						content: formatContent(params, data.notExists).join(","),
					};
					break;
				case data.stockNotEnough.length > 0:
					returnValue.success = false;
					returnValue.data = {
						title: "库存不足",
						content: formatContent(params, data.stockNotEnough, "stockNotEnough").join(","),
					};
					break;
				case data.limitNotEnough.length > 0:
					//    limitNotEnough:, 超限购的商品 数据格式[{key,limit:限购数,bought:已购数},{key,limit:限购数,bought:已购数}]、
					//    Key可能是两种格式商品限购KEY格式为 {shopId,productId}，sku 限购 KEY 格式为{shopId,productId,skuId}
					returnValue.success = false;
					returnValue.data = {
						title: "超限购",
						content: formatContent(params, data.limitNotEnough, "limitNotEnough").join(","),
					};
					break;
				default:
					break;
			}
			return returnValue;
		case 30037:
			returnValue.success = false;
			// returnValue.data = {
			// 	title: '提示',
			// 	content: msg
			// }
			returnValue.data = {
				title: "提示",
				content: `抱歉给您带来不便！系统检测到[账号身份异常]，为保护资金安全，将临时冻结下单等功能，您可正常浏览商品、联系客服等，请联系客服人员快速解冻！`,
			};
			return returnValue;
		default:
			return returnValue;
	}
};

function formatContent(arr1 = [], arr2 = [], key = "") {
	const content = [];
	for (let i = 0; i < arr1.length; i++) {
		const paraItem = arr1[i];
		for (let j = 0; j < arr2.length; j++) {
			const notItem = arr2[j];
			paraItem.products.forEach((product) => {
				const isShop = paraItem.shopId === (notItem.shopId || notItem.key.shopId);
				const isProduct = product.productId === (notItem.productId || notItem.key.productId);
				const isSkuId = product.skuId === (notItem.skuId || notItem.key.skuId);
				const specs = product.specs ? product.specs : [];
				let productKey = `${product.productName + " 规格:" + specs.join(",")}`;
				const limitNotEnoughIsSku = !notItem.key?.skuId || isSkuId;
				if (key === "limitNotEnough" && isShop && isProduct && limitNotEnoughIsSku) {
					//   notItem.key.skuId  不存在是商品限购 存在是规格限购
					const str = `商品:${productKey} 此${notItem.key.skuId ? "规格" : "商品"}限购${notItem.limit}件,已购${notItem.bought}件`;
					content.push(str);
					return;
				}
				if (isShop && isProduct && isSkuId) {
					if (key === "stockNotEnough") {
						productKey += `库存仅剩${notItem.stock}件`;
					}
					content.push(productKey);
				}
			});
		}
	}
	return content;
}

/**
 * 根据订单号获取订单类型、店铺 id
 */
export const getOrderTypeByOrderNo = (
	params = {
		orderNo: "",
	},
	config = {}
) => {
	return uni.$uv.http.get(`/yays-mall-order/order/orderType/${params.orderNo}`, {
		params: {
			...params,
		},
		...config,
	});
};

/**
 * 获取订单基础设置
 */
export const getOrderBaseConfig = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-order/order/config/timeout`, {
		params: {
			...params,
		},
		...config,
	});
};

/**
 * @description 收获地址是否在白名单 /uk-health/yk/address-white-list/isAddressInWhiteList
 * @param province 省
 * @param city 市
 */
export const IsAddressInWhiteList = (data = {}, config = {}) => {
	return uni.$uv.http.post(`/uk-health/yk/address-white-list/isAddressInWhiteList`, { ...data }, { ...config });
};
