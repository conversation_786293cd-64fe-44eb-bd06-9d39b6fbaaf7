/**
 * 创建/编辑发票抬头
 */
export const editInvoiceHeader = (data = {}, config = {}) => {
	return uni.$uv.http.post(`/addon-invoice/invoice/invoice-headers/invoice-header`, {
		...data
	}, {
		...config
	})
}

/**
 * 分页查询发票抬头
 */
export const getInvoiceHeader = (params = {
	size: 500,
	ownerType: 'USER',
}, config = {}) => {
	return uni.$uv.http.get(
		`/addon-invoice/invoice/invoice-headers/pageInvoiceHeader`, {
			params: {
				...params
			},
			...config
		})
}

/**
 * 设置默认抬头
 */
export const setDefaultInvoiceHeader = (data = {}, config = {}) => {
	return uni.$uv.http.put(`/addon-invoice/invoice/invoice-headers/default-invoice-header`, {
		...data
	}, {
		...config
	})
}

/**
 * 获取默认抬头
 */
export const getDefaultInvoiceHeader = (params = {
	invoiceHeaderOwnerType: 'USER'
}, config = {}) => {
	return uni.$uv.http.get(
		`/addon-invoice/invoice/invoice-headers/getDefaultInvoiceHeader`, {
			params: {
				...params
			},
			...config
		})
}

/**
 * 删除发票抬头
 */
export const delInvoiceHeader = (data = {
	invoiceHeaderId: ''
}, config = {}) => {
	return uni.$uv.http.delete(`/addon-invoice/invoice/invoice-headers/${data.invoiceHeaderId}`, {
		...data
	}, {
		...config
	})
}

/**
 * 获取发票抬头详情
 */
export const getInvoiceHeaderDetail = (params, config = {}) => {
	return uni.$uv.http.get(
		`/addon-invoice/invoice/invoice-headers`, {
			params,
			...config
		})
}

/**
 * 临时存储用户选中的抬头 id
 */
export const storageSetInvoiceHeaderId = (data = {}, config = {}) => {
	return uni.$uv.http.post(`/addon-invoice/invoice/invoice-headers/temporaryStorageInvoiceHeaderId`, {
		...data
	}, {
		...config
	})
}

/**
 * 获取临时存储用户选中的抬头 id
 */
export const storageGetInvoiceHeaderId = (params, config = {}) => {
	return uni.$uv.http.get(
		`/addon-invoice/invoice/invoice-headers/getInvoiceHeaderId`, {
			params,
			...config
		})
}

/**
 * 申请开票
 */
export const invoiceRequest = (data = {}, config = {}) => {
	return uni.$uv.http.post(`/addon-invoice/invoice/invoiceRequest`, {
		...data
	}, {
		...config
	})
}

/**
 * 查询发票详情
 */
export const invoiceDetail = (params = {
	id: ''
}, config = {}) => {
	return uni.$uv.http.get(
		`/addon-invoice/invoice/invoiceRequest/${params.id}`, {
			params,
			...config
		})
}

/**
 * 获取发票设置
 */
export const getShopInvoiceSettings = (params, config = {}) => {
	return uni.$uv.http.get(
		`/addon-invoice/invoice/invoiceSettings`, {
			params,
			...config
		})
}

/**
 * 获取发票状态
 */
export const getInvoiceStatus = (params, config = {}) => {
	return uni.$uv.http.get(
		`/addon-invoice/invoice/invoiceRequest/pre-request`, {
			params,
			...config
		})
}

/**
 * 撤销开票
 */
export const withdrawInvoiceRequest = (data = {
	id: ''
}, config = {}) => {
	return uni.$uv.http.put(`/addon-invoice/invoice/invoiceRequest/${data.id}`, {
		...data
	}, {
		...config
	})
}


/**
 * 重申
 */
export const anewSendInvoice = (data = {
	id: ''
}, config = {}) => {
	return uni.$uv.http.post(`/addon-invoice/invoice/invoiceAttachment/re-send`, {
		...data
	}, {
		...config
	})
}