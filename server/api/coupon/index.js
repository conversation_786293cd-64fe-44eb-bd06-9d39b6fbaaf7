/**
 * @description 查询当前用户是否提示活动
 *
 */
export const checkIfShowedActivity = (params, config = {}) => {
  return uni.$uv.http.get(`/yays-mall-uaa/uaa/login-activity-config/getActivity`, {
			params,
			...config
		});
};


/**
 * @description 标记用户已提示活动
 */
export const  markUserGotCoupon = (data, config = {}) => {
  return uni.$uv.http.post(`/yays-mall-uaa/uaa/login-activity-user/create`, data, config);
};


