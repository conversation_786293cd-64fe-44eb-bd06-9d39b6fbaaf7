/**
 * 添加购物车
 */
export const addToCart = (data = {}, config = {}) => {
	return uni.$uv.http.post(`/yays-mall-cart/cart`, {
		...data
	}, {
		...config
	})
};

/**
 * 获取购物车列表
 */
export const getShopCarList = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-cart/cart`, {
		params: {
			...params
		},
		...config
	})
};


/**
 * 删除购物车数据
 */
export const deleteShopCarData = (data, config = {}) => {
	return uni.$uv.http.put(`/yays-mall-cart/cart`, data, {
		...config
	})
};

/**
 * 修改购物车商品
 */
export const editShopCarGood = (data = {
	id: '',
}, config = {}) => {
	return uni.$uv.http.put(`/yays-mall-cart/cart/${data.id}`, data, {
		...config
	})
};

/**
 * 清空购物车失效商品
 */
export const emptyShopCarData = (data = {}, config = {}) => {
	return uni.$uv.http.delete(`/yays-mall-cart/cart/invalid`, {
		...data
	}, {
		...config
	})
};