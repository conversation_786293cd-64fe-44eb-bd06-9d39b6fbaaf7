/**
 * @description 获取该家庭圈下所有成员
 * @param {String} params.userId 用户id
 * @param {*} config
 * @returns
 */
export const GetFamilyAllMembers = (params, config = {}) => {
	return uni.$uv.http.get(`/uk-health/yk/family-member-realtion/getFamilyAllMembers`, { params, config });
};

/**
 * @description 查看当前用户步骤完成状态
 * @param {String} params.userId 用户id
 */
export const GetUserRegisterStatusByUserId = (params, config = {}) => {
	return uni.$uv.http.get(`/uk-health/yk/user-register-status/getUserRegisterStatusByUserId`, { params, config });
};

/**
 * @description 通过userId查询用户基本信息 /uk-health/yk/user-basic-info/getInfoByUserId
 */
export const GetUserInfoByUserId = (params, config = {}) => {
	return uni.$uv.http.get(`/uk-health/yk/user-basic-info/getInfoByUserId`, { params, config });
};

/**
 * @description 创建用户基本档案
 */
export const CreateUserBasicInfo = (data, config = {}) => {
	return uni.$uv.http.post(`/uk-health/yk/user-basic-info/create`, { ...data }, { ...config });
};

/**
 * @description 用户基本信息修改
 * @param {Object} data
 * @param {String} data.id 用户id
 * @param {String} data.userName 用户名称
 * @param {String} data.phone 用户手机号
 * @param {String} data.sex 性别
 * @param {String} data.birth 出生年月
 * @param {String} data.height 身高
 * @param {String} data.weight 体重
 * @param {String} data.circum 臀围
 * @param {String} data.waist 腰围
 * @param {String} data.ra 类风湿性关节炎
 * @param {String} data.op 骨质疏松症
 * @param {String} data.secondaryOp 骨质疏松相关疾病
 * @param {String} data.ach 您是否服用过(强的松、塞米松) 等药品
 * @param {String} data.smoke 吸烟状态
 */
export const UpdateUserBasicInfo = (data, config = {}) => {
	return uni.$uv.http.put(`/uk-health/yk/user-basic-info/update`, { ...data }, { ...config });
};

/**
 * @description 获取用户所有健康问卷
 * @param {String} params.userId 用户id
 */
export const GetUserQuestionnairePage = (params, config = {}) => {
	return uni.$uv.http.get(`/uk-health/yk/user-question-naire/page`, { params, config });
};

/**
 * @description 创建问卷调查
 * @param {Object} data
 * @param {String} data.userId 用户id
 * @param {String} data.naireType 问卷类型 - 0:基础版 1:详尽版
 * @param {String} data.naireNo 问卷no
 * @param {String} data.naireContent 问卷内容
 */
export const CreateUserQuestionnaire = (data, config = {}) => {
	return uni.$uv.http.post(`/uk-health/yk/user-question-naire/create`, { ...data }, { ...config });
};

/**
 * @description 获取问卷调查内容
 * @param {String} params.id
 */
export const GetUserQuestionnaire = (params, config = {}) => {
	return uni.$uv.http.get(`/uk-health/yk/user-question-naire/get`, { params, config });
};

/**
 * @description 删除问卷调查
 * @param {String} data.id 问卷id
 */
export const DeleteUserQuestionnaire = (data, config = {}) => {
	return uni.$uv.http.delete(`/uk-health/yk/user-question-naire/delete?id=${data.id}`, {}, { ...config });
};

/**
 * @description 新增家庭圈人员 (废弃)
 * @param {Object} data
 * @param {String} data.id 主键编号
 * @param {String} data.familyId 家庭圈id
 * @param {String} data.userId 用户id
 * @param {String} data.familyName 家庭圈名称
 * @param {String} data.userName 用户名称
 * @param {String} data.nikeName 用户昵称
 * @param {String} data.phone 用户手机号
 * @param {String} data.isCreated 是否创建者
 */
export const CreateFamilyMemberRealtion = (data, config = {}) => {
	return uni.$uv.http.post(`/uk-health/yk/family-member-realtion/create`, { ...data }, { ...config });
};

/**
 * @description 更新家庭圈关注人信息 / 更新家庭圈名称（传id、familyId、userId）
 * @param {Object} data
 * @param {String} data.id 主键编号
 * @param {String} data.familyId 家庭圈id
 * @param {String} data.userId 用户id
 * @param {String} data.familyName 家庭圈名称
 * @param {String} data.userName 用户名称
 * @param {String} data.nikeName 用户昵称
 * @param {String} data.phone 用户手机号
 * @param {String} data.isCreated 是否创建者
 */
export const UpdateFamilyMemberRealtion = (data, config = {}) => {
	return uni.$uv.http.put(`/uk-health/yk/family-member-realtion/update`, { ...data }, { ...config });
};

/**
 * @description 删除家庭圈人员
 * @param {String} data.id 主键编号
 */
export const DeleteFamilyMemberRealtion = (data, config = {}) => {
	return uni.$uv.http.delete(`/uk-health/yk/family-member-realtion/delete?id=${data.id}`, {}, { ...config });
};

/**
 * @description 添加成员-发送验证码
 * @param {String} data.mobile 手机号
 */
export const SendAddMemberCaptcha = (data, config = {}) => {
	return uni.$uv.http.post(`/yays-mall-uaa/uaa/auth/captcha/sms/ADDMEMBER`, { ...data }, { ...config });
};

/**
 * @description 添加家庭圈成员
 * @param {Object} data
 * @param {String} data.familyId 家庭圈id
 * @param {String} data.familyName 家庭圈名称
 * @param {String} data.nikeName 用户昵称
 * @param {String} data.phone 用户手机号
 * @param {String} data.smsCode 验证码
 */
export const AddFamilyMember = (data, config = {}) => {
	return uni.$uv.http.post(`/uk-health/yk/family-member-realtion/createForApp`, { ...data }, { ...config });
};
