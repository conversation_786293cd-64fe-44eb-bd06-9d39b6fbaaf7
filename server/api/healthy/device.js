/**
 * @description 获取用户所有设备
 * @param {String} params.userId 用户id
 */
export const GetUserAllDevice = (params, config = {}) => {
	return uni.$uv.http.get(`/uk-health/yk/member-device-realtion/page`, { params, config });
};

/**
 * @description 新增设备
 * @param {String} data.userId 用户id
 * @param {String} data.imei 设备号
 * @param {String} data.deviceName 设备名称
 * @param {String} data.productName 设备类型
 * @param {String} data.ccid ccid
 */
export const CreateUserDevice = (data, config = {}) => {
	return uni.$uv.http.post(`/uk-health/yk/member-device-realtion/create`, { ...data }, { ...config });
};

/**
 * @description 解绑设备
 * @param {String} data.id 主键id
 */
export const DeleteUserDevice = (data, config = {}) => {
	return uni.$uv.http.delete(`/uk-health/yk/member-device-realtion/delete?id=${data.id}`, {}, { ...config });
};

/**
 * @description 获取产品列表
 * @param {String} params.categoryId 产品类型id 健康=1 安防=2
 */
export const GetProductsForAll = (params, config = {}) => {
	return uni.$uv.http.get(`/uk-health/yk/member-device-realtion/getProductsForAll`, { params, config });
};

/**
 * @description 获取设备当前数据
 * @param {String} params.userId 用户id
 */
export const GetAllDeviceRealTimeData = (params, config = {}) => {
	return uni.$uv.http.get(`/uk-health/yk/member-device-realtion/getAllDeviceRealTimeData`, { params, config });
};

/**
 * @description 获取设备是否在线
 */
export const GetDeviceIsOnline = (params, config = {}) => {
	return uni.$uv.http.get(`/uk-health/yk/member-device-realtion/getDeviceIsOnline`, { params, config });
};
