/**
 * @description 周报-总览 /yk/dolphin-result/overviewByWeek
 * @param {Object} params
 * @param {String} params.userId 用户id
 * @param {String} params.year 年份(年份、周数不传，默认返回最新一份周报)
 * @param {String} params.weekNo 周数(年份、周数不传，默认返回最新一份周报)
 * @param {String} params.resultId 报告主表id,传这个其它参数可以不传
 */
export const GetHealthReportOverview = (params, config = {}) => {
	return uni.$uv.http.get(`/uk-health/yk/dolphin-result/overviewByWeek`, { params, config });
};

/**
 * @description 周报-慢病风险评估 /yk/dolphin-result/riskByWeek
 * @param {Object} params
 * @param {String} params.userId 用户id
 * @param {String} params.year 年份(年份、周数不传，默认返回最新一份周报)
 * @param {String} params.weekNo 周数(年份、周数不传，默认返回最新一份周报)
 * @param {String} params.resultId 报告主表id,传这个其它参数可以不传
 */
export const GetHealthReportRisk = (params, config = {}) => {
	return uni.$uv.http.get(`/uk-health/yk/dolphin-result/riskByWeek`, { params, config });
};

/**
 * @description 周报-目前膳食摄入
 * @param {Object} params
 * @param {String} params.userId 用户id
 * @param {String} params.year 年份(年份、周数不传，默认返回最新一份周报)
 * @param {String} params.weekNo 周数(年份、周数不传，默认返回最新一份周报)
 * @param {String} params.resultId 报告主表id,传这个其它参数可以不传
 */
export const GetHealthReportDiet = (params, config = {}) => {
	return uni.$uv.http.get(`/uk-health/yk/dolphin-result/dietByWeek`, { params, config });
};

/**
 * @description 周报-单项指标趋势
 * @param {Object} params
 * @param {String} params.userId 用户id
 * @param {String} params.year 年份(年份、周数不传，默认返回最新一份周报)
 * @param {String} params.weekNo 周数(年份、周数不传，默认返回最新一份周报)
 * @param {String} params.resultId 报告主表id,传这个其它参数可以不传
 * @param {String} params.type 类型 1=血压 2=血糖 3=尿酸 4=血脂4项
 */
export const GetHealthReportIndex = (params, config = {}) => {
	return uni.$uv.http.post(`/uk-health/yk/dolphin-result/indexByWeek`, { ...params }, { ...config });
};

/**
 * @description 日常监测
 * @param {String} params.userId 用户id
 * @param {String} params.date 日期
 */
export const GetHealthReportDailyMonitor = (params, config = {}) => {
	return uni.$uv.http.get(`/uk-health/yk/member-device-realtion/getMonthData`, { params, config });
};

/**
 * @description 周报-结果日期列表
 * @param {String} params.userId 用户id
 */
// 导出一个名为GetHealthReportWeekList的函数，用于获取健康报告周列表
export const GetHealthReportWeekList = (params, config = {}) => {
	// 使用uni.$uv.http.get方法，发送GET请求，获取健康报告周列表
	return uni.$uv.http.get(`/uk-health/yk/dolphin-result/weekList`, { params, config });
};

/**
 * @description 生成周报
 */
export const GenerateWeeklyReport = (params, config = {}) => {
	return uni.$uv.http.post(`/uk-health/yk/weekly-publication/submitQuestionnaire`, { ...params }, { ...config });
};
