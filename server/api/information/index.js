/**
 * @description 获取资讯类目
 *
 */
export const getCategoryList = () => {
  return uni.$uv.http.post(`/uk-health/yk/article-information-category/page`, {
    pageNo: 1,
    pageSize: 100,
  });
};

/**
 * @description 获取资讯文章列表
 *
 */
export const getArticleInformationList = (data, config = {}) => {
  return uni.$uv.http.post(
    `/uk-health/yk/article-information/pageByApp`,
    { ...data, status: 1 },
    config
  );
};

/**
 * @description 获取资讯文章详情
 *
 */

export const getArticleDetail = (data, config = {}) => {
  return uni.$uv.http.post(
    `/uk-health/yk/article-information/getArticleByApp`,
    data,
    config
  );
};

/**
 * @description 给文章点赞
 * */
export const thumbUpArticle = (data, config = {}) => {
  return uni.$uv.http.post(
    `/uk-health/yk/article-information/thumbs`,
    data,
    config
  );
};

/**
 * @description 增加浏览量
 * */
export const addArticleViews = (data, config = {}) => {
  return uni.$uv.http.post(
    `/uk-health/yk/article-information/views`,
    data,
    config
  );
};

/**
 * @description 举报文章
 * */
export const reportArticle = (data, config = {}) => {
  return uni.$uv.http.post(
    `/uk-health/yk/article-report-record/create`,
    data,
    config
  );
};
