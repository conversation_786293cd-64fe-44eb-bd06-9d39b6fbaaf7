/**
 * 获取积分规则信息
 */
export const getIntegralRulesInfo = (params = {}, config = {}) => {
	return uni.$uv.http.get(
		`/addon-integral/integral/rules/info`, {
			params: {
				...params
			},
			...config
		})
}

/**
 * 积分商品列表
 */
export const getIntegralProductList = (params = {}, config = {}) => {
	return uni.$uv.http.get(
		`/addon-integral/integral/product/list`, {
			params: {
				...params
			},
			...config
		})
}

/**
 * 积分商品详情
 */
export const getIntegralProductInfo = (params = {}, config = {}) => {
	return uni.$uv.http.get(
		`/addon-integral/integral/product/info`, {
			params: {
				...params
			},
			...config
		})
}

/**
 * 积分行为天数
 */
export const getIntegralBehaviorDays = (params = {
	ruleType: '', // SHARE = '分享' LOGIN = '登入' SING_IN = '签到'
}, config = {}) => {
	return uni.$uv.http.get(
		`/addon-integral/integral/behavior/days`, {
			params: {
				...params
			},
			...config
		})
}

/**
 * 查看剩余积分
 */
export const getUserIntegralSystemtotal = (params = {
	ruleType: '', // SHARE = '分享' LOGIN = '登入' SING_IN = '签到'
}, config = {}) => {
	return uni.$uv.http.get(
		`/yays-mall-user/user/integral/system/total`, {
			params: {
				...params
			},
			...config
		})
}

/**
 * 查看积分明细
 */
export const getUserIntegralDetailInfo = (params = {}, config = {}) => {
	return uni.$uv.http.get(
		`/yays-mall-user/user/integral/detail/info`, {
			params: {
				...params
			},
			...config
		})
}