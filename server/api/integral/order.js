/**
 * 创建积分商品订单
 */
export const createIntegralOrderCreate = (data = {}, config = {}) => {
	return uni.$uv.http.post(`/addon-integral/integral/order/create`, {
		...data
	}, {
		...config
	})
}

/**
 * 查询创建情况
 */
export const getIntegralOrderCreateConditions = (params = {
	orderNo: '',
}, config = {}) => {
	return uni.$uv.http.get(
		`/addon-integral/integral/order/${params.orderNo}/creation`, {
			params: {
				...params
			},
			...config
		})
}

/**
 * 积分订单列表
 */
export const getIntegralOrderList = (params = {}, config = {}) => {
	return uni.$uv.http.get(
		`/addon-integral/integral/order/list`, {
			params: {
				...params
			},
			...config
		})
}

/**
 * 积分订单详情
 */
export const getIntegralOrderDetail = (params = {
	orderNo: '',
}, config = {}) => {
	return uni.$uv.http.get(
		`/addon-integral/integral/order/get/${params.orderNo}`, {
			params: {
				...params
			},
			...config
		})
}

/**
 * 修改积分地址
 */
export const putIntegralOrderReceiver = (data = {
	orderNo: ''
}, config = {}) => {
	return uni.$uv.http.put(`/addon-integral/integral/${data.orderNo}/receiver`, {
		...data
	}, {
		...config
	})
}

/**
 * 取消订单
 */
export const closeIntegralOrderByOrderNo = (data = {}, config = {}) => {
	return uni.$uv.http.post(`/addon-integral/integral/order/close/${data.orderNo}`, {
		...data
	}, {
		...config
	})
}

/**
 * 确认订单
 */
export const confirmIntegralGoods = (data = {
	orderNo: '',
	shopId: ''
}, config = {}) => {
	return uni.$uv.http.put(`/addon-integral/integral/order/deliver/complete/${data.orderNo}`, {
		...data
	}, {
		...config
	})
}

/**
 * 积分订单支付
 */
export const submitIntegralOrderPay = (data = {}, config = {}) => {
	return uni.$uv.http.post(`/addon-integral/integral/order/pay/get`, {
		...data
	}, {
		...config
	})
}

/**
 * 查询积分订单是否支付完成
 */
export const getIntegralOrderIsPaySuccess = (params = {
	outTradeNo: '',
}, config = {}) => {
	return uni.$uv.http.get(
		`/addon-integral/integral/order/pay/success/${params.outTradeNo}`, {
			params: {
				...params
			},
			...config
		})
}