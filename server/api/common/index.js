export * from './message'

/**
 * 上传文件
 */
export const upload = (filePath, config = {}) => {
	const uploadUrl = import.meta.env.VITE_UPLOAD_URI
	return uni.$uv.http.upload(uploadUrl, {
		filePath,
		name: 'file',
		fileType: 'image',
		...config
	})
}

/**
 * 获取功能
 */
export const getAppPlugin = (params = {}, config = {}) => {
	return uni.$uv.http.get('/yays-mall-carrier-pigeon/system/addon/addons', {
		params: {
			...params
		},
		...config
	})
}

/**
 * 根据模块获取配置信息
 */
export const queryConfigByModule = (params = {}, config = {}) => {
	return uni.$uv.http.get('/yays-mall-addon-platform/platform/config/query-config-by-module', {
		params: {
			...params
		},
		...config
	})
}

/**
 * 语音转文字
 */
export const speechToText = (filePath, config = {}) => {
	return uni.$uv.http.upload('/yays-mall-user/utils/speechToText', {
		filePath,
		name: 'file',
		fileType: 'audio',
		...config
	})
};