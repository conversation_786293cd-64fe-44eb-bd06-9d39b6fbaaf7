/**
 * 发送消息（用户发起）
 */
export const sendPigeonMessageShop = (data = {}, config = {}) => {
	return uni.$uv.http.post(`/yays-mall-carrier-pigeon/pigeon/message/shop/${data.shopId}/message`, {
		...data
	}, {
		...config
	})
}

/**
 * 客服消息列表
 */
export const getPigeonMessageShop = (params = {}, config = {}) => {
	return uni.$uv.http.get(
		`/yays-mall-carrier-pigeon/pigeon/platform-chat-room-messages/chat-rooms`, {
			params: {
				...params
			},
			...config
		})
}

/**
 * 分页查询客服聊天室记录
 */
export const getPigeonMessageShopByShopId = (params = {}, config = {}) => {
	return uni.$uv.http.get(
		`/yays-mall-carrier-pigeon/pigeon/message/shop/${params.shopId}/message`, {
			params: {
				...params
			},
			...config
		})
}

/**
 * 消息已读
 */
export const putPigeonMessageShopRead = (data = {}, config = {}) => {
	return uni.$uv.http.put(`/yays-mall-carrier-pigeon/pigeon/message/shop/${data.shopId}/read`, {
		...data
	}, {
		...config
	})
}

/**
 * 我的消息查询
 */
export const getPigeonMessageMyCount = (params = {}, config = {}) => {
	return uni.$uv.http.get(
		`/yays-mall-carrier-pigeon/pigeon/message/my/unread/count`, {
			params: {
				...params
			},
			...config
		})
}

/**
 * 用户发送信息给平台
 */
export const sendMessagePlatform = (data = {}, config = {}) => {
	return uni.$uv.http.post(`/yays-mall-carrier-pigeon/pigeon/platform-chat-room-messages/message`, {
		...data
	}, {
		...config
	})
}

/**
 * 获取平台聊天列表
 */
export const getPlatformChatRoom = (params = {}, config = {}) => {
	return uni.$uv.http.get(
		`/yays-mall-carrier-pigeon/pigeon/platform-chat-room-messages/chat-room`, {
			params: {
				...params
			},
			...config
		})
}

/**
 * 创建聊天室
 */
export const getMessagesChatRoom = (data = {}, config = {}) => {
	return uni.$uv.http.post(
		`/yays-mall-carrier-pigeon/pigeon/platform-chat-rooms/${data.shopId}${data.userId ? `/${data.userId}` : ''}`, {
			...data
		}, {
			...config
		})
}

/**
 * 删除聊天
 */
export const delMessagesChatListItem = (params = {}, config = {}) => {
	return uni.$uv.http.get(
		`/yays-mall-carrier-pigeon/pigeon/platform-chat-room-messages/del-rooms/${params.toId}`, {
			params: {
				...params
			},
			...config
		})
}