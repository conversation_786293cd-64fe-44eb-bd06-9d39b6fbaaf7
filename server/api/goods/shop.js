/**
 * 获取店铺关注状态
 */
export const getOrderConcernStatusByShopId = (params = {
	shopId: '',
}, config = {}) => {
	return uni.$uv.http.get(
		`/yays-mall-goods/shop/follow/or/not/${params.shopId}`, {
			params: {
				...params
			},
			...config
		})
}

/**
 * 取消关注|关注
 */
export const cancelAttentionAndAttention = (data = {
	name: '',
	shopId: '',
	isFollow: false,
	shopLogo: '',
}, config = {}) => {
	return uni.$uv.http.put(`/yays-mall-goods/shop/follow`, {
		...data
	}, {
		...config
	})
};

/**
 * 获取店铺关注数量
 */
export const getShopFollowCount = (params = {
	shopId: '',
}, config = {}) => {
	return uni.$uv.http.get(
		`/yays-mall-goods/shop/follow/followCount/${params.shopId}`, {
			params: {
				...params
			},
			...config
		})
}

/**
 * 获取全部关注列表
 */
export const getConcernList = (params = {}, config = {}) => {
	return uni.$uv.http.get(
		`/yays-mall-goods/shop/follow/myFollow`, {
			params: {
				...params
			},
			...config
		})
}

/**
 * 获取我的页面中关注列表
 */
export const getConcernListFromMine = (params = {
	shopName: '',
	status: 'ALL_SHOP', // ALL_SHOP->全部店铺 RECENTLY->最近查看 NEW_PRODUCTS->上新商品
	current: 1,
	size: 10
}, config = {}) => {
	return uni.$uv.http.get(
		`/yays-mall-goods/shop/follow/myFollow/${params.status}`, {
			params: {
				...params
			},
			...config
		})
}

/**
 * 获取推荐关注店铺
 */
export const getConcernShopListInfo = (params = {
	current: 1,
	size: 10
}, config = {}) => {
	return uni.$uv.http.get(
		`/yays-mall-goods/shop/follow/shopInfo`, {
			params: {
				...params
			},
			...config
		})
}

/**
 * 获取推荐店铺
 */
export const getConcernRecommandShops = (params = {
	current: 1,
	size: 10
}, config = {}) => {
	return uni.$uv.http.get(
		`/yays-mall-shop/shop/info/searchRecommendationShop`, {
			params: {
				...params
			},
			...config
		})
}