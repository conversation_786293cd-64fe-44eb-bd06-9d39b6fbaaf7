/**
 * 获取商品详情 
 */
export const getProductDetails = (data, config = {}) => {
	return uni.$uv.http.post(`/yays-mall-goods/api/product/details`, {
		...data
	}, {
		...config
	})
};

/**
 * 获取商品详情 
 */
export const getProductDetailsWithSku = (data = {
	productId: '',
	shopId: ''
}, config = {}) => {
	return uni.$uv.http.post(`/yays-mall-goods/api/product/details/${data.productId}`, {
		...data
	}, {
		...config,
		header: {
			'Shop-Id': data.shopId
		}
	})
};

/**
 * 获取单个商品信息
 */
export const getProductInfo = (params = {
	id: '',
	shopId: ''
}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-goods/api/product/get/${params.id}`, {
		params: {
			...params
		},
		...config,
		header: {
			'Shop-Id': params.shopId
		}
	})
};

/**
 * 获取一级类目商品列表
 */
export const getPrimaryTypeGoodsList = (data, config = {}) => {
	return uni.$uv.http.post(`/yays-mall-goods/api/product/getcategoryproduct`, {
		...data
	}, {
		...config
	})
};

/**
 * 获取二级类目商品列表
 */
export const getTypeGoodsList = (data, config = {}) => {
	return uni.$uv.http.post(`/yays-mall-search/search/product/by/category/list`, {
		...data
	}, {
		...config
	})
};

/**
 * 获取推荐商品列表
 */
export const getWinnowGoodsList = (data, config = {}) => {
	return uni.$uv.http.post(`/yays-mall-search/search/product/mall/list`, {
		...data
	}, {
		...config
	})
};

/**
 * 获取运费相关
 */
export const getGproductDelivery = (data, config = {}) => {
	return uni.$uv.http.post(`/yays-mall-goods/api/product/productDelivery`, {
		...data
	}, {
		...config
	})
};