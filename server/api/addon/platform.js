/**
 * 获取文章、协议
 */
export const getArticleDetails = (params = {
	params: '1',
	id: ''
}, config = {}) => {
	return uni.$uv.http.get(
		`/yays-mall-addon-platform/platform/privacyAgreement/info/${params.platform}/${params.id}`, {
			params: {
				...params
			},
			...config
		})
};

/**
 * 获取分类 - 暂弃
 */
export const getAllCategory = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-addon-platform/platform/category/apiList`, {
		params: {
			...params
		},
		...config
	})
};

/**
 * 获取服务类型
 */
export const getServiceType = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-addon-platform/platform/category/save`, {
		params: {
			...params
		},
		...config
	})
};

/**
 * 获取 Banner 列表
 */
export const getBannerList = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-addon-platform/adimg/list`, {
		params: {
			...params
		},
		...config
	})
};

/**
 * 获取 Banner 详情
 */
export const getBannerDetails = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-addon-platform/adimg/info/${params.id}`, {
		params: {
			...params
		},
		...config
	})
};

/**
 * 获取金刚区列表
 */
export const getKingKongList = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-addon-platform/column/apiList`, {
		params: {
			...params
		},
		...config
	})
};


/**
 * 获取金刚区列表 - 2
 */
export const getKingKongList2 = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-addon-platform/column/list`, {
		params: {
			...params
		},
		...config
	})
};

/**
 * 获取隐私协议
 */
export const getPrivateAgreement = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-addon-platform/platform/privacyAgreement`, {
		params: {
			...params
		},
		...config
	})
};

/**
 * 根据终端查询广告信息
 */
export const getNotification = (params = {
	endPoint: ''
}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-addon-platform/home/<USER>/win/use/${params.endPoint}`, {
		params: {
			...params
		},
		...config
	})
};

/**
 * 获取隐私协议
 */
export const getOpenScreenAdvertisement = (params = {
	endPoint: ''
}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-addon-platform/splash/use/${params.endPoint}`, {
		params: {
			...params
		},
		...config
	})
};

/**
 * 分类页查询分类
 */
export const getCategoryLevelFromPlatform = (params = {
	endPoint: ''
}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-addon-platform/platform/category/list`, {
		params: {
			...params
		},
		...config
	})
};

/**
 * 根据一级分类获取商品
 */
export const getCategoryLevelFromPlatformById = (params = {
	ids: '',
	categoryLevel: '', // LEVEL_1 LEVEL_2 LEVEL_3
}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-addon-platform/platform/category/by/ids`, {
		params: {
			...params
		},
		...config
	})
};

/**
 * 根据二级分类查询商品
 */
export const getCommodityBySecCateIdFromPlatform = (params = {
	ids: '',
	categoryLevel: '', // LEVEL_1 LEVEL_2 LEVEL_3
}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-addon-platform/platform/category/by/platformCategoryId`, {
		params: {
			...params
		},
		...config
	})
};

/**
 * 使用地区换取机构 id
 */
export const getAreaAgencyId = (data = {}, config = {}) => {
	return uni.$uv.http.post(`/yays-mall-addon-platform/agency/area`, data, {
		...config
	})
}

/**
 * 使用地区换取机构 id
 */
export const getAreaAgencyIdApi = (data = {}, config = {}) => {
	return uni.$uv.http.post(`/yays-mall-addon-platform/agency/apiArea`, data, {
		...config
	})
}