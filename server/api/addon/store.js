/**
 * 获取门店提货点 按距离排序
 */
export const getStoreDistanceList = (data = {}, config = {}) => {
	return uni.$uv.http.post(`/addon-shop-store/store/distance/list`, {
		...data
	}, {
		...config
	})
}

/**
 * 根据门店提货点查询提货时间
 */
export const getDeliveryTime = (params = {
	shopId: '',
	id: ''
}, config = {}) => {
	return uni.$uv.http.get(`/addon-shop-store/store/optional/delivery/time/${params.shopId}`, {
		params: {
			...params
		},
		...config
	})
}

/**
 * 根据门店提货点查询提货时间
 */
export const getOrderGetCodeByStoreId = (params = {
	storeId: '',
	orderNo: ''
}, config = {}) => {
	return uni.$uv.http.get(`/addon-shop-store/store/order/get/code/${params.storeId}/${params.orderNo}`, {
		params: {
			...params
		},
		...config
	})
}