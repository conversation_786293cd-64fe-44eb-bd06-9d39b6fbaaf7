/**
 * 获取优惠券列表
 */
export const getCouponList = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/addon-coupon/coupon/consumer`, {
		params: {
			...params
		},
		...config
	})
}

/**
 * 优惠券数量
 */
export const getCouponNumber = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/addon-coupon/coupon/consumer/consumerCouponNum`, {
		params: {
			...params
		},
		...config
	})
}

/**
 * 领取优惠券
 */
export const consumerCollectCoupon = (data = {
	shopId: '',
	couponId: ''
}, config = {}) => {
	return uni.$uv.http.post(`/addon-coupon/coupon/consumer/collect/shop/${data.shopId}/${data.couponId}`, {
		...data
	}, {
		...config
	})
}

/**
 * 结算获取优惠券列表
 */
export const getOrderShopCouponPage = (data = {}, config = {}) => {
	return uni.$uv.http.post(`/addon-coupon/coupon/consumer/order`, {
		...data
	}, {
		...config
	})
}

/**
 * 商品详情页获取优惠券列表
 */
export const getGoodsDetailsCouponPage = (data = {}, config = {}) => {
	return uni.$uv.http.post(`/addon-coupon/coupon/consumer/product`, {
		...data
	}, {
		...config
	})
}