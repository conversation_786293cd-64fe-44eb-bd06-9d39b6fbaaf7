/**
 * 获取分销设置
 */
export const getDistributeConfig = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/addon-distribute/distribute/config`, {
		params: {
			...params
		},
		...config
	})
}

/**
 * 获取分销设置
 */
export const getDistributeCode = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/addon-distribute/distribute/distributor/mine`, {
		params: {
			...params
		},
		...config
	})
}

/**
 * 获取分销短信验证码
 */
export const getDistributeSms = (data = {
	mobile: ''
}, config = {}) => {
	return uni.$uv.http.post(`/addon-distribute/distribute/distributor/affairs/apply/sms/${data.mobile}`, {
		...data
	}, {
		...config
	})
}

/**
 * 分销申请
 */
export const submitDistribute = (data = {
	mobile: ''
}, config = {}) => {
	return uni.$uv.http.post(`/addon-distribute/distribute/distributor/affairs/apply`, {
		...data
	}, {
		...config
	})
}

/**
 * 获取分销中心数据
 */
export const getDistributeCenter = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/addon-distribute/distribute/distributor/mine/statistics`, {
		params: {
			...params
		},
		...config
	})
}

/**
 * 获取分销客户
 */
export const getDistributeCustomer = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/addon-distribute/distribute/consumer/mine/customer`, {
		params: {
			...params
		},
		...config
	})
}

/**
 * 获取分销订单
 */
export const getDistributeOrder = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/addon-distribute/distribute/order`, {
		params: {
			...params
		},
		...config
	})
}

/**
 * 佣金提现检查
 */
export const getCheckWithdrawOrder = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/addon-distribute/distribute/bonus/withdraw`, {
		params: {
			...params
		},
		...config
	})
}

/**
 * 佣金提现
 */
export const submitCheckWithdrawOrder = (data = {}, config = {}) => {
	return uni.$uv.http.post(`/addon-distribute/distribute/bonus/withdraw`, {
		...data
	}, {
		...config
	})
}

/**
 * 佣金明细
 */
export const getWithdrawList = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-overview/overview/withdraw/distribute/mine`, {
		params,
		...config
	})
}

/**
 * 查询分销商品
 */
export const getWithdrawGoods = (data = {}, config = {}) => {
	return uni.$uv.http.post(`/addon-distribute/distribute/product/page`, {
		...data
	}, {
		...config
	})
}

/**
 * 分页查询分销排行榜
 */
export const getDistributeRank = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/addon-distribute/distribute/distributor/rank`, {
		params: {
			...params
		},
		...config
	})
}

/**
 * 根据分销商用户id查询下线分销员
 */
export const getDistributeTeam = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/addon-distribute/distribute/distributor/team`, {
		params: {
			...params
		},
		...config
	})
}

/**
 * 查询我的佣金提现账户
 */
export const getWithdrawAccounts = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-overview/withdraw/bonus/accounts`, {
		params: {
			...params
		},
		...config
	})
}

/**
 * 设置我的佣金提现账户
 */
export const setWithdrawAccounts = (data = {}, config = {}) => {
	return uni.$uv.http.put(`/yays-mall-overview/withdraw/bonus/account`, {
		...data
	}, {
		...config
	})
}

/**
 * 查询分销配置
 */
export const getDistributionEnable = (params = {}, config = {}) => {
	return uni.$uv.http.get(`/yays-mall-payment/pay/valid/service/enable`, {
		params: {
			...params
		},
		...config
	})
}

/**
 * 用户绑定激活码
 */
export const subActivatePartners = (data = {}, config = {}) => {
	return uni.$uv.http.post(`/yays-mall-user/a_code/user_bind_code`, {
		...data
	}, {
		...config
	})
}