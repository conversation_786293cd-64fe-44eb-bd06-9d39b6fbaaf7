import presetWeapp from 'unocss-preset-weapp'
import {
	transformerAttributify,
	transformerClass
} from 'unocss-preset-weapp/transformer'
import {
	defineConfig
} from 'unocss'

export default defineConfig({
	rules: [
		[
			/^(?:width-|w-)(\d+)$/,
			([, value]) => ({
				width: `${value}rpx`,
			}),
		],
		[
			/^(?:max-w-|maxw-)(\d+)$/,
			([, value]) => ({
				'max-width': `${value}rpx`,
			}),
		],
		[
			/^(?:min-w-|minw-)(\d+)$/,
			([, value]) => ({
				'min-width': `${value}rpx`,
			}),
		],
		[
			/^(?:height-|h-)(\d+)$/,
			([, value]) => ({
				height: `${value}rpx`,
			}),
		],
		[
			/^(?:max-h-|maxh-)(\d+)$/,
			([, value]) => ({
				'max-height': `${value}rpx`,
			}),
		],
		[
			/^(?:min-h-|minh-)(\d+)$/,
			([, value]) => ({
				'min-height': `${value}rpx`,
			}),
		],
		[
			/^(?:margin-|m-|margin|m)(top|left|right|bottom|t|l|r|b|x|y)-(\d+)$/,
			([, direction, value]) => {
				if (direction === 'x') {
					return {
						'margin-inline': `${value}rpx`,
					};
				} else if (direction === 'y') {
					return {
						'margin-block': `${value}rpx`,
					};
				} else {
					let keyObj = {
						t: 'top',
						l: 'left',
						r: 'right',
						b: 'bottom',
						top: 'top',
						left: 'left',
						right: 'right',
						bottom: 'bottom',
					};

					let directionKey = keyObj[direction];

					return {
						[`margin-${directionKey}`]: `${value}rpx`,
					};
				}
			},
		],
		[
			/^(?:padding-|p-|padding|p)(top|left|right|bottom|t|l|r|b|x|y)-(\d+)$/,
			([, direction, value]) => {
				if (direction === 'x') {
					return {
						'padding-inline': `${value}rpx`,
					};
				} else if (direction === 'y') {
					return {
						'padding-block': `${value}rpx`,
					};
				} else {
					let keyObj = {
						t: 'top',
						l: 'left',
						r: 'right',
						b: 'bottom',
						top: 'top',
						left: 'left',
						right: 'right',
						bottom: 'bottom',
					};

					let directionKey = keyObj[direction];

					return {
						[`padding-${directionKey}`]: `${value}rpx`,
					};
				}
			},
		],
		[
			/^(?:font-size-|text-)(\d+)$/,
			([, d]) => ({
				'font-size': `${d}rpx`,
			}),
		],
		[
			/^line-height-(\d+)$/,
			([, d]) => ({
				'line-height': `${d}rpx`,
			}),
		],
		[
			/^(?:border-|b-)?(?:rounded|rd)()(?:-(.+))?$/,
			([, , d]) => ({
				'border-radius': `${d}rpx`,
			}),
		],
		[
			/^(?:border-|b-|border|b)(top|left|right|bottom|t|l|r|b|x|y)-(\d+)$/,
			([, direction, value]) => {
				if (direction === 'x') {
					return {
						'border-left-width': `${value}rpx`,
						'border-right-width': `${value}rpx`,
					};
				} else if (direction === 'y') {
					return {
						'border-top-width': `${value}rpx`,
						'border-bottom-width': `${value}rpx`,
					};
				} else {
					let keyObj = {
						t: 'top',
						l: 'left',
						r: 'right',
						b: 'bottom',
						top: 'top',
						left: 'left',
						right: 'right',
						bottom: 'bottom',
					};

					let directionKey = keyObj[direction];

					return {
						[`border-${directionKey}-width`]: `${value}rpx`,
					};
				}
			},
		],
		[
			/^(?:position-|pos-)?(top|left|right|bottom|t|l|r|b|x|y)-(\d+)$/,
			([, direction, value]) => {
				if (direction === 'x') {
					return {
						left: `${value}rpx`,
						right: `${value}rpx`,
					};
				} else if (direction === 'y') {
					return {
						top: `${value}rpx`,
						bottom: `${value}rpx`,
					};
				} else {
					let keyObj = {
						t: 'top',
						l: 'left',
						r: 'right',
						b: 'bottom',
						top: 'top',
						left: 'left',
						right: 'right',
						bottom: 'bottom',
					};

					let directionKey = keyObj[direction];

					return {
						[`${directionKey}`]: `${value}rpx`,
					};
				}
			},
		],
		[/^p-?(\d+)$/, ([, d]) => ({
			padding: `${d}rpx`
		})],
		[/^m-?(\d+)$/, ([, d]) => ({
			margin: `${d}rpx`
		})],
		[/^gap-?(\d+)$/, ([, d]) => ({
			'gap': `${d}rpx`
		})],
	],
	// rules: [
	// 	[/^p-?(\d+)$/, ([, d]) => ({ padding: `${d}rpx` })],
	// 	[/^p-?t-?(\d+)$/, ([, d]) => ({ 'padding-top': `${d}rpx` })],
	// 	[/^p-?b-?(\d+)$/, ([, d]) => ({ 'padding-bottom': `${d}rpx` })],
	// 	[/^p-?l-?(\d+)$/, ([, d]) => ({ 'padding-left': `${d}rpx` })],
	// 	[/^p-?r-?(\d+)$/, ([, d]) => ({ 'padding-right': `${d}rpx` })],
	// 	[/^p-?x-?(\d+)$/, ([, d]) => ({ 'padding-inline': `${d}rpx` })],
	// 	[/^p-?y-?(\d+)$/, ([, d]) => ({ 'padding-block': `${d}rpx` })],

	// 	[/^m-?(\d+)$/, ([, d]) => ({ margin: `${d}rpx` })],
	// 	[/^m-?t-?(\d+)$/, ([, d]) => ({ 'margin-top': `${d}rpx` })],
	// 	[/^m-?b-?(\d+)$/, ([, d]) => ({ 'margin-bottom': `${d}rpx` })],
	// 	[/^m-?l-?(\d+)$/, ([, d]) => ({ 'margin-left': `${d}rpx` })],
	// 	[/^m-?r-?(\d+)$/, ([, d]) => ({ 'margin-right': `${d}rpx` })],
	// 	[/^m-?x-?(\d+)$/, ([, d]) => ({ 'margin-inline': `${d}rpx` })],
	// 	[/^m-?y-?(\d+)$/, ([, d]) => ({ 'margin-block': `${d}rpx` })],

	// 	[/^border-?rd-?(\d+)$/, ([, d]) => ({ 'border-radius': `${d}rpx` })],

	// 	[/^line-?height-?(\d+)$/, ([, d]) => ({ 'line-height': `${d}rpx` })],

	// 	[/^gap-?(\d+)$/, ([, d]) => ({ 'gap': `${d}rpx` })],
	// ],
	presets: [
		// https://github.com/MellowCo/unocss-preset-weapp
		presetWeapp({
			transform: true,
			platform: "uniapp",
			designWidth: 750,
			deviceRatio: 750,
		}),
	],
	shortcuts: [{
		'border-base': 'border border-gray-500_10',
		'center': 'flex justify-center items-center',
		'flex-center': 'justify-center items-center'
	}],
	transformers: [
		// options https://github.com/MellowCo/unocss-preset-weapp/tree/main/src/transformer/transformerAttributify
		transformerAttributify(),

		// options https://github.com/MellowCo/unocss-preset-weapp/tree/main/src/transformer/transformerClass
		transformerClass(),
	],
	content: {
		pipeline: {
			include: [
				/\.vue$/,
				/\.vue\?vue/,
				/\.stylus$/,
				/\.jsx?$/ // 增加对 JS/JSX 文件的扫描
			]
		}
	}
})