{
    "Codegeex.CommitMessageStyle": "Auto",

    // Prettier 配置
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "editor.formatOnPaste": true,
    "editor.formatOnType": false,

    // 文件类型关联
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[typescript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[vue]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[json]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[html]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[css]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[scss]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[less]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },

    // Prettier 选项配置
    "prettier.printWidth": 150,
    "prettier.tabWidth": 4,
    "prettier.useTabs": false,
    "prettier.semi": false,
    "prettier.singleQuote": true,
    "prettier.quoteProps": "as-needed",
    "prettier.jsxSingleQuote": false,
    "prettier.trailingComma": "all",
    "prettier.bracketSpacing": true,
    "prettier.bracketSameLine": false,
    "prettier.arrowParens": "always",
    "prettier.proseWrap": "preserve",
    "prettier.htmlWhitespaceSensitivity": "css",
    "prettier.endOfLine": "auto",
    "prettier.requireConfig": false,
    "prettier.ignorePath": ".prettierignore"
}
