<script setup>
import { onLaunch, onShow } from "@dcloudio/uni-app";

import { usePublicStore, useAppStore, useLoginStore, useUserStore, useMessageStore } from "@/store";

import storage from "@/common/storage";
import { canENV, getQueryParam } from "@/common/utils";

// #ifdef APP
import { registerRequestPermissionTipsListener, unregisterRequestPermissionTipsListener, setRequestPermissionTips } from "@/uni_modules/uni-registerRequestPermissionTips";
import { GetWgtVersion } from "@/server/api/app/wgt";
import { compareVersion } from "@/common/utils";
// #endif

const publicStore = usePublicStore();
const appStore = useAppStore();
const loginStore = useLoginStore();

const PermissionTips = {
	"android.permission.CAMERA": '<h4>申请相机权限说明</h4><br><font color="#bdbdbd" size="16px">实现您拍摄、录视频，上传头像等功能</font>',
	"android.permission.WRITE_EXTERNAL_STORAGE": '<h4>申请存储权限说明</h4><br><font color="#bdbdbd" size="16px">为了帮您实现上传照片、上传头像、保存照片、保存邀请码等功能</font>',
	"android.permission.READ_EXTERNAL_STORAGE": '<h4>申请存储权限说明</h4><br><font color="#bdbdbd" size="16px">为了帮您实现上传照片、上传头像、保存照片、保存邀请码等功能</font>',
	"android.permission.ACCESS_COARSE_LOCATION": '<h4>申请位置权限说明</h4><br><font color="#bdbdbd" size="16px">用于在你浏览、搜索附近服务，向你推荐最新附近的服务、使用导航、设置地址等功能，以提升用户使用体验</font>',
	"android.permission.ACCESS_FINE_LOCATION": '<h4>申请位置权限说明</h4><br><font color="#bdbdbd" size="16px">用于在你浏览、搜索附近服务，向你推荐最新附近的服务、使用导航、设置地址等功能，以提升用户使用体验</font>',
	"android.permission.RECORD_AUDIO": '<h4>申请录音权限说明</h4><br><font color="#bdbdbd" size="16px">用于设置个性语音、发送语音等功能</font>',
};

let isAndroidApp = false;

// #ifdef APP
isAndroidApp = uni.getSystemInfoSync().osName === "android";
// #endif

function resetData() {
	storage.remove("isRefreshToken");
}

function getPushMessage(res) {
	canENV(() => {
		console.log(`Push Get Message`, res);
	});
}

function loadUIProps() {
	// #ifdef APP
	if (isAndroidApp) {
		const brand = uni.getSystemInfoSync().deviceBrand;
		setRequestPermissionTips(PermissionTips);
		registerRequestPermissionTipsListener({
			onRequest: (e) => {},
			onConfirm: (e) => {},
			onComplete: (e) => {
				if (brand.toLowerCase() === "huawei") {
					var tips = {};
					var hasDeniedPermission = false;
					for (var k in PermissionTips) {
						if (e[k] != "denied") {
							tips[k] = PermissionTips[k];
						} else {
							hasDeniedPermission = true;
						}
					}
					setRequestPermissionTips(tips);
					if (hasDeniedPermission) {
						uni.showModal({
							content: "权限已经被拒绝，请前往设置中开启",
							success(res) {
								if (res.confirm) {
									uni.openAppAuthorizeSetting();
								}
							},
						});
					} else {
						if (e["android.permission.ACCESS_COARSE_LOCATION"] === "grant") {
						}
					}
				}
			},
		});
	}
	// #endif
}

function getAppInfo() {
	appStore.initAppInfo();

	// 初始化用户信息（包含固定token检查）
	useUserStore().initUserInfo();
}

function loadData() {
	getAppInfo();

	// uni.getPushClientId({
	// 	success: (res) => {
	// 		canENV(() => {
	// 			console.log(`客户端 Push Client Id => `, res);
	// 		});
	// 	},
	// 	fail: (err) => {
	// 		console.error(err);
	// 	}
	// });

	uni.onPushMessage(getPushMessage);

	// #ifdef APP
	plus.runtime.setBadgeNumber(0);
	plus.runtime.setBadgeNumber(-1);
	// #endif

	useMessageStore().initMsg();
}

onShow(() => {
	loadEnterOptions();
});

onLaunch(() => {
	// loadEnterOptions();

	// #ifdef APP || H5 || MP-WEIXIN
	const systemInfo = {};
	const windowInfo = uni.getWindowInfo();
	const deviceInfo = uni.getDeviceInfo();
	systemInfo.statusBarHeight = windowInfo.statusBarHeight;
	systemInfo.safeAreaBottom = windowInfo.safeAreaBottom;
	systemInfo.windowWidth = windowInfo.windowWidth;
	systemInfo.windowHeight = windowInfo.windowHeight;
	systemInfo.safeAreaInsets = windowInfo.safeAreaInsets;
	systemInfo.platform = deviceInfo.platform;
	// #endif
	// #ifndef APP || H5 || MP-WEIXIN
	const systemInfo = uni.getSystemInfoSync();
	// #endif

	publicStore.updateSystemInfo(systemInfo);
	// #ifdef MP-WEIXIN
	const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
	publicStore.updateMenuButtonInfo(menuButtonInfo);
	// console.log(menuButtonInfo);
	// #endif

	loadUIProps();

	resetData();

	loadData();

	// #ifdef MP-WEIXIN
	checkForUpdate();
	// #endif
	// #ifdef APP
	// 开发环境开启热更新会影响调试
	canENV(
		() => {},
		() => {
			checkAppVersion();
		}
	);
	// #endif
});

function loadEnterOptions() {
	const enterOptions = uni.getEnterOptionsSync();

	canENV(() => {
		console.log(enterOptions);
	});

	// uni.showModal({
	// 	content: JSON.stringify(enterOptions)
	// });

	if (enterOptions.query.referral_code) {
		appReferralCode(enterOptions.query.referral_code, Boolean(Number(enterOptions.query.login || 0)));
	}

	if (enterOptions.query.scene) {
		const scene = decodeURIComponent(enterOptions.query.scene);
		const referral_code = getQueryParam(scene, "referral_code");
		const goLogin = getQueryParam(scene, "login");
		if (referral_code) {
			appReferralCode(referral_code, Boolean(Number(goLogin || 0)));
		}
	}
}

function appReferralCode(code, goLogin = false) {
	canENV(() => {
		console.log("邀请码 =>", code, goLogin);
	});
	appStore.changeInviteCode(code);
	if (goLogin) {
		loginStore.logOut();
	}
}

// 检测是否更新
function checkForUpdate() {
	// 检查小程序是否有新版本发布
	const updateManager = uni.getUpdateManager();
	// 请求完新版本信息的回调
	updateManager.onCheckForUpdate((res) => {
		//检测到新版本，需要更新，给出提示
		if (res && res.hasUpdate) {
			uni.showModal({
				title: "更新提示",
				content: "检测到新版本，是否下载新版本并重启小程序？",
				success(res) {
					if (res.confirm) {
						//用户确定下载更新小程序，小程序下载及更新静默进行
						downLoadAndUpdate(updateManager);
					} else {
						// 若用户点击了取消按钮，二次弹窗，强制更新，如果用户选择取消后不需要进行任何操作，则以下内容可忽略
						uni.showModal({
							title: "温馨提示~",
							content: "本次版本更新涉及到新的功能添加，旧版本无法正常访问的哦~",
							confirmText: "确定更新",
							cancelText: "取消更新",
							success(res) {
								if (res.confirm) {
									//下载新版本，并重新应用
									downLoadAndUpdate(updateManager);
								}
							},
						});
					}
				},
			});
		}
	});
}

// 下载小程序新版本并重启应用
function downLoadAndUpdate(updateManager) {
	uni.showLoading({ title: "小程序更新中" });

	// //静默下载更新小程序新版本
	updateManager.onUpdateReady((res) => {
		uni.hideLoading();
		//新的版本已经下载好，调用 applyUpdate 应用新版本并重启
		updateManager.applyUpdate();
	});

	// 更新失败
	updateManager.onUpdateFailed((res) => {
		// 新的版本下载失败
		uni.hideLoading();
		uni.showModal({
			title: "已经有新版本了哟~",
			content: "新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~",
			showCancel: false,
		});
	});
}

// app热更新 需要在manifest手动增加版本号
function checkAppVersion() {
	// #ifdef APP
	// 获取指定APPID对应的应用信息
	plus.runtime.getProperty(plus.runtime.appid, async function (wgtinfo) {
		const version = wgtinfo.version;
		try {
			// 获取热更新版本
			const res = await GetWgtVersion({ type: 2 });
			if (res?.data?.list?.length > 0) {
				const data = res.data.list[0];
				// 版本检测，当前软件版本小于热更新版本则更新
				if (data.version && compareVersion(data.version, version) > 0) {
					uni.showLoading({ title: "版本更新中..." });
					// 下载文件资源到本地，返回文件的本地临时路径
					uni.downloadFile({
						url: data.url,
						success: (downloadResult) => {
							if (downloadResult.statusCode === 200) {
								// 安装应用 force:false 表示只检测版本号
								plus.runtime.install(
									downloadResult.tempFilePath,
									{
										force: false,
									},
									function () {
										uni.hideLoading();
										// 重启当前应用
										plus.runtime.restart();
									},
									function (e) {
										uni.hideLoading();
									}
								);
							}
						},
					});
				}
			}
		} catch (error) {
			canENV(() => {
				console.error("热更新检查失败：", error);
			});
		}
	});
	// #endif
}
</script>

<style lang="scss">
/*每个页面公共css */
@import "@/uni_modules/uv-ui-tools/index.scss";

.active-opacity,
.ac-op {
	&:active {
		opacity: 0.8;
	}
}

.bd-box {
	box-sizing: border-box;
}

/* #ifndef MP-WEIXIN */
* {
	box-sizing: border-box;
}

body,
page {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
	font-family: "PingFang-SC-Medium";
}

/* #endif */

view,
image {
	box-sizing: border-box;
}

.page-main {
	min-width: 100vw;
	min-height: calc(100vh - var(--window-top) - var(--window-bottom));
}

.nav-center {
	/* #ifndef MP-WEIXIN */
	text-align: center;
	/* #endif */
}

.text-main {
	color: #00b496;
}

.no-scroll {
	overflow: hidden;
}
</style>
