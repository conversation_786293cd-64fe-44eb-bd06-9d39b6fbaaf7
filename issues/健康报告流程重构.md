# 健康报告流程重构

## 重构目标
按照用户需求重新整理健康报告的逻辑流程：
1. 先查weeklist接口，根据有无数据进行下一步
2. 无数据时调用GetUserRegisterStatusByUserId，根据状态显示"去生成"或"去完成"按钮
3. 有数据时拿最新一条的resultId调用总览接口，根据status显示相应状态
4. 实现3秒轮询机制

## 重构内容

### 1. 新增状态管理
- `reportGenerating`: 报告生成中状态
- `pollingTimer`: 轮询定时器

### 2. 主控制函数 `initHealthReportFlow`
替换原来的 `getUserRegisterStatus`，集中管理整个周报流程：
- 步骤1：调用 `getHealthReportWeekListData()` 获取周报列表
- 步骤2A：无数据时调用 `handleNoWeekListData()` 处理注册状态
- 步骤2B：有数据时调用 `handleGenerateAHealthReportWithResultId()` 处理报告

### 3. 轮询机制
- `startPolling()`: 开始3秒轮询
- `clearPolling()`: 清除轮询定时器
- 轮询条件：生成报告后或status为0时
- 轮询目标：重新调用 `initHealthReportFlow()`

### 4. 辅助函数
- `getHealthReportWeekListData()`: 纯数据获取，不更新UI
- `handleNoWeekListData()`: 处理无周报数据情况
- `handleGenerateAHealthReportWithResultId()`: 使用resultId调用总览接口
- `handleReportAction()`: 处理按钮点击事件

### 5. UI更新
- 按钮显示逻辑：根据注册状态显示"去生成"或"去完成"
- 生成中状态：显示"生成中..."按钮并禁用
- 生成中按钮样式：灰色背景，禁用状态

### 6. 调用点更新
- `getFamilyAllMembers()`: 调用 `initHealthReportFlow()`
- `handleMemberClick()`: 调用 `initHealthReportFlow()`
- `onRefresh()`: 重新初始化流程

## 流程图

```
开始
  ↓
调用weeklist接口
  ↓
有数据？
├─ 是 → 取最新resultId → 调用总览接口 → status=0？
│                                      ├─ 是 → 显示"生成中" + 开始轮询
│                                      └─ 否 → 显示报告
└─ 否 → 调用注册状态接口 → 所有状态=1？
                        ├─ 是 → 显示"去生成"按钮
                        └─ 否 → 显示"去完成"按钮
```

## 测试要点
1. 无周报数据时的状态判断和按钮显示
2. 有周报数据时的resultId调用和状态处理
3. 生成中状态的轮询机制
4. 按钮点击的正确跳转逻辑
5. 页面切换和刷新时的状态重置
6. 定时器的正确清理
