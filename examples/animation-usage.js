/**
 * Canvas动画使用示例
 * 展示如何在类外使用动画功能
 */

import { renderWithAnimation, stopAnimation, hasActiveAnimation } from "@/utils/canvasUtils.js";

// ==================== 示例1: 简单的函数式动画 ====================

// 简单的渲染函数
async function drawSimpleCircle(canvasManager, score, options = {}) {
	const ctx = await canvasManager.getContext();

	// 清空画布
	canvasManager.clear();

	// 绘制圆环
	const centerX = canvasManager.width / 2;
	const centerY = canvasManager.height / 2;
	const radius = options.radius || 35;
	const progress = score / 100;
	const endAngle = -Math.PI / 2 + progress * 2 * Math.PI;

	// 背景环
	ctx.beginPath();
	ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
	ctx.strokeStyle = "#f0f0f0";
	ctx.lineWidth = 8;
	ctx.stroke();

	// 进度环
	if (score > 0) {
		ctx.beginPath();
		ctx.arc(centerX, centerY, radius, -Math.PI / 2, endAngle);
		ctx.strokeStyle = "#00b496";
		ctx.lineWidth = 8;
		ctx.lineCap = "round";
		ctx.stroke();
	}

	canvasManager.draw();
	return { success: true, score };
}

// 使用动画的函数式方法
export async function animateSimpleCircle(canvasManager, fromScore, toScore, options = {}) {
	const animationId = `simple-circle-${canvasManager.canvasId}`;

	return await renderWithAnimation(
		animationId,
		fromScore,
		toScore,
		// 渲染函数
		async (currentScore) => {
			return await drawSimpleCircle(canvasManager, currentScore, options);
		},
		// 动画选项
		{
			duration: options.duration || 800,
			easing: options.easing || "easeInOutCubic",
			onUpdate: (score) => {
				console.log("当前分数:", score.toFixed(1));
			},
			onComplete: (score) => {
				console.log("动画完成，最终分数:", score);
			},
		}
	);
}

// ==================== 示例2: 对象式动画管理 ====================

export class SimpleAnimatedChart {
	constructor(canvasManager) {
		this.canvasManager = canvasManager;
		this.currentValue = 0;
		this.animationId = `chart-${canvasManager.canvasId}`;
	}

	// 渲染单帧
	async renderFrame(value, options = {}) {
		const ctx = await this.canvasManager.getContext();

		// 清空画布
		this.canvasManager.clear();

		// 绘制简单柱子
		const barHeight = (value / 100) * (this.canvasManager.height - 40);
		const barWidth = 40;
		const x = (this.canvasManager.width - barWidth) / 2;
		const y = this.canvasManager.height - 20 - barHeight;

		// 绘制柱子
		ctx.fillStyle = options.color || "#1890ff";
		ctx.fillRect(x, y, barWidth, barHeight);

		// 绘制数值
		ctx.fillStyle = "#333";
		ctx.font = "14px Arial";
		ctx.textAlign = "center";
		ctx.fillText(value.toFixed(0), this.canvasManager.width / 2, this.canvasManager.height - 5);

		this.canvasManager.draw();
		return { success: true, value };
	}

	// 动画更新到新值
	async animateTo(targetValue, options = {}) {
		// 检查是否有正在进行的动画
		if (hasActiveAnimation(this.animationId)) {
			console.log("停止现有动画");
			stopAnimation(this.animationId);
		}

		return await renderWithAnimation(
			this.animationId,
			this.currentValue,
			targetValue,
			// 渲染函数
			async (currentValue) => {
				this.currentValue = currentValue;
				return await this.renderFrame(currentValue, options);
			},
			// 动画选项
			{
				duration: options.duration || 600,
				easing: options.easing || "easeOutCubic",
			}
		);
	}

	// 立即设置值（无动画）
	async setValue(value, options = {}) {
		this.currentValue = value;
		return await this.renderFrame(value, options);
	}

	// 获取当前值
	getCurrentValue() {
		return this.currentValue;
	}

	// 停止动画
	stopAnimation() {
		stopAnimation(this.animationId);
	}
}

// ==================== 示例3: 多个Canvas同步动画 ====================

export class MultiCanvasAnimator {
	constructor(canvasManagers) {
		this.canvasManagers = canvasManagers;
		this.currentValues = new Array(canvasManagers.length).fill(0);
	}

	// 同步动画到多个Canvas
	async animateAll(targetValues, options = {}) {
		const animations = this.canvasManagers.map((canvasManager, index) => {
			const animationId = `multi-${canvasManager.canvasId}`;
			const fromValue = this.currentValues[index];
			const toValue = targetValues[index] || 0;

			return renderWithAnimation(
				animationId,
				fromValue,
				toValue,
				// 渲染函数
				async (currentValue) => {
					this.currentValues[index] = currentValue;
					return await this.renderCanvas(canvasManager, currentValue, options);
				},
				// 动画选项
				{
					duration: options.duration || 800,
					easing: options.easing || "easeInOutCubic",
				}
			);
		});

		// 等待所有动画完成
		return await Promise.all(animations);
	}

	// 渲染单个Canvas
	async renderCanvas(canvasManager, value, options = {}) {
		// 这里可以是任意的渲染逻辑
		return await drawSimpleCircle(canvasManager, value, options);
	}
}

// ==================== 示例4: 自定义缓动函数 ====================

// 弹性缓动函数
function easeOutElastic(t) {
	const c4 = (2 * Math.PI) / 3;
	return t === 0 ? 0 : t === 1 ? 1 : Math.pow(2, -10 * t) * Math.sin((t * 10 - 0.75) * c4) + 1;
}

// 使用自定义缓动的动画
export async function animateWithCustomEasing(canvasManager, fromValue, toValue, renderFn) {
	// 注册自定义缓动函数
	const { EASING_FUNCTIONS } = await import("@/utils/canvasUtils.js");
	EASING_FUNCTIONS.easeOutElastic = easeOutElastic;

	return await renderWithAnimation(`custom-${canvasManager.canvasId}`, fromValue, toValue, renderFn, {
		duration: 1200,
		easing: "easeOutElastic",
	});
}

// ==================== 使用示例 ====================

/*
// 在Vue组件中使用

import { animateSimpleCircle, SimpleAnimatedChart, MultiCanvasAnimator } from '@/examples/animation-usage.js';

// 示例1: 简单函数式动画
await animateSimpleCircle(canvasManager, 0, 85, {
  duration: 800,
  radius: 40
});

// 示例2: 对象式动画管理
const chart = new SimpleAnimatedChart(canvasManager);
await chart.animateTo(75, { color: '#52c41a' });

// 示例3: 多Canvas同步动画
const multiAnimator = new MultiCanvasAnimator([canvas1, canvas2, canvas3]);
await multiAnimator.animateAll([85, 72, 95]);

// 示例4: 自定义缓动
await animateWithCustomEasing(canvasManager, 0, 100, renderFunction);
*/
